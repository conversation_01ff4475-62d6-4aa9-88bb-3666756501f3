# Subscription Entry Implementation Guide

## Overview

The application uses a **flat subscription architecture** where all subscription sources (Stripe, perks, giveaways, free) are stored as individual entries in a single `userSubscriptions` collection. This provides a unified, scalable, and efficient subscription system.

## Architecture

### Collection Structure

```
userSubscriptions/
├── {subscriptionId1}  # Stripe subscription entry
├── {subscriptionId2}  # Perk subscription entry
├── {subscriptionId3}  # Free subscription entry
└── {subscriptionId4}  # Giveaway subscription entry
```

### Entry Schema

```typescript
interface UserSubscriptionEntry {
  id: string
  userId: string
  source: "stripe" | "perk" | "giveaway" | "free"
  status: "applied" | "pending"
  precedence: number
  startDate: Timestamp
  endDate?: Timestamp // Optional for indefinite subscriptions
  subscriptionData:
    | StripeSubscriptionData
    | PerkSubscriptionData
    | GiveawaySubscriptionData
    | FreeSubscriptionData
  createdAt: Timestamp
  updatedAt: Timestamp
}
```

### Precedence System

The system automatically applies the highest-precedence subscription:

1. **Perk** (precedence: 1) - Highest priority
2. **Giveaway** (precedence: 2) - Medium priority
3. **Stripe** (precedence: 3) - Low priority
4. **Free** (precedence: 999) - Lowest priority (fallback)

### Status Management

- **Applied**: Currently active subscription (only one per user)
- **Pending**: Available but not active due to higher precedence subscription

## Core Service

### FlatSubscriptionService

The main service that handles all subscription operations:

```typescript
import { FlatSubscriptionService } from "@/lib/domains/user-subscription/flat-subscription.service"

// Get user's current active subscription
const subscription = await FlatSubscriptionService.getCurrentSubscription(userId)

// Get subscription limits
const limits = await FlatSubscriptionService.getSubscriptionLimits(userId)

// Multi-user subscriptions (for squad badges)
const subscriptions = await FlatSubscriptionService.getMultiUserSubscriptions(userIds)
```

### Key Methods

#### getCurrentSubscription(userId)

Returns the user's currently active subscription (status: "applied").

```typescript
const subscription = await FlatSubscriptionService.getCurrentSubscription(userId)
if (subscription?.source !== "free") {
  // User has premium subscription
}
```

#### getSubscriptionLimits(userId)

Returns subscription limits based on current subscription:

```typescript
const limits = await FlatSubscriptionService.getSubscriptionLimits(userId)
// Returns: { maxSquads, maxTripsPerSquad, maxDailyAIRequests, maxWeeklyAIRequests, hasTripChat }
```

#### getMultiUserSubscriptions(userIds)

Efficient multi-user query for squad member badges:

```typescript
const subscriptions = await FlatSubscriptionService.getMultiUserSubscriptions(squadMemberIds)
// Returns array of active subscriptions for all users
```

## Subscription Sources

### 1. Stripe Subscriptions

**Source**: `"stripe"`
**Data Structure**:

```typescript
interface StripeSubscriptionData {
  customerId: string
  subscriptionId: string
  subscriptionStatus: "active" | "canceled" | "past_due" | "unpaid"
  subscriptionPlan: "monthly" | "yearly"
  currentPeriodEnd: Timestamp
}
```

**Creation**:

```typescript
import { createStripeSubscriptionEntry } from "@/lib/domains/user-subscription/user-subscription.types"

const stripeEntry = createStripeSubscriptionEntry(userId, {
  customerId: "cus_xxx",
  subscriptionId: "sub_xxx",
  subscriptionStatus: "active",
  subscriptionPlan: "monthly",
  currentPeriodEnd: endDate,
})
```

### 2. Perk Subscriptions

**Source**: `"perk"`
**Data Structure**:

```typescript
interface PerkSubscriptionData {
  perkId: string
  appliedAt: Timestamp
  duration: number // days
}
```

**Creation**:

```typescript
import { createPerkSubscriptionEntry } from "@/lib/domains/user-subscription/user-subscription.types"

const perkEntry = createPerkSubscriptionEntry(userId, {
  perkId: "perk_2months_free",
  appliedAt: new Date(),
  duration: 60,
})
```

### 3. Free Subscriptions

**Source**: `"free"`
**Data Structure**:

```typescript
interface FreeSubscriptionData {
  createdAt: Timestamp
}
```

**Creation**:

```typescript
import { createFreeSubscriptionEntry } from "@/lib/domains/user-subscription/user-subscription.types"

const freeEntry = createFreeSubscriptionEntry(userId)
```

## Hooks and State Management

### Primary Store

```typescript
import { useUserSubscriptionStore } from "@/lib/domains/user-subscription/user-subscription.store"

const {
  currentSubscription,
  isSubscribed,
  subscriptionPlan,
  maxSquads,
  maxTripsPerSquad,
  hasTripChat,
  fetchCurrentSubscription,
} = useUserSubscriptionStore()
```

### Convenient Hooks

```typescript
import {
  useIsSubscribed,
  useSubscriptionPlan,
  useMaxSquads,
  useMaxTripsPerSquad,
  useHasTripChat,
} from "@/hooks/use-subscription"

const isSubscribed = useIsSubscribed()
const maxSquads = useMaxSquads()
```

### Realtime Hooks

```typescript
import {
  useCurrentSubscriptionRealtime,
  useAllSubscriptionsRealtime,
  useSubscriptionLimitsRealtime,
} from "@/lib/domains/user-subscription/flat-subscription.realtime.hooks"

// Real-time current subscription
const { currentSubscription, loading, error } = useCurrentSubscriptionRealtime(userId)

// Real-time all user subscriptions
const { allSubscriptions } = useAllSubscriptionsRealtime(userId)

// Real-time subscription limits
const { limits } = useSubscriptionLimitsRealtime(userId)
```

## Common Patterns

### Check if User is Subscribed

```typescript
const subscription = await FlatSubscriptionService.getCurrentSubscription(userId)
const isSubscribed = subscription?.source !== "free"
```

### Get Subscription Type

```typescript
const subscription = await FlatSubscriptionService.getCurrentSubscription(userId)

switch (subscription?.source) {
  case "stripe":
    // Paid Stripe subscription
    const stripeData = subscription.subscriptionData as StripeSubscriptionData
    console.log(`Plan: ${stripeData.subscriptionPlan}`)
    break
  case "perk":
    // Perk-based subscription
    const perkData = subscription.subscriptionData as PerkSubscriptionData
    console.log(`Perk: ${perkData.perkId}`)
    break
  case "free":
    // Free subscription
    break
}
```

### Squad Member Badges

```typescript
const subscriptions = await FlatSubscriptionService.getMultiUserSubscriptions(memberIds)
const subscribedMembers = subscriptions.filter((sub) => sub.source !== "free")
```

## Migration Status

### ✅ Completed Migrations

- **User Subscriptions**: All 24 users migrated to flat structure
- **Subscription Perks**: All unlocked perks migrated to flat entries
- **Code Migration**: All services use FlatSubscriptionService
- **Database Migration**: 100% complete, no old structure entries remain

### Current State

- **30 total subscription entries**
- **25 free entries** (all users have free fallback)
- **4 Stripe entries** (paid subscriptions)
- **1 perk entry** (subscription perk)
- **0 old structure entries**

## Best Practices

### 1. Always Use FlatSubscriptionService

```typescript
// ✅ Correct
const subscription = await FlatSubscriptionService.getCurrentSubscription(userId)

// ❌ Don't use deprecated services
const subscription = await UserSubscriptionService.getUserSubscription(userId)
```

### 2. Check Subscription Source

```typescript
// ✅ Correct way to check if user has premium features
const subscription = await FlatSubscriptionService.getCurrentSubscription(userId)
const hasPremium = subscription?.source !== "free"

// ✅ Or use the limits directly
const limits = await FlatSubscriptionService.getSubscriptionLimits(userId)
const hasPremium = limits.maxSquads > 1
```

### 3. Use Realtime Hooks for UI

```typescript
// ✅ For components that need real-time updates
const { currentSubscription } = useCurrentSubscriptionRealtime(userId)

// ✅ For one-time checks in server actions
const subscription = await FlatSubscriptionService.getCurrentSubscription(userId)
```

### 4. Multi-User Queries

```typescript
// ✅ Efficient batch query
const subscriptions = await FlatSubscriptionService.getMultiUserSubscriptions(userIds)

// ❌ Don't loop individual queries
for (const userId of userIds) {
  const sub = await FlatSubscriptionService.getCurrentSubscription(userId) // Inefficient
}
```

## Troubleshooting

### Common Issues

1. **User has no subscription entries**

   - All users should have at least a free entry
   - Run migration script if needed

2. **Multiple applied entries per user**

   - Only one entry per user should have status "applied"
   - Others should be "pending"

3. **Perk not taking precedence**
   - Check that perk entry has precedence: 1
   - Verify other entries are set to "pending"

### Validation

```bash
# Check current subscription state
npm run migrate:flat-subscriptions:simple -- --validate-only

# Check perk migration state
npm run migrate:subscription-perks -- --validate-only
```

## API Endpoints

### Get Current Subscription

```typescript
// GET /api/subscriptions/current?userId={userId}
const response = await fetch(`/api/subscriptions/current?userId=${userId}`)
const { data: subscription } = await response.json()
```

### Webhook Handling

Stripe webhooks automatically update subscription entries through `FlatSubscriptionWebhooks`.

## Security

### Firestore Rules

```javascript
// Users can read any subscription data (for squad badges)
match /userSubscriptions/{subscriptionId} {
  allow read: if isAuthenticated();

  // Only system can write subscription entries
  allow write: if false;
}
```

### Data Privacy

- Subscription data is readable by authenticated users (needed for squad features)
- Sensitive Stripe data is stored in subscriptionData field
- No PII is exposed in subscription entries

## Performance

### Optimizations

- **Single query** for current subscription (no joins needed)
- **Batch queries** for multi-user subscriptions
- **Indexed queries** on userId and status fields
- **Real-time subscriptions** only for active UI components

### Monitoring

- Monitor query performance in Firebase Console
- Track subscription entry counts
- Monitor precedence conflicts (should be rare)

---

This flat subscription architecture provides a scalable, maintainable, and efficient foundation for all subscription-related features in the application.
