# Stripe Testing Guide

This document provides information on how to test the Stripe integration in Togeda.ai without using real credit cards or making actual charges.

## Test Mode

All API requests made with test API keys will create test objects that work exactly like their live counterparts but won't process actual payments. This allows you to test your integration without charging real credit cards.

## Test Cards

You can use the following test card numbers to simulate different scenarios:

### Successful Payments

| Card Number         | Brand            | CVC          | Date            |
| ------------------- | ---------------- | ------------ | --------------- |
| ************** 4242 | Visa             | Any 3 digits | Any future date |
| ************** 5556 | Visa (debit)     | Any 3 digits | Any future date |
| ************** 4444 | Mastercard       | Any 3 digits | Any future date |
| 3782 822463 10005   | American Express | Any 4 digits | Any future date |
| ************** 1117 | Discover         | Any 3 digits | Any future date |

### Special Test Scenarios

| Card Number         | Scenario                            |
| ------------------- | ----------------------------------- |
| 4000 0000 0000 0002 | Card declined (generic decline)     |
| 4000 0000 0000 9995 | Card declined (insufficient funds)  |
| 4000 0000 0000 9987 | Card declined (lost card)           |
| 4000 0000 0000 9979 | Card declined (stolen card)         |
| 4000 0027 6000 3184 | 3D Secure authentication required   |
| 4000 0000 0000 3220 | 3D Secure 2 authentication required |

### Testing Specific Subscription Scenarios

| Card Number         | Scenario                                                                           |
| ------------------- | ---------------------------------------------------------------------------------- |
| 4000 0000 0000 0341 | Attaching this card to a subscription will succeed, but the next payment will fail |
| 4000 0000 0000 0069 | Charge succeeds but address verification fails                                     |
| 4000 0000 0000 3055 | Charge succeeds but CVC verification fails                                         |

## Testing Webhooks Locally

To test webhooks locally, you can use the Stripe CLI:

1. Install the [Stripe CLI](https://stripe.com/docs/stripe-cli)
2. Login to your Stripe account:
   ```
   stripe login
   ```
3. Start forwarding webhooks to your local server:
   ```
   stripe listen --forward-to http://localhost:3000/api/stripe/webhooks
   ```
4. The CLI will output a webhook signing secret. Add this to your `.env.local` file:
   ```
   STRIPE_WEBHOOK_SECRET=whsec_...
   ```

## Testing the Subscription Flow

1. Start your development server
2. Navigate to the settings page and click on "Upgrade" in the billing section
3. Use one of the test card numbers above
4. For the expiration date, use any future date
5. For the CVC, use any 3 digits (or 4 for Amex)
6. For the postal code, use any valid postal code (e.g., "12345")

## Giving access to the Customer Portal

1. Go to https://dashboard.stripe.com/test/settings/billing/portal
2. Enable/Disable the customer portal features you want your customer to see
3. Save changes

## Viewing Test Data in the Stripe Dashboard

You can view all test objects (customers, subscriptions, payments) in the [Stripe Dashboard](https://dashboard.stripe.com/test/dashboard) while in test mode.

## Important Notes

- Test API keys start with `pk_test_` and `sk_test_`
- Live API keys start with `pk_live_` and `sk_live_`
- Make sure you're using test keys in development and live keys in production
- Test mode data is completely separate from live mode data
- You can switch between test and live mode in the Stripe Dashboard

## Additional Resources

- [Stripe Testing Documentation](https://stripe.com/docs/testing)
- [Stripe API Reference](https://stripe.com/docs/api)
- [Stripe Webhooks Guide](https://stripe.com/docs/webhooks)
