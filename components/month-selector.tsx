"use client"
import { useState } from "react"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Calendar } from "lucide-react"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"

interface MonthSelectorProps {
  selectedMonths: string[]
  onChange: (months: string[]) => void
  onConfirm?: () => void
}

export function MonthSelector({ selectedMonths, onChange, onConfirm }: MonthSelectorProps) {
  const [open, setOpen] = useState(false)

  const months = [
    { id: "jan", name: "January", season: "Winter" },
    { id: "feb", name: "February", season: "Winter" },
    { id: "mar", name: "March", season: "Spring" },
    { id: "apr", name: "April", season: "Spring" },
    { id: "may", name: "May", season: "Spring" },
    { id: "jun", name: "June", season: "Summer" },
    { id: "jul", name: "July", season: "Summer" },
    { id: "aug", name: "August", season: "Summer" },
    { id: "sep", name: "September", season: "Fall" },
    { id: "oct", name: "October", season: "Fall" },
    { id: "nov", name: "November", season: "Fall" },
    { id: "dec", name: "December", season: "Winter" },
  ]

  const seasons = [
    { id: "winter", name: "Winter", months: ["jan", "feb", "dec"] },
    { id: "spring", name: "Spring", months: ["mar", "apr", "may"] },
    { id: "summer", name: "Summer", months: ["jun", "jul", "aug"] },
    { id: "fall", name: "Fall", months: ["sep", "oct", "nov"] },
  ]

  const toggleMonth = (monthId: string) => {
    if (selectedMonths.includes(monthId)) {
      onChange(selectedMonths.filter((id) => id !== monthId))
    } else {
      onChange([...selectedMonths, monthId])
    }
  }

  const toggleSeason = (seasonMonths: string[]) => {
    const allIncluded = seasonMonths.every((month) => selectedMonths.includes(month))

    if (allIncluded) {
      // Remove all months in this season
      onChange(selectedMonths.filter((month) => !seasonMonths.includes(month)))
    } else {
      // Add all months in this season that aren't already selected
      const newMonths = [...selectedMonths]
      seasonMonths.forEach((month) => {
        if (!newMonths.includes(month)) {
          newMonths.push(month)
        }
      })
      onChange(newMonths)
    }
  }

  const getSelectedMonthsText = () => {
    if (selectedMonths.length === 0) {
      return "Select months"
    }

    if (selectedMonths.length === 12) {
      return "Any time (All year)"
    }

    // Check if entire seasons are selected
    const selectedSeasons = seasons.filter((season) =>
      season.months.every((month) => selectedMonths.includes(month))
    )

    if (selectedSeasons.length > 0 && selectedMonths.length <= 6) {
      return selectedSeasons.map((s) => s.name).join(", ")
    }

    // Otherwise show selected months
    return months
      .filter((month) => selectedMonths.includes(month.id))
      .map((month) => month.name.substring(0, 3))
      .join(", ")
  }

  const handleDoneClick = () => {
    setOpen(false)
    if (onConfirm) {
      onConfirm()
    }
  }

  return (
    <div className="space-y-2">
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button variant="outline" className="w-full justify-start text-left font-normal">
            <Calendar className="mr-2 h-4 w-4" />
            <span>{getSelectedMonthsText()}</span>
            {selectedMonths.length > 0 && (
              <Badge className="ml-auto">{selectedMonths.length}</Badge>
            )}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-full p-0" align="start">
          <div className="p-4 space-y-4">
            <div className="space-y-2">
              <h4 className="font-medium text-sm">Seasons</h4>
              <div className="flex flex-wrap gap-2">
                {seasons.map((season) => {
                  const allIncluded = season.months.every((month) => selectedMonths.includes(month))
                  return (
                    <Button
                      key={season.id}
                      variant={allIncluded ? "default" : "outline"}
                      size="sm"
                      onClick={() => toggleSeason(season.months)}
                    >
                      {season.name}
                    </Button>
                  )
                })}
                <Button
                  variant={selectedMonths.length === 12 ? "default" : "outline"}
                  size="sm"
                  onClick={() => {
                    if (selectedMonths.length === 12) {
                      onChange([])
                    } else {
                      onChange(months.map((m) => m.id))
                    }
                  }}
                >
                  All Year
                </Button>
              </div>
            </div>

            <div className="space-y-2">
              <h4 className="font-medium text-sm">Months</h4>
              <div className="grid grid-cols-3 gap-2">
                {months.map((month) => (
                  <Button
                    key={month.id}
                    variant={selectedMonths.includes(month.id) ? "default" : "outline"}
                    size="sm"
                    onClick={() => toggleMonth(month.id)}
                  >
                    {month.name.substring(0, 3)}
                  </Button>
                ))}
              </div>
            </div>

            <div className="flex justify-between">
              <Button variant="ghost" size="sm" onClick={() => onChange([])}>
                Clear
              </Button>
              <div className="flex gap-2">
                <Button size="sm" onClick={() => onChange(months.map((m) => m.id))}>
                  Select All
                </Button>
                {onConfirm && (
                  <Button size="sm" variant="default" onClick={handleDoneClick}>
                    Done
                  </Button>
                )}
              </div>
            </div>
          </div>
        </PopoverContent>
      </Popover>
    </div>
  )
}
