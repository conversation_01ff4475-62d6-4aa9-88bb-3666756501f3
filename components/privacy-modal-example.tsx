"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { PrivacyModal } from "@/components/privacy-modal"

export function PrivacyModalExample() {
  const [privacyModalOpen, setPrivacyModalOpen] = useState(false)

  return (
    <div>
      <Button variant="link" onClick={() => setPrivacyModalOpen(true)}>
        Privacy Policy
      </Button>

      <PrivacyModal open={privacyModalOpen} onOpenChange={setPrivacyModalOpen} />
    </div>
  )
}
