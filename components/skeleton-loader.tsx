"use client"

import { Skeleton } from "@/components/ui/skeleton"

interface SkeletonCardProps {
  count?: number
  aspectRatio?: "square" | "video" | "portrait" | "wide"
  className?: string
}

export function SkeletonCard({
  count = 1,
  aspectRatio = "video",
  className = "",
}: SkeletonCardProps) {
  const aspectRatioClasses = {
    square: "aspect-square",
    video: "aspect-video",
    portrait: "aspect-[3/4]",
    wide: "aspect-[2/1]",
  }

  return (
    <>
      {Array.from({ length: count }).map((_, index) => (
        <div key={index} className={`space-y-3 ${className}`}>
          <Skeleton className={`w-full ${aspectRatioClasses[aspectRatio]} rounded-lg`} />
          <div className="space-y-2">
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-3/4" />
          </div>
        </div>
      ))}
    </>
  )
}

interface SkeletonListProps {
  count?: number
  className?: string
}

export function SkeletonList({ count = 3, className = "" }: SkeletonListProps) {
  return (
    <div className={`space-y-3 ${className}`}>
      {Array.from({ length: count }).map((_, index) => (
        <div key={index} className="flex items-center space-x-4 p-4 border rounded-md">
          <Skeleton className="h-12 w-12 rounded-full" />
          <div className="space-y-2 flex-1">
            <Skeleton className="h-4 w-1/3" />
            <Skeleton className="h-4 w-1/2" />
          </div>
        </div>
      ))}
    </div>
  )
}

interface SkeletonStatsProps {
  className?: string
}

export function SkeletonStats({ className = "" }: SkeletonStatsProps) {
  return (
    <div className={`space-y-4 ${className}`}>
      <Skeleton className="h-8 w-1/3" />
      <div className="space-y-2">
        {Array.from({ length: 3 }).map((_, index) => (
          <div key={index} className="flex justify-between items-center">
            <Skeleton className="h-4 w-1/4" />
            <Skeleton className="h-4 w-12" />
          </div>
        ))}
      </div>
      <Skeleton className="h-8 w-1/3 mt-4" />
      <div className="flex space-x-1">
        {Array.from({ length: 5 }).map((_, index) => (
          <Skeleton key={index} className="h-5 w-5" />
        ))}
      </div>
    </div>
  )
}
