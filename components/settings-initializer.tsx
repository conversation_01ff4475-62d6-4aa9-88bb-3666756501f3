"use client"

import { useUserPreferences } from "@/lib/domains/user-preferences/user-preferences.hooks"

/**
 * SettingsInitializer component
 *
 * This component initializes the user preferences.
 * It should be included in layouts or pages where user preferences data is needed.
 */
export function SettingsInitializer() {
  // Use the domain-based user preferences hook with realtime updates
  // This will automatically fetch and subscribe to user preferences
  useUserPreferences(undefined, true) // Use realtime updates

  return null
}
