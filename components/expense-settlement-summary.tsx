import { Progress } from "@/components/ui/progress"

interface ExpenseSettlementSummaryProps {
  settledPercentage: number
  totalAmount: number
  settledAmount: number
}

export function ExpenseSettlementSummary({
  settledPercentage,
  totalAmount,
  settledAmount,
}: ExpenseSettlementSummaryProps) {
  return (
    <div className="space-y-2">
      <div className="flex justify-between items-center">
        <h4 className="text-sm font-medium">Settlement Progress</h4>
        <span className="text-sm text-muted-foreground">
          ${settledAmount.toLocaleString()} of ${totalAmount.toLocaleString()}
        </span>
      </div>
      <div className="flex items-center gap-2">
        <Progress value={settledPercentage} className="h-2 flex-1" />
        <span className="text-sm font-medium">{settledPercentage}%</span>
      </div>
    </div>
  )
}
