"use client"

import { useEffect, useState } from "react"
import { useThemeStore } from "@/lib/domains/theme/theme.store"
import { useSidebarStore } from "@/lib/stores/sidebar-store"
import { useAuthSync, useUser } from "@/lib/domains/auth/auth.hooks"
import { useAuthStore } from "@/lib/domains/auth/auth.store"

/**
 * StoreInitializer component
 *
 * This component initializes only essential global stores:
 * - Theme store: Required for UI appearance
 * - Sidebar store: Required for layout
 * - Auth token: Required for API calls
 *
 * Domain-specific stores should be initialized in their respective components
 * using domain-specific initializers.
 */
export function StoreInitializer() {
  const [isHydrated, setIsHydrated] = useState(false)
  const { user, loading } = useAuthStore()
  // const { loading } = useAuthSync()
  // const user = useUser()

  // Rehydrate only essential global stores
  useEffect(() => {
    // This will run only on the client side
    useThemeStore.persist.rehydrate()
    useSidebarStore.persist.rehydrate()
    setIsHydrated(true)
  }, [])

  // Initialize theme store when hydrated
  useEffect(() => {
    if (isHydrated) {
      // Initialize the theme store
      useThemeStore.getState().initializeTheme()
    }
  }, [isHydrated])

  // Store auth token in localStorage when user changes
  useEffect(() => {
    const storeAuthToken = async () => {
      if (!user) {
        localStorage.removeItem("authToken")
        return
      }

      // Get the auth token and store it in localStorage for API calls
      const token = await user.getIdToken()
      localStorage.setItem("authToken", token)
    }

    if (!loading) {
      storeAuthToken()
    }
  }, [user, loading])

  return null
}
