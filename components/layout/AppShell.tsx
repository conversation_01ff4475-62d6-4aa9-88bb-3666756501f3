"use client"

import { useEffect } from "react"
import { usePathname, useRouter } from "next/navigation"
import { publicRoutes } from "@/lib/domains/auth/auth.types"

import { useAuthStore } from "@/lib/domains/auth/auth.store"
import { PageLoading } from "@/components/page-loading"

export function AppShell({ children }: { children: React.ReactNode }) {
  const user = useAuthStore((state) => state.user)
  const authLoading = useAuthStore((state) => state.loading)
  const router = useRouter()
  const pathname = usePathname()

  useEffect(() => {
    if (authLoading) return

    if (user && publicRoutes.includes(pathname)) {
      router.push("/dashboard")
    }

    if (!user && !publicRoutes.includes(pathname)) {
      router.push("/login")
    }
  }, [user, authLoading, pathname, router])

  if (
    authLoading ||
    (user && publicRoutes.includes(pathname)) ||
    (!user && !publicRoutes.includes(pathname))
  ) {
    return <PageLoading message="Authenticating..." />
  }

  return <>{children}</>
}
