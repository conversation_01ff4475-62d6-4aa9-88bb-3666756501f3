"use client"

import React from "react"

interface ImageAttributionProps {
  attribution: string
  attributionUrl?: string
  index: number
}

export function ImageAttribution({ attribution, attributionUrl, index }: ImageAttributionProps) {
  return (
    <div className="absolute bottom-1 sm:bottom-2 right-2 sm:right-3 text-[10px] sm:text-xs text-white/80">
      {attributionUrl ? (
        <a
          href={attributionUrl}
          data-attribution-index={index}
          target="_blank"
          rel="noopener noreferrer"
          className="hover:text-white transition-colors bg-black/30 px-1 sm:px-2 py-0.5 sm:py-1 rounded"
          onClick={(e) => {
            e.stopPropagation() // Prevent triggering carousel navigation
          }}
        >
          Photo: {attribution}
        </a>
      ) : (
        <span className="bg-black/30 px-1 sm:px-2 py-0.5 sm:py-1 rounded">
          Photo: {attribution}
        </span>
      )}
    </div>
  )
}
