"use client"

import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { SubscriberBadge } from "@/components/subscriber-badge"
import { getBestAvatar, getInitials } from "@/lib/utils/avatar-utils"

interface UserDisplayProps {
  displayName: string | null
  photoURL?: string | null
  isSubscriber?: boolean
  size?: "sm" | "md" | "lg"
  showBadge?: boolean
}

export function UserDisplay({
  displayName,
  photoURL,
  isSubscriber = false,
  size = "md",
  showBadge = true,
}: UserDisplayProps) {
  const sizeClasses = {
    sm: "h-6 w-6",
    md: "h-8 w-8",
    lg: "h-10 w-10",
  }

  const sizePixels = {
    sm: 24,
    md: 32,
    lg: 40,
  }

  const avatarSrc = getBestAvatar(photoURL, displayName, sizePixels[size])

  return (
    <div className="flex items-center gap-2">
      <Avatar className={sizeClasses[size]}>
        <AvatarImage src={avatarSrc} alt={displayName || "User"} />
        <AvatarFallback>{getInitials(displayName)}</AvatarFallback>
      </Avatar>
      <div className="flex items-center gap-1">
        <span className="font-medium">{displayName}</span>
        {isSubscriber && showBadge && <SubscriberBadge className="ml-1" />}
      </div>
    </div>
  )
}
