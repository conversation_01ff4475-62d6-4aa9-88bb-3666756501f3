"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Check, ArrowRight, ExternalLink, Send } from "lucide-react"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Progress } from "@/components/ui/progress"

interface SettleUpModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  expenses: any
  attendees: any[]
}

export function SettleUpModal({ open, onOpenChange, expenses, attendees }: SettleUpModalProps) {
  const [activeTab, setActiveTab] = useState("balances")
  const [settledPayments, setSettledPayments] = useState<string[]>([])

  // Calculate simplified payment paths
  // In a real app, this would use a more sophisticated algorithm
  const paymentPaths = calculatePaymentPaths(attendees, expenses)

  const markAsSettled = (paymentId: string) => {
    if (settledPayments.includes(paymentId)) {
      setSettledPayments(settledPayments.filter((id) => id !== paymentId))
    } else {
      setSettledPayments([...settledPayments, paymentId])
    }
  }

  const sendReminder = (paymentId: string) => {
    // In a real app, this would trigger an API call to send a notification
    console.log(`Sending reminder for payment ${paymentId}`)
    alert("Reminder sent!")
  }

  // Calculate settlement progress
  const settlementProgress =
    settledPayments.length > 0
      ? Math.round((settledPayments.length / paymentPaths.length) * 100)
      : 0

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Settle Up Expenses</DialogTitle>
          <DialogDescription>Review balances and settle payments for your trip</DialogDescription>
        </DialogHeader>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 py-4">
          <div className="p-3 rounded-md bg-muted/50 space-y-1">
            <p className="text-sm text-muted-foreground">Total Trip Cost</p>
            <p className="text-xl font-bold">${expenses.total.toLocaleString()}</p>
          </div>
          <div className="p-3 rounded-md bg-muted/50 space-y-1">
            <p className="text-sm text-muted-foreground">Per Person</p>
            <p className="text-xl font-bold">${expenses.perPerson.toLocaleString()}</p>
          </div>
          <div className="p-3 rounded-md bg-muted/50 space-y-1">
            <p className="text-sm text-muted-foreground">Settlement Progress</p>
            <div className="flex items-center gap-2">
              <Progress value={settlementProgress} className="h-2 flex-1" />
              <span className="text-sm font-medium">{settlementProgress}%</span>
            </div>
          </div>
        </div>

        <Tabs defaultValue="balances" className="w-full" onValueChange={setActiveTab}>
          <TabsList className="grid grid-cols-2 w-full">
            <TabsTrigger value="balances">Balances</TabsTrigger>
            <TabsTrigger value="payments">Payment Plan</TabsTrigger>
          </TabsList>

          <TabsContent value="balances" className="space-y-4 mt-4">
            <div className="space-y-3">
              {attendees.map((person, index) => {
                const personExpenses = expenses.items.filter(
                  (expense: any) => expense.paidBy?.uid === person.uid
                )
                const personTotal = personExpenses.reduce(
                  (sum: number, expense: any) => sum + expense.amount,
                  0
                )
                const balance = personTotal - expenses.perPerson

                return (
                  <div key={index} className="p-3 rounded-md border space-y-2">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <Avatar className="h-8 w-8">
                          <AvatarImage src={person.photoURL} alt={person.displayName} />
                          <AvatarFallback>{person.displayName?.charAt(0) || "?"}</AvatarFallback>
                        </Avatar>
                        <h3 className="font-medium">{person.displayName}</h3>
                      </div>
                      <div className="flex items-center gap-2">
                        <Badge
                          variant={balance > 0 ? "outline" : balance < 0 ? "outline" : "outline"}
                          className={
                            balance > 0
                              ? "bg-green-100 text-green-800 border-green-200"
                              : balance < 0
                                ? "bg-red-100 text-red-800 border-red-200"
                                : ""
                          }
                        >
                          {balance > 0
                            ? `Gets back $${balance.toFixed(2)}`
                            : balance < 0
                              ? `Owes $${Math.abs(balance).toFixed(2)}`
                              : `Settled`}
                        </Badge>
                      </div>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-muted-foreground">
                        Paid: ${personTotal.toLocaleString()}
                      </span>
                      <span className="text-muted-foreground">
                        Should pay: ${expenses.perPerson.toLocaleString()}
                      </span>
                    </div>
                  </div>
                )
              })}
            </div>
          </TabsContent>

          <TabsContent value="payments" className="space-y-4 mt-4">
            <div className="space-y-3">
              {paymentPaths.map((payment, index) => {
                const isSettled = settledPayments.includes(payment.id)

                return (
                  <div
                    key={index}
                    className={`p-3 rounded-md border space-y-2 ${isSettled ? "bg-muted/50 border-dashed" : ""}`}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <Avatar className="h-8 w-8">
                          <AvatarImage src={payment.from.photoURL} alt={payment.from.displayName} />
                          <AvatarFallback>
                            {payment.from.displayName?.charAt(0) || "?"}
                          </AvatarFallback>
                        </Avatar>
                        <div className="flex items-center gap-1">
                          <span className="font-medium">{payment.from.displayName}</span>
                          <ArrowRight className="h-4 w-4 text-muted-foreground" />
                          <span className="font-medium">{payment.to.displayName}</span>
                        </div>
                      </div>
                      <div className="font-medium">${payment.amount.toFixed(2)}</div>
                    </div>

                    <div className="flex justify-between">
                      <div className="flex gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          className="flex gap-1"
                          onClick={() => window.open("https://venmo.com", "_blank")}
                        >
                          <ExternalLink className="h-3 w-3" /> Venmo
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          className="flex gap-1"
                          onClick={() => window.open("https://paypal.me", "_blank")}
                        >
                          <ExternalLink className="h-3 w-3" /> PayPal
                        </Button>
                      </div>
                      <div className="flex gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          className="flex gap-1"
                          onClick={() => sendReminder(payment.id)}
                          disabled={isSettled}
                        >
                          <Send className="h-3 w-3" /> Remind
                        </Button>
                        <Button
                          variant={isSettled ? "outline" : "default"}
                          size="sm"
                          className="flex gap-1"
                          onClick={() => markAsSettled(payment.id)}
                        >
                          <Check className="h-3 w-3" /> {isSettled ? "Undo" : "Mark Paid"}
                        </Button>
                      </div>
                    </div>
                  </div>
                )
              })}
            </div>
          </TabsContent>
        </Tabs>

        <DialogFooter className="flex flex-col sm:flex-row gap-2">
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
            className="sm:w-auto w-full"
          >
            Close
          </Button>
          <Button
            className="sm:w-auto w-full"
            onClick={() => {
              // In a real app, this would save the settlement status to the backend
              alert("Settlement status saved!")
              onOpenChange(false)
            }}
          >
            Save Settlement Status
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

// Helper function to calculate payment paths
// This is a simplified version - a real app would use a more sophisticated algorithm
function calculatePaymentPaths(attendees: any[], expenses: any) {
  const paths = []
  const perPerson = expenses.perPerson

  // Find who owes money (negative balance)
  const debtors = attendees
    .map((person) => {
      const personExpenses = expenses.items.filter(
        (expense: any) => expense.paidBy?.uid === person.uid
      )
      const personTotal = personExpenses.reduce(
        (sum: number, expense: any) => sum + expense.amount,
        0
      )
      return {
        ...person,
        balance: personTotal - perPerson,
      }
    })
    .filter((person) => person.balance < 0)
    .sort((a, b) => a.balance - b.balance) // Sort by most negative first

  // Find who is owed money (positive balance)
  const creditors = attendees
    .map((person) => {
      const personExpenses = expenses.items.filter(
        (expense: any) => expense.paidBy?.uid === person.uid
      )
      const personTotal = personExpenses.reduce(
        (sum: number, expense: any) => sum + expense.amount,
        0
      )
      return {
        ...person,
        balance: personTotal - perPerson,
      }
    })
    .filter((person) => person.balance > 0)
    .sort((a, b) => b.balance - a.balance) // Sort by most positive first

  // Create payment paths
  let pathId = 1
  for (const debtor of debtors) {
    let remaining = Math.abs(debtor.balance)

    for (const creditor of creditors) {
      if (remaining <= 0 || creditor.balance <= 0) continue

      const amount = Math.min(remaining, creditor.balance)
      if (amount > 0) {
        paths.push({
          id: `payment-${pathId++}`,
          from: debtor,
          to: creditor,
          amount: Number.parseFloat(amount.toFixed(2)),
        })

        remaining -= amount
        creditor.balance -= amount
      }
    }
  }

  return paths
}
