"use client"

import { useEffect } from "react"

interface PerformanceMetrics {
  lcp?: number // Largest Contentful Paint
  fid?: number // First Input Delay
  cls?: number // Cumulative Layout Shift
  fcp?: number // First Contentful Paint
  ttfb?: number // Time to First Byte
}

export function PerformanceMonitor() {
  useEffect(() => {
    // Only run in production and if performance API is available
    // Temporarily disabled for testing - uncomment for production
    // if (process.env.NODE_ENV !== "production" || typeof window === "undefined") {
    //   return
    // }
    if (typeof window === "undefined") {
      return
    }

    const metrics: PerformanceMetrics = {}
    const observers: PerformanceObserver[] = []

    // Lightweight Core Web Vitals measurement - only track key metrics
    const measureWebVitals = () => {
      // Largest Contentful Paint (LCP) - most important for perceived performance
      try {
        const lcpObserver = new PerformanceObserver((entryList) => {
          const entries = entryList.getEntries()
          const lastEntry = entries[entries.length - 1] as PerformanceEntry & {
            renderTime?: number
            loadTime?: number
          }
          metrics.lcp = lastEntry.renderTime || lastEntry.loadTime || 0
          lcpObserver.disconnect() // Stop observing after first meaningful value
        })
        lcpObserver.observe({ entryTypes: ["largest-contentful-paint"] })
        observers.push(lcpObserver)
      } catch (e) {
        // Silently fail if not supported
      }

      // First Input Delay (FID) - only measure once
      try {
        const fidObserver = new PerformanceObserver((entryList) => {
          const entries = entryList.getEntries()
          if (entries.length > 0) {
            const entry = entries[0]
            const fidEntry = entry as PerformanceEntry & { processingStart?: number }
            metrics.fid = fidEntry.processingStart ? fidEntry.processingStart - entry.startTime : 0
            fidObserver.disconnect() // Stop after first input
          }
        })
        fidObserver.observe({ entryTypes: ["first-input"] })
        observers.push(fidObserver)
      } catch (e) {
        // Silently fail if not supported
      }

      // Cumulative Layout Shift (CLS) - sample for 5 seconds only
      try {
        let clsValue = 0
        const clsObserver = new PerformanceObserver((entryList) => {
          const entries = entryList.getEntries()
          entries.forEach((entry) => {
            const layoutShiftEntry = entry as PerformanceEntry & {
              value?: number
              hadRecentInput?: boolean
            }
            if (!layoutShiftEntry.hadRecentInput) {
              clsValue += layoutShiftEntry.value || 0
            }
          })
          metrics.cls = clsValue
        })
        clsObserver.observe({ entryTypes: ["layout-shift"] })
        observers.push(clsObserver)

        // Stop CLS observation after 5 seconds to reduce overhead
        setTimeout(() => clsObserver.disconnect(), 5000)
      } catch (e) {
        // Silently fail if not supported
      }
    }

    // Measure Navigation Timing
    const measureNavigationTiming = () => {
      const navigation = performance.getEntriesByType(
        "navigation"
      )[0] as PerformanceNavigationTiming
      if (navigation) {
        metrics.ttfb = navigation.responseStart - navigation.requestStart
      }
    }

    // Lightweight metrics sending - only send critical data
    const sendMetrics = () => {
      // Only send if we have meaningful data and limit frequency
      const hasData = metrics.lcp || metrics.fid || metrics.cls
      if (!hasData) return

      // Throttle sending - only once per session
      const sessionKey = "perf_metrics_sent"
      if (sessionStorage.getItem(sessionKey)) return
      sessionStorage.setItem(sessionKey, "true")

      // Log to console in development/staging for debugging
      console.log("Performance Metrics:", metrics)

      // Send minimal data to analytics (uncomment and configure as needed)
      // try {
      //   fetch('/api/analytics/performance', {
      //     method: 'POST',
      //     headers: { 'Content-Type': 'application/json' },
      //     body: JSON.stringify({
      //       url: window.location.pathname,
      //       lcp: metrics.lcp ? Math.round(metrics.lcp) : undefined,
      //       fid: metrics.fid ? Math.round(metrics.fid) : undefined,
      //       cls: metrics.cls ? Math.round(metrics.cls * 1000) / 1000 : undefined,
      //       timestamp: Date.now(),
      //     }),
      //   }).catch(() => {}) // Silently fail
      // } catch (e) {
      //   // Silently fail
      // }
    }

    // Initialize measurements
    measureWebVitals()
    measureNavigationTiming()

    // Send metrics after page load - reduced timeout for faster cleanup
    const sendMetricsTimeout = setTimeout(sendMetrics, 3000) // Wait 3 seconds for metrics

    // Cleanup function to prevent memory leaks
    return () => {
      clearTimeout(sendMetricsTimeout)
      // Disconnect all observers
      observers.forEach((observer) => {
        try {
          observer.disconnect()
        } catch (e) {
          // Silently fail
        }
      })
    }
  }, [])

  // This component doesn't render anything
  return null
}

// Utility function to get performance grade
export function getPerformanceGrade(metrics: PerformanceMetrics): {
  lcp: "good" | "needs-improvement" | "poor"
  fid: "good" | "needs-improvement" | "poor"
  cls: "good" | "needs-improvement" | "poor"
  overall: "good" | "needs-improvement" | "poor"
} {
  const lcpGrade = !metrics.lcp
    ? "good"
    : metrics.lcp <= 2500
      ? "good"
      : metrics.lcp <= 4000
        ? "needs-improvement"
        : "poor"

  const fidGrade = !metrics.fid
    ? "good"
    : metrics.fid <= 100
      ? "good"
      : metrics.fid <= 300
        ? "needs-improvement"
        : "poor"

  const clsGrade = !metrics.cls
    ? "good"
    : metrics.cls <= 0.1
      ? "good"
      : metrics.cls <= 0.25
        ? "needs-improvement"
        : "poor"

  const grades = [lcpGrade, fidGrade, clsGrade]
  const poorCount = grades.filter((g) => g === "poor").length
  const needsImprovementCount = grades.filter((g) => g === "needs-improvement").length

  const overall = poorCount > 0 ? "poor" : needsImprovementCount > 0 ? "needs-improvement" : "good"

  return {
    lcp: lcpGrade,
    fid: fidGrade,
    cls: clsGrade,
    overall,
  }
}
