"use client"

import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import {
  Calendar,
  Menu,
  Search,
  Settings,
  User2,
  LayoutDashboard,
  LogOutIcon,
  Crown,
} from "lucide-react"
import { useAuthStatus, useUserData, useUserDataLoading } from "@/lib/domains/auth/auth.hooks"
import { useIsUserSubscribed } from "@/lib/domains/user-subscription"
import { logOut } from "@/lib/firebase-service"
import { useRouter } from "next/navigation"
import { useSidebar } from "@/hooks/use-sidebar"
import { NotificationBell } from "./notification-bell"
import { UpgradeButton } from "./ui/upgrade-button"
import { getBestAvatar, getInitials } from "@/lib/utils/avatar-utils"
import { Logo } from "./ui/logo"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"

export function AppHeader() {
  const { user, loading } = useAuthStatus()
  const userDataLoading = useUserDataLoading()
  const userData = useUserData()
  const isSubscribed = useIsUserSubscribed()
  const router = useRouter()
  const { toggleSidebar } = useSidebar()

  const handleLogout = async () => {
    try {
      await logOut()
      router.push("/login")
    } catch (error) {
      console.error("Error logging out:", error)
    }
  }

  return (
    <header className="fixed top-0 left-0 right-0 z-10 border-b bg-background">
      <div className="w-full px-4 py-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Button variant="ghost" size="icon" className="md:hidden" onClick={toggleSidebar}>
              <Menu className="h-5 w-5" />
            </Button>
            <Link href="/dashboard">
              <Logo />
            </Link>
            <div className="hidden md:flex relative max-w-sm">
              <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input className="pl-10" placeholder="Search trips, squads..." />
            </div>
          </div>

          <div className="flex items-center gap-2 shrink-0">
            <Link href="/calendar">
              <Button variant="ghost" size="icon" className="hidden md:flex">
                <Calendar className="h-5 w-5" />
              </Button>
            </Link>
            {/* Show upgrade button only for non-pro users */}
            {!isSubscribed && user && (
              <>
                {/* Mobile: Icon only */}
                <UpgradeButton showText={false} className="md:hidden" />
                {/* Desktop: Icon + Text */}
                <UpgradeButton showText={true} className="hidden md:flex" />
              </>
            )}
            <NotificationBell />

            {loading || userDataLoading ? (
              <div className="h-8 w-8 rounded-full bg-muted animate-pulse"></div>
            ) : user ? (
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <button className="flex items-center cursor-pointer min-w-0 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 rounded-full">
                    <div className="relative">
                      <Avatar className="h-8 w-8 shrink-0">
                        <AvatarImage
                          src={getBestAvatar(
                            userData?.photoURL || user.photoURL,
                            userData?.displayName || user.displayName,
                            32
                          )}
                          alt={userData?.displayName || user.displayName || ""}
                        />
                        <AvatarFallback>
                          {getInitials(userData?.displayName || user.displayName)}
                        </AvatarFallback>
                      </Avatar>
                      {/* Crown overlay for Pro users */}
                      {isSubscribed && (
                        <div className="absolute -top-3 -right-3 bg-gradient-to-br from-[#FFD54F] to-[#FFB300] rounded-full p-1 shadow-lg">
                          <Crown className="h-3 w-3 text-[#FFF8DC] [filter:drop-shadow(1px_1px_2px_rgba(0,0,0,0.5))]" />
                        </div>
                      )}
                    </div>
                  </button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-56">
                  <DropdownMenuItem asChild>
                    <Link href="/settings?tab=profile">
                      <User2 className="mr-2 h-4 w-4" />
                      <span>Profile</span>
                    </Link>
                  </DropdownMenuItem>
                  <DropdownMenuItem asChild>
                    <Link href="/dashboard">
                      <LayoutDashboard className="mr-2 h-4 w-4" />
                      <span>Dashboard</span>
                    </Link>
                  </DropdownMenuItem>
                  <DropdownMenuItem asChild>
                    <Link href="/settings?tab=preferences">
                      <Settings className="mr-2 h-4 w-4" />
                      <span>Settings</span>
                    </Link>
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={handleLogout}>
                    <LogOutIcon className="mr-2 h-4 w-4" />
                    <span>Log out</span>
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            ) : (
              <Button variant="default" size="sm" asChild>
                <Link href="/login">Login</Link>
              </Button>
            )}
          </div>
        </div>
      </div>
    </header>
  )
}
