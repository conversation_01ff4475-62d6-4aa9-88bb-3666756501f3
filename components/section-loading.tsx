"use client"

/**
 * SectionLoading component for loading states within smaller containers like tabs, cards, or sections.
 * Use this instead of PageLoading when the loading state is for a specific section rather than the entire page.
 *
 * @param message - Loading message to display
 * @param size - Size variant: "sm" for small sections, "md" for medium sections, "lg" for large sections
 * @param className - Additional CSS classes
 */
interface SectionLoadingProps {
  message?: string
  size?: "sm" | "md" | "lg"
  className?: string
}

export function SectionLoading({
  message = "Loading...",
  size = "md",
  className = "",
}: SectionLoadingProps) {
  // Size configurations for different use cases
  const sizeConfig = {
    sm: {
      container: "py-8",
      spinner: "h-6 w-6",
      text: "text-sm",
    },
    md: {
      container: "py-12",
      spinner: "h-8 w-8",
      text: "text-base",
    },
    lg: {
      container: "py-16",
      spinner: "h-10 w-10",
      text: "text-lg",
    },
  }

  const config = sizeConfig[size]

  return (
    <div className={`flex items-center justify-center ${config.container} ${className}`}>
      <div className="text-center">
        <div
          className={`animate-spin rounded-full ${config.spinner} border-b-2 border-primary mx-auto`}
        ></div>
        <p className={`mt-3 text-muted-foreground ${config.text}`}>{message}</p>
      </div>
    </div>
  )
}
