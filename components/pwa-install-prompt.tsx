"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { usePwaInstall } from "@/hooks/use-pwa-install"
import { Download, X } from "lucide-react"

export default function PwaInstallPrompt() {
  const { isInstallable, promptInstall } = usePwaInstall()
  const [dismissed, setDismissed] = useState(false)
  const [showPrompt, setShowPrompt] = useState(false)
  const [isMounted, setIsMounted] = useState(false)

  // Handle hydration - only run effects after component is mounted
  useEffect(() => {
    setIsMounted(true)
  }, [])

  useEffect(() => {
    // Only show the prompt after a delay if it's installable and not dismissed
    // and only after component is mounted (client-side)
    if (isMounted && isInstallable && !dismissed) {
      const timer = setTimeout(() => {
        setShowPrompt(true)
      }, 3000)

      return () => clearTimeout(timer)
    } else {
      setShowPrompt(false)
    }
  }, [isInstallable, dismissed, isMounted])

  if (!showPrompt) return null

  return (
    <div className="fixed bottom-4 left-4 right-4 md:left-auto md:right-4 md:w-80 bg-card border rounded-lg shadow-lg p-4 z-50">
      <div className="flex justify-between items-start mb-2">
        <div className="flex items-center gap-2">
          <div className="h-10 w-10 rounded-lg bg-primary/10 flex items-center justify-center">
            <Download className="h-5 w-5 text-primary" />
          </div>
          <h3 className="font-semibold">Install Togeda.ai</h3>
        </div>
        <Button variant="ghost" size="icon" className="h-8 w-8" onClick={() => setDismissed(true)}>
          <X className="h-4 w-4" />
        </Button>
      </div>
      <p className="text-sm text-muted-foreground mb-3">
        Install Togeda.ai as an app on your device for a better experience and offline access.
      </p>
      <div className="flex gap-2">
        <Button variant="outline" className="flex-1" onClick={() => setDismissed(true)}>
          Not Now
        </Button>
        <Button className="flex-1" onClick={promptInstall}>
          Install
        </Button>
      </div>
    </div>
  )
}
