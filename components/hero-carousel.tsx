"use client"

import { useState, useEffect } from "react"
import Image from "next/image"
import { cn } from "@/lib/utils"
import { Carousel, CarouselContent, CarouselItem } from "@/components/ui/carousel"
import { OptimizedImage } from "@/components/optimized-image"
import { ImageAttribution } from "@/components/image-attribution"

interface CarouselImage {
  src: string
  alt: string
  caption: string
  attribution?: string
  attributionUrl?: string
}

interface HeroCarouselProps {
  images: CarouselImage[]
  autoplayInterval?: number
  className?: string
}

export function HeroCarousel({ images, autoplayInterval = 5000, className }: HeroCarouselProps) {
  const [currentIndex, setCurrentIndex] = useState(0)
  const [isTransitioning, setIsTransitioning] = useState(false)

  // Auto-rotate images
  useEffect(() => {
    const interval = setInterval(() => {
      setIsTransitioning(true)
      setTimeout(() => {
        setCurrentIndex((prevIndex) => (prevIndex + 1) % images.length)
        setTimeout(() => {
          setIsTransitioning(false)
        }, 400) // Match this with the CSS transition duration
      }, 400) // Delay for blur effect before changing image
    }, autoplayInterval)

    return () => clearInterval(interval)
  }, [images.length, autoplayInterval])

  return (
    <div
      className={cn(
        "relative rounded-lg overflow-hidden shadow-xl h-[300px] sm:h-[400px] md:h-[500px] w-full",
        className
      )}
    >
      {images.map((image, index) => (
        <div
          key={index}
          className={cn(
            "absolute inset-0 transition-all duration-500 ease-in-out h-full w-full",
            currentIndex === index
              ? isTransitioning
                ? "opacity-50 blur-md"
                : "opacity-100 blur-0"
              : "opacity-0"
          )}
        >
          <img
            src={image.src && image.src.trim() !== "" ? image.src : "/placeholder.svg"}
            alt={image.alt}
            className="object-cover w-full h-full"
          />
          <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-4 sm:p-6 z-10">
            <p className="text-white font-semibold text-base sm:text-xl text-center">
              {image.caption}
            </p>
            {image.attribution && (
              <ImageAttribution
                attribution={image.attribution}
                attributionUrl={image.attributionUrl}
                index={index}
              />
            )}
          </div>
        </div>
      ))}

      {/* Indicators */}
      <div className="absolute bottom-16 sm:bottom-24 left-0 right-0 z-20 flex justify-center gap-2">
        {images.map((_, index) => (
          <button
            key={index}
            className={cn(
              "w-2 h-2 rounded-full transition-all",
              currentIndex === index ? "bg-white w-4" : "bg-white/50"
            )}
            onClick={() => {
              setIsTransitioning(true)
              setTimeout(() => {
                setCurrentIndex(index)
                setTimeout(() => {
                  setIsTransitioning(false)
                }, 400)
              }, 400)
            }}
            aria-label={`Go to slide ${index + 1}`}
          />
        ))}
      </div>
    </div>
  )
}
