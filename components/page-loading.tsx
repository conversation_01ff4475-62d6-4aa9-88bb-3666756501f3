"use client"

/**
 * PageLoading component for full-page loading states.
 * Use this for entire page loading scenarios like login, main pages, or route-level loading.
 * For smaller sections like tabs or cards, use SectionLoading instead.
 *
 * @param message - Loading message to display
 */
interface PageLoadingProps {
  message?: string
}

export function PageLoading({ message = "Loading..." }: PageLoadingProps) {
  return (
    <div className="min-h-screen flex flex-col">
      <div className="flex-1 flex">
        <main className="flex-1 p-6 flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
            <p className="mt-4 text-muted-foreground">{message}</p>
          </div>
        </main>
      </div>
    </div>
  )
}
