"use client"

import type React from "react"

import { useState, useRef, useEffect } from "react"
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { X, Send } from "lucide-react"

interface TripChatSectionProps {
  suggestion: {
    id: string
    title: string
  }
  onClose: () => void
}

export function TripChatSection({ suggestion, onClose }: TripChatSectionProps) {
  const [messages, setMessages] = useState<any[]>([
    {
      id: "m1",
      userId: "u2",
      name: "<PERSON>",
      avatar: "/placeholder.svg?height=40&width=40",
      text: "I really like this destination! The hiking trails look amazing.",
      timestamp: new Date(Date.now() - 1000 * 60 * 60).toISOString(),
    },
    {
      id: "m2",
      userId: "u3",
      name: "<PERSON>",
      avatar: "/placeholder.svg?height=40&width=40",
      text: "Agreed! I've been wanting to go here for a while.",
      timestamp: new Date(Date.now() - 1000 * 60 * 30).toISOString(),
    },
    {
      id: "m3",
      userId: "u5",
      name: "David Brown",
      avatar: "/placeholder.svg?height=40&width=40",
      text: "I'm a bit concerned about the cost. Can we look at some cheaper options?",
      timestamp: new Date(Date.now() - 1000 * 60 * 15).toISOString(),
    },
  ])
  const [newMessage, setNewMessage] = useState("")
  const messagesEndRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" })
  }, [messages])

  const handleSendMessage = (e: React.FormEvent) => {
    e.preventDefault()
    if (!newMessage.trim()) return

    const message = {
      id: `m${messages.length + 1}`,
      userId: "current-user",
      name: "You",
      avatar: "/placeholder.svg?height=40&width=40",
      text: newMessage.trim(),
      timestamp: new Date().toISOString(),
    }

    setMessages([...messages, message])
    setNewMessage("")
  }

  const formatTime = (timestamp: string) => {
    const date = new Date(timestamp)
    return date.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" })
  }

  return (
    <Card className="h-full flex flex-col">
      <CardHeader className="pb-2 flex flex-row items-center justify-between">
        <div>
          <CardTitle className="text-lg">Discussion</CardTitle>
          <CardDescription>{suggestion.title}</CardDescription>
        </div>
        <Button variant="ghost" size="icon" onClick={onClose}>
          <X className="h-4 w-4" />
        </Button>
      </CardHeader>
      <CardContent className="flex-1 overflow-y-auto">
        <div className="space-y-4">
          {messages.map((message) => (
            <div
              key={message.id}
              className={`flex gap-2 ${message.userId === "current-user" ? "flex-row-reverse" : ""}`}
            >
              <Avatar className="h-8 w-8">
                <AvatarImage src={message.avatar} alt={message.name} />
                <AvatarFallback>{message.name.charAt(0)}</AvatarFallback>
              </Avatar>
              <div
                className={`max-w-[80%] rounded-lg p-3 ${
                  message.userId === "current-user"
                    ? "bg-primary text-primary-foreground"
                    : "bg-muted"
                }`}
              >
                <div className="flex justify-between items-center gap-4 mb-1">
                  <span className="text-xs font-medium">{message.name}</span>
                  <span className="text-xs opacity-70">{formatTime(message.timestamp)}</span>
                </div>
                <p className="text-sm">{message.text}</p>
              </div>
            </div>
          ))}
          <div ref={messagesEndRef} />
        </div>
      </CardContent>
      <CardFooter className="pt-2">
        <form onSubmit={handleSendMessage} className="flex w-full gap-2">
          <Input
            placeholder="Type your message..."
            value={newMessage}
            onChange={(e) => setNewMessage(e.target.value)}
            className="flex-1"
          />
          <Button type="submit" size="icon">
            <Send className="h-4 w-4" />
          </Button>
        </form>
      </CardFooter>
    </Card>
  )
}
