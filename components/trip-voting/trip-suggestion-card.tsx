"use client"

import { useState } from "react"
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { ThumbsUp, ThumbsDown, MessageCircle, Calendar, DollarSign, MapPin } from "lucide-react"
import { cn } from "@/lib/utils"
import { OptimizedImage } from "@/components/optimized-image"

interface TripSuggestionCardProps {
  suggestion: {
    id: string
    title: string
    destination: string
    dates: string
    cost: string
    image: string
    description: string
    tags: string[]
    votes: {
      userId: string
      name: string
      avatar: string
      vote: "yes" | "no" | null
    }[]
  }
  currentUserId: string
  onVote: (suggestionId: string, vote: "yes" | "no") => void
  onOpenChat: (suggestionId: string) => void
}

export function TripSuggestionCard({
  suggestion,
  currentUserId,
  onVote,
  onOpenChat,
}: TripSuggestionCardProps) {
  const [isExpanded, setIsExpanded] = useState(false)

  const currentUserVote = suggestion.votes.find((vote) => vote.userId === currentUserId)?.vote
  const yesVotes = suggestion.votes.filter(
    (vote) => vote.userId !== currentUserId && vote.vote === "yes"
  ).length
  const totalVotes = suggestion.votes.filter(
    (vote) => vote.userId !== currentUserId && vote.vote !== null
  ).length
  const allVoted = suggestion.votes.every((vote) => vote.vote !== null)
  const consensus = suggestion.votes.every((vote) => vote.vote === "yes")

  return (
    <Card className="overflow-hidden">
      <div className="aspect-video relative">
        <OptimizedImage
          src={
            suggestion.image && suggestion.image.trim() !== ""
              ? suggestion.image
              : "/placeholder.svg?height=200&width=400"
          }
          alt={suggestion.title}
          aspectRatio="video"
          className="object-cover w-full h-full"
          fallbackSrc="/placeholder.svg?height=200&width=400"
        />
        <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/60 to-transparent p-4">
          <h3 className="text-white font-bold text-xl">{suggestion.title}</h3>
          <p className="text-white/80 text-sm">{suggestion.destination}</p>
        </div>
      </div>
      <CardHeader className="pb-2">
        <div className="flex justify-between items-center">
          <CardTitle>{suggestion.title}</CardTitle>
          <div className="flex gap-1">
            {suggestion.tags.map((tag) => (
              <Badge key={tag} variant="secondary" className="text-xs">
                {tag}
              </Badge>
            ))}
          </div>
        </div>
        <CardDescription>
          <div className="flex flex-col gap-1 mt-2">
            <div className="flex items-center gap-2">
              <Calendar className="h-4 w-4 text-muted-foreground" />
              <span>{suggestion.dates}</span>
            </div>
            <div className="flex items-center gap-2">
              <DollarSign className="h-4 w-4 text-muted-foreground" />
              <span>{suggestion.cost}</span>
            </div>
            <div className="flex items-center gap-2">
              <MapPin className="h-4 w-4 text-muted-foreground" />
              <span>{suggestion.destination}</span>
            </div>
          </div>
        </CardDescription>
      </CardHeader>
      <CardContent>
        <p className={cn("text-sm text-muted-foreground", !isExpanded && "line-clamp-2")}>
          {suggestion.description}
        </p>
        {suggestion.description.length > 100 && (
          <button
            className="text-xs text-primary mt-1 hover:underline"
            onClick={() => setIsExpanded(!isExpanded)}
          >
            {isExpanded ? "Show less" : "Show more"}
          </button>
        )}

        <div className="mt-4">
          <p className="text-sm font-medium mb-2">Squad Votes</p>
          <div className="flex flex-wrap gap-2">
            {suggestion.votes.map((vote) => (
              <div
                key={vote.userId}
                className="flex items-center gap-1 p-1 rounded-full border"
                title={vote.name}
              >
                <Avatar className="h-6 w-6">
                  <AvatarImage src={vote.avatar} alt={vote.name} />
                  <AvatarFallback>{vote.name.charAt(0)}</AvatarFallback>
                </Avatar>
                {vote.vote === "yes" && <ThumbsUp className="h-3 w-3 text-green-500" />}
                {vote.vote === "no" && <ThumbsDown className="h-3 w-3 text-red-500" />}
                {vote.vote === null && <div className="h-3 w-3 rounded-full bg-gray-200" />}
              </div>
            ))}
          </div>
          <div className="mt-2 h-2 bg-muted rounded-full overflow-hidden">
            <div
              className="h-full bg-primary"
              style={{ width: `${totalVotes === 0 ? 0 : (yesVotes / totalVotes) * 100}%` }}
            ></div>
          </div>
          <p className="text-xs text-muted-foreground mt-1">
            {yesVotes} of {totalVotes} squad members voted yes
          </p>
        </div>
      </CardContent>
      <CardFooter className="flex justify-between">
        <div className="flex gap-2">
          <Button
            variant={currentUserVote === "yes" ? "default" : "outline"}
            size="sm"
            onClick={() => onVote(suggestion.id, "yes")}
            className={currentUserVote === "yes" ? "bg-green-600 hover:bg-green-700" : ""}
          >
            <ThumbsUp className="h-4 w-4 mr-1" />
            Yes
          </Button>
          <Button
            variant={currentUserVote === "no" ? "default" : "outline"}
            size="sm"
            onClick={() => onVote(suggestion.id, "no")}
            className={currentUserVote === "no" ? "bg-red-600 hover:bg-red-700" : ""}
          >
            <ThumbsDown className="h-4 w-4 mr-1" />
            No
          </Button>
        </div>
        <Button variant="ghost" size="sm" onClick={() => onOpenChat(suggestion.id)}>
          <MessageCircle className="h-4 w-4 mr-1" />
          Discuss
        </Button>
      </CardFooter>
      {consensus && allVoted && (
        <div className="bg-green-100 text-green-800 p-2 text-center text-sm font-medium">
          Squad has reached consensus on this trip!
        </div>
      )}
    </Card>
  )
}
