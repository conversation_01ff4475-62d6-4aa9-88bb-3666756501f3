"use client"

import { useEffect } from "react"
import { useUser } from "@/lib/domains/auth/auth.hooks"
import { useUserSubscriptionStore } from "@/lib/domains/user-subscription/user-subscription.store"

/**
 * SubscriptionInitializer component
 *
 * This component initializes the user subscription store.
 * It should be included in layouts or pages where subscription data is needed.
 */
export function SubscriptionInitializer() {
  const user = useUser()
  const fetchCurrentSubscription = useUserSubscriptionStore(
    (state) => state.fetchCurrentSubscription
  )

  // Initialize subscription store when user changes
  useEffect(() => {
    if (user?.uid) {
      fetchCurrentSubscription(user.uid)
    }
  }, [user, fetchCurrentSubscription])

  return null
}
