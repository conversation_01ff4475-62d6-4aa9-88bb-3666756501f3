"use client"

interface InlineLoadingProps {
  message?: string
  size?: "small" | "medium" | "large"
}

export function InlineLoading({ message, size = "medium" }: InlineLoadingProps) {
  const sizeClasses = {
    small: "h-4 w-4",
    medium: "h-6 w-6",
    large: "h-12 w-12",
  }

  return (
    <div className="flex flex-col items-center justify-center py-4">
      <div
        className={`animate-spin rounded-full ${sizeClasses[size]} border-b-2 border-primary mx-auto`}
      ></div>
      {message && <p className="mt-2 text-sm text-muted-foreground">{message}</p>}
    </div>
  )
}
