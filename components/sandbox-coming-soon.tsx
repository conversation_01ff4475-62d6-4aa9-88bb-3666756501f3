"use client"

import { <PERSON>, CardContent, CardTitle } from "@/components/ui/card"
import { MapPin, Clock } from "lucide-react"

interface SandboxComingSoonProps {
  title?: string
  description?: string
  className?: string
}

export function SandboxComingSoon({
  title = "Local Experiences - Coming Soon",
  description = "We're currently testing this feature with a select group of users. Stay tuned for the full launch!",
  className = "",
}: SandboxComingSoonProps) {
  return (
    <div className="min-h-screen bg-background">
      {/* Page Header */}
      <div className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="container mx-auto px-4 py-6">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Local Experiences</h1>
            <p className="text-muted-foreground mt-2">
              Discover unique activities and manage your bookings
            </p>
          </div>
        </div>
      </div>

      {/* Coming Soon Content */}
      <div className="container mx-auto px-4 py-8">
        <Card className={`max-w-2xl mx-auto ${className}`}>
          <CardContent className="flex flex-col items-center justify-center p-6 md:p-12">
            <div className="w-16 h-16 md:w-20 md:h-20 rounded-full bg-primary/10 flex items-center justify-center mb-6 md:mb-8">
              <MapPin className="h-8 w-8 md:h-10 md:w-10 text-primary" />
            </div>

            <CardTitle className="text-2xl md:text-3xl font-bold text-center mb-4 md:mb-6">
              {title}
            </CardTitle>

            <p className="text-muted-foreground text-center max-w-md mb-6 text-base md:text-lg leading-relaxed">
              {description}
            </p>

            <div className="bg-muted/50 p-4 md:p-6 rounded-lg w-full max-w-md">
              <h3 className="font-semibold text-center mb-3">What's Coming</h3>
              <ul className="space-y-2 text-sm text-muted-foreground">
                <li className="flex items-center gap-2">
                  <div className="w-1.5 h-1.5 bg-primary rounded-full"></div>
                  Unique local experiences and activities
                </li>
                <li className="flex items-center gap-2">
                  <div className="w-1.5 h-1.5 bg-primary rounded-full"></div>
                  Easy booking and payment system
                </li>
                <li className="flex items-center gap-2">
                  <div className="w-1.5 h-1.5 bg-primary rounded-full"></div>
                  Connect with local hosts and guides
                </li>
                <li className="flex items-center gap-2">
                  <div className="w-1.5 h-1.5 bg-primary rounded-full"></div>
                  Seamless integration with your trips
                </li>
              </ul>
            </div>

            <div className="flex items-center text-sm text-muted-foreground mt-6 md:mt-8">
              <Clock className="h-4 w-4 mr-2" />
              <span>Stay tuned for updates!</span>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
