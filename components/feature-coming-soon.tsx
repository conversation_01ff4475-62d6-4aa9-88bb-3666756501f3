"use client"

import { ReactNode } from "react"
import { Card, CardContent, CardTitle } from "@/components/ui/card"
import { Clock } from "lucide-react"

interface FeatureComingSoonProps {
  title?: string
  description?: string
  icon?: ReactNode
  className?: string
}

export function FeatureComingSoon({
  title = "Coming Soon",
  description = "We're working on this feature. Check back soon!",
  icon,
  className = "",
}: FeatureComingSoonProps) {
  return (
    <Card className={className}>
      <CardContent className="flex flex-col items-center justify-center p-6 md:p-12">
        {icon && (
          <div className="w-12 h-12 md:w-16 md:h-16 rounded-full bg-primary/10 flex items-center justify-center mb-4 md:mb-6">
            {icon}
          </div>
        )}
        <CardTitle className="text-xl md:text-2xl font-bold text-center mb-2 md:mb-3">
          {title}
        </CardTitle>
        <p className="text-muted-foreground text-center max-w-md mb-2 text-sm md:text-base">
          {description}
        </p>
        <div className="flex items-center text-sm text-muted-foreground mt-3 md:mt-4">
          <Clock className="h-3 w-3 md:h-4 md:w-4 mr-2" />
          <span>Stay tuned!</span>
        </div>
      </CardContent>
    </Card>
  )
}
