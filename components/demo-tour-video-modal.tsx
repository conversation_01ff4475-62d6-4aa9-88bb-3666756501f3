"use client"

import React, { useCallback } from "react"
import { X } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"

interface DemoTourVideoModalProps {
  isOpen: boolean
  onClose: () => void
}

export function DemoTourVideoModal({ isOpen, onClose }: DemoTourVideoModalProps) {
  const handleClose = useCallback(() => {
    onClose()
  }, [onClose])

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && handleClose()}>
      <DialogContent className="max-w-none w-screen h-screen p-0 m-0 border-0 rounded-none">
        {/* Add DialogTitle and DialogDescription for accessibility - visually hidden */}
        <DialogHeader className="sr-only">
          <DialogTitle>Togeda.ai Demo Tour Video</DialogTitle>
          <DialogDescription>
            Interactive demo tour showing how to use Togeda.ai to plan epic trips with your squads
          </DialogDescription>
        </DialogHeader>

        {/* Header with close button */}
        <div className="absolute top-0 left-0 right-0 z-50 bg-background/95 backdrop-blur-sm border-b p-4 flex justify-between items-center">
          <div className="flex items-center gap-4">
            <h2 className="text-lg font-semibold">Togeda.ai Demo Tour</h2>
            <p className="text-sm text-muted-foreground">
              Learn how to plan epic trips with your squads
            </p>
          </div>
          <Button variant="ghost" size="icon" onClick={handleClose}>
            <X className="h-4 w-4" />
          </Button>
        </div>

        {/* Iframe container */}
        <div className="pt-20 h-full">
          <div
            style={{
              position: "relative",
              boxSizing: "content-box",
              maxHeight: "80svh",
              width: "100%",
              aspectRatio: "1.718362282878412",
              padding: "40px 0 40px 0",
            }}
          >
            <iframe
              src="https://app.supademo.com/embed/cmebvnofa6dc0h3py1ptxpsdm?embed_v=2&utm_source=embed"
              loading="lazy"
              title="Use Togeda.ai - Plan Your Next Adventure  to Plan Epic Trips With Your Squads!"
              allow="clipboard-write; fullscreen; autoplay; encrypted-media"
              allowFullScreen
              className="border-0"
              style={{
                position: "absolute",
                top: 0,
                left: 0,
                width: "100%",
                height: "100%",
              }}
            />
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
