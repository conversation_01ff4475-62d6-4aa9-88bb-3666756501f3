"use client"

import { useEffect } from "react"
import { useUser } from "@/lib/domains/auth/auth.hooks"
import { useAuthStore } from "@/lib/domains/auth/auth.store"

/**
 * UserDataInitializer component
 *
 * This component initializes the user data in the auth store.
 * It should be included in layouts or pages where user data is needed.
 */
export function UserDataInitializer() {
  const user = useUser()
  const fetchUserData = useAuthStore((state) => state.fetchUserData)

  // Initialize user data when user changes
  useEffect(() => {
    if (user?.uid) {
      fetchUserData()
    }
  }, [user, fetchUserData])

  return null
}
