"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { cn } from "@/lib/utils"
import { useRouter } from "next/navigation"
import { Crown } from "lucide-react"

interface UpgradeButtonProps {
  className?: string
  showText?: boolean
}

export function UpgradeButton({ className, showText = true }: UpgradeButtonProps) {
  const router = useRouter()

  const handleUpgrade = () => {
    router.push("/settings?tab=billing")
  }

  return (
    <Button
      onClick={handleUpgrade}
      className={cn(
        // Use Sand Yellow gradient for Pro branding with cream text and shadow
        "bg-gradient-to-br from-[#FFD54F] to-[#FFB300] hover:from-[#FFCC02] hover:to-[#FF8F00]",
        "text-[#FFF8DC] font-medium",
        // Add text shadow for visibility
        "[text-shadow:1px_1px_2px_rgba(0,0,0,0.5)]",
        // Touch-friendly sizing for mobile
        "min-h-[44px] min-w-[44px]",
        // Smooth transitions
        "transition-all duration-300 ease-in-out",
        "hover:scale-105 active:scale-95",
        // Focus styles
        "focus:outline-none focus:ring-0 focus:ring-offset-0",
        className
      )}
      size={showText ? "sm" : "icon"}
      aria-label="Upgrade to Pro"
    >
      <Crown className={cn("h-4 w-4", showText && "mr-2")} />
      {showText && <span>Upgrade to Pro</span>}
    </Button>
  )
}
