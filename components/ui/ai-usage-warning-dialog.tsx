"use client"

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import { Al<PERSON><PERSON>riangle, MessageSquare, Users } from "lucide-react"

interface AIUsageWarningDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onConfirm: () => void
}

/**
 * Warning dialog for first-time AI usage in trip chat
 * Informs users that AI messages are visible to all trip members
 */
export function AIUsageWarningDialog({ open, onOpenChange, onConfirm }: AIUsageWarningDialogProps) {
  const handleConfirm = () => {
    onConfirm()
    onOpenChange(false)
  }

  return (
    <AlertDialog open={open} onOpenChange={onOpenChange}>
      <AlertDialogContent className="sm:max-w-md">
        <AlertDialogHeader>
          <div className="flex items-center gap-2">
            <div className="flex h-10 w-10 items-center justify-center rounded-full bg-amber-100 dark:bg-amber-900/20">
              <AlertTriangle className="h-5 w-5 text-amber-600 dark:text-amber-400" />
            </div>
            <AlertDialogTitle className="text-left">AI Chat Notice</AlertDialogTitle>
          </div>
          <AlertDialogDescription className="text-left space-y-3">
            <div className="flex items-start gap-3">
              <MessageSquare className="h-4 w-4 text-muted-foreground mt-0.5 flex-shrink-0" />
              <div>
                <p className="font-medium text-foreground">AI responses are visible to everyone</p>
                <p className="text-sm text-muted-foreground">
                  When you use /togeda commands, both your question and the AI response will appear
                  in the trip chat for all members to see.
                </p>
              </div>
            </div>

            <div className="flex items-start gap-3">
              <Users className="h-4 w-4 text-muted-foreground mt-0.5 flex-shrink-0" />
              <div>
                <p className="font-medium text-foreground">Shared conversation</p>
                <p className="text-sm text-muted-foreground">
                  This helps keep everyone informed about trip planning discussions and AI
                  suggestions.
                </p>
              </div>
            </div>

            <div className="bg-muted/50 rounded-lg p-3 mt-4">
              <p className="text-sm text-muted-foreground">
                <span className="font-medium">Pro feature:</span> You have 20 AI requests per day.
                Use them wisely for the best trip planning experience!
              </p>
            </div>
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogAction onClick={handleConfirm} className="w-full">
            I understand, continue
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  )
}
