"use client"

import * as React from "react"
import { useState, useEffect } from "react"
import { CalendarIcon, Check } from "lucide-react"
import { format } from "date-fns"
import { DateRange } from "react-day-picker"

import { cn } from "@/lib/utils"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Calendar } from "@/components/ui/calendar"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"

interface DateRangePickerProps {
  startDate?: Date | null
  endDate?: Date | null
  onDateRangeChange?: (startDate: Date | null, endDate: Date | null) => void
  placeholder?: string
  disabled?: (date: Date) => boolean
  className?: string
  required?: boolean
}

export function DateRangePicker({
  startDate,
  endDate,
  onDateRangeChange,
  placeholder = "Pick date range",
  disabled,
  className,
  required = false,
}: DateRangePickerProps) {
  const [open, setOpen] = useState(false)
  const [tempRange, setTempRange] = useState<DateRange | undefined>({
    from: startDate || undefined,
    to: endDate || undefined,
  })
  const [isMobile, setIsMobile] = useState(false)

  // Check if mobile on mount and resize
  useEffect(() => {
    const checkMobile = () => setIsMobile(window.innerWidth < 768)
    checkMobile()
    window.addEventListener("resize", checkMobile)
    return () => window.removeEventListener("resize", checkMobile)
  }, [])

  // Format the display text for the button (always show placeholder)
  const getDisplayText = () => {
    return placeholder
  }

  // Format the selected dates for display below the button
  const getSelectedDatesText = () => {
    if (startDate && endDate) {
      return `${format(startDate, "MMM dd, yyyy")} - ${format(endDate, "MMM dd, yyyy")}`
    }
    if (startDate) {
      return `${format(startDate, "MMM dd, yyyy")} - End date`
    }
    return null
  }

  // Handle date selection
  const handleSelect = (range: DateRange | undefined) => {
    setTempRange(range)
  }

  // Handle confirm button click
  const handleConfirm = () => {
    if (tempRange?.from && tempRange?.to) {
      onDateRangeChange?.(tempRange.from, tempRange.to)
      setOpen(false)
    } else if (tempRange?.from && !tempRange?.to) {
      // If only start date is selected, don't close yet
      return
    } else {
      // Clear selection
      onDateRangeChange?.(null, null)
      setOpen(false)
    }
  }

  // Handle cancel/close
  const handleCancel = () => {
    setTempRange({
      from: startDate || undefined,
      to: endDate || undefined,
    })
    setOpen(false)
  }

  // Reset temp range when opening
  const handleOpenChange = (isOpen: boolean) => {
    if (isOpen) {
      setTempRange({
        from: startDate || undefined,
        to: endDate || undefined,
      })
    }
    setOpen(isOpen)
  }

  return (
    <div className="space-y-2">
      <Popover open={open} onOpenChange={handleOpenChange}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            className={cn(
              "w-full justify-start text-left font-normal",
              !startDate && !endDate && "text-muted-foreground",
              className
            )}
            aria-required={required}
          >
            <CalendarIcon className="mr-2 h-4 w-4" />
            {getDisplayText()}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-auto p-0" align="start">
          <div className="p-3">
            <Calendar
              initialFocus
              mode="range"
              defaultMonth={startDate || undefined}
              selected={tempRange}
              onSelect={handleSelect}
              numberOfMonths={isMobile ? 1 : 2}
              disabled={disabled}
              className="rounded-md border-0"
              classNames={{
                months: "flex flex-col sm:flex-row space-y-4 sm:space-x-4 sm:space-y-0",
                month: "space-y-4",
              }}
            />
            <div className="pt-3 border-t space-y-3 sm:space-y-0">
              {/* Date selection text - separate row on mobile */}
              <div className="flex justify-center sm:hidden">
                <div className="text-sm text-muted-foreground">
                  {tempRange?.from && tempRange?.to
                    ? `${format(tempRange.from, "MMM dd")} - ${format(tempRange.to, "MMM dd")}`
                    : tempRange?.from
                      ? `${format(tempRange.from, "MMM dd")} - Select end date`
                      : "Select date range"}
                </div>
              </div>

              {/* Desktop layout - text and buttons on same row */}
              <div className="hidden sm:flex items-center justify-between">
                <div className="text-sm text-muted-foreground">
                  {tempRange?.from && tempRange?.to
                    ? `${format(tempRange.from, "MMM dd")} - ${format(tempRange.to, "MMM dd")}`
                    : tempRange?.from
                      ? `${format(tempRange.from, "MMM dd")} - Select end date`
                      : "Select date range"}
                </div>
                <div className="flex gap-2">
                  <Button variant="outline" size="sm" onClick={handleCancel}>
                    Cancel
                  </Button>
                  <Button
                    size="sm"
                    onClick={handleConfirm}
                    disabled={!tempRange?.from || !tempRange?.to}
                  >
                    <Check className="mr-1 h-3 w-3" />
                    Confirm
                  </Button>
                </div>
              </div>

              {/* Mobile layout - buttons only, centered */}
              <div className="flex justify-center gap-2 sm:hidden">
                <Button variant="outline" size="sm" onClick={handleCancel}>
                  Cancel
                </Button>
                <Button
                  size="sm"
                  onClick={handleConfirm}
                  disabled={!tempRange?.from || !tempRange?.to}
                >
                  <Check className="mr-1 h-3 w-3" />
                  Confirm
                </Button>
              </div>
            </div>
          </div>
        </PopoverContent>
      </Popover>

      {/* Display selected dates below the button */}
      {getSelectedDatesText() && (
        <div className="text-sm text-muted-foreground px-3 py-2 bg-muted/50 rounded-md">
          <span className="font-medium">Selected dates:</span> {getSelectedDatesText()}
        </div>
      )}
    </div>
  )
}
