"use client"

import Link from "next/link"

export function AppFooter() {
  return (
    <footer className="border-t bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="container mx-auto px-4 py-3">
        {/* Desktop Layout */}
        <div className="hidden sm:flex justify-between items-center">
          {/* Left side - Brand info */}
          <div className="flex items-center gap-3">
            <span className="text-sm font-semibold text-primary">Togeda.ai</span>
            <span className="text-xs text-muted-foreground">
              © {new Date().getFullYear()} Valencia Dr.
            </span>
          </div>

          {/* Right side - Links */}
          <div className="flex items-center gap-4 text-xs">
            <Link
              href="/privacy"
              className="text-muted-foreground hover:text-foreground transition-colors"
            >
              Privacy
            </Link>
            <Link
              href="/terms"
              className="text-muted-foreground hover:text-foreground transition-colors"
            >
              Terms
            </Link>
            <Link
              href="https://sga.formaloo.me/togeda-ai-contact-us"
              target="_blank"
              rel="noopener noreferrer"
              className="text-muted-foreground hover:text-foreground transition-colors"
            >
              Contact
            </Link>
          </div>
        </div>

        {/* Mobile Layout */}
        <div className="sm:hidden flex flex-col items-center gap-2">
          {/* Brand and copyright in same line */}
          <div className="text-xs text-muted-foreground">
            <span className="font-semibold text-primary">Togeda.ai</span> ©{" "}
            {new Date().getFullYear()} Valencia Dr.
          </div>

          {/* Links */}
          <div className="flex items-center gap-4 text-xs">
            <Link
              href="/privacy"
              className="text-muted-foreground hover:text-foreground transition-colors"
            >
              Privacy
            </Link>
            <Link
              href="/terms"
              className="text-muted-foreground hover:text-foreground transition-colors"
            >
              Terms
            </Link>
            <Link
              href="https://sga.formaloo.me/togeda-ai-contact-us"
              target="_blank"
              rel="noopener noreferrer"
              className="text-muted-foreground hover:text-foreground transition-colors"
            >
              Contact
            </Link>
          </div>
        </div>
      </div>
    </footer>
  )
}
