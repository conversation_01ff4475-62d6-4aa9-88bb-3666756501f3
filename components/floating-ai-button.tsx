"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { cn } from "@/lib/utils"

interface FloatingAIButtonProps {
  onClick: () => void
  className?: string
  children?: React.ReactNode
}

export function FloatingAIButton({ onClick, className, children }: FloatingAIButtonProps) {
  return (
    <div className="floating-ai-button">
      <Button
        onClick={onClick}
        className={cn(
          // Size and shape
          "h-14 w-14 rounded-full p-0",
          // Colors with gradient effect
          "bg-gradient-to-br from-[#00796B] to-[#004D40]",
          "hover:from-[#00695C] hover:to-[#00332A]",
          "border-2 border-[#FFD54F]/20",
          // Shadow and glow effects
          "shadow-lg shadow-[#00796B]/25",
          "hover:shadow-xl hover:shadow-[#00796B]/40",
          // Glow animation
          "animate-pulse",
          // Shine effect
          "relative overflow-hidden",
          "before:absolute before:inset-0",
          "before:bg-gradient-to-br before:from-white/20 before:via-transparent before:to-transparent",
          "before:opacity-0 hover:before:opacity-100",
          "before:transition-opacity before:duration-300",
          // Touch-friendly sizing for mobile
          "min-h-[44px] min-w-[44px]",
          // Smooth transitions
          "transition-all duration-300 ease-in-out",
          "hover:scale-110 active:scale-95",
          // Focus styles - remove yellow outline on click
          "focus:outline-none focus:ring-0 focus:ring-offset-0",
          // Ensure button is clickable
          "pointer-events-auto",
          className
        )}
        aria-label="AI Suggestions"
        style={{
          animation: "wiggle 2s ease-in-out infinite, glow 2s ease-in-out infinite alternate",
        }}
      >
        <span
          className="text-2xl select-none"
          style={{
            filter: "drop-shadow(0 0 8px rgba(255, 213, 79, 0.6))",
            animation: "sparkle 1.5s ease-in-out infinite",
          }}
        >
          {children || "✨"}
        </span>
      </Button>

      <style jsx>{`
        @keyframes wiggle {
          0%,
          100% {
            transform: rotate(0deg);
          }
          25% {
            transform: rotate(-3deg);
          }
          75% {
            transform: rotate(3deg);
          }
        }

        @keyframes glow {
          0% {
            box-shadow:
              0 0 20px rgba(0, 121, 107, 0.4),
              0 0 40px rgba(0, 121, 107, 0.2);
          }
          100% {
            box-shadow:
              0 0 30px rgba(0, 121, 107, 0.6),
              0 0 60px rgba(0, 121, 107, 0.3);
          }
        }

        @keyframes sparkle {
          0%,
          100% {
            transform: scale(1) rotate(0deg);
          }
          50% {
            transform: scale(1.1) rotate(5deg);
          }
        }

        .floating-ai-button button {
          animation:
            wiggle 2s ease-in-out infinite,
            glow 2s ease-in-out infinite alternate !important;
        }

        @media (max-width: 768px) {
          .floating-ai-button button {
            height: 3.5rem;
            width: 3.5rem;
            min-height: 44px;
            min-width: 44px;
          }

          .floating-ai-button span {
            font-size: 1.25rem;
          }
        }

        @media (min-width: 769px) {
          .floating-ai-button button {
            height: 3.5rem;
            width: 3.5rem;
          }

          .floating-ai-button span {
            font-size: 1.5rem;
          }
        }
      `}</style>
    </div>
  )
}
