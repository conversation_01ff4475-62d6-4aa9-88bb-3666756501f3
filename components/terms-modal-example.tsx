"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { TermsModal } from "@/components/terms-modal"

export function TermsModalExample() {
  const [termsModalOpen, setTermsModalOpen] = useState(false)

  return (
    <div>
      <Button variant="link" onClick={() => setTermsModalOpen(true)}>
        Terms of Service
      </Button>

      <TermsModal open={termsModalOpen} onOpenChange={setTermsModalOpen} />
    </div>
  )
}
