rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Helper functions
    function isAuthenticated() {
      return request.auth != null;
    }

    function isOwner(userId) {
      return isAuthenticated() && request.auth.uid == userId;
    }

    function isSquadLeader(squadId) {
      return isAuthenticated() &&
             exists(/databases/$(database)/documents/squads/$(squadId)) &&
             get(/databases/$(database)/documents/squads/$(squadId)).data.leaderId == request.auth.uid;
    }

    function isTripLeader(tripId) {
      return isAuthenticated() &&
             exists(/databases/$(database)/documents/trips/$(tripId)) &&
             get(/databases/$(database)/documents/trips/$(tripId)).data.leaderId == request.auth.uid;
    }

    function isTripLeaderForSavings(savingsId) {
      return isAuthenticated() &&
             exists(/databases/$(database)/documents/tripSavings/$(savingsId)) &&
             isTripLeader(get(/databases/$(database)/documents/tripSavings/$(savingsId)).data.tripId);
    }

    // Check if two users share at least one squad
    function usersShareSquad(uid1, uid2) {
      // If it's the same user, they share a squad with themselves
      return uid1 == uid2 ||
        // Otherwise, check if there's any squad that contains both users
        // For security rules, we need to allow this access more broadly
        // since we can't efficiently query all squads
        // This is a temporary solution to fix the access issue
        isAuthenticated();
    }

    // Check if user is adding themselves to a squad (for invitation links)
    // This function handles arrayUnion operations used by the addMember service
    function isAddingSelfToSquad() {
      // Check if only members and updatedAt fields are being modified
      return request.resource.data.diff(resource.data).affectedKeys().hasOnly(['members', 'updatedAt']) &&
             // For arrayUnion operations, we check if the user is not already a member
             !(request.auth.uid in resource.data.members) &&
             // And that they will be a member after the operation
             (request.auth.uid in request.resource.data.members);
    }

    // Check if user is incrementing member count when joining via subcollection
    function isIncrementingMemberCount() {
      // Check if only memberCount and updatedAt fields are being modified
      return request.resource.data.diff(resource.data).affectedKeys().hasOnly(['memberCount', 'updatedAt']) &&
             // Check that memberCount is being incremented by 1
             request.resource.data.memberCount == resource.data.memberCount + 1;
    }

    // Note: Removed isUserAttendingTrip function as it was causing permissions issues
    // The business logic in the app now handles proper access control instead of relying on Firestore rules



    // User rules
    match /users/{userId} {
      allow read: if isAuthenticated();
      allow create, update: if isOwner(userId);
    }

    // Squad rules
    match /squads/{squadId} {
      allow read: if isAuthenticated();
      allow create: if isAuthenticated();
      allow update, delete: if isAuthenticated() && (
        resource.data.leaderId == request.auth.uid ||
        // Check if user is a member via subcollection
        exists(/databases/$(database)/documents/squads/$(squadId)/members/$(request.auth.uid)) ||
        // Allow users to add themselves to a squad via invitation link (legacy array-based)
        // The client validates the invitation link before calling this operation
        isAddingSelfToSquad() ||
        // Allow users to increment member count when joining via subcollection
        // This happens when users join via invitation links with the new subcollection approach
        isIncrementingMemberCount()
      );

      // Squad members subcollection rules
      match /members/{userId} {
        // Allow reading if user is authenticated and is either:
        // - The user themselves
        // - A member of the squad
        // - The squad leader
        allow read: if isAuthenticated() && (
          request.auth.uid == userId ||
          exists(/databases/$(database)/documents/squads/$(squadId)/members/$(request.auth.uid)) ||
          get(/databases/$(database)/documents/squads/$(squadId)).data.leaderId == request.auth.uid
        );

        // Allow creating member documents when:
        // - User is adding themselves (via invitation)
        // - Squad leader is adding someone
        allow create: if isAuthenticated() && (
          (request.auth.uid == userId && request.resource.data.userId == userId) ||
          get(/databases/$(database)/documents/squads/$(squadId)).data.leaderId == request.auth.uid
        );

        // Allow updates only by squad leader
        allow update: if isAuthenticated() &&
          get(/databases/$(database)/documents/squads/$(squadId)).data.leaderId == request.auth.uid;

        // Allow deletion by squad leader or the user themselves
        allow delete: if isAuthenticated() && (
          request.auth.uid == userId ||
          get(/databases/$(database)/documents/squads/$(squadId)).data.leaderId == request.auth.uid
        );
      }

      // Squad invitation-sends subcollection rules
      match /invitation-sends/{sendId} {
        // Allow broader read access for invitation functionality
        // Squad leaders can read all invitation sends for their squads
        // Any authenticated user can read invitation sends (needed for invitation status checking)
        allow read: if isAuthenticated();

        // Allow creating invitation send records for email tracking
        // Only squad leaders can create invitation sends (for email tracking purposes)
        allow create: if isAuthenticated() &&
          get(/databases/$(database)/documents/squads/$(squadId)).data.leaderId == request.auth.uid &&
          (
            // For email tracking: requires invitationId and email
            request.resource.data.keys().hasAll(['invitationId', 'email']) ||
            // For user join tracking: requires invitationId, userId, userEmail, userName, status
            request.resource.data.keys().hasAll(['invitationId', 'userId', 'userEmail', 'userName', 'status'])
          );

        // Allow squad leaders to update invitation sends (e.g., mark as accepted/rejected)
        // Also allow invitees to update their own invitation send status
        allow update: if isAuthenticated() && (
          get(/databases/$(database)/documents/squads/$(squadId)).data.leaderId == request.auth.uid ||
          // Allow invitees to update their own invitation send status (for accept/reject)
          (resource.data.email != null &&
           request.auth.token.email != null &&
           resource.data.email.lower() == request.auth.token.email.lower() &&
           // Only allow updating status and updatedAt fields
           request.resource.data.diff(resource.data).affectedKeys().hasOnly(['status', 'updatedAt']))
        );

        // Allow squad leaders to delete invitation sends
        allow delete: if isAuthenticated() &&
          get(/databases/$(database)/documents/squads/$(squadId)).data.leaderId == request.auth.uid;
      }
    }

    // Trip rules
    match /trips/{tripId} {
      allow read: if isAuthenticated();
      allow create: if isAuthenticated();

      // Allow full update/delete for trip creators and trip leaders
      allow update, delete: if isAuthenticated() && (
        resource.data.createdBy == request.auth.uid ||
        resource.data.leaderId == request.auth.uid
      );

      // Allow any authenticated user to update task count fields
      // The business logic in the app will handle proper access control
      allow update: if isAuthenticated() &&
        request.resource.data.diff(resource.data).affectedKeys().hasAny(['tasksCompleted', 'totalTasks']);

      // Allow squad members to update attendees field (for sync operations)
      allow update: if isAuthenticated() &&
      (exists(/databases/$(database)/documents/squads/$(resource.data.squadId)) &&
         request.auth.uid in get(/databases/$(database)/documents/squads/$(resource.data.squadId)).data.members) &&
        request.resource.data.diff(resource.data).affectedKeys().hasOnly(['attendees', 'updatedAt']);

      // Allow any authenticated user to sync attendees field (for permissions fix)
      // This is safe because it only allows updating attendees and updatedAt fields
      // and the sync function gets data from userTrips collection which has proper security
      allow update: if isAuthenticated() &&
        request.resource.data.diff(resource.data).affectedKeys().hasOnly(['attendees', 'updatedAt']);

      // Trip messages subcollection
      match /messages/{messageId} {
        // Allow any authenticated user to read messages
        // The business logic in the app will handle proper access control
        allow read: if isAuthenticated();

        // Allow any authenticated user to create messages
        // The business logic in the app will handle attendee verification
        allow create: if isAuthenticated() && (
          // Regular user messages
          request.auth.uid == request.resource.data.senderId ||
          // AI messages created by the system (senderId will be "ai-assistant")
          (request.resource.data.senderId == "ai-assistant" &&
           request.resource.data.senderName == "Togeda AI" &&
           request.resource.data.isAIResponse == true &&
           request.resource.data.messageType == "ai_response")
        );

        // Allow message sender to update their own messages (limited fields)
        // AI messages cannot be updated after creation
        allow update: if isAuthenticated() &&
          request.auth.uid == resource.data.senderId &&
          resource.data.senderId != "ai-assistant" &&
          request.resource.data.diff(resource.data).affectedKeys().hasOnly(['content', 'updatedAt']);

        // Allow message sender to delete their own messages
        // AI messages cannot be deleted by users
        allow delete: if isAuthenticated() &&
          request.auth.uid == resource.data.senderId &&
          resource.data.senderId != "ai-assistant";
      }

      // Trip travel details subcollection
      match /travel-details/{memberId} {
        // Allow any authenticated user to read travel details (simplified for now)
        // TODO: Restrict to trip attendees once we have proper trip context
        allow read: if isAuthenticated();

        // Allow users to create/update their own travel details
        allow create, update: if isAuthenticated() &&
          request.auth.uid == memberId &&
          request.resource.data.id == memberId;

        // Allow users to delete their own travel details
        allow delete: if isAuthenticated() &&
          request.auth.uid == memberId;
      }

      // Trip reviews subcollection
      match /reviews/{reviewId} {
        // Allow any authenticated user to read reviews
        // The business logic in the app will handle proper access control
        allow read: if isAuthenticated();

        // Allow authenticated users to create reviews
        // The server action handles the actual attendee verification using userTrips collection
        // and ensures trip is completed before allowing reviews
        allow create: if isAuthenticated() &&
          request.auth.uid == request.resource.data.userId &&
          request.resource.data.tripId == tripId;

        // Reviews are immutable after creation - no updates allowed
        allow update: if false;

        // Only allow review author to delete their own review
        allow delete: if isAuthenticated() &&
          request.auth.uid == resource.data.userId;
      }
    }

    // Trip Task rules
    match /tripTasks/{taskId} {
      allow read: if isAuthenticated();
      allow create: if isAuthenticated();
      allow update, delete: if isAuthenticated() && (
        resource.data.createdBy == request.auth.uid ||
        resource.data.assigneeId == request.auth.uid ||
        // Allow trip leaders to update/delete tasks
        isTripLeader(resource.data.tripId) ||
        // Allow squad leaders to update tasks if the trip belongs to a squad
        (exists(/databases/$(database)/documents/trips/$(resource.data.tripId)) &&
         get(/databases/$(database)/documents/trips/$(resource.data.tripId)).data.squadId != null &&
         isSquadLeader(get(/databases/$(database)/documents/trips/$(resource.data.tripId)).data.squadId))
      );
    }

    // Invitation list rules
    match /invitations {
      // Allow listing invitations if user is the invitee or the squad leader
      allow list: if isAuthenticated() && (
        // User can list their own invitations (legacy individual invitations)
        (request.query.limit <= 100 &&
         request.query.where.size() >= 1 &&
         request.query.where[0].field_path == "inviteeId" &&
         request.query.where[0].op == "==" &&
         request.query.where[0].value == request.auth.uid) ||
        // Squad leader can list all invitations for their squad
        (request.query.limit <= 100 &&
         request.query.where.size() >= 1 &&
         request.query.where[0].field_path == "squadId" &&
         request.query.where[0].op == "==" &&
         isSquadLeader(request.query.where[0].value))
      );
    }

    // Legacy email invitation document rules
    match /invitations/{invitationId} {
      // Allow read for any authenticated user
      // This is needed for the invitation page and email sending
      allow read: if isAuthenticated();

      // Allow create if user is authenticated and is the squad leader
      allow create: if isAuthenticated() &&
                     isSquadLeader(request.resource.data.squadId) &&
                     request.resource.data.inviterId == request.auth.uid;

      // Allow update if user is authenticated and is the squad leader or the invitee
      allow update: if isAuthenticated() && (
        resource.data.inviterId == request.auth.uid ||
        // Allow invitee to update their invitation status
        resource.data.inviteeId == request.auth.uid
      );

      // Only the inviter (squad leader) can delete invitations
      allow delete: if isAuthenticated() && resource.data.inviterId == request.auth.uid;
    }

    // Invitation link document rules
    match /invitation-links/{invitationId} {
      // Allow read for any authenticated user
      // This is needed for the invitation page
      allow read: if isAuthenticated();

      // Allow create if user is authenticated and is the squad leader
      allow create: if isAuthenticated() &&
                     isSquadLeader(request.resource.data.squadId) &&
                     request.resource.data.inviterId == request.auth.uid;

      // Only the inviter (squad leader) can update invitation links
      allow update: if isAuthenticated() && resource.data.inviterId == request.auth.uid;

      // Only the inviter (squad leader) can delete invitation links
      allow delete: if isAuthenticated() && resource.data.inviterId == request.auth.uid;
    }

    // User Trip Status rules
    match /userTrips/{userTripId} {
      allow read: if isAuthenticated();
      allow create: if isAuthenticated() && request.resource.data.userId == request.auth.uid;
      allow update, delete: if isAuthenticated() && (
        resource.data.userId == request.auth.uid ||
        // Allow trip leaders to delete user trip statuses when deleting a trip
        isTripLeader(resource.data.tripId)
      );
    }

    // Trip Savings rules
    match /tripSavings/{savingsId} {
      allow read: if isAuthenticated();
      allow create: if isAuthenticated() && request.resource.data.userId == request.auth.uid;
      allow update, delete: if isAuthenticated() && (
        resource.data.userId == request.auth.uid ||
        // Allow trip leaders to delete trip savings when deleting a trip
        isTripLeader(resource.data.tripId)
      );
    }

    // Savings Transaction rules
    match /savingsTransactions/{transactionId} {
      allow read: if isAuthenticated();
      allow create: if isAuthenticated() && request.resource.data.userId == request.auth.uid;
      allow update, delete: if isAuthenticated() && (
        resource.data.userId == request.auth.uid ||
        // Allow trip leaders to delete savings transactions when deleting a trip
        isTripLeaderForSavings(resource.data.savingsId)
      );
    }

    // Trip Itinerary rules
    match /tripItineraries/{itemId} {
      allow read: if isAuthenticated();
      allow create: if isAuthenticated();
      allow update, delete: if isAuthenticated() && (
        resource.data.createdBy == request.auth.uid ||
        // Allow trip leaders to delete itinerary items when deleting a trip
        isTripLeader(resource.data.tripId)
      );
    }

    // Location Images rules
    match /locationImages/{imageId} {
      allow read: if isAuthenticated();
      allow create, update: if isAuthenticated();
    }

    // AI Requests rules
    match /ai_requests/{requestId} {
      allow read: if isAuthenticated();
      allow create: if isAuthenticated();
    }

    // User Subscriptions rules (flat subscription system)
    match /userSubscriptions/{subscriptionId} {
      // Allow any authenticated user to read subscription data
      // This is needed for displaying subscription status in squad members list
      allow read: if isAuthenticated();

      // Allow users to create subscription entries for themselves
      // This supports the new flat subscription system with multiple entry types
      allow create: if isAuthenticated() &&
        request.auth.uid == request.resource.data.userId &&
        // Validate required fields for flat subscription entries
        request.resource.data.source in ["stripe", "perk", "giveaway", "free"] &&
        request.resource.data.status in ["applied", "pending", "paused", "expired"] &&
        request.resource.data.precedence is number &&
        request.resource.data.startDate is timestamp &&
        request.resource.data.subscriptionData is map;

      // Allow users to update their own subscription entries (for status changes, etc.)
      allow update: if isAuthenticated() &&
        request.auth.uid == resource.data.userId &&
        // Prevent changing core identity fields
        request.resource.data.userId == resource.data.userId &&
        request.resource.data.source == resource.data.source;

      // Disallow delete from client (handled by cron jobs)
      allow delete: if false;
    }

    // User Preferences rules
    match /userPreferences/{userId} {
      // Allow users to read their own preferences or preferences of users in the same squad
      allow read: if isAuthenticated() && (
        // User can read their own preferences
        isOwner(userId) ||

        // User can read preferences of users in the same squad
        // This requires a separate function to check squad membership
        usersShareSquad(request.auth.uid, userId)
      );

      // Allow users to create and update their own preferences
      allow create, update: if isOwner(userId);

      // Only allow users to delete their own preferences
      allow delete: if isOwner(userId);
    }

    // Activity Preferences rules (Pro-only feature)
    match /activityPreferences/{userId} {
      // Allow users to read their own activity preferences only
      // Activity preferences are Pro-only, so access is restricted
      allow read: if isAuthenticated() && isOwner(userId);

      // Allow users to create and update their own activity preferences
      // The service layer validates Pro subscription before allowing operations
      allow create, update: if isAuthenticated() && isOwner(userId);

      // Allow users to delete their own activity preferences
      allow delete: if isAuthenticated() && isOwner(userId);
    }

    // User AI Usage rules
    match /userAiUsage/{userId} {
      allow read: if isAuthenticated() && isOwner(userId);
      allow create: if isAuthenticated() && isOwner(userId);
      allow update: if isAuthenticated() && isOwner(userId); // Allow users to update their own AI usage

      // Allow system to update AI usage for trip chat (via API routes)
      // This is needed for the AI chat feature to track usage and rate limiting
      allow update: if isAuthenticated() &&
        request.resource.data.diff(resource.data).affectedKeys().hasAny([
          'trip_chat', 'trip_chat.dailyUsage', 'trip_chat.weeklyUsage', 'trip_chat.monthlyUsage',
          'trip_chat.lastMinuteRequests', 'trip_chat.isProcessing', 'trip_chat.lastRequestTime'
        ]);
    }

    // User Notifications rules
    match /users/{userId}/notifications/{notificationId} {
      // Allow users to read their own notifications
      allow read: if isAuthenticated() && request.auth.uid == userId;

      // Allow creating notifications for any user (for system notifications)
      // This is needed for message mentions and other automated notifications
      allow create: if isAuthenticated();

      // Allow users to update their own notifications (mark as read)
      allow update: if isAuthenticated() && request.auth.uid == userId;

      // Allow users to delete their own notifications
      allow delete: if isAuthenticated() && request.auth.uid == userId;
    }

    // User Notifications collection rules (for listing/querying)
    match /users/{userId}/notifications {
      // Allow users to list their own notifications
      allow list: if isAuthenticated() && request.auth.uid == userId;
    }

    // User Squads subcollection rules
    match /users/{userId}/squads/{squadId} {
      // Allow users to read their own squad memberships
      // Allow squad members to read other members' squad relationships
      allow read: if isAuthenticated() && (
        request.auth.uid == userId ||
        exists(/databases/$(database)/documents/squads/$(squadId)/members/$(request.auth.uid))
      );

      // Allow creating user squad documents when:
      // - User is adding themselves (via invitation)
      // - Squad leader is adding someone
      allow create: if isAuthenticated() && (
        (request.auth.uid == userId && request.resource.data.squadId == squadId) ||
        get(/databases/$(database)/documents/squads/$(squadId)).data.leaderId == request.auth.uid
      );

      // Allow updates only by squad leader
      allow update: if isAuthenticated() &&
        get(/databases/$(database)/documents/squads/$(squadId)).data.leaderId == request.auth.uid;

      // Allow deletion by squad leader or the user themselves
      allow delete: if isAuthenticated() && (
        request.auth.uid == userId ||
        get(/databases/$(database)/documents/squads/$(squadId)).data.leaderId == request.auth.uid
      );
    }

    // User Squads collection rules (for listing/querying)
    match /users/{userId}/squads {
      // Allow users to list their own squads
      allow list: if isAuthenticated() && request.auth.uid == userId;
    }

    // User Local Experience Bookings subcollection rules
    match /users/{userId}/localExperienceBookings/{bookingId} {
      // Allow users to read only their own bookings
      allow read: if isAuthenticated() && request.auth.uid == userId;

      // Allow creation of booking records (handled by local-experiences domain service via transactions)
      // This is created automatically when a booking is made in the localExperiences collection
      allow create: if isAuthenticated() &&
        request.resource.data.userId == userId &&
        request.resource.data.id == bookingId;

      // Allow updates of booking records (handled by local-experiences domain service via transactions)
      // This is updated automatically when a booking is updated in the localExperiences collection
      allow update: if isAuthenticated() &&
        resource.data.userId == userId &&
        request.resource.data.userId == userId;

      // Prevent deletion of booking records from client side
      // Bookings should only be cancelled through the proper service layer
      allow delete: if false;
    }

    // User Local Experience Bookings collection rules (for listing/querying)
    match /users/{userId}/localExperienceBookings {
      // Allow users to list their own bookings
      allow list: if isAuthenticated() && request.auth.uid == userId;
    }

    // Squad Member Join tracking rules
    match /squadMemberJoins/{joinId} {
      // Allow squad leaders to read join records for their squads
      allow read: if isAuthenticated() &&
                   exists(/databases/$(database)/documents/squads/$(resource.data.squadId)) &&
                   isSquadLeader(resource.data.squadId);

      // Allow creating join records when users join squads
      // This is done by the system when users join via any method
      allow create: if isAuthenticated() &&
                     request.resource.data.userId == request.auth.uid &&
                     request.resource.data.keys().hasAll(['squadId', 'userId', 'userEmail', 'userName', 'joinMethod']);

      // Only allow squad leaders to update join records (if needed)
      allow update: if isAuthenticated() &&
                     exists(/databases/$(database)/documents/squads/$(resource.data.squadId)) &&
                     isSquadLeader(resource.data.squadId);

      // Only allow squad leaders to delete join records
      allow delete: if isAuthenticated() &&
                     exists(/databases/$(database)/documents/squads/$(resource.data.squadId)) &&
                     isSquadLeader(resource.data.squadId);
    }

    // Squad Member Joins collection rules (for listing/querying)
    match /squadMemberJoins {
      // Allow authenticated users to list join records with limits
      // Security is enforced in the service layer by checking squad leadership
      allow list: if isAuthenticated() && request.query.limit <= 100;
    }

    // Referral collection rules
    match /referral/{referralCode} {
      // Allow any authenticated user to read referral codes (for validation)
      allow read: if isAuthenticated();

      // Allow users to create referral codes (handled by server actions)
      allow create: if isAuthenticated();

      // Allow users to update their own referral codes
      allow update: if isAuthenticated() &&
        resource.data.userId == request.auth.uid;

      // Prevent deletion of referral codes
      allow delete: if false;


    }

    // User referrals subcollection rules
    match /users/{userId}/referrals/{referralId} {
      // Allow users to read their own referrals
      allow read: if isAuthenticated() && isOwner(userId);

      // Allow creation of referral records (handled by server actions)
      allow create: if isAuthenticated() && isOwner(userId);

      // Prevent updates and deletions of referral records
      allow update, delete: if false;
    }

    // User perks subcollection rules
    match /users/{userId}/perks/{perkId} {
      // Allow users to read their own perks
      allow read: if isAuthenticated() && isOwner(userId);

      // Allow creation and updates of perk records (handled by server actions)
      allow create, update: if isAuthenticated() && isOwner(userId);

      // Prevent deletion of perk records
      allow delete: if false;
    }

    // Global perks collection rules
    match /perks/{perkId} {
      // Allow any authenticated user to read global perk definitions
      allow read: if isAuthenticated();

      // Prevent client-side creation/modification of global perks
      allow create, update, delete: if false;
    }

    // Local Experiences collection rules
    match /localExperiences/{experienceId} {
      // Allow any authenticated user to read local experiences
      allow read: if isAuthenticated();

      // Prevent client-side creation/modification of experiences
      // Experiences should be managed through admin interfaces
      allow create, update, delete: if false;

      // Local Experience Bookings subcollection rules
      match /bookings/{bookingId} {
        // Allow any authenticated user to read bookings (for host management)
        // Individual booking privacy is handled at the application level
        allow read: if isAuthenticated();

        // Allow creation of booking records (handled by local-experiences domain service)
        allow create: if isAuthenticated() &&
          request.resource.data.userId == request.auth.uid &&
          request.resource.data.id == bookingId;

        // Allow updates of booking records (for status changes, payment updates)
        // Only allow the booking owner or system to update
        allow update: if isAuthenticated() &&
          resource.data.userId == request.auth.uid;

        // Prevent deletion of booking records from client side
        // Bookings should only be cancelled through proper service layer
        allow delete: if false;
      }

      // Local Experience Bookings collection rules (for listing/querying)
      match /bookings {
        // Allow authenticated users to list bookings with proper filtering
        // The service layer enforces proper access control
        allow list: if isAuthenticated();
      }

      // Local Experience Availability subcollection rules
      match /availability/{dateId} {
        // Allow any authenticated user to read availability data
        // This is needed for users to see available time slots
        allow read: if isAuthenticated();

        // Prevent client-side creation/modification of availability
        // Availability should be managed through admin interfaces or scripts
        allow create, update, delete: if false;
      }

      // Local Experience Availability collection rules (for listing/querying)
      match /availability {
        // Allow authenticated users to list availability data
        allow list: if isAuthenticated();
      }
    }
  }
}
