"use client"

import { useSettingsStore as useZustandSettingsStore } from "@/lib/stores/settings-store"

// Export the base store
export const useSettingsStore = useZustandSettingsStore

// Selector hooks for optimized re-renders
export const useThemeSettings = () => useSettingsStore((state) => state.theme)
export const useSetTheme = () => useSettingsStore((state) => state.setTheme)

// Individual selectors for travel preferences to avoid object recreation
export const useTravelPreferencesTypes = () => useSettingsStore((state) => state.travelPreferences)
export const useBudgetRange = () => useSettingsStore((state) => state.budgetRange)
export const useAvailabilityPreferences = () =>
  useSettingsStore((state) => state.availabilityPreferences)
export const usePreferredTravelSeasons = () =>
  useSettingsStore((state) => state.preferredTravelSeasons)
export const useTravelGroupPreferences = () =>
  useSettingsStore((state) => state.travelGroupPreferences)
export const useUpdateTravelPreferences = () =>
  useSettingsStore((state) => state.updateTravelPreferences)

// Individual selectors for AI preferences
export const useAIEnabled = () => useSettingsStore((state) => state.aiEnabled)
export const useProactiveSuggestions = () => useSettingsStore((state) => state.proactiveSuggestions)
export const useUpdateAIPreferences = () => useSettingsStore((state) => state.updateAIPreferences)

// Individual selectors for notification preferences
export const useNotificationsEnabled = () => useSettingsStore((state) => state.notificationsEnabled)
export const useEmailNotifications = () => useSettingsStore((state) => state.emailNotifications)
export const usePushNotifications = () => useSettingsStore((state) => state.pushNotifications)
export const useTripUpdatesNotifications = () =>
  useSettingsStore((state) => state.tripUpdatesNotifications)
export const useSquadMessagesNotifications = () =>
  useSettingsStore((state) => state.squadMessagesNotifications)
export const useInvitationNotifications = () =>
  useSettingsStore((state) => state.invitationNotifications)
export const useAISuggestionsNotifications = () =>
  useSettingsStore((state) => state.aiSuggestionsNotifications)
export const useUpdateNotificationPreferences = () =>
  useSettingsStore((state) => state.updateNotificationPreferences)

export const useSettingsLoading = () => useSettingsStore((state) => state.loading)

// Convenience hooks that combine multiple selectors (use with caution)
export const useTravelPreferences = () => {
  const travelPreferences = useTravelPreferencesTypes()
  const budgetRange = useBudgetRange()
  const availabilityPreferences = useAvailabilityPreferences()
  const preferredTravelSeasons = usePreferredTravelSeasons()
  const travelGroupPreferences = useTravelGroupPreferences()
  const updateTravelPreferences = useUpdateTravelPreferences()

  return {
    travelPreferences,
    budgetRange,
    availabilityPreferences,
    preferredTravelSeasons,
    travelGroupPreferences,
    updateTravelPreferences,
  }
}

export const useAIPreferences = () => {
  const aiEnabled = useAIEnabled()
  const proactiveSuggestions = useProactiveSuggestions()
  const updateAIPreferences = useUpdateAIPreferences()

  return {
    aiEnabled,
    proactiveSuggestions,
    updateAIPreferences,
  }
}

export const useNotificationPreferences = () => {
  const notificationsEnabled = useNotificationsEnabled()
  const emailNotifications = useEmailNotifications()
  const pushNotifications = usePushNotifications()
  const tripUpdatesNotifications = useTripUpdatesNotifications()
  const squadMessagesNotifications = useSquadMessagesNotifications()
  const invitationNotifications = useInvitationNotifications()
  const aiSuggestionsNotifications = useAISuggestionsNotifications()
  const updateNotificationPreferences = useUpdateNotificationPreferences()

  return {
    notificationsEnabled,
    emailNotifications,
    pushNotifications,
    tripUpdatesNotifications,
    squadMessagesNotifications,
    invitationNotifications,
    aiSuggestionsNotifications,
    updateNotificationPreferences,
  }
}
