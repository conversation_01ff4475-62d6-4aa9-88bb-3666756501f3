"use client"

import { useState, useCallback } from "react"
import {
  compressImage,
  CompressionOptions,
  CompressionResult,
  CompressionProgress,
  COMPRESSION_PRESETS,
  validateImageFile,
  shouldCompressFile,
} from "@/lib/utils/image-compression"

export interface UseImageCompressionOptions {
  preset?: keyof typeof COMPRESSION_PRESETS
  customOptions?: Partial<CompressionOptions>
  onProgress?: (progress: CompressionProgress) => void
  onComplete?: (result: CompressionResult) => void
  onError?: (error: string) => void
}

export interface UseImageCompressionReturn {
  compress: (file: File) => Promise<CompressionResult>
  isCompressing: boolean
  progress: CompressionProgress | null
  result: CompressionResult | null
  reset: () => void
  validateFile: (file: File) => { valid: boolean; error?: string }
  needsCompression: (file: File) => boolean
}

export function useImageCompression(
  options: UseImageCompressionOptions = {}
): UseImageCompressionReturn {
  const [isCompressing, setIsCompressing] = useState(false)
  const [progress, setProgress] = useState<CompressionProgress | null>(null)
  const [result, setResult] = useState<CompressionResult | null>(null)

  const compressionOptions: CompressionOptions = {
    ...COMPRESSION_PRESETS[options.preset || "general"],
    ...options.customOptions,
  }

  const handleProgress = useCallback(
    (progressData: CompressionProgress) => {
      setProgress(progressData)
      options.onProgress?.(progressData)
    },
    [options.onProgress]
  )

  const compress = useCallback(
    async (file: File): Promise<CompressionResult> => {
      setIsCompressing(true)
      setProgress(null)
      setResult(null)

      try {
        const compressionResult = await compressImage(file, compressionOptions, handleProgress)

        setResult(compressionResult)

        if (compressionResult.success) {
          options.onComplete?.(compressionResult)
        } else {
          options.onError?.(compressionResult.error || "Compression failed")
        }

        return compressionResult
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : "Unknown error"
        const errorResult: CompressionResult = {
          success: false,
          originalSize: file.size,
          error: errorMessage,
        }

        setResult(errorResult)
        options.onError?.(errorMessage)

        return errorResult
      } finally {
        setIsCompressing(false)
      }
    },
    [compressionOptions, handleProgress, options]
  )

  const reset = useCallback(() => {
    setIsCompressing(false)
    setProgress(null)
    setResult(null)
  }, [])

  const validateFile = useCallback((file: File) => {
    return validateImageFile(file)
  }, [])

  const needsCompression = useCallback(
    (file: File) => {
      return shouldCompressFile(file, compressionOptions.maxSizeMB)
    },
    [compressionOptions.maxSizeMB]
  )

  return {
    compress,
    isCompressing,
    progress,
    result,
    reset,
    validateFile,
    needsCompression,
  }
}

// Specialized hooks for common use cases
export function useProfilePictureCompression(
  options: Omit<UseImageCompressionOptions, "preset"> = {}
) {
  return useImageCompression({ ...options, preset: "profilePicture" })
}

export function useTravelDetailsCompression(
  options: Omit<UseImageCompressionOptions, "preset"> = {}
) {
  return useImageCompression({ ...options, preset: "travelDetails" })
}
