"use client"

import * as React from "react"
import { useSidebarStore } from "@/lib/stores/sidebar-store"
import { useIsMobile } from "./use-mobile"

// Create a hook that combines the zustand store with additional functionality
export const useSidebar = () => {
  const isMobile = useIsMobile()
  const { isOpen, isMobileOpen, setIsOpen, setMobileOpen, toggleSidebar, toggleMobileSidebar } =
    useSidebarStore()

  // Compute state for styling
  const state = isOpen ? "expanded" : "collapsed"

  // Ensure the toggleSidebar function is properly connected to the mobile state
  const handleToggleSidebar = React.useCallback(() => {
    if (isMobile) {
      toggleMobileSidebar()
    } else {
      toggleSidebar()
    }
  }, [isMobile, toggleMobileSidebar, toggleSidebar])

  // Return an object with the same shape as the old context
  return {
    state,
    open: isMobile ? isMobileOpen : isOpen,
    setOpen: isMobile ? setMobileOpen : setIsOpen,
    isMobile,
    openMobile: isMobileOpen,
    setOpenMobile: setMobileOpen,
    toggleSidebar: handleToggleSidebar,
  }
}
