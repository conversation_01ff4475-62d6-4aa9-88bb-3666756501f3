"use client"

import { useEffect } from "react"
import { useUserSubscriptionStore } from "@/lib/domains/user-subscription/user-subscription.store"
import { useAuthLoading } from "./use-auth-store"
import { useUser } from "@/lib/domains/auth/auth.hooks"
import { useRouter } from "next/navigation"
import { SubscriptionErrorType } from "@/lib/domains/user-subscription/user-subscription.types"

/**
 * Selectors for subscription store
 */

// Basic state selectors
export const useSubscription = () => useUserSubscriptionStore()
export const useSubscriptionLoading = () => useUserSubscriptionStore((state) => state.loading)
export const useIsSubscribed = () => useUserSubscriptionStore((state) => state.isSubscribed)
export const useSubscriptionPlan = () => useUserSubscriptionStore((state) => state.subscriptionPlan)
export const useSubscriptionStatus = () =>
  useUserSubscriptionStore((state) => state.subscriptionStatus)
export const useSubscriptionDetails = () => useUserSubscriptionStore((state) => state.subscription)

// Limit selectors
export const useMaxSquads = () => useUserSubscriptionStore((state) => state.maxSquads)
export const useMaxTripsPerSquad = () => useUserSubscriptionStore((state) => state.maxTripsPerSquad)
export const useMaxDailyAIRequests = () =>
  useUserSubscriptionStore((state) => state.maxDailyAIRequests)
export const useMaxWeeklyAIRequests = () =>
  useUserSubscriptionStore((state) => state.maxWeeklyAIRequests)
export const useHasTripChatLegacy = () => useUserSubscriptionStore((state) => state.hasTripChat)

// Action selectors
export const useFetchSubscription = () =>
  useUserSubscriptionStore((state) => state.fetchCurrentSubscription)
export const useCanCreateMoreSquads = () =>
  useUserSubscriptionStore((state) => state.canCreateMoreSquads)
export const useCanCreateMoreTripsInSquad = () =>
  useUserSubscriptionStore((state) => state.canCreateMoreTripsInSquad)

/**
 * Enhanced subscription hook with auto-initialization
 */
export const useSubscriptionWithInit = () => {
  const authLoading = useAuthLoading()
  const user = useUser()
  const router = useRouter()
  const store = useUserSubscriptionStore()
  const { fetchCurrentSubscription, refreshSubscriptionIfNeeded } = store

  // Fetch subscription data when auth state changes
  useEffect(() => {
    if (!authLoading && user?.uid) {
      fetchCurrentSubscription(user.uid)
    }
  }, [authLoading, user?.uid, fetchCurrentSubscription])

  // Add a periodic refresh for very long sessions
  useEffect(() => {
    if (user?.uid) {
      // Check when component mounts and when window regains focus
      refreshSubscriptionIfNeeded(user.uid)

      const handleFocus = () => {
        refreshSubscriptionIfNeeded(user.uid)
      }

      window.addEventListener("focus", handleFocus)
      return () => window.removeEventListener("focus", handleFocus)
    }
  }, [user?.uid, refreshSubscriptionIfNeeded])

  // Create a router-aware error handler
  const handleSubscriptionError = (errorType: SubscriptionErrorType) => {
    store.handleSubscriptionErrorWithRouter(errorType, router)
  }

  // Return the store with the router-aware error handler
  return {
    ...store,
    handleSubscriptionError,
  }
}
