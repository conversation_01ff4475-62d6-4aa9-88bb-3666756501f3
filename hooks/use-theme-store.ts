"use client"

import { useThemeStore as useZustandThemeStore } from "@/lib/stores/theme-store"
import { useUser } from "@/lib/domains/auth/auth.hooks"

// Export the base store
export const useThemeStore = useZustandThemeStore

// Selector hooks for optimized re-renders
export const useTheme = () => useThemeStore((state) => state.theme)
export const useThemeLoading = () => useThemeStore((state) => state.isLoading)
export const useThemeInitialized = () => useThemeStore((state) => state.isInitialized)

// Action hooks
export const useSetTheme = () => {
  const setTheme = useThemeStore((state) => state.setTheme)
  const user = useUser()

  return (theme: "light" | "dark" | "system") => setTheme(theme, user?.uid)
}

export const useInitializeTheme = () => {
  const initializeTheme = useThemeStore((state) => state.initializeTheme)
  const user = useUser()

  return () => initializeTheme(user?.uid)
}
