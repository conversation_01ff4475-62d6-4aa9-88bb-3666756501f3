// hooks/useRouteGuard.ts
"use client"

import { useEffect } from "react"
import { useRouter } from "next/navigation"
import { useAuthStore } from "@/lib/domains/auth/auth.store"

interface GuardConfig {
  /** Page requires a logged-in user. Redirects to loginPath if not. */
  isProtected?: boolean
  /** Page is for guests only. Redirects to destinationPath if logged in. */
  isGuestOnly?: boolean
  /** Path to redirect to if guest-only check fails. */
  destinationPath?: string
  /** Path to redirect to if protected check fails. */
  loginPath?: string
}

export function useRouteGuard(config: GuardConfig = {}) {
  const user = useAuthStore((state) => state.user)
  const authLoading = useAuthStore((state) => state.loading)
  const router = useRouter()

  const {
    isProtected = false,
    isGuestOnly = false,
    destinationPath = "/dashboard",
    loginPath = "/login",
  } = config

  useEffect(() => {
    if (authLoading) return // Wait until auth state is resolved

    if (isGuestOnly && user) {
      router.push(destinationPath)
    }

    if (isProtected && !user) {
      router.push(loginPath)
    }
  }, [authLoading, user, isGuestOnly, isProtected, destinationPath, loginPath, router])

  // Return a loading state that the component can use to prevent content flash
  const isGuardLoading = authLoading || (isGuestOnly && user) || (isProtected && !user)
  return { isGuardLoading }
}
