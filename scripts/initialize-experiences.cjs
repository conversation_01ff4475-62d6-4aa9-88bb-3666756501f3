#!/usr/bin/env node

/**
 * Initialize Local Experiences Script
 *
 * This script:
 * 1. Loads experiences from mock-experiences.json
 * 2. Adds them to Firestore
 * 3. Creates default availability entries with timeslots for each experience
 *
 * Usage: node scripts/initialize-experiences.cjs
 */

const admin = require("firebase-admin")
const fs = require("fs")
const path = require("path")
const dotenv = require("dotenv")

// Load environment variables
dotenv.config({ path: ".env.vercel" })

// Initialize Firebase Admin
if (!admin.apps.length) {
  const serviceAccount = JSON.parse(process.env.FIREBASE_SERVICE_ACCOUNT_KEY || "{}")

  admin.initializeApp({
    credential: admin.credential.cert(serviceAccount),
    projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID || "brotrip-mvp",
  })
}

const db = admin.firestore()

/**
 * Convert mock data timestamps to Firestore timestamps
 */
function convertTimestamps(obj) {
  if (obj && typeof obj === "object") {
    if (obj.__datatype__ === "timestamp" && obj.value) {
      return admin.firestore.Timestamp.fromDate(
        new Date(obj.value._seconds * 1000 + obj.value._nanoseconds / 1000000)
      )
    }

    if (Array.isArray(obj)) {
      return obj.map(convertTimestamps)
    }

    const converted = {}
    for (const [key, value] of Object.entries(obj)) {
      converted[key] = convertTimestamps(value)
    }
    return converted
  }
  return obj
}

/**
 * Generate availability ID based on time slot
 * Format: "slot-HH-MM" (e.g., "slot-09-00", "slot-14-30")
 */
function generateAvailabilityId(time) {
  return `slot-${time.replace(":", "-")}`
}

/**
 * Create default availability entries for an experience
 */
async function createDefaultAvailability(experienceId, experienceTitle) {
  console.log(`  Creating weekly availability patterns for: ${experienceTitle}`)

  const timeSlots = [
    {
      availabilityId: generateAvailabilityId("09:00"),
      time: "09:00",
      available: true,
      maxGuests: 8,
      currentBookings: 0,
    },
    {
      availabilityId: generateAvailabilityId("11:00"),
      time: "11:00",
      available: true,
      maxGuests: 8,
      currentBookings: 0,
    },
    {
      availabilityId: generateAvailabilityId("14:00"),
      time: "14:00",
      available: true,
      maxGuests: 8,
      currentBookings: 0,
    },
    {
      availabilityId: generateAvailabilityId("16:00"),
      time: "16:00",
      available: true,
      maxGuests: 8,
      currentBookings: 0,
    },
  ]

  // Create weekly availability for Monday through Friday
  const weekdays = ["monday", "tuesday", "wednesday", "thursday", "friday"]
  const batch = db.batch()

  for (const dayOfWeek of weekdays) {
    const availabilityRef = db
      .collection("localExperiences")
      .doc(experienceId)
      .collection("availability")
      .doc(dayOfWeek)

    const weeklyAvailability = {
      date: dayOfWeek,
      type: "weekly",
      daysOfWeek: [dayOfWeek],
      timeSlots: timeSlots,
      isDefault: false,
      createdAt: admin.firestore.FieldValue.serverTimestamp(),
      updatedAt: admin.firestore.FieldValue.serverTimestamp(),
    }

    batch.set(availabilityRef, weeklyAvailability)
  }

  // Also create a default fallback
  const defaultAvailabilityRef = db
    .collection("localExperiences")
    .doc(experienceId)
    .collection("availability")
    .doc("default")

  const defaultAvailability = {
    date: "default",
    type: "default",
    timeSlots: timeSlots,
    isDefault: true,
    createdAt: admin.firestore.FieldValue.serverTimestamp(),
    updatedAt: admin.firestore.FieldValue.serverTimestamp(),
  }

  batch.set(defaultAvailabilityRef, defaultAvailability)

  await batch.commit()
  console.log(
    `    ✓ Weekly availability created for ${weekdays.length} weekdays + default fallback`
  )
}

/**
 * Delete all existing experiences and their subcollections
 */
async function deleteExistingExperiences() {
  console.log("🗑️  Deleting existing experiences...")

  const experiencesSnapshot = await db.collection("localExperiences").get()

  if (experiencesSnapshot.empty) {
    console.log("   No existing experiences found")
    return
  }

  console.log(`   Found ${experiencesSnapshot.docs.length} existing experiences to delete`)

  for (const experienceDoc of experiencesSnapshot.docs) {
    const experienceData = experienceDoc.data()
    console.log(`   Deleting: ${experienceData.title}`)

    // Delete availability subcollection
    const availabilitySnapshot = await experienceDoc.ref.collection("availability").get()
    for (const availDoc of availabilitySnapshot.docs) {
      await availDoc.ref.delete()
    }

    // Delete bookings subcollection
    const bookingsSnapshot = await experienceDoc.ref.collection("bookings").get()
    for (const bookingDoc of bookingsSnapshot.docs) {
      await bookingDoc.ref.delete()
    }

    // Delete the experience document
    await experienceDoc.ref.delete()
  }

  console.log("   ✓ All existing experiences deleted")
}

/**
 * Main initialization function
 */
async function initializeExperiences() {
  try {
    console.log("🚀 Initializing Local Experiences...")

    // Delete existing experiences first
    await deleteExistingExperiences()

    // Load mock experiences
    const mockDataPath = path.join(__dirname, "mock-experiences.json")
    if (!fs.existsSync(mockDataPath)) {
      throw new Error(`Mock data file not found: ${mockDataPath}`)
    }

    const mockData = JSON.parse(fs.readFileSync(mockDataPath, "utf8"))
    const experiences = Object.values(mockData.experiences || mockData)
    console.log(`\n📄 Loaded ${experiences.length} experiences from mock-experiences.json`)

    // Step 1: Create all experiences with random IDs
    console.log("\n📍 Step 1: Creating experiences with random IDs...")
    const createdExperienceIds = []

    for (const experience of experiences) {
      console.log(`\n📍 Processing: ${experience.title}`)

      // Convert timestamps and remove the hardcoded ID
      const convertedExperience = convertTimestamps(experience)
      delete convertedExperience.id // Remove hardcoded ID to let Firestore generate random ID

      // Add to Firestore with random ID
      const docRef = db.collection("localExperiences").doc() // No ID specified = random ID
      await docRef.set({
        ...convertedExperience,
        id: docRef.id, // Set the ID field to match the document ID
      })

      console.log(`  ✓ Added experience with random ID: ${docRef.id}`)
      createdExperienceIds.push({
        id: docRef.id,
        title: experience.title,
      })
    }

    // Step 2: Create availability for all created experiences
    console.log("\n📍 Step 2: Creating availability for all experiences...")

    for (const experienceInfo of createdExperienceIds) {
      console.log(`\n📅 Creating availability for: ${experienceInfo.title}`)
      await createDefaultAvailability(experienceInfo.id, experienceInfo.title)
    }

    console.log("\n🎉 Successfully initialized all experiences and availability!")
    console.log("\n📋 Summary:")
    console.log(`   • ${experiences.length} experiences added with random IDs`)
    console.log(`   • ${experiences.length} default availability entries created`)
    console.log(`   • Each experience has 4 default time slots (09:00, 11:00, 14:00, 16:00)`)
    console.log("\n📝 Created Experience IDs:")
    createdExperienceIds.forEach((exp, index) => {
      console.log(`   ${index + 1}. ${exp.title} → ${exp.id}`)
    })
  } catch (error) {
    console.error("❌ Error initializing experiences:", error)
    process.exit(1)
  }
}

// Run the script
if (require.main === module) {
  initializeExperiences()
    .then(() => {
      console.log("\n✅ Initialization complete!")
      process.exit(0)
    })
    .catch((error) => {
      console.error("❌ Initialization failed:", error)
      process.exit(1)
    })
}

module.exports = { initializeExperiences }
