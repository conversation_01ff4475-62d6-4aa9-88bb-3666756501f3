/**
 * <PERSON><PERSON><PERSON> to add dummy referrals to a specific user
 *
 * This script:
 * 1. Takes a user ID and number of dummy referrals to add
 * 2. Generates realistic dummy referred user data
 * 3. Creates user referral records in users/{userId}/referrals/{referralId}
 * 4. Updates the referral code's totalReferrals count
 * 5. Checks for newly unlocked perks
 *
 * Usage:
 * npx tsx scripts/add-dummy-referrals.ts <userId> <numberOfReferrals>
 * npx tsx scripts/add-dummy-referrals.ts <userId> <numberOfReferrals> .env.production
 * ENV_FILE=.env.staging npx tsx scripts/add-dummy-referrals.ts <userId> <numberOfReferrals>
 */

import { initializeApp, cert, getApps } from "firebase-admin/app"
import { getFirestore } from "firebase-admin/firestore"
import { config } from "dotenv"
import { resolve } from "path"

// Load environment variables
const envFile = process.env.ENV_FILE || process.argv[4] || ".env.local"
const envPath = resolve(process.cwd(), envFile)

console.log(`Loading environment from: ${envPath}`)
config({ path: envPath })

// Validate required environment variables
const requiredEnvVars = ["FIREBASE_SERVICE_ACCOUNT_KEY"]

for (const envVar of requiredEnvVars) {
  if (!process.env[envVar]) {
    console.error(`❌ Missing required environment variable: ${envVar}`)
    process.exit(1)
  }
}

// Parse the service account key
let serviceAccount
try {
  serviceAccount = JSON.parse(process.env.FIREBASE_SERVICE_ACCOUNT_KEY!)
} catch (error) {
  console.error("❌ Failed to parse FIREBASE_SERVICE_ACCOUNT_KEY as JSON:", error)
  process.exit(1)
}

// Initialize Firebase Admin
if (getApps().length === 0) {
  initializeApp({
    credential: cert(serviceAccount),
  })
}

const db = getFirestore()

/**
 * Generate realistic dummy email addresses
 */
function generateDummyEmail(index: number): string {
  const firstNames = [
    "alex",
    "jordan",
    "taylor",
    "casey",
    "morgan",
    "riley",
    "avery",
    "quinn",
    "sage",
    "river",
    "phoenix",
    "dakota",
    "skyler",
    "cameron",
    "drew",
    "blake",
  ]
  const lastNames = [
    "smith",
    "johnson",
    "williams",
    "brown",
    "jones",
    "garcia",
    "miller",
    "davis",
    "rodriguez",
    "martinez",
    "hernandez",
    "lopez",
    "gonzalez",
    "wilson",
    "anderson",
    "thomas",
  ]

  const firstName = firstNames[index % firstNames.length]
  const lastName = lastNames[Math.floor(index / firstNames.length) % lastNames.length]
  const domain = ["gmail.com", "yahoo.com", "hotmail.com", "outlook.com"][index % 4]

  return `${firstName}.${lastName}${index}@${domain}`
}

/**
 * Generate a dummy user ID
 */
function generateDummyUserId(index: number): string {
  const timestamp = Date.now() - index * 86400000 // Spread over days
  return `dummy_user_${timestamp}_${Math.random().toString(36).substring(2, 11)}`
}

/**
 * Get user's referral code
 */
async function getUserReferralCode(userId: string): Promise<string | null> {
  try {
    const referralQuery = await db
      .collection("referral")
      .where("userId", "==", userId)
      .limit(1)
      .get()

    if (referralQuery.empty) {
      console.error(`❌ No referral code found for user: ${userId}`)
      return null
    }

    const referralDoc = referralQuery.docs[0]
    console.log(`✅ Found referral code: ${referralDoc.id} for user: ${userId}`)
    return referralDoc.id
  } catch (error) {
    console.error("Error fetching user referral code:", error)
    return null
  }
}

/**
 * Add dummy referrals to a user
 */
async function addDummyReferrals(userId: string, numberOfReferrals: number): Promise<void> {
  console.log(`Adding ${numberOfReferrals} dummy referrals to user: ${userId}`)

  // Get user's referral code
  const referralCode = await getUserReferralCode(userId)
  if (!referralCode) {
    throw new Error(`User ${userId} does not have a referral code`)
  }

  // Get current referral count
  const referralCodeDoc = await db.collection("referral").doc(referralCode).get()
  if (!referralCodeDoc.exists) {
    throw new Error(`Referral code document not found: ${referralCode}`)
  }

  const currentData = referralCodeDoc.data()
  const currentTotalReferrals = currentData?.totalReferrals || 0

  console.log(`Current total referrals: ${currentTotalReferrals}`)

  // Use batch writes for better performance
  const batch = db.batch()
  const createdReferrals: string[] = []

  for (let i = 0; i < numberOfReferrals; i++) {
    // Generate dummy referred user data
    const dummyUserId = generateDummyUserId(i)
    const dummyEmail = generateDummyEmail(i)

    // Create referral record
    const referralRef = db.collection("users").doc(userId).collection("referrals").doc()

    const referralData = {
      referredUserId: dummyUserId,
      referredUserEmail: dummyEmail,
      referralCode: referralCode,
      status: "completed",
      createdAt: new Date(Date.now() - i * 3600000), // Spread over hours
      updatedAt: new Date(Date.now() - i * 3600000),
    }

    batch.set(referralRef, referralData)
    createdReferrals.push(referralRef.id)

    console.log(`📝 Prepared referral ${i + 1}: ${dummyEmail} (${dummyUserId})`)
  }

  // Update referral code with new total
  const newTotalReferrals = currentTotalReferrals + numberOfReferrals
  batch.update(db.collection("referral").doc(referralCode), {
    totalReferrals: newTotalReferrals,
    updatedAt: new Date(),
  })

  // Commit the batch
  console.log("💾 Committing batch write...")
  await batch.commit()

  console.log(`✅ Successfully added ${numberOfReferrals} dummy referrals`)
  console.log(`📊 Updated total referrals from ${currentTotalReferrals} to ${newTotalReferrals}`)
  console.log(`📋 Created referral IDs: ${createdReferrals.join(", ")}`)

  // Check for newly unlocked perks
  try {
    console.log("🎁 Checking for newly unlocked perks...")
    const { PerkAdminService } = await import("../lib/domains/perk/perk.admin.service")
    const perksUnlocked = await PerkAdminService.unlockEligiblePerks(userId, newTotalReferrals)

    if (perksUnlocked.length > 0) {
      console.log(`🎉 Unlocked ${perksUnlocked.length} new perks: ${perksUnlocked.join(", ")}`)
    } else {
      console.log("📝 No new perks unlocked")
    }
  } catch (error) {
    console.error("⚠️  Error checking for unlocked perks:", error)
    console.log("📝 Referrals were still added successfully")
  }
}

/**
 * Validate user exists
 */
async function validateUser(userId: string): Promise<boolean> {
  try {
    const userDoc = await db.collection("users").doc(userId).get()
    if (!userDoc.exists) {
      console.error(`❌ User not found: ${userId}`)
      return false
    }

    const userData = userDoc.data()
    console.log(`✅ User found: ${userData?.email || "No email"} (${userId})`)
    return true
  } catch (error) {
    console.error("Error validating user:", error)
    return false
  }
}

/**
 * Main function
 */
async function main() {
  const userId = process.argv[2]
  const numberOfReferralsStr = process.argv[3]

  if (!userId || !numberOfReferralsStr) {
    console.error(
      "❌ Usage: npx tsx scripts/add-dummy-referrals.ts <userId> <numberOfReferrals> [envFile]"
    )
    console.error("Example: npx tsx scripts/add-dummy-referrals.ts abc123 5")
    process.exit(1)
  }

  const numberOfReferrals = parseInt(numberOfReferralsStr, 10)

  if (isNaN(numberOfReferrals) || numberOfReferrals <= 0) {
    console.error("❌ Number of referrals must be a positive integer")
    process.exit(1)
  }

  if (numberOfReferrals > 100) {
    console.error("❌ Maximum 100 referrals allowed per run for safety")
    process.exit(1)
  }

  console.log("🚀 Starting dummy referral addition...")
  console.log("=".repeat(50))
  console.log(`User ID: ${userId}`)
  console.log(`Number of referrals to add: ${numberOfReferrals}`)
  console.log("=".repeat(50))

  try {
    // Validate user exists
    const userExists = await validateUser(userId)
    if (!userExists) {
      process.exit(1)
    }

    // Add dummy referrals
    await addDummyReferrals(userId, numberOfReferrals)

    console.log("\n✅ Dummy referrals added successfully!")
    console.log("\nNext steps:")
    console.log("1. Check the user's referral dashboard to see the new referrals")
    console.log("2. Verify that any unlocked perks are visible")
    console.log("3. Test that the referral count is updated correctly")
  } catch (error) {
    console.error("\n❌ Failed to add dummy referrals:", error)
    process.exit(1)
  }
}

// Run the script if executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main()
    .then(() => {
      console.log("\n🎉 Script completed!")
      process.exit(0)
    })
    .catch((error) => {
      console.error("\n💥 Script failed:", error)
      process.exit(1)
    })
}

export { addDummyReferrals, validateUser, getUserReferralCode }
