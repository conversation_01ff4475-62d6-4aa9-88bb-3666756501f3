#!/bin/bash

# Exit immediately if a command exits with a non-zero status.
set -e

echo "🔍 Checking for Firestore file changes..."

# On the very first deploy for a project, VERCEL_GIT_PREVIOUS_SHA is the same as VERCEL_GIT_COMMIT_SHA.
# In this case, we should always deploy the rules.
if [[ "$VERCEL_GIT_COMMIT_SHA" == "$VERCEL_GIT_PREVIOUS_SHA" ]]; then
  echo "🚀 First deployment detected. Deploying Firestore rules..."
  firebase deploy --only firestore --token "$FIREBASE_TOKEN"
  exit 0
fi

# Get a list of changed files between the last deployed commit and the current one.
# Use grep -q to silently check if our files are in the list.
if git diff --name-only $VERCEL_GIT_PREVIOUS_SHA $VERCEL_GIT_COMMIT_SHA | grep -qE "firestore\.rules|firestore\.indexes\.json"; then
  echo "🔥 Firestore file changes detected. Deploying to Firebase..."
  firebase deploy --only firestore --token "$FIREBASE_TOKEN" --project "$FIREBASE_PROJECT_ID"
else
  echo "✅ No changes to Firestore files. Skipping deployment."
fi