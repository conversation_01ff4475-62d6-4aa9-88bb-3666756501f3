// Import the functions you need from the SDKs you need
import { initializeApp, getApps, getApp } from "firebase/app"
import { getAuth } from "firebase/auth"
import { getFirestore } from "firebase/firestore"
import { getStorage } from "firebase/storage"

// Your web app's Firebase configuration
const firebaseConfig = {
  apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY,
  authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN,
  projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
  storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID,
  appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID,
  measurementId: process.env.NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID,
}

// Initialize Firebase
const app = getApps().length > 0 ? getApp() : initializeApp(firebaseConfig)
const auth = getAuth(app)
const db = getFirestore(app)
const storage = getStorage(app)

// Note: Auth emulator is disabled in development because Google OAuth doesn't work with emulators
// We use real Firebase Auth even in development for OAuth functionality
// Other services (Firestore, Storage) can still use emulators if needed

// Uncomment the lines below if you want to use Firestore emulator in development
// import { connectFirestoreEmulator } from "firebase/firestore"
// if (process.env.NODE_ENV === "development" && typeof window !== "undefined") {
//   try {
//     connectFirestoreEmulator(db, "localhost", 8080)
//   } catch (error) {
//     // Emulator already connected or not available
//   }
// }

export { app, auth, db, storage }
