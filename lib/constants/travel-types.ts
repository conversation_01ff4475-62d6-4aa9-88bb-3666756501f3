// Define the allowed travel types that match the ones in travel-preferences.tsx
export const ALLOWED_TRAVEL_TYPES = [
  "Beach",
  "Mountains",
  "City",
  "Countryside",
  "Adventure",
  "Relaxation",
] as const

export type TravelType = (typeof ALLOWED_TRAVEL_TYPES)[number]

// Define the allowed availability preferences that match the ones in signup-form.tsx
export const ALLOWED_AVAILABILITY_PREFERENCES = [
  "Weekends",
  "Week-long",
  "2+ weeks",
  "Flexible",
] as const

export type AvailabilityPreference = (typeof ALLOWED_AVAILABILITY_PREFERENCES)[number]

// Define the allowed travel group preferences that match the ones in signup-form.tsx
export const ALLOWED_TRAVEL_GROUP_PREFERENCES = [
  "Solo",
  "Couples",
  "Friends",
  "Family w/ Kids",
] as const

export type TravelGroupPreference = (typeof ALLOWED_TRAVEL_GROUP_PREFERENCES)[number]

// Function to validate travel types
export function isValidTravelType(type: string): boolean {
  return ALLOWED_TRAVEL_TYPES.includes(type as TravelType)
}

// Function to validate availability preferences
export function isValidAvailabilityPreference(preference: string): boolean {
  return ALLOWED_AVAILABILITY_PREFERENCES.includes(preference as AvailabilityPreference)
}

// Function to validate travel group preferences
export function isValidTravelGroupPreference(preference: string): boolean {
  return ALLOWED_TRAVEL_GROUP_PREFERENCES.includes(preference as TravelGroupPreference)
}
