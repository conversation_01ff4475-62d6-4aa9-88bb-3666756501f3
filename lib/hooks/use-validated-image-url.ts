"use client"

import { useState, useEffect } from "react"
import { Trip } from "@/lib/domains/trip/trip.types"
import { CachedTripSuggestion } from "@/lib/domains/ai-suggestions/ai-suggestions-cache.service"
import {
  getTripImageUrl,
  getSuggestionImageUrl,
  refreshTripImageUrl,
  refreshSuggestionImageUrl,
} from "@/lib/utils/trip-image-utils"

/**
 * Hook to get a validated image URL for a trip with smart error handling
 * Returns the immediate URL first, then validates only if the image fails to load
 */
export function useValidatedTripImageUrl(
  trip: Trip | null,
  placeholderSize: string = "400x1200",
  enableValidation: boolean = true
) {
  const [imageUrl, setImageUrl] = useState<string>("")
  const [isValidating, setIsValidating] = useState(false)
  const [hasValidated, setHasValidated] = useState(false)
  const [imageError, setImageError] = useState(false)

  useEffect(() => {
    if (!trip) {
      setImageUrl("")
      setHasValidated(false)
      setImageError(false)
      return
    }

    // Set immediate URL first (synchronous)
    const immediateUrl = getTripImageUrl(trip, placeholderSize)
    // Ensure we never set an empty string as the image URL
    setImageUrl(
      immediateUrl ||
        `/placeholder.svg?height=${placeholderSize.split("x")[1]}&width=${placeholderSize.split("x")[0]}`
    )
    setHasValidated(false)
    setImageError(false)
  }, [trip?.id, trip?.locationThumbnail, trip?.image, placeholderSize])

  // Function to handle image load errors
  const handleImageError = async () => {
    if (!enableValidation || isValidating || hasValidated || !trip) {
      return
    }

    setImageError(true)
    setIsValidating(true)

    try {
      const refreshedUrl = await refreshTripImageUrl(trip, placeholderSize)
      // Ensure we never set an empty string
      const validUrl =
        refreshedUrl && refreshedUrl.trim() !== ""
          ? refreshedUrl
          : `/placeholder.svg?height=${placeholderSize.split("x")[1]}&width=${placeholderSize.split("x")[0]}`

      if (validUrl !== imageUrl) {
        setImageUrl(validUrl)
        setImageError(false) // Reset error state if we got a new URL
      }
      setHasValidated(true)
    } catch (error) {
      console.error("Error refreshing trip image URL:", error)
      setHasValidated(true)
    } finally {
      setIsValidating(false)
    }
  }

  return {
    imageUrl,
    isValidating,
    hasValidated,
    imageError,
    handleImageError,
  }
}

/**
 * Hook to get a validated image URL for a suggestion
 * Returns the immediate URL first, then validates and updates if needed
 */
export function useValidatedSuggestionImageUrl(
  suggestion: CachedTripSuggestion | null,
  placeholderSize: string = "300x150",
  enableValidation: boolean = true
) {
  const [imageUrl, setImageUrl] = useState<string>("")
  const [isValidating, setIsValidating] = useState(false)
  const [hasValidated, setHasValidated] = useState(false)
  const [imageError, setImageError] = useState(false)

  useEffect(() => {
    if (!suggestion) {
      setImageUrl("")
      setHasValidated(false)
      setImageError(false)
      return
    }

    // Set immediate URL first (synchronous)
    const immediateUrl = getSuggestionImageUrl(suggestion, placeholderSize)
    // Ensure we never set an empty string as the image URL
    setImageUrl(
      immediateUrl ||
        `/placeholder.svg?height=${placeholderSize.split("x")[1]}&width=${placeholderSize.split("x")[0]}`
    )
    setHasValidated(false)
    setImageError(false)
  }, [suggestion?.id, suggestion?.image, suggestion?.destination, placeholderSize])

  // Function to handle image load errors
  const handleImageError = async () => {
    if (!enableValidation || isValidating || hasValidated || !suggestion) {
      return
    }

    setImageError(true)
    setIsValidating(true)

    try {
      const refreshedUrl = await refreshSuggestionImageUrl(suggestion, placeholderSize)
      // Ensure we never set an empty string
      const validUrl =
        refreshedUrl && refreshedUrl.trim() !== ""
          ? refreshedUrl
          : `/placeholder.svg?height=${placeholderSize.split("x")[1]}&width=${placeholderSize.split("x")[0]}`

      if (validUrl !== imageUrl) {
        setImageUrl(validUrl)
        setImageError(false) // Reset error state if we got a new URL
      }
      setHasValidated(true)
    } catch (error) {
      console.error("Error refreshing suggestion image URL:", error)
      setHasValidated(true)
    } finally {
      setIsValidating(false)
    }
  }

  return {
    imageUrl,
    isValidating,
    hasValidated,
    imageError,
    handleImageError,
  }
}

/**
 * Hook for generic image URL validation
 * Useful for any image URL that might need validation and refresh
 */
export function useImageUrlValidation(initialUrl: string, enableValidation: boolean = true) {
  const [imageUrl, setImageUrl] = useState<string>(initialUrl)
  const [isValidating, setIsValidating] = useState(false)
  const [hasValidated, setHasValidated] = useState(false)
  const [isAccessible, setIsAccessible] = useState<boolean | null>(null)

  useEffect(() => {
    setImageUrl(initialUrl)
    setHasValidated(false)
    setIsAccessible(null)

    if (!initialUrl || !enableValidation) {
      setHasValidated(true)
      return
    }

    // Import the validation function dynamically to avoid SSR issues
    import("@/lib/google-places").then(({ isImageUrlAccessible }) => {
      setIsValidating(true)

      isImageUrlAccessible(initialUrl)
        .then((accessible) => {
          setIsAccessible(accessible)
          setHasValidated(true)
        })
        .catch((error) => {
          console.error("Error validating image URL:", error)
          setIsAccessible(false)
          setHasValidated(true)
        })
        .finally(() => {
          setIsValidating(false)
        })
    })
  }, [initialUrl, enableValidation])

  return {
    imageUrl,
    isValidating,
    hasValidated,
    isAccessible,
  }
}
