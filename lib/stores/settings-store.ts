"use client"

import { create } from "zustand"
import { persist, createJSONStorage } from "zustand/middleware"
import { useAuthStore } from "./auth-store"
import { useThemeStore } from "./theme-store"
import { useAppStore } from "@/lib/store"
import {
  getUserPreferences,
  updateUserPreferences,
  type UserPreferences,
} from "@/lib/firebase/user-preferences-service"
import { toast } from "@/components/ui/use-toast"

// Create stable default values to prevent infinite re-renders
const DEFAULT_TRAVEL_PREFERENCES: string[] = []
const DEFAULT_BUDGET_RANGE: [number, number] = [500, 2000]
const DEFAULT_AVAILABILITY_PREFERENCES: string[] = []
const DEFAULT_TRAVEL_SEASONS: string[] = []
const DEFAULT_TRAVEL_GROUP_PREFERENCES: string[] = []

// Define the settings store state
interface SettingsState {
  // State
  preferences: UserPreferences | null
  loading: boolean

  // Theme settings
  theme: "light" | "dark" | "system"

  // Travel preferences
  travelPreferences: string[]
  budgetRange: [number, number] | string
  availabilityPreferences: string[]
  preferredTravelSeasons: string[]
  travelGroupPreferences: string[]

  // AI preferences
  aiEnabled: boolean
  proactiveSuggestions: boolean

  // Notification preferences
  notificationsEnabled: boolean
  emailNotifications: boolean
  pushNotifications: boolean
  tripUpdatesNotifications: boolean
  squadMessagesNotifications: boolean
  invitationNotifications: boolean
  aiSuggestionsNotifications: boolean

  // Actions
  setPreferences: (preferences: UserPreferences | null) => void
  setLoading: (loading: boolean) => void
  setTheme: (theme: "light" | "dark" | "system") => Promise<void>
  updateTravelPreferences: (
    preferences: Partial<{
      travelPreferences: string[]
      budgetRange: [number, number] | string
      availabilityPreferences: string[]
      preferredTravelSeasons: string[]
      travelGroupPreferences: string[]
    }>
  ) => Promise<void>
  updateAIPreferences: (
    preferences: Partial<{
      aiEnabled: boolean
      proactiveSuggestions: boolean
    }>
  ) => Promise<void>
  updateNotificationPreferences: (
    preferences: Partial<{
      notificationsEnabled: boolean
      emailNotifications: boolean
      pushNotifications: boolean
      tripUpdatesNotifications: boolean
      squadMessagesNotifications: boolean
      invitationNotifications: boolean
      aiSuggestionsNotifications: boolean
    }>
  ) => Promise<void>
  fetchPreferences: () => Promise<void>
}

// Create a custom storage object that only uses localStorage on the client side
const customStorage = {
  getItem: (name: string) => {
    if (typeof window === "undefined") return null
    return window.localStorage.getItem(name)
  },
  setItem: (name: string, value: string) => {
    if (typeof window !== "undefined") {
      window.localStorage.setItem(name, value)
    }
  },
  removeItem: (name: string) => {
    if (typeof window !== "undefined") {
      window.localStorage.removeItem(name)
    }
  },
}

// Create the settings store
export const useSettingsStore = create<SettingsState>()(
  persist(
    (set, get) => ({
      // Initial state
      preferences: null,
      loading: true,

      // Theme settings
      theme: "system",

      // Travel preferences
      travelPreferences: DEFAULT_TRAVEL_PREFERENCES,
      budgetRange: DEFAULT_BUDGET_RANGE,
      availabilityPreferences: DEFAULT_AVAILABILITY_PREFERENCES,
      preferredTravelSeasons: DEFAULT_TRAVEL_SEASONS,
      travelGroupPreferences: DEFAULT_TRAVEL_GROUP_PREFERENCES,

      // AI preferences
      aiEnabled: true,
      proactiveSuggestions: true,

      // Notification preferences
      notificationsEnabled: true,
      emailNotifications: true,
      pushNotifications: true,
      tripUpdatesNotifications: true,
      squadMessagesNotifications: true,
      invitationNotifications: true,
      aiSuggestionsNotifications: true,

      // Actions
      setPreferences: (preferences) => {
        if (!preferences) {
          set({
            preferences: null,
            travelPreferences: DEFAULT_TRAVEL_PREFERENCES,
            budgetRange: DEFAULT_BUDGET_RANGE,
            availabilityPreferences: DEFAULT_AVAILABILITY_PREFERENCES,
            preferredTravelSeasons: DEFAULT_TRAVEL_SEASONS,
            travelGroupPreferences: DEFAULT_TRAVEL_GROUP_PREFERENCES,
            aiEnabled: true,
            proactiveSuggestions: true,
            notificationsEnabled: true,
            emailNotifications: true,
            pushNotifications: true,
            tripUpdatesNotifications: true,
            squadMessagesNotifications: true,
            invitationNotifications: true,
            aiSuggestionsNotifications: true,
          })
          return
        }

        set({
          preferences,
          travelPreferences: preferences.travelPreferences ?? DEFAULT_TRAVEL_PREFERENCES,
          budgetRange: preferences.budgetRange ?? DEFAULT_BUDGET_RANGE,
          availabilityPreferences:
            preferences.availabilityPreferences ?? DEFAULT_AVAILABILITY_PREFERENCES,
          preferredTravelSeasons: preferences.preferredTravelSeasons ?? DEFAULT_TRAVEL_SEASONS,
          travelGroupPreferences:
            preferences.travelGroupPreferences ?? DEFAULT_TRAVEL_GROUP_PREFERENCES,
          aiEnabled: preferences.aiEnabled ?? true,
          proactiveSuggestions: preferences.proactiveSuggestions ?? true,
          notificationsEnabled: preferences.notificationsEnabled ?? true,
          emailNotifications: preferences.emailNotifications ?? true,
          pushNotifications: preferences.pushNotifications ?? true,
          tripUpdatesNotifications: preferences.tripUpdatesNotifications ?? true,
          squadMessagesNotifications: preferences.squadMessagesNotifications ?? true,
          invitationNotifications: preferences.invitationNotifications ?? true,
          aiSuggestionsNotifications: preferences.aiSuggestionsNotifications ?? true,
        })
      },
      setLoading: (loading) => set({ loading }),

      // Set theme and update in Firebase
      setTheme: async (theme) => {
        const user = useAuthStore.getState().user

        try {
          // Update the theme store - this will handle DOM updates and localStorage
          await useThemeStore.getState().setTheme(theme, user?.uid)

          // Update the app store for backward compatibility
          useAppStore
            .getState()
            .setDarkMode(
              theme === "dark" ||
                (theme === "system" &&
                  typeof window !== "undefined" &&
                  window.matchMedia("(prefers-color-scheme: dark)").matches)
            )

          // Update the settings store
          set({ theme })

          // Update the preferences in the store if they exist
          const preferences = get().preferences
          if (preferences) {
            set({
              preferences: {
                ...preferences,
                theme,
              },
            })
          }

          // No need to show toast here as the theme store will handle it
        } catch (error) {
          console.error("Error updating theme:", error)
          toast({
            title: "Error",
            description: "Failed to update theme. Please try again.",
            variant: "destructive",
          })
          throw error // Re-throw to allow proper error handling in components
        }
      },

      // Update travel preferences
      updateTravelPreferences: async (preferences) => {
        const user = useAuthStore.getState().user
        if (!user?.uid) return

        const currentPreferences = get().preferences || {}
        const updatedPreferences = {
          ...currentPreferences,
          ...preferences,
        }

        try {
          await updateUserPreferences(user.uid, updatedPreferences)

          // Update the store - fix the type error by properly updating the preferences object
          // First update the individual fields from the preferences parameter
          set(preferences)

          // Then update the full preferences object separately
          // Cast to UserPreferences to fix type error
          set({
            preferences: updatedPreferences as unknown as UserPreferences,
          })

          // Update the app store for backward compatibility
          if (preferences.budgetRange) {
            useAppStore
              .getState()
              .setBudgetRange(
                Array.isArray(preferences.budgetRange) ? preferences.budgetRange : [500, 2000]
              )
          }

          if (preferences.travelPreferences) {
            useAppStore.getState().setTravelTypes(preferences.travelPreferences)
          }

          toast({
            title: "Preferences updated",
            description: "Your travel preferences have been updated successfully.",
          })
        } catch (error) {
          console.error("Error updating travel preferences:", error)
          toast({
            title: "Error",
            description: "Failed to update preferences. Please try again.",
            variant: "destructive",
          })
        }
      },

      // Update AI preferences
      updateAIPreferences: async (preferences) => {
        const user = useAuthStore.getState().user
        if (!user?.uid) return

        const currentPreferences = get().preferences || {}
        const updatedPreferences = {
          ...currentPreferences,
          ...preferences,
        }

        try {
          await updateUserPreferences(user.uid, updatedPreferences)

          // Update the store - fix the type error by properly updating the preferences object
          // First update the individual fields from the preferences parameter
          set(preferences)

          // Then update the full preferences object separately
          // Cast to UserPreferences to fix type error
          set({
            preferences: updatedPreferences as unknown as UserPreferences,
          })

          toast({
            title: "AI preferences updated",
            description: "Your AI preferences have been updated successfully.",
          })
        } catch (error) {
          console.error("Error updating AI preferences:", error)
          toast({
            title: "Error",
            description: "Failed to update AI preferences. Please try again.",
            variant: "destructive",
          })
        }
      },

      // Update notification preferences
      updateNotificationPreferences: async (preferences) => {
        const user = useAuthStore.getState().user
        if (!user?.uid) return

        const currentPreferences = get().preferences || {}
        const updatedPreferences = {
          ...currentPreferences,
          ...preferences,
        }

        try {
          await updateUserPreferences(user.uid, updatedPreferences)

          // Update the store - fix the type error by properly updating the preferences object
          // First update the individual fields from the preferences parameter
          set(preferences)

          // Then update the full preferences object separately
          // Cast to UserPreferences to fix type error
          set({
            preferences: updatedPreferences as unknown as UserPreferences,
          })

          // Update the app store for backward compatibility
          if (preferences.notificationsEnabled !== undefined) {
            useAppStore.getState().setNotificationsEnabled(preferences.notificationsEnabled)
          }

          toast({
            title: "Notification preferences updated",
            description: "Your notification preferences have been updated successfully.",
          })
        } catch (error) {
          console.error("Error updating notification preferences:", error)
          toast({
            title: "Error",
            description: "Failed to update notification preferences. Please try again.",
            variant: "destructive",
          })
        }
      },

      // Fetch user preferences
      fetchPreferences: async () => {
        const user = useAuthStore.getState().user

        if (!user?.uid) {
          set({ loading: false, preferences: null })
          return
        }

        try {
          set({ loading: true })

          // Get the user preferences from Firebase
          const userPreferences = await getUserPreferences(user.uid)

          // Update the store with the preferences
          get().setPreferences(userPreferences)

          // Initialize the theme store - this will handle all theme synchronization
          // between localStorage and database
          await useThemeStore.getState().initializeTheme(user.uid)

          // Update the settings store theme to match the theme store
          const currentTheme = useThemeStore.getState().theme
          set({ theme: currentTheme })

          // Update the app store for backward compatibility
          useAppStore
            .getState()
            .setDarkMode(
              currentTheme === "dark" ||
                (currentTheme === "system" &&
                  typeof window !== "undefined" &&
                  window.matchMedia("(prefers-color-scheme: dark)").matches)
            )

          set({ loading: false })
        } catch (error) {
          console.error("Error fetching user preferences:", error)
          set({ loading: false })
        }
      },
    }),
    {
      name: "togeda-settings-storage",
      storage: createJSONStorage(() => customStorage),
      skipHydration: true, // Skip hydration to prevent hydration mismatch
      partialize: (state) => ({
        // Only persist these fields
        theme: state.theme,
        travelPreferences: state.travelPreferences,
        budgetRange: state.budgetRange,
        availabilityPreferences: state.availabilityPreferences,
        preferredTravelSeasons: state.preferredTravelSeasons,
        travelGroupPreferences: state.travelGroupPreferences,
        aiEnabled: state.aiEnabled,
        proactiveSuggestions: state.proactiveSuggestions,
        notificationsEnabled: state.notificationsEnabled,
        emailNotifications: state.emailNotifications,
        pushNotifications: state.pushNotifications,
        tripUpdatesNotifications: state.tripUpdatesNotifications,
        squadMessagesNotifications: state.squadMessagesNotifications,
        invitationNotifications: state.invitationNotifications,
        aiSuggestionsNotifications: state.aiSuggestionsNotifications,
      }),
    }
  )
)

// Export the store for use in hooks
export type { SettingsState }
