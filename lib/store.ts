import { create } from "zustand"
import { persist, createJSONStorage } from "zustand/middleware"

interface UserPreferences {
  darkMode: boolean
  notificationsEnabled: boolean
  budgetRange: [number, number]
  travelTypes: string[]
}

interface AppState {
  userPreferences: UserPreferences
  setDarkMode: (darkMode: boolean) => void
  setNotificationsEnabled: (enabled: boolean) => void
  setBudgetRange: (range: [number, number]) => void
  setTravelTypes: (types: string[]) => void
}

// Create a custom storage object that only uses localStorage on the client side
const customStorage = {
  getItem: (name: string) => {
    if (typeof window === "undefined") return null
    return window.localStorage.getItem(name)
  },
  setItem: (name: string, value: string) => {
    if (typeof window !== "undefined") {
      window.localStorage.setItem(name, value)
    }
  },
  removeItem: (name: string) => {
    if (typeof window !== "undefined") {
      window.localStorage.removeItem(name)
    }
  },
}

export const useAppStore = create<AppState>()(
  persist(
    (set) => ({
      userPreferences: {
        darkMode: false,
        notificationsEnabled: true,
        budgetRange: [500, 2000],
        travelTypes: ["Beach", "Mountains", "City"],
      },
      setDarkMode: (darkMode) =>
        set((state) => ({
          userPreferences: {
            ...state.userPreferences,
            darkMode,
          },
        })),
      setNotificationsEnabled: (notificationsEnabled) =>
        set((state) => ({
          userPreferences: {
            ...state.userPreferences,
            notificationsEnabled,
          },
        })),
      setBudgetRange: (budgetRange) =>
        set((state) => ({
          userPreferences: {
            ...state.userPreferences,
            budgetRange,
          },
        })),
      setTravelTypes: (travelTypes) =>
        set((state) => ({
          userPreferences: {
            ...state.userPreferences,
            travelTypes,
          },
        })),
    }),
    {
      name: "togeda-storage",
      storage: createJSONStorage(() => customStorage),
      skipHydration: true, // Skip hydration to prevent hydration mismatch
    }
  )
)
