import { db } from "../firebase"
import {
  collection,
  doc,
  getDoc,
  getDocs,
  updateDoc,
  query,
  where,
  serverTimestamp,
  type Timestamp,
  setDoc,
} from "firebase/firestore"

// Types
export interface UserPreferences {
  id: string // Same as userId for easy lookup
  userId: string
  theme: "light" | "dark" | "system"
  travelPreferences: string[]
  budgetRange: [number, number] | string
  availabilityPreferences: string[]
  preferredTravelSeasons: string[]
  travelGroupPreferences: string[]
  // AI preferences
  aiEnabled: boolean
  proactiveSuggestions: boolean
  // Notification preferences
  notificationsEnabled: boolean
  emailNotifications: boolean
  pushNotifications: boolean
  tripUpdatesNotifications: boolean
  squadMessagesNotifications: boolean
  invitationNotifications: boolean
  aiSuggestionsNotifications: boolean
  // Created/updated timestamps
  createdAt: Timestamp | null
  updatedAt: Timestamp | null
}

/**
 * Get a user's preferences
 */
export const getUserPreferences = async (userId: string): Promise<UserPreferences | null> => {
  try {
    const preferencesDoc = await getDoc(doc(db, "userPreferences", userId))

    if (preferencesDoc.exists()) {
      return { id: preferencesDoc.id, ...preferencesDoc.data() } as UserPreferences
    }

    // If no preferences document exists, create a default one
    const defaultPreferences: Omit<UserPreferences, "id"> = {
      userId,
      theme: "system",
      travelPreferences: [],
      budgetRange: [500, 2000],
      availabilityPreferences: [],
      preferredTravelSeasons: [],
      travelGroupPreferences: [],
      // AI preferences
      aiEnabled: true,
      proactiveSuggestions: true,
      // Notification preferences
      notificationsEnabled: true,
      emailNotifications: true,
      pushNotifications: true,
      tripUpdatesNotifications: true,
      squadMessagesNotifications: true,
      invitationNotifications: true,
      aiSuggestionsNotifications: true,
      // Timestamps
      createdAt: serverTimestamp() as Timestamp,
      updatedAt: serverTimestamp() as Timestamp,
    }

    await setDoc(doc(db, "userPreferences", userId), defaultPreferences)

    return { id: userId, ...defaultPreferences } as UserPreferences
  } catch (error) {
    console.error("Error getting user preferences:", error)
    return null
  }
}

/**
 * Update a user's preferences
 */
export const updateUserPreferences = async (
  userId: string,
  preferencesData: Partial<Omit<UserPreferences, "id" | "userId" | "createdAt">>
) => {
  try {
    const preferencesRef = doc(db, "userPreferences", userId)
    const preferencesDoc = await getDoc(preferencesRef)

    if (preferencesDoc.exists()) {
      // Update existing preferences
      await updateDoc(preferencesRef, {
        ...preferencesData,
        updatedAt: serverTimestamp(),
      })
    } else {
      // Create new preferences with defaults and overrides
      const defaultPreferences: Omit<UserPreferences, "id"> = {
        userId,
        theme: preferencesData.theme || "system",
        travelPreferences: preferencesData.travelPreferences || [],
        budgetRange: preferencesData.budgetRange || [500, 2000],
        availabilityPreferences: preferencesData.availabilityPreferences || [],
        preferredTravelSeasons: preferencesData.preferredTravelSeasons || [],
        travelGroupPreferences: preferencesData.travelGroupPreferences || [],
        // AI preferences
        aiEnabled: preferencesData.aiEnabled !== undefined ? preferencesData.aiEnabled : true,
        proactiveSuggestions:
          preferencesData.proactiveSuggestions !== undefined
            ? preferencesData.proactiveSuggestions
            : true,
        // Notification preferences
        notificationsEnabled:
          preferencesData.notificationsEnabled !== undefined
            ? preferencesData.notificationsEnabled
            : true,
        emailNotifications:
          preferencesData.emailNotifications !== undefined
            ? preferencesData.emailNotifications
            : true,
        pushNotifications:
          preferencesData.pushNotifications !== undefined
            ? preferencesData.pushNotifications
            : true,
        tripUpdatesNotifications:
          preferencesData.tripUpdatesNotifications !== undefined
            ? preferencesData.tripUpdatesNotifications
            : true,
        squadMessagesNotifications:
          preferencesData.squadMessagesNotifications !== undefined
            ? preferencesData.squadMessagesNotifications
            : true,
        invitationNotifications:
          preferencesData.invitationNotifications !== undefined
            ? preferencesData.invitationNotifications
            : true,
        aiSuggestionsNotifications:
          preferencesData.aiSuggestionsNotifications !== undefined
            ? preferencesData.aiSuggestionsNotifications
            : true,
        // Timestamps
        createdAt: serverTimestamp() as Timestamp,
        updatedAt: serverTimestamp() as Timestamp,
      }

      await setDoc(preferencesRef, defaultPreferences)
    }

    return { success: true }
  } catch (error) {
    console.error("Error updating user preferences:", error)
    throw error
  }
}

/**
 * Update user theme preference
 */
export const updateUserTheme = async (userId: string, theme: "light" | "dark" | "system") => {
  return updateUserPreferences(userId, { theme })
}

/**
 * Migrate user preferences from users collection to userPreferences collection
 * This is a one-time migration function
 */
export const migrateUserPreferences = async (userId: string) => {
  try {
    // Get user document
    const userDoc = await getDoc(doc(db, "users", userId))

    if (!userDoc.exists()) {
      console.error("User document does not exist")
      return { success: false, error: "User document does not exist" }
    }

    const userData = userDoc.data()

    // Create preferences document
    const preferencesData: Omit<UserPreferences, "id"> = {
      userId,
      theme: userData.theme || "system",
      travelPreferences: userData.travelPreferences || [],
      budgetRange: userData.budgetRange || [500, 2000],
      availabilityPreferences: userData.availabilityPreferences || [],
      preferredTravelSeasons: userData.preferredTravelSeasons || [],
      travelGroupPreferences: userData.travelGroupPreferences || [],
      // AI preferences - defaults since these might not exist in the user document
      aiEnabled: true,
      proactiveSuggestions: true,
      // Notification preferences - defaults since these might not exist in the user document
      notificationsEnabled: true,
      emailNotifications: true,
      pushNotifications: true,
      tripUpdatesNotifications: true,
      squadMessagesNotifications: true,
      invitationNotifications: true,
      aiSuggestionsNotifications: true,
      // Timestamps
      createdAt: serverTimestamp() as Timestamp,
      updatedAt: serverTimestamp() as Timestamp,
    }

    await setDoc(doc(db, "userPreferences", userId), preferencesData)

    return { success: true }
  } catch (error) {
    console.error("Error migrating user preferences:", error)
    return { success: false, error }
  }
}
