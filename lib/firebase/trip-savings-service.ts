import { db } from "../firebase"
import {
  collection,
  doc,
  getDoc,
  getDocs,
  updateDoc,
  query,
  where,
  serverTimestamp,
  type Timestamp,
  setDoc,
} from "firebase/firestore"

// Types
export interface TripSavings {
  id: string
  userId: string
  tripId: string
  name: string
  goalAmount: number
  savedAmount: number
  targetDate: Timestamp
  isPrivate: boolean
  createdAt: Timestamp | null
  updatedAt: Timestamp | null
}

export interface SavingsTransaction {
  id: string
  savingsId: string
  userId: string
  amount: number
  date: Timestamp
  method: string
  createdAt: Timestamp | null
}

// Trip Savings operations
export const createTripSavings = async (
  savingsData: Omit<TripSavings, "id" | "createdAt" | "updatedAt" | "savedAmount">
) => {
  try {
    const savingsRef = doc(collection(db, "tripSavings"))
    const savingsId = savingsRef.id

    const newSavings = {
      ...savingsData,
      id: savingsId,
      savedAmount: 0,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp(),
    }

    await setDoc(savingsRef, newSavings)
    return savingsId
  } catch (error) {
    console.error("Error creating trip savings:", error)
    throw error
  }
}

export const getTripSavings = async (savingsId: string) => {
  try {
    const savingsDoc = await getDoc(doc(db, "tripSavings", savingsId))
    if (savingsDoc.exists()) {
      return { ...savingsDoc.data(), id: savingsId } as TripSavings
    }
    return null
  } catch (error) {
    console.error("Error getting trip savings:", error)
    throw error
  }
}

export const getUserTripSavings = async (userId: string, tripId: string) => {
  try {
    const q = query(
      collection(db, "tripSavings"),
      where("userId", "==", userId),
      where("tripId", "==", tripId)
    )
    const querySnapshot = await getDocs(q)

    if (!querySnapshot.empty) {
      const savingsDoc = querySnapshot.docs[0]
      return { ...savingsDoc.data(), id: savingsDoc.id } as TripSavings
    }

    return null
  } catch (error) {
    console.error("Error getting user trip savings:", error)
    throw error
  }
}

export const getSquadTripSavings = async (tripId: string) => {
  try {
    const q = query(collection(db, "tripSavings"), where("tripId", "==", tripId))
    const querySnapshot = await getDocs(q)
    return querySnapshot.docs.map((doc) => ({ ...doc.data(), id: doc.id }) as TripSavings)
  } catch (error) {
    console.error("Error getting squad trip savings:", error)
    throw error
  }
}

export const updateTripSavings = async (
  userId: string,
  tripId: string,
  savingsData: Partial<TripSavings>
) => {
  try {
    // First, get the user's savings for this trip
    const q = query(
      collection(db, "tripSavings"),
      where("userId", "==", userId),
      where("tripId", "==", tripId)
    )
    const querySnapshot = await getDocs(q)

    if (querySnapshot.empty) {
      throw new Error("No savings found for this user and trip")
    }

    const savingsDoc = querySnapshot.docs[0]
    const savingsId = savingsDoc.id

    await updateDoc(doc(db, "tripSavings", savingsId), {
      ...savingsData,
      updatedAt: serverTimestamp(),
    })
    return { success: true }
  } catch (error) {
    console.error("Error updating trip savings:", error)
    throw error
  }
}

export const addSavingsTransaction = async (
  userId: string,
  tripId: string,
  transactionData: { amount: number; date: Timestamp; description?: string; method?: string }
) => {
  try {
    // First, get the user's savings for this trip
    const q = query(
      collection(db, "tripSavings"),
      where("userId", "==", userId),
      where("tripId", "==", tripId)
    )
    const querySnapshot = await getDocs(q)

    if (querySnapshot.empty) {
      throw new Error("No savings found for this user and trip")
    }

    const savingsDoc = querySnapshot.docs[0]
    const savingsId = savingsDoc.id
    const savings = savingsDoc.data() as TripSavings

    // Create the transaction
    const transactionRef = doc(collection(db, "savingsTransactions"))
    const transactionId = transactionRef.id

    const newTransaction = {
      ...transactionData,
      id: transactionId,
      userId,
      savingsId,
      method: transactionData.method || "manual",
      createdAt: serverTimestamp(),
    }

    await setDoc(transactionRef, newTransaction)

    // Update the savings amount
    const newSavedAmount = (savings.savedAmount || 0) + transactionData.amount

    await updateDoc(doc(db, "tripSavings", savingsId), {
      savedAmount: newSavedAmount,
      updatedAt: serverTimestamp(),
    })

    return transactionId
  } catch (error) {
    console.error("Error adding savings transaction:", error)
    throw error
  }
}

export const getSavingsTransactions = async (savingsId: string) => {
  try {
    const q = query(collection(db, "savingsTransactions"), where("savingsId", "==", savingsId))
    const querySnapshot = await getDocs(q)
    return querySnapshot.docs.map((doc) => ({ ...doc.data(), id: doc.id }) as SavingsTransaction)
  } catch (error) {
    console.error("Error getting savings transactions:", error)
    throw error
  }
}
