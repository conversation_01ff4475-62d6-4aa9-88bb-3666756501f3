import { db } from "../firebase"
import { doc, getDoc, updateDoc, increment, serverTimestamp, Timestamp } from "firebase/firestore"
import { hasActiveSubscription } from "./subscription-service"

// Interface for AI usage data
export interface AIUsage {
  userId: string
  aiUsageToday: number
  aiUsageThisWeek: number
  aiUsageLastReset: Timestamp | null
  aiUsageWeekStart: Timestamp | null
}

// AI usage limits for free users
export const AI_USAGE_LIMITS = {
  FREE: {
    DAILY: 10,
    WEEKLY: 50,
  },
  PRO: {
    DAILY: Infinity,
    WEEKLY: Infinity,
  },
}

// Check if user can make more AI requests today
export const canMakeAIRequest = async (userId: string): Promise<boolean> => {
  try {
    // Check if user has an active subscription
    const isSubscribed = await hasActiveSubscription(userId)
    if (isSubscribed) {
      return true
    }

    // Get user's AI usage
    const userDoc = await getDoc(doc(db, "users", userId))
    if (!userDoc.exists()) return false

    const userData = userDoc.data()

    // Check daily limit
    const now = new Date()
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate()).getTime()

    // If aiUsageLastReset is not set or is from a previous day, the user hasn't used AI today
    if (
      !userData.aiUsageLastReset ||
      (userData.aiUsageLastReset.toMillis && userData.aiUsageLastReset.toMillis() < today)
    ) {
      return true
    }

    // Check if daily limit is reached
    if (userData.aiUsageToday && userData.aiUsageToday >= AI_USAGE_LIMITS.FREE.DAILY) {
      return false
    }

    // Check weekly limit
    const oneWeekAgo = new Date()
    oneWeekAgo.setDate(oneWeekAgo.getDate() - 7)
    const weekStart = oneWeekAgo.getTime()

    // If aiUsageWeekStart is not set or is older than a week, reset the weekly counter
    if (
      !userData.aiUsageWeekStart ||
      (userData.aiUsageWeekStart.toMillis && userData.aiUsageWeekStart.toMillis() < weekStart)
    ) {
      return true
    }

    // Check if weekly limit is reached
    if (userData.aiUsageThisWeek && userData.aiUsageThisWeek >= AI_USAGE_LIMITS.FREE.WEEKLY) {
      return false
    }

    return true
  } catch (error) {
    console.error("Error checking AI usage:", error)
    // Default to allowing the request if there's an error checking
    return true
  }
}

// Increment AI usage counter for a user
export const incrementAIUsage = async (userId: string): Promise<void> => {
  try {
    const userDoc = await getDoc(doc(db, "users", userId))
    if (!userDoc.exists()) return

    const userData = userDoc.data()
    const now = new Date()
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate()).getTime()

    // If this is the first AI request today, reset the daily counter
    if (
      !userData.aiUsageLastReset ||
      (userData.aiUsageLastReset.toMillis && userData.aiUsageLastReset.toMillis() < today)
    ) {
      await updateDoc(doc(db, "users", userId), {
        aiUsageToday: 1,
        aiUsageLastReset: serverTimestamp(),
      })
    } else {
      // Increment the daily counter
      await updateDoc(doc(db, "users", userId), {
        aiUsageToday: increment(1),
      })
    }

    // Check if we need to initialize or update the weekly counter
    const oneWeekAgo = new Date()
    oneWeekAgo.setDate(oneWeekAgo.getDate() - 7)
    const weekStart = oneWeekAgo.getTime()

    if (
      !userData.aiUsageWeekStart ||
      (userData.aiUsageWeekStart.toMillis && userData.aiUsageWeekStart.toMillis() < weekStart)
    ) {
      // Initialize the weekly counter
      await updateDoc(doc(db, "users", userId), {
        aiUsageThisWeek: 1,
        aiUsageWeekStart: serverTimestamp(),
      })
    } else {
      // Increment the weekly counter
      await updateDoc(doc(db, "users", userId), {
        aiUsageThisWeek: increment(1),
      })
    }
  } catch (error) {
    console.error("Error incrementing AI usage:", error)
  }
}

// Get current AI usage for a user
export const getAIUsage = async (
  userId: string
): Promise<{
  daily: number
  weekly: number
  dailyLimit: number
  weeklyLimit: number
  canMakeRequest: boolean
}> => {
  try {
    // Check if user has an active subscription
    const isSubscribed = await hasActiveSubscription(userId)

    // Get user's AI usage - use a fresh read to ensure we have the latest data
    const userDoc = await getDoc(doc(db, "users", userId))
    if (!userDoc.exists()) {
      return {
        daily: 0,
        weekly: 0,
        dailyLimit: AI_USAGE_LIMITS.FREE.DAILY,
        weeklyLimit: AI_USAGE_LIMITS.FREE.WEEKLY,
        canMakeRequest: true,
      }
    }

    const userData = userDoc.data()

    // Get the appropriate limits based on subscription status
    const dailyLimit = isSubscribed ? AI_USAGE_LIMITS.PRO.DAILY : AI_USAGE_LIMITS.FREE.DAILY
    const weeklyLimit = isSubscribed ? AI_USAGE_LIMITS.PRO.WEEKLY : AI_USAGE_LIMITS.FREE.WEEKLY

    // Check if we need to reset counters
    const now = new Date()
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate()).getTime()

    let daily = userData.aiUsageToday || 0

    // Reset daily counter if it's a new day
    if (
      !userData.aiUsageLastReset ||
      (userData.aiUsageLastReset.toMillis && userData.aiUsageLastReset.toMillis() < today)
    ) {
      daily = 0
      // If we detect it's a new day, update the database to reset the counter
      // This ensures other components will see the reset value without requiring a full page refresh
      await updateDoc(doc(db, "users", userId), {
        aiUsageToday: 0,
        aiUsageLastReset: serverTimestamp(),
      })
    }

    // Check weekly counter
    const oneWeekAgo = new Date()
    oneWeekAgo.setDate(oneWeekAgo.getDate() - 7)
    const weekStart = oneWeekAgo.getTime()

    let weekly = userData.aiUsageThisWeek || 0

    // Reset weekly counter if it's been more than a week
    if (
      !userData.aiUsageWeekStart ||
      (userData.aiUsageWeekStart.toMillis && userData.aiUsageWeekStart.toMillis() < weekStart)
    ) {
      weekly = 0
      // If we detect it's a new week, update the database to reset the counter
      await updateDoc(doc(db, "users", userId), {
        aiUsageThisWeek: 0,
        aiUsageWeekStart: serverTimestamp(),
      })
    }

    // Determine if the user can make a request
    const canMakeRequest = isSubscribed || (daily < dailyLimit && weekly < weeklyLimit)

    return {
      daily,
      weekly,
      dailyLimit,
      weeklyLimit,
      canMakeRequest,
    }
  } catch (error) {
    console.error("Error getting AI usage:", error)
    // Default values if there's an error
    return {
      daily: 0,
      weekly: 0,
      dailyLimit: AI_USAGE_LIMITS.FREE.DAILY,
      weeklyLimit: AI_USAGE_LIMITS.FREE.WEEKLY,
      canMakeRequest: true,
    }
  }
}
