import { db } from "../firebase"
import {
  collection,
  doc,
  getDoc,
  getDocs,
  updateDoc,
  query,
  where,
  serverTimestamp,
  type Timestamp,
  setDoc,
  deleteDoc,
} from "firebase/firestore"
import { getTrip } from "./trip-service"

// Types
export interface Task {
  id: string
  tripId: string
  title: string
  description?: string
  category: "planning" | "booking" | "preparation" | "coordination" | "during-trip"
  assigneeId: string
  dueDate: Timestamp | null
  completed: boolean
  createdBy: string
  createdAt: Timestamp | null
  tags?: string[] // Added tags for affiliate link matching
}

// Task operations
export const createTask = async (taskData: Omit<Task, "id" | "createdAt" | "completed">) => {
  try {
    const taskRef = doc(collection(db, "tripTasks"))
    const taskId = taskRef.id

    const newTask = {
      ...taskData,
      id: taskId,
      completed: false,
      createdAt: serverTimestamp(),
    }

    await setDoc(taskRef, newTask)

    // Update trip task count
    const tripRef = doc(db, "trips", taskData.tripId)
    const tripDoc = await getDoc(tripRef)

    if (tripDoc.exists()) {
      const tripData = tripDoc.data()
      const totalTasks = (tripData.totalTasks || 0) + 1

      await updateDoc(tripRef, { totalTasks })
    }

    return taskId
  } catch (error) {
    console.error("Error creating task:", error)
    throw error
  }
}

export const getTripTasks = async (tripId: string) => {
  try {
    const q = query(collection(db, "tripTasks"), where("tripId", "==", tripId))
    const querySnapshot = await getDocs(q)
    return querySnapshot.docs.map((doc) => ({ ...doc.data(), id: doc.id }) as Task)
  } catch (error) {
    console.error("Error getting trip tasks:", error)
    throw error
  }
}

export const updateTask = async (taskId: string, taskData: Partial<Task>) => {
  try {
    const taskRef = doc(db, "tripTasks", taskId)
    const taskDoc = await getDoc(taskRef)

    if (!taskDoc.exists()) {
      throw new Error("Task not found")
    }

    const oldTask = taskDoc.data() as Task
    const wasCompleted = oldTask.completed
    const willBeCompleted = taskData.completed !== undefined ? taskData.completed : wasCompleted

    await updateDoc(taskRef, taskData)

    // If completion status changed, update trip task completion count
    if (wasCompleted !== willBeCompleted) {
      const tripRef = doc(db, "trips", oldTask.tripId)
      const tripDoc = await getDoc(tripRef)

      if (tripDoc.exists()) {
        const tripData = tripDoc.data()
        const tasksCompleted = (tripData.tasksCompleted || 0) + (willBeCompleted ? 1 : -1)
        console.log("Updating trip tasks completed:", tasksCompleted)
        await updateDoc(tripRef, { tasksCompleted })
      }
    }

    return { success: true }
  } catch (error) {
    console.error("Error updating task:", error)
    throw error
  }
}

export const deleteTask = async (taskId: string) => {
  try {
    const taskRef = doc(db, "tripTasks", taskId)
    const taskDoc = await getDoc(taskRef)

    if (!taskDoc.exists()) {
      throw new Error("Task not found")
    }

    const task = taskDoc.data() as Task

    await deleteDoc(taskRef)

    // Update trip task counts
    const tripRef = doc(db, "trips", task.tripId)
    const tripDoc = await getDoc(tripRef)

    if (tripDoc.exists()) {
      const tripData = tripDoc.data()
      const totalTasks = Math.max(0, (tripData.totalTasks || 0) - 1)
      const tasksCompleted = Math.max(0, (tripData.tasksCompleted || 0) - (task.completed ? 1 : 0))

      await updateDoc(tripRef, {
        totalTasks,
        tasksCompleted,
      })
    }

    return { success: true }
  } catch (error) {
    console.error("Error deleting task:", error)
    throw error
  }
}

// Check if a user can manage a task (is trip leader or task assignee)
export const canUserManageTask = async (userId: string, taskId: string) => {
  try {
    const taskRef = doc(db, "tripTasks", taskId)
    const taskDoc = await getDoc(taskRef)

    if (!taskDoc.exists()) {
      return false
    }

    const task = taskDoc.data() as Task

    // Check if user is the task assignee
    console.log("task.assigneeId === userId")
    if (task.assigneeId === userId) {
      return true
    }

    // Check if user is the task creator
    console.log("task.createdBy === userId")
    if (task.createdBy === userId) {
      return true
    }

    // Get the trip data
    const trip = await getTrip(task.tripId)

    if (!trip) {
      return false
    }

    // Check if user is the trip leader
    console.log("trip.leaderId === userId")
    if (trip.leaderId === userId) {
      return true
    }

    return false
  } catch (error) {
    console.error("Error checking if user can manage task:", error)
    return false
  }
}
