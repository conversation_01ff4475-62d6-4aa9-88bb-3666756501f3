import { db } from "../firebase"
import {
  collection,
  doc,
  getDoc,
  getDocs,
  updateDoc,
  query,
  where,
  serverTimestamp,
  type Timestamp,
  setDoc,
} from "firebase/firestore"

// Types
export interface UserTrip {
  id: string
  userId: string
  tripId: string
  status: "going" | "not-going" | "undecided"
  createdAt: Timestamp | null
  updatedAt: Timestamp | null
}

// User Trip Status operations

/**
 * Checks if a user trip status exists without creating a new entry
 * This is the safer version that doesn't automatically create entries
 */
export const checkUserTripStatus = async (
  userId: string,
  tripId: string
): Promise<UserTrip | null> => {
  try {
    const q = query(
      collection(db, "userTrips"),
      where("userId", "==", userId),
      where("tripId", "==", tripId)
    )
    const querySnapshot = await getDocs(q)

    if (!querySnapshot.empty) {
      const userTripDoc = querySnapshot.docs[0]
      return { ...userTripDoc.data(), id: userTripDoc.id } as UserTrip
    }

    return null
  } catch (error) {
    console.error("Error checking user trip status:", error)
    throw error
  }
}

/**
 * Gets or creates a user trip status
 * @deprecated This function automatically creates entries which can be a security risk.
 * Use checkUserTripStatus() instead for checking status, and only create entries
 * when the authenticated user is modifying their own status.
 */
export const getUserTripStatus = async (userId: string, tripId: string) => {
  try {
    // First check if the status exists
    const existingStatus = await checkUserTripStatus(userId, tripId)
    if (existingStatus) {
      return existingStatus
    }

    // If no status is found, create a default "not-going" status
    const userTripRef = doc(collection(db, "userTrips"))
    const userTripId = userTripRef.id

    const newUserTrip: UserTrip = {
      id: userTripId,
      userId,
      tripId,
      status: "not-going",
      createdAt: serverTimestamp() as Timestamp,
      updatedAt: serverTimestamp() as Timestamp,
    }

    await setDoc(userTripRef, newUserTrip)
    return newUserTrip
  } catch (error) {
    console.error("Error getting user trip status:", error)
    throw error
  }
}

/**
 * Get all users who are attending a trip (status = "going")
 * This is the source of truth for trip attendees
 */
export const getTripAttendees = async (tripId: string): Promise<string[]> => {
  try {
    const q = query(
      collection(db, "userTrips"),
      where("tripId", "==", tripId),
      where("status", "==", "going")
    )
    const querySnapshot = await getDocs(q)

    // Return array of user IDs who are attending
    return querySnapshot.docs.map((doc) => doc.data().userId)
  } catch (error) {
    console.error("Error getting trip attendees:", error)
    return []
  }
}

/**
 * Synchronize the trip's attendees array with the actual attendees from userTrips
 * This ensures the trip.attendees field is always accurate
 */
export const syncTripAttendees = async (tripId: string): Promise<boolean> => {
  try {
    // Get all users with "going" status
    const attendees = await getTripAttendees(tripId)

    // Update the trip document with the correct attendees
    const tripRef = doc(db, "trips", tripId)
    await updateDoc(tripRef, { attendees })

    return true
  } catch (error) {
    console.error("Error syncing trip attendees:", error)
    return false
  }
}

export const updateUserTripStatus = async (
  userId: string,
  tripId: string,
  status: "going" | "not-going" | "undecided"
) => {
  try {
    // Get the trip reference
    const tripRef = doc(db, "trips", tripId)
    const tripDoc = await getDoc(tripRef)

    if (!tripDoc.exists()) {
      throw new Error("Trip not found")
    }

    // Check if user trip status exists
    const existingStatus = await checkUserTripStatus(userId, tripId)
    let userTripId = ""

    if (existingStatus) {
      userTripId = existingStatus.id
      await updateDoc(doc(db, "userTrips", userTripId), {
        status,
        updatedAt: serverTimestamp(),
      })
    } else {
      // If no status exists, create a new one
      const userTripRef = doc(collection(db, "userTrips"))
      userTripId = userTripRef.id

      await setDoc(userTripRef, {
        id: userTripId,
        userId,
        tripId,
        status,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
      })
    }

    // REMOVED: syncTripAttendees call to prevent infinite loops
    // await syncTripAttendees(tripId)

    return { success: true, id: userTripId }
  } catch (error) {
    console.error("Error updating user trip status:", error)
    throw error
  }
}
