import { db } from "../firebase"
import {
  collection,
  doc,
  getDoc,
  getDocs,
  updateDoc,
  query,
  where,
  serverTimestamp,
  type Timestamp,
  setDoc,
  deleteDoc,
  orderBy,
} from "firebase/firestore"

// Types
export interface TripItinerary {
  id: string
  tripId: string
  title: string
  description?: string
  location?: string
  time: string
  day: number
  createdBy: string
  createdAt: Timestamp | null
  updatedAt: Timestamp | null
}

// Itinerary operations
export const createItineraryItem = async (
  itemData: Omit<TripItinerary, "id" | "createdAt" | "updatedAt">
) => {
  try {
    const itemRef = doc(collection(db, "tripItineraries"))
    const itemId = itemRef.id

    const newItem = {
      ...itemData,
      id: itemId,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp(),
    }

    await setDoc(itemRef, newItem)
    return itemId
  } catch (error) {
    console.error("Error creating itinerary item:", error)
    throw error
  }
}

export const getItineraryItems = async (tripId: string) => {
  try {
    const q = query(
      collection(db, "tripItineraries"),
      where("tripId", "==", tripId),
      orderBy("day", "asc"),
      orderBy("time", "asc")
    )
    const querySnapshot = await getDocs(q)

    return querySnapshot.docs.map((doc) => {
      const data = doc.data()
      return {
        ...data,
        id: doc.id,
      } as TripItinerary
    })
  } catch (error) {
    console.error("Error getting itinerary items:", error)
    throw error
  }
}

export const getItineraryItemsByDay = async (tripId: string, day: number) => {
  try {
    const q = query(
      collection(db, "tripItineraries"),
      where("tripId", "==", tripId),
      where("day", "==", day),
      orderBy("time", "asc")
    )
    const querySnapshot = await getDocs(q)

    return querySnapshot.docs.map((doc) => {
      const data = doc.data()
      return {
        ...data,
        id: doc.id,
      } as TripItinerary
    })
  } catch (error) {
    console.error("Error getting itinerary items for day:", error)
    throw error
  }
}

export const updateItineraryItem = async (itemId: string, itemData: Partial<TripItinerary>) => {
  try {
    await updateDoc(doc(db, "tripItineraries", itemId), {
      ...itemData,
      updatedAt: serverTimestamp(),
    })
    return { success: true }
  } catch (error) {
    console.error("Error updating itinerary item:", error)
    throw error
  }
}

export const deleteItineraryItem = async (itemId: string) => {
  try {
    await deleteDoc(doc(db, "tripItineraries", itemId))
    return { success: true }
  } catch (error) {
    console.error("Error deleting itinerary item:", error)
    throw error
  }
}
