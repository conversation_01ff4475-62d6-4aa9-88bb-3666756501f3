import { NextRequest, NextResponse } from "next/server"
import { verifyAuthToken } from "./firebase-admin"

/**
 * Authentication result interface
 */
export interface AuthResult {
  isAuthenticated: boolean
  response: NextResponse | null
  userId: string | null
  userEmail?: string | null
  authMethod?: string
}

/**
 * Enhanced helper function to verify authentication for API routes
 * Supports multiple authentication providers through Firebase Auth
 */
export async function verifyAuth(req: NextRequest): Promise<AuthResult> {
  try {
    // Get the token from the X-Auth-Token header (set by middleware)
    const token = req.headers.get("X-Auth-Token")

    if (!token) {
      return {
        isAuthenticated: false,
        response: NextResponse.json({ error: "Unauthorized: Missing token" }, { status: 401 }),
        userId: null,
      }
    }

    // Verify the token with Firebase Admin
    const authResult = await verifyAuthToken(token)

    if (!authResult.isValid) {
      return {
        isAuthenticated: false,
        response: NextResponse.json({ error: "Unauthorized: Invalid token" }, { status: 401 }),
        userId: null,
      }
    }

    // Extract authentication method from Firebase user record
    const authMethod = authResult.firebase_sign_in_provider || "unknown"

    // Authentication successful
    return {
      isAuthenticated: true,
      response: null,
      userId: authResult.uid || null,
      userEmail: authResult.email || null,
      authMethod,
    }
  } catch (error) {
    console.error("Error verifying authentication:", error)
    return {
      isAuthenticated: false,
      response: NextResponse.json({ error: "Authentication error" }, { status: 500 }),
      userId: null,
    }
  }
}

/**
 * Helper function to check if user authenticated with specific provider
 */
export function isAuthenticatedWithProvider(authResult: AuthResult, providerId: string): boolean {
  if (!authResult.isAuthenticated || !authResult.authMethod) {
    return false
  }

  // Map Firebase provider IDs to our provider IDs
  const providerMap: Record<string, string> = {
    "google.com": "google",
    "github.com": "github",
    "apple.com": "apple",
    "facebook.com": "facebook",
  }

  const mappedProvider = providerMap[authResult.authMethod] || authResult.authMethod
  return mappedProvider === providerId
}

/**
 * Helper function to require specific authentication provider
 */
export async function requireProviderAuth(
  req: NextRequest,
  requiredProvider: string
): Promise<AuthResult> {
  const authResult = await verifyAuth(req)

  if (!authResult.isAuthenticated) {
    return authResult
  }

  if (!isAuthenticatedWithProvider(authResult, requiredProvider)) {
    return {
      isAuthenticated: false,
      response: NextResponse.json(
        { error: `This endpoint requires authentication with ${requiredProvider}` },
        { status: 403 }
      ),
      userId: null,
    }
  }

  return authResult
}
