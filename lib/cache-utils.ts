// Simple in-memory cache for API responses
type CacheEntry<T> = {
  data: T
  timestamp: number
  expiresAt: number
}

type CacheOptions = {
  expirationMs?: number // How long to keep the cache entry (default: 5 minutes)
  keyPrefix?: string // Optional prefix for the cache key
}

// In-memory cache store
const memoryCache: Record<string, CacheEntry<any>> = {}

/**
 * Creates a cache key from the provided parameters
 */
export function createCacheKey(type: string, params: any, options?: CacheOptions): string {
  const prefix = options?.keyPrefix || ""
  // Create a deterministic string from the params object
  const paramsString = JSON.stringify(params, Object.keys(params).sort())
  return `${prefix}${type}_${paramsString}`
}

/**
 * Gets a value from the cache
 */
export function getCachedValue<T>(key: string): T | null {
  const entry = memoryCache[key]

  if (!entry) {
    return null
  }

  // Check if the entry has expired
  if (Date.now() > entry.expiresAt) {
    // Remove expired entry
    delete memoryCache[key]
    return null
  }

  return entry.data
}

/**
 * Sets a value in the cache
 */
export function setCachedValue<T>(key: string, value: T, options?: CacheOptions): void {
  const expirationMs = options?.expirationMs || 5 * 60 * 1000 // Default: 5 minutes

  memoryCache[key] = {
    data: value,
    timestamp: Date.now(),
    expiresAt: Date.now() + expirationMs,
  }
}

/**
 * Wrapper function that implements caching for async functions
 * Will return cached value if available, otherwise will call the function and cache the result
 */
export async function withCache<T>(
  cacheKey: string,
  fn: () => Promise<T>,
  options?: CacheOptions
): Promise<T> {
  // Try to get from cache first
  const cachedValue = getCachedValue<T>(cacheKey)
  if (cachedValue !== null) {
    return cachedValue
  }

  // If not in cache, call the function
  const result = await fn()

  // Cache the result
  setCachedValue(cacheKey, result, options)

  return result
}

/**
 * Clears all cache entries or entries with a specific prefix
 */
export function clearCache(prefix?: string): void {
  if (prefix) {
    // Clear only entries with the given prefix
    Object.keys(memoryCache).forEach((key) => {
      if (key.startsWith(prefix)) {
        delete memoryCache[key]
      }
    })
  } else {
    // Clear all entries
    Object.keys(memoryCache).forEach((key) => {
      delete memoryCache[key]
    })
  }
}
