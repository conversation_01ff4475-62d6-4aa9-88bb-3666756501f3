import { getFirestore, FieldValue } from "firebase-admin/firestore"
import { AIMessageCreateData } from "@/lib/domains/message/message.types"

/**
 * Server-side Message Service using Firebase Admin SDK
 */
export class MessageServerService {
  private static readonly COLLECTION = "trips"
  private static readonly SUBCOLLECTION = "messages"

  /**
   * Create an AI response message using Firebase Admin SDK
   */
  static async createAIMessage(data: AIMessageCreateData): Promise<string> {
    try {
      const adminDb = getFirestore()
      const messageRef = adminDb
        .collection(this.COLLECTION)
        .doc(data.tripId)
        .collection(this.SUBCOLLECTION)
        .doc()
      const messageId = messageRef.id

      // Sanitize AI response content
      const sanitizedContent = this.sanitizeContent(data.aiResponse)

      // Create AI response message
      const messageData = {
        id: messageId,
        tripId: data.tripId,
        senderId: "ai-assistant", // Special sender ID for AI
        senderName: "Togeda AI",
        content: sanitizedContent,
        mentionedUserIds: [], // AI responses don't mention users
        isAIResponse: true,
        aiPrompt: data.userPrompt,
        messageType: "ai_response",
        createdAt: FieldValue.serverTimestamp(),
        // No senderPhotoURL for AI
      }

      await messageRef.set(messageData)

      return messageId
    } catch (error) {
      console.error("Error creating AI message (server):", error)
      throw error
    }
  }

  /**
   * Sanitize message content for security
   */
  private static sanitizeContent(content: string): string {
    // Basic sanitization - remove HTML tags and limit length
    return content
      .replace(/<[^>]*>/g, "") // Remove HTML tags
      .trim()
      .substring(0, 750) // Enforce character limit
  }
}
