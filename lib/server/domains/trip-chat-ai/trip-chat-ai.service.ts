import { getFirestore, FieldValue, Timestamp } from "firebase-admin/firestore"
import { FlatSubscriptionAdminService } from "@/lib/domains/user-subscription/flat-subscription-admin.service"
import { AIUsageCategory, TRIP_CHAT_LIMITS } from "@/lib/domains/user-ai-usage/user-ai-usage.types"

export interface TripContextSnapshot {
  destination: string
  startDate: string
  endDate: string
  budget: string
  attendeeCount: number
  tripStatus: string
  description: string
}

export interface AIRequestValidation {
  canMake: boolean
  reason?: "not_pro" | "daily_limit" | "rate_limit" | "invalid_prompt"
  dailyUsage: number
  dailyLimit: number
  remainingRequests: number
}

export interface TripChatUsageTracking {
  count: number
  lastReset: any
  lastRequest: any
  isProcessing: boolean
}

/**
 * Server-side Trip Chat AI Service using Firebase Admin SDK
 */
export class TripChatAIServerService {
  private static readonly USER_AI_USAGE_COLLECTION = "userAiUsage"

  /**
   * Validate AI request on server-side
   */
  static async validateAIRequest(userId: string, prompt: string): Promise<AIRequestValidation> {
    try {
      const now = new Date()

      // Check if prompt is valid
      const trimmedPrompt = prompt.trim()
      if (!trimmedPrompt) {
        return {
          canMake: false,
          reason: "invalid_prompt",
          dailyUsage: 0,
          dailyLimit: 0,
          remainingRequests: 0,
        }
      }

      // Check if user has Pro subscription
      const currentSubscription = await FlatSubscriptionAdminService.getCurrentSubscription(userId)
      const isProUser =
        currentSubscription &&
        currentSubscription.status === "applied" &&
        ["stripe", "perk", "giveaway"].includes(currentSubscription.source)

      if (!isProUser) {
        return {
          canMake: false,
          reason: "not_pro",
          dailyUsage: 0,
          dailyLimit: 0,
          remainingRequests: 0,
        }
      }

      // Get current usage data
      const adminDb = getFirestore()
      const userDocRef = adminDb.collection(this.USER_AI_USAGE_COLLECTION).doc(userId)
      const userDoc = await userDocRef.get()
      const userData = userDoc.data()
      const tripChatUsage = userData?.categoryUsage?.[AIUsageCategory.TRIP_CHAT] as
        | TripChatUsageTracking
        | undefined

      // Check if user is already processing a request
      if (tripChatUsage?.isProcessing) {
        return {
          canMake: false,
          reason: "rate_limit",
          dailyUsage: tripChatUsage.count || 0,
          dailyLimit: TRIP_CHAT_LIMITS.DAILY_LIMIT,
          remainingRequests: Math.max(0, TRIP_CHAT_LIMITS.DAILY_LIMIT - (tripChatUsage.count || 0)),
        }
      }

      // Check daily limit
      const dailyUsage = this.getDailyUsage(tripChatUsage, now)
      if (dailyUsage >= TRIP_CHAT_LIMITS.DAILY_LIMIT) {
        return {
          canMake: false,
          reason: "daily_limit",
          dailyUsage,
          dailyLimit: TRIP_CHAT_LIMITS.DAILY_LIMIT,
          remainingRequests: 0,
        }
      }

      // Check rate limit
      const rateLimitViolated = this.checkRateLimit(tripChatUsage, now)
      if (rateLimitViolated) {
        return {
          canMake: false,
          reason: "rate_limit",
          dailyUsage,
          dailyLimit: TRIP_CHAT_LIMITS.DAILY_LIMIT,
          remainingRequests: Math.max(0, TRIP_CHAT_LIMITS.DAILY_LIMIT - dailyUsage),
        }
      }

      return {
        canMake: true,
        dailyUsage,
        dailyLimit: TRIP_CHAT_LIMITS.DAILY_LIMIT,
        remainingRequests: Math.max(0, TRIP_CHAT_LIMITS.DAILY_LIMIT - dailyUsage),
      }
    } catch (error) {
      console.error("Error validating AI request (server):", error)
      throw error
    }
  }

  /**
   * Get trip context for AI requests
   */
  static async getTripContext(tripId: string): Promise<TripContextSnapshot> {
    try {
      const adminDb = getFirestore()
      const tripDoc = await adminDb.collection("trips").doc(tripId).get()

      if (!tripDoc.exists) {
        throw new Error("Trip not found")
      }

      const trip = tripDoc.data()
      if (!trip) {
        throw new Error("Trip data is invalid")
      }

      return {
        destination: trip.destination || "",
        startDate: trip.startDate?.toDate?.()?.toISOString()?.split("T")[0] || "",
        endDate: trip.endDate?.toDate?.()?.toISOString()?.split("T")[0] || "",
        budget: trip.budget || "",
        attendeeCount: trip.attendees?.length || 0,
        tripStatus: trip.status || "planning",
        description: trip.description || "",
      }
    } catch (error) {
      console.error("Error getting trip context (server):", error)
      throw error
    }
  }

  /**
   * Set processing state
   */
  static async setProcessingState(userId: string, isProcessing: boolean): Promise<void> {
    try {
      const adminDb = getFirestore()
      const userDocRef = adminDb.collection(this.USER_AI_USAGE_COLLECTION).doc(userId)
      const userDoc = await userDocRef.get()

      if (!userDoc.exists) {
        // Create initial document
        await userDocRef.set({
          userId,
          categoryUsage: {
            [AIUsageCategory.TRIP_CHAT]: {
              count: 0,
              lastReset: FieldValue.serverTimestamp(),
              lastRequest: null,
              isProcessing,
            },
          },
          createdAt: FieldValue.serverTimestamp(),
          updatedAt: FieldValue.serverTimestamp(),
        })
      } else {
        await userDocRef.update({
          [`categoryUsage.${AIUsageCategory.TRIP_CHAT}.isProcessing`]: isProcessing,
          updatedAt: FieldValue.serverTimestamp(),
        })
      }
    } catch (error) {
      console.error("Error setting processing state (server):", error)
      throw error
    }
  }

  /**
   * Track AI usage after successful request
   */
  static async trackUsage(userId: string): Promise<void> {
    try {
      const adminDb = getFirestore()
      const userDocRef = adminDb.collection("userAiUsage").doc(userId)
      const userDoc = await userDocRef.get()

      const now = Timestamp.now()
      const today = new Date()
      today.setHours(0, 0, 0, 0)

      if (!userDoc.exists) {
        // Create initial document with first usage
        await userDocRef.set({
          userId,
          categoryUsage: {
            [AIUsageCategory.TRIP_CHAT]: {
              count: 1,
              lastReset: Timestamp.fromDate(today),
              lastRequest: now,
              isProcessing: false,
            },
          },
          createdAt: now,
          updatedAt: now,
        })
      } else {
        const userData = userDoc.data()
        const tripChatUsage = userData?.categoryUsage?.[AIUsageCategory.TRIP_CHAT]

        // Check if we need to reset daily count
        const lastReset = tripChatUsage?.lastReset?.toDate()
        const shouldReset = !lastReset || lastReset < today

        if (shouldReset) {
          // Reset daily count
          await userDocRef.update({
            [`categoryUsage.${AIUsageCategory.TRIP_CHAT}.count`]: 1,
            [`categoryUsage.${AIUsageCategory.TRIP_CHAT}.lastReset`]: Timestamp.fromDate(today),
            [`categoryUsage.${AIUsageCategory.TRIP_CHAT}.lastRequest`]: now,
            [`categoryUsage.${AIUsageCategory.TRIP_CHAT}.isProcessing`]: false,
            updatedAt: now,
          })
        } else {
          // Increment count
          await userDocRef.update({
            [`categoryUsage.${AIUsageCategory.TRIP_CHAT}.count`]: FieldValue.increment(1),
            [`categoryUsage.${AIUsageCategory.TRIP_CHAT}.lastRequest`]: now,
            [`categoryUsage.${AIUsageCategory.TRIP_CHAT}.isProcessing`]: false,
            updatedAt: now,
          })
        }
      }
    } catch (error) {
      console.error("Error tracking usage (server):", error)
      throw error
    }
  }

  /**
   * Get daily usage count
   */
  private static getDailyUsage(
    tripChatUsage: TripChatUsageTracking | undefined,
    today: Date
  ): number {
    if (!tripChatUsage) return 0

    const lastReset = tripChatUsage.lastReset?.toDate()
    if (!lastReset || lastReset < today) {
      return 0 // Usage should be reset
    }

    return tripChatUsage.count || 0
  }

  /**
   * Check rate limit (3 requests per minute)
   */
  private static checkRateLimit(
    tripChatUsage: TripChatUsageTracking | undefined,
    now: Date
  ): boolean {
    if (!tripChatUsage?.lastRequest) return false

    const lastRequest = tripChatUsage.lastRequest.toDate()
    const timeDiff = now.getTime() - lastRequest.getTime()
    const minutesSinceLastRequest = timeDiff / (1000 * 60)

    // Rate limit: 3 requests per minute
    const rateLimitMinutes = TRIP_CHAT_LIMITS.RATE_LIMIT_WINDOW_MS / (1000 * 60)
    return minutesSinceLastRequest < rateLimitMinutes
  }
}
