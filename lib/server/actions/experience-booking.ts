"use server"

import { stripe } from "@/lib/server/stripe"
import { getAdminInstance } from "@/lib/firebase-admin"
import { redirect } from "next/navigation"

/**
 * Create a Stripe checkout session for experience booking
 */
export async function createExperienceCheckoutSession(
  experienceId: string,
  bookingId: string,
  experienceTitle: string,
  totalAmount: number,
  currency: string = "USD",
  userToken: string
) {
  try {
    // Verify the user is authenticated
    const { adminAuth } = await getAdminInstance()
    if (!adminAuth) {
      throw new Error("Server configuration error")
    }

    let decodedToken
    try {
      decodedToken = await adminAuth.verifyIdToken(userToken)
    } catch (error) {
      throw new Error("Invalid authentication token")
    }

    const userId = decodedToken.uid
    const userEmail = decodedToken.email

    if (!userId || !userEmail) {
      throw new Error("User information missing")
    }

    // Validate required fields
    if (!experienceId || !bookingId || !experienceTitle || !totalAmount) {
      throw new Error("Missing required fields")
    }

    // Get admin Firestore instance
    const { adminDb } = await getAdminInstance()
    if (!adminDb) {
      throw new Error("Server configuration error")
    }

    // Verify the booking exists and belongs to the user using admin access
    const bookingRef = adminDb
      .collection("localExperiences")
      .doc(experienceId)
      .collection("bookings")
      .doc(bookingId)

    const bookingDoc = await bookingRef.get()
    if (!bookingDoc.exists) {
      throw new Error("Booking not found")
    }

    const booking = bookingDoc.data()
    if (!booking || booking.userId !== userId) {
      throw new Error("Unauthorized access to booking")
    }

    if (booking.status !== "pending") {
      throw new Error("Booking is not in pending status")
    }

    // Create a Stripe checkout session with dynamic pricing
    const checkoutSession = await stripe.checkout.sessions.create({
      mode: "payment",
      payment_method_types: ["card"],
      line_items: [
        {
          price_data: {
            currency: currency.toLowerCase(),
            product_data: {
              name: experienceTitle,
              description: `Experience booking for ${booking.guests} guest${booking.guests > 1 ? "s" : ""}`,
              metadata: {
                experienceId,
                bookingId,
                userId,
                type: "experience_booking",
              },
            },
            unit_amount: Math.round(totalAmount * 100), // Convert to cents
          },
          quantity: 1,
        },
      ],
      customer_email: userEmail,
      client_reference_id: userId,
      success_url: `${process.env.NEXT_PUBLIC_APP_URL}/experiences/booking/success?session_id={CHECKOUT_SESSION_ID}&booking_id=${bookingId}&experience_id=${experienceId}`,
      cancel_url: `${process.env.NEXT_PUBLIC_APP_URL}/experiences?canceled=true`,
      metadata: {
        userId,
        experienceId,
        bookingId,
        type: "experience_booking",
        totalAmount: totalAmount.toString(),
        currency,
      },
      custom_text: {
        submit: {
          message: "Togeda.ai is an entity of Valencia Dr.",
        },
      },
    })

    // Update the booking with the Stripe session ID using admin access
    await bookingRef.update({
      stripeSessionId: checkoutSession.id,
    })

    // Also update the user's booking copy
    const userBookingRef = adminDb
      .collection("users")
      .doc(userId)
      .collection("localExperienceBookings")
      .doc(bookingId)

    await userBookingRef.update({
      stripeSessionId: checkoutSession.id,
    })

    return {
      success: true,
      sessionId: checkoutSession.id,
      url: checkoutSession.url,
    }
  } catch (error) {
    console.error("Error creating experience booking checkout session:", error)
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to create checkout session",
    }
  }
}

/**
 * Confirm booking after successful payment (called from success page)
 */
export async function confirmExperienceBooking(
  sessionId: string,
  bookingId: string,
  experienceId: string,
  userToken: string
) {
  try {
    // Verify the user is authenticated
    const { adminAuth } = await getAdminInstance()
    if (!adminAuth) {
      throw new Error("Server configuration error")
    }

    let decodedToken
    try {
      decodedToken = await adminAuth.verifyIdToken(userToken)
    } catch (error) {
      throw new Error("Invalid authentication token")
    }

    const userId = decodedToken.uid

    if (!userId) {
      throw new Error("User information missing")
    }

    // Validate required fields
    if (!sessionId || !bookingId || !experienceId) {
      throw new Error("Missing required fields")
    }

    // Get admin Firestore instance
    const { adminDb, adminFieldValue } = await getAdminInstance()
    if (!adminDb) {
      throw new Error("Server configuration error")
    }

    // Verify the booking exists and belongs to the user using admin access
    const bookingRef = adminDb
      .collection("localExperiences")
      .doc(experienceId)
      .collection("bookings")
      .doc(bookingId)

    const bookingDoc = await bookingRef.get()
    if (!bookingDoc.exists) {
      throw new Error("Booking not found")
    }

    const booking = bookingDoc.data()
    if (!booking || booking.userId !== userId) {
      throw new Error("Unauthorized access to booking")
    }

    // Retrieve the Stripe session to verify payment
    const session = await stripe.checkout.sessions.retrieve(sessionId)

    if (session.payment_status !== "paid") {
      throw new Error("Payment not completed")
    }

    if (session.metadata?.bookingId !== bookingId) {
      throw new Error("Session does not match booking")
    }

    // Confirm the booking using admin access
    const updateData = {
      status: "confirmed",
      paymentStatus: "paid",
      confirmedAt: adminFieldValue.serverTimestamp(),
      stripeSessionId: sessionId,
      stripePaymentIntentId: session.payment_intent as string,
      stripeCustomerId: session.customer as string,
    }

    // Update both booking collections atomically
    const batch = adminDb.batch()

    // Update experience booking
    batch.update(bookingRef, updateData)

    // Update user booking copy
    const userBookingRef = adminDb
      .collection("users")
      .doc(userId)
      .collection("localExperienceBookings")
      .doc(bookingId)
    batch.update(userBookingRef, updateData)

    await batch.commit()

    return {
      success: true,
      message: "Booking confirmed successfully",
    }
  } catch (error) {
    console.error("Error confirming experience booking:", error)
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to confirm booking",
    }
  }
}

/**
 * Redirect to Stripe checkout (server action)
 */
export async function redirectToExperienceCheckout(
  experienceId: string,
  bookingId: string,
  experienceTitle: string,
  totalAmount: number,
  currency: string = "USD",
  userToken: string
) {
  const result = await createExperienceCheckoutSession(
    experienceId,
    bookingId,
    experienceTitle,
    totalAmount,
    currency,
    userToken
  )

  if (result.success && result.url) {
    redirect(result.url)
  } else {
    throw new Error(result.error || "Failed to create checkout session")
  }
}
