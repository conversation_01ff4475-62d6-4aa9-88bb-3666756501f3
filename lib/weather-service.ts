/**
 * Weather service for fetching weather data from WeatherAPI.com
 */

import { auth } from "./firebase"

// Helper function to get the authentication token
async function getAuthToken() {
  try {
    const currentUser = auth.currentUser
    if (!currentUser) {
      throw new Error("User not authenticated")
    }

    const token = await currentUser.getIdToken()
    return token
  } catch (error) {
    console.error("Error getting auth token:", error)
    throw error
  }
}

// Define types for the weather API response
export interface WeatherForecast {
  location: {
    name: string
    region: string
    country: string
    lat: number
    lon: number
    localtime: string
  }
  current: {
    temp_c: number
    temp_f: number
    condition: {
      text: string
      icon: string
      code: number
    }
  }
  forecast: {
    forecastday: ForecastDay[]
  }
}

export interface ForecastDay {
  date: string
  date_epoch: number
  day: {
    maxtemp_c: number
    maxtemp_f: number
    mintemp_c: number
    mintemp_f: number
    avgtemp_c: number
    avgtemp_f: number
    condition: {
      text: string
      icon: string
      code: number
    }
  }
}

export interface WeatherDay {
  date: string
  condition: string
  temperature: string
  icon: string
  conditionCode: number
}

/**
 * Fetches weather forecast for a location
 * @param location The location to get weather for (city name, zip code, etc.)
 * @param days Number of days to forecast (max 14)
 * @returns Weather forecast data or null if there was an error
 */
export async function fetchWeatherForecast(
  location: string,
  days: number = 7
): Promise<WeatherForecast | null> {
  try {
    // Make sure days is within valid range (1-14)
    const forecastDays = Math.min(Math.max(days, 1), 14)

    // Get authentication token
    const token = await getAuthToken()

    // Server-side API call with authentication
    const response = await fetch(
      `/api/weather?location=${encodeURIComponent(location)}&days=${forecastDays}`,
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }
    )

    if (!response.ok) {
      if (response.status === 401) {
        throw new Error("Authentication required to access weather data")
      }
      throw new Error(`Weather API error: ${response.statusText}`)
    }

    const data = await response.json()
    return data
  } catch (error) {
    console.error("Error fetching weather data:", error)
    return null
  }
}

/**
 * Formats weather data for display in the weather widget
 * @param forecast The weather forecast data
 * @returns Formatted weather data for display
 */
export function formatWeatherData(forecast: WeatherForecast): WeatherDay[] {
  if (!forecast || !forecast.forecast || !forecast.forecast.forecastday) {
    return []
  }

  return forecast.forecast.forecastday.map((day) => ({
    date: new Date(day.date).toLocaleDateString("en-US", { month: "short", day: "numeric" }),
    condition: day.day.condition.text,
    temperature: `${Math.round(day.day.avgtemp_f)}°F`,
    icon: day.day.condition.icon,
    conditionCode: day.day.condition.code,
  }))
}

/**
 * Maps weather condition codes to emoji icons
 * @param conditionCode The weather condition code from the API
 * @returns An emoji representing the weather condition
 */
export function getWeatherEmoji(conditionCode: number): string {
  // Map common weather condition codes to emojis
  // Full list of codes: https://www.weatherapi.com/docs/weather_conditions.json
  if (conditionCode === 1000) return "☀️" // Sunny
  if (conditionCode === 1003) return "⛅" // Partly cloudy
  if ([1006, 1009].includes(conditionCode)) return "☁️" // Cloudy
  if ([1030, 1135, 1147].includes(conditionCode)) return "🌫️" // Mist/Fog
  if ([1063, 1150, 1153, 1180, 1183, 1186, 1189, 1240].includes(conditionCode)) return "🌧️" // Light rain
  if ([1066, 1114, 1117, 1210, 1213, 1216, 1219, 1222, 1225, 1255, 1258].includes(conditionCode))
    return "❄️" // Snow
  if ([1087, 1273, 1276, 1279, 1282].includes(conditionCode)) return "⛈️" // Thunderstorm
  if ([1192, 1195, 1201, 1243, 1246].includes(conditionCode)) return "🌧️" // Heavy rain

  // Default for any other condition
  return "🌤️"
}
