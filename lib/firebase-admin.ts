// Use a dynamic import for firebase-admin to ensure it only runs on the server
// This prevents the 'node:' scheme error in the browser
import { AppOptions } from "firebase-admin/app"

// Define types for our admin auth and firestore
type AdminAuth = any
type AdminFirestore = any

// Variables to hold our admin instances
let adminAuth: AdminAuth | null = null
let adminDb: AdminFirestore | null = null
let adminFieldValue: any = null

// Function to initialize Firebase Admin
async function initializeFirebaseAdmin() {
  // Only import firebase-admin on the server side
  if (typeof window === "undefined") {
    try {
      // Dynamically import firebase-admin modules
      const { initializeApp, cert, getApps } = await import("firebase-admin/app")
      const { getAuth } = await import("firebase-admin/auth")
      const { getFirestore, FieldValue } = await import("firebase-admin/firestore")

      // Initialize Firebase Admin SDK if it hasn't been initialized
      if (!getApps().length) {
        try {
          // Check if we have the service account key
          if (!process.env.FIREBASE_SERVICE_ACCOUNT_KEY) {
            throw new Error("FIREBASE_SERVICE_ACCOUNT_KEY environment variable is not set")
          }

          const serviceAccount = JSON.parse(process.env.FIREBASE_SERVICE_ACCOUNT_KEY)

          const options: AppOptions = {
            credential: cert(serviceAccount),
          }

          if (process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID) {
            options.databaseURL = `https://${process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID}.firebaseio.com`
          }

          initializeApp(options)
        } catch (error) {
          console.error("Firebase admin initialization error:", error)

          // Fallback for development environment
          if (process.env.NODE_ENV === "development") {
            console.warn("Using development fallback for Firebase Admin SDK")
            initializeApp({
              projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
            })
          }
        }
      }

      // Set our admin instances
      adminAuth = getAuth()
      adminDb = getFirestore()
      adminFieldValue = FieldValue

      return { adminAuth, adminDb, adminFieldValue }
    } catch (error) {
      console.error("Error initializing Firebase Admin:", error)
      throw error
    }
  }

  return { adminAuth: null, adminDb: null, adminFieldValue: null }
}

// Helper function to get admin instances
export async function getAdminInstance() {
  return await initializeFirebaseAdmin()
}

// Helper function to verify a Firebase ID token
export async function verifyAuthToken(token: string) {
  try {
    // Initialize Firebase Admin if needed
    const { adminAuth } = await initializeFirebaseAdmin()

    if (!adminAuth) {
      console.error("Firebase Admin Auth is not initialized")
      return { isValid: false }
    }

    const decodedToken = await adminAuth.verifyIdToken(token)
    return {
      uid: decodedToken.uid,
      email: decodedToken.email,
      firebase_sign_in_provider: decodedToken.firebase?.sign_in_provider,
      isValid: true,
    }
  } catch (error) {
    console.error("Error verifying auth token:", error)
    return { isValid: false }
  }
}
