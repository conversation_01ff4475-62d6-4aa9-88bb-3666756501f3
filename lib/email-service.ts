// Client-side email service that calls the server API endpoints

import { Invitation } from "@/lib/domains/invitation/invitation.types"
import { getAuth } from "firebase/auth"

export interface EmailResult {
  success: boolean
  error?: string
  messageId?: string
}

/**
 * Send a single email using template
 */
export const sendSingleEmail = async (options: {
  to: string
  templateId: number
  params: Record<string, any>
  subject?: string
}): Promise<EmailResult> => {
  try {
    // Get a fresh auth token from Firebase Auth
    let token = localStorage.getItem("authToken")
    if (!token) {
      const auth = getAuth()
      const user = auth.currentUser
      if (user) {
        token = await user.getIdToken(true)
        localStorage.setItem("authToken", token)
      } else {
        throw new Error("User not authenticated")
      }
    }

    const response = await fetch("/api/email/send", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify({
        to: options.to,
        subject: options.subject || "Notification", // Subject not needed for templates but API requires it
        templateId: options.templateId,
        params: options.params,
      }),
    })

    if (!response.ok) {
      const errorData = await response.json()
      console.error("Failed to send email:", errorData)
      return {
        success: false,
        error: errorData.error || `HTTP ${response.status}: ${response.statusText}`,
      }
    }

    const result = await response.json()

    return {
      success: true,
      messageId: result.messageId,
    }
  } catch (error) {
    console.error("Error sending email:", error)
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error occurred",
    }
  }
}

/**
 * Generate an invitation link
 */
export const generateInvitationLink = (invitationId: string, invitationSendId?: string): string => {
  const baseUrl = process.env.NEXT_PUBLIC_APP_URL || "http://localhost:3000"

  if (invitationSendId) {
    // For specific email invitations, use the public route (will redirect to authenticated route after login)
    return `${baseUrl}/invitation/${invitationId}/${invitationSendId}`
  } else {
    // For general invitation links, use the public route (will redirect to authenticated route after login)
    return `${baseUrl}/invitation/${invitationId}`
  }
}

/**
 * Send multiple invitation emails via the batch API for better performance
 */
export const sendBatchInvitationEmails = async (
  invitationId: string,
  emails: string[],
  invitationSendIds: string[],
  templateId?: number
): Promise<{
  success: boolean
  results: Array<{
    email: string
    success: boolean
    error?: string
    sendId: string
  }>
  summary: {
    total: number
    successful: number
    failed: number
  }
  error?: string
}> => {
  try {
    // Get a fresh auth token from Firebase Auth
    let token = localStorage.getItem("authToken")

    if (!token) {
      const auth = getAuth()
      const user = auth.currentUser
      if (user) {
        token = await user.getIdToken(true)
        localStorage.setItem("authToken", token)
      } else {
        throw new Error("User not authenticated")
      }
    }

    console.log("Sending batch invitation emails:", {
      invitationId,
      emailCount: emails.length,
      token: token ? "Token present" : "No token",
    })

    const response = await fetch("/api/email/invitation/batch", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify({
        invitationId,
        emails,
        invitationSendIds,
        templateId,
      }),
    })

    const data = await response.json()

    if (response.ok) {
      return {
        success: true,
        results: data.results || [],
        summary: data.summary || { total: 0, successful: 0, failed: 0 },
      }
    } else {
      console.error("Batch invitation email sending failed:", {
        status: response.status,
        statusText: response.statusText,
        data: data,
      })

      return {
        success: false,
        results: [],
        summary: { total: emails.length, successful: 0, failed: emails.length },
        error: data?.error || "Failed to send batch invitation emails",
      }
    }
  } catch (error) {
    console.error("Error sending batch invitation emails:", error)
    return {
      success: false,
      results: [],
      summary: { total: emails.length, successful: 0, failed: emails.length },
      error: "An unexpected error occurred while sending batch invitation emails",
    }
  }
}

/**
 * Send an invitation email via the server API
 */
export const sendInvitationEmail = async (
  invitation: Invitation,
  _invitationLink: string, // Not used here as the server generates the link
  templateId?: number, // Optional template ID to use
  invitationSendId?: string // Optional invitation send ID for specific email invitations
): Promise<EmailResult> => {
  try {
    // Get a fresh auth token from Firebase Auth
    let token = localStorage.getItem("authToken")

    // Try to get a fresh token if we have access to Firebase Auth
    try {
      const { auth } = await import("@/lib/firebase")
      if (auth.currentUser) {
        token = await auth.currentUser.getIdToken(true) // Force refresh
        // Update localStorage with fresh token
        localStorage.setItem("authToken", token)
      }
    } catch (authError) {
      console.warn("Could not refresh auth token, using stored token:", authError)
    }

    if (!token) {
      return {
        success: false,
        error: "Authentication required to send emails",
      }
    }

    // Call our API endpoint for sending invitation emails
    console.log("Sending invitation email with token:", token ? "Token present" : "No token")

    const response = await fetch("/api/email/invitation", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify({
        invitationId: invitation.id,
        templateId: templateId,
        inviteeEmail: invitation.inviteeEmail,
        invitationSendId: invitationSendId,
      }),
    })

    console.log("API response status:", response.status, response.statusText)

    let data
    try {
      data = await response.json()
    } catch (parseError) {
      console.error("Failed to parse API response:", parseError)
      return {
        success: false,
        error: `API response parsing failed: ${response.status} ${response.statusText}`,
      }
    }

    if (response.ok) {
      return {
        success: true,
        messageId: data.messageId || `email-${Date.now()}`,
      }
    } else {
      console.error("Invitation email sending failed:", {
        status: response.status,
        statusText: response.statusText,
        data: data,
      })

      // Provide more detailed error information
      const errorMessage = data?.error || "Failed to send invitation email"
      const errorDetails = data?.details ? `: ${data.details}` : ""
      const statusInfo = ` (Status: ${response.status})`

      return {
        success: false,
        error: `${errorMessage}${errorDetails}${statusInfo}`,
      }
    }
  } catch (error) {
    console.error("Error sending invitation email:", error)
    return {
      success: false,
      error: "An unexpected error occurred while sending the invitation email",
    }
  }
}
