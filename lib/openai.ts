// Re-export all types and functions from api-client.ts
// This file is maintained for backward compatibility

import {
  generateTripSuggestions,
  generateItinerary,
  processAIChatMessage,
  generateTaskSuggestions,
  generateTaskSuggestionsWithAffiliateLinks,
  generateTaskCompletionSuggestions,
  suggestAffiliateLinks,
  generateDestinationActivities,
  generateActivitySuggestions,
  type TaskSuggestion,
  type TripSuggestion,
  type ActivitySuggestion,
  type ItinerarySuggestion,
  type AffiliateLink,
} from "./api-client"

// Re-export everything for backward compatibility
export {
  generateTripSuggestions,
  generateItinerary,
  processAIChatMessage,
  generateTaskSuggestions,
  generateTaskSuggestionsWithAffiliateLinks,
  generateTaskCompletionSuggestions,
  suggestAffiliateLinks,
  generateDestinationActivities,
  generateActivitySuggestions,
  type TaskSuggestion,
  type TripSuggestion,
  type ActivitySuggestion,
  type ItinerarySuggestion,
  type AffiliateLink,
}
