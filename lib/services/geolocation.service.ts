"use client"

export interface GeolocationResult {
  latitude: number
  longitude: number
  accuracy: number
}

export interface LocationData {
  location: string
  locationPlaceId: string
  coordinates?: {
    latitude: number
    longitude: number
  }
}

export enum GeolocationError {
  PERMISSION_DENIED = "PERMISSION_DENIED",
  POSITION_UNAVAILABLE = "POSITION_UNAVAILABLE",
  TIMEOUT = "TIMEOUT",
  NOT_SUPPORTED = "NOT_SUPPORTED",
  REVERSE_GEOCODING_FAILED = "REVERSE_GEOCODING_FAILED",
}

export class GeolocationService {
  /**
   * Check if geolocation is supported by the browser
   */
  static isSupported(): boolean {
    return "geolocation" in navigator
  }

  /**
   * Get current position using browser geolocation API
   */
  static async getCurrentPosition(): Promise<GeolocationResult> {
    return new Promise((resolve, reject) => {
      if (!this.isSupported()) {
        reject(new Error(GeolocationError.NOT_SUPPORTED))
        return
      }

      const options: PositionOptions = {
        enableHighAccuracy: true,
        timeout: 15000, // 15 seconds
        maximumAge: 300000, // 5 minutes cache
      }

      navigator.geolocation.getCurrentPosition(
        (position) => {
          resolve({
            latitude: position.coords.latitude,
            longitude: position.coords.longitude,
            accuracy: position.coords.accuracy,
          })
        },
        (error) => {
          let errorType: GeolocationError
          switch (error.code) {
            case error.PERMISSION_DENIED:
              errorType = GeolocationError.PERMISSION_DENIED
              break
            case error.POSITION_UNAVAILABLE:
              errorType = GeolocationError.POSITION_UNAVAILABLE
              break
            case error.TIMEOUT:
              errorType = GeolocationError.TIMEOUT
              break
            default:
              errorType = GeolocationError.POSITION_UNAVAILABLE
          }
          reject(new Error(errorType))
        },
        options
      )
    })
  }

  /**
   * Reverse geocode coordinates to get address using Google Places API
   */
  static async reverseGeocode(latitude: number, longitude: number): Promise<LocationData> {
    try {
      const response = await fetch("/api/places/reverse-geocode", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          latitude,
          longitude,
        }),
      })

      if (!response.ok) {
        throw new Error("Reverse geocoding API failed")
      }

      const data = await response.json()

      if (!data.location || !data.locationPlaceId) {
        throw new Error("Invalid reverse geocoding response")
      }

      return {
        location: data.location,
        locationPlaceId: data.locationPlaceId,
        coordinates: {
          latitude,
          longitude,
        },
      }
    } catch (error) {
      console.error("Reverse geocoding failed:", error)
      throw new Error(GeolocationError.REVERSE_GEOCODING_FAILED)
    }
  }

  /**
   * Get user location with automatic reverse geocoding
   * This is the main method to use for getting location data
   */
  static async getUserLocation(): Promise<LocationData> {
    try {
      // Step 1: Get coordinates from browser
      console.log("Getting user position...")
      const position = await this.getCurrentPosition()
      console.log("Position obtained:", {
        lat: position.latitude,
        lng: position.longitude,
        accuracy: position.accuracy,
      })

      // Step 2: Convert coordinates to address
      console.log("Starting reverse geocoding...")
      const locationData = await this.reverseGeocode(position.latitude, position.longitude)
      console.log("Reverse geocoding successful:", locationData.location)

      return locationData
    } catch (error) {
      console.error("getUserLocation failed:", error)
      // Re-throw with proper error type
      throw error
    }
  }

  /**
   * Get user-friendly error message for display
   */
  static getErrorMessage(error: Error): string {
    const isMobile = this.isMobileDevice()

    switch (error.message) {
      case GeolocationError.PERMISSION_DENIED:
        if (isMobile) {
          return "Location access was denied. On mobile devices, please check your browser settings or device location settings and try again."
        }
        return "Location access was denied. Please enable location permissions in your browser settings and try again."
      case GeolocationError.POSITION_UNAVAILABLE:
        if (isMobile) {
          return "Your location could not be determined. This may be due to poor GPS signal, being indoors, or network issues. Try moving to an area with better signal or enter your location manually."
        }
        return "Your location could not be determined. This may be due to poor GPS signal or network issues. Please try again or enter your location manually."
      case GeolocationError.TIMEOUT:
        return "Location request timed out. Please check your internet connection and try again, or enter your location manually."
      case GeolocationError.NOT_SUPPORTED:
        return "Geolocation is not supported by your browser. Please enter your location manually."
      case GeolocationError.REVERSE_GEOCODING_FAILED:
        return "Your coordinates were found but we could not determine your address. Please enter your location manually."
      default:
        return "Could not get your location. Please enter your location manually."
    }
  }

  /**
   * Check if user has previously denied location permission
   * This helps with UX to show appropriate messaging
   */
  static async checkPermissionStatus(): Promise<"granted" | "denied" | "prompt" | "unsupported"> {
    if (!this.isSupported()) {
      return "unsupported"
    }

    try {
      // Check if Permissions API is supported
      if ("permissions" in navigator) {
        const permission = await navigator.permissions.query({ name: "geolocation" })
        return permission.state
      }

      // Fallback: assume prompt if Permissions API not supported
      return "prompt"
    } catch (error) {
      // Fallback for browsers that don't support permissions query
      return "prompt"
    }
  }

  /**
   * Detect if we're on a mobile device
   */
  static isMobileDevice(): boolean {
    return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
      navigator.userAgent
    )
  }

  /**
   * Get location with better mobile handling
   * This method provides better UX for mobile devices
   */
  static async getUserLocationWithMobileSupport(): Promise<LocationData> {
    const isMobile = this.isMobileDevice()
    const permissionStatus = await this.checkPermissionStatus()

    console.log("Device info:", { isMobile, permissionStatus, userAgent: navigator.userAgent })

    // For mobile devices, provide specific guidance
    if (isMobile && permissionStatus === "denied") {
      throw new Error(GeolocationError.PERMISSION_DENIED)
    }

    // For mobile devices, try to trigger permission prompt more reliably
    if (isMobile && permissionStatus === "prompt") {
      console.log("Mobile device with prompt status, attempting to trigger permission")

      // On some mobile browsers, we need to use a more aggressive approach
      // to trigger the permission prompt
      try {
        // First try with a shorter timeout to see if permission prompt appears
        const position = await new Promise<GeolocationResult>((resolve, reject) => {
          const options: PositionOptions = {
            enableHighAccuracy: false, // Less aggressive for initial prompt
            timeout: 10000, // Shorter timeout for mobile
            maximumAge: 0, // Don't use cached position for permission check
          }

          navigator.geolocation.getCurrentPosition(
            (pos) => {
              resolve({
                latitude: pos.coords.latitude,
                longitude: pos.coords.longitude,
                accuracy: pos.coords.accuracy,
              })
            },
            (error) => {
              let errorType: GeolocationError
              switch (error.code) {
                case error.PERMISSION_DENIED:
                  errorType = GeolocationError.PERMISSION_DENIED
                  break
                case error.POSITION_UNAVAILABLE:
                  errorType = GeolocationError.POSITION_UNAVAILABLE
                  break
                case error.TIMEOUT:
                  errorType = GeolocationError.TIMEOUT
                  break
                default:
                  errorType = GeolocationError.POSITION_UNAVAILABLE
              }
              reject(new Error(errorType))
            },
            options
          )
        })

        // If we got position, continue with reverse geocoding
        console.log("Mobile position obtained:", position)
        const locationData = await this.reverseGeocode(position.latitude, position.longitude)
        return locationData
      } catch (error) {
        console.log("Mobile geolocation failed:", error)
        throw error
      }
    }

    // For non-mobile or other cases, use the standard method
    try {
      return await this.getUserLocation()
    } catch (error) {
      // If it's a mobile device and permission was denied, provide mobile-specific guidance
      if (isMobile && (error as Error).message === GeolocationError.PERMISSION_DENIED) {
        console.log("Mobile permission denied, providing mobile-specific error")
      }
      throw error
    }
  }
}
