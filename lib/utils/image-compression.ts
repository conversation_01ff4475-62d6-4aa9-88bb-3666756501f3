import imageCompression from "browser-image-compression"

// Maximum file size: 50MB
export const MAX_FILE_SIZE = 50 * 1024 * 1024

// Allowed file types
export const ALLOWED_TYPES = [
  "image/jpeg",
  "image/jpg",
  "image/png",
  "image/webp",
  "image/heic",
  "image/heif",
]

export interface CompressionOptions {
  maxSizeMB: number
  maxWidthOrHeight: number
  useWebWorker: boolean
  fileType?: string
  initialQuality: number
}

export interface CompressionResult {
  success: boolean
  file?: File
  originalSize: number
  compressedSize?: number
  error?: string
  compressionRatio?: number
}

export interface CompressionProgress {
  stage: "validating" | "compressing" | "complete" | "error"
  progress: number // 0-100
  message: string
  estimatedTimeRemaining?: number
}

/**
 * Default compression options for different use cases
 */
export const COMPRESSION_PRESETS = {
  profilePicture: {
    maxSizeMB: 2,
    maxWidthOrHeight: 800,
    useWebWorker: true,
    fileType: "image/webp",
    initialQuality: 0.85,
  } as CompressionOptions,

  travelDetails: {
    maxSizeMB: 3,
    maxWidthOrHeight: 1200,
    useWebWorker: true,
    fileType: "image/webp",
    initialQuality: 0.8,
  } as CompressionOptions,

  general: {
    maxSizeMB: 2,
    maxWidthOrHeight: 1920,
    useWebWorker: true,
    fileType: "image/webp",
    initialQuality: 0.8,
  } as CompressionOptions,
}

/**
 * Validates if a file meets basic requirements before compression
 */
export function validateImageFile(file: File): { valid: boolean; error?: string } {
  if (!file) {
    return { valid: false, error: "No file provided" }
  }

  if (!ALLOWED_TYPES.includes(file.type)) {
    return {
      valid: false,
      error: `Invalid file type. Supported formats: ${ALLOWED_TYPES.join(", ")}`,
    }
  }

  if (file.size > MAX_FILE_SIZE) {
    const maxSizeMB = Math.round(MAX_FILE_SIZE / (1024 * 1024))
    const fileSizeMB = Math.round(file.size / (1024 * 1024))
    return {
      valid: false,
      error: `File size too large (${fileSizeMB}MB). Maximum size is ${maxSizeMB}MB.`,
    }
  }

  return { valid: true }
}

/**
 * Determines if a file needs compression based on size and type
 */
export function shouldCompressFile(file: File, targetSizeMB: number = 2): boolean {
  const targetSizeBytes = targetSizeMB * 1024 * 1024
  return file.size > targetSizeBytes
}

/**
 * Compresses an image file with progress tracking
 */
export async function compressImage(
  file: File,
  options: CompressionOptions,
  onProgress?: (progress: CompressionProgress) => void
): Promise<CompressionResult> {
  const startTime = Date.now()

  try {
    // Validate file first
    onProgress?.({
      stage: "validating",
      progress: 0,
      message: "Validating image file...",
    })

    const validation = validateImageFile(file)
    if (!validation.valid) {
      onProgress?.({
        stage: "error",
        progress: 0,
        message: validation.error || "Validation failed",
      })
      return {
        success: false,
        originalSize: file.size,
        error: validation.error,
      }
    }

    // Check if compression is needed
    if (!shouldCompressFile(file, options.maxSizeMB)) {
      onProgress?.({
        stage: "complete",
        progress: 100,
        message: "File already optimized, no compression needed",
      })
      return {
        success: true,
        file,
        originalSize: file.size,
        compressedSize: file.size,
        compressionRatio: 1,
      }
    }

    // Start compression
    onProgress?.({
      stage: "compressing",
      progress: 10,
      message: "Starting image compression...",
    })

    // Create compression options with progress tracking
    const compressionOptions = {
      ...options,
      onProgress: (progress: number) => {
        const adjustedProgress = 10 + progress * 0.8 // 10-90% range
        const elapsed = Date.now() - startTime
        const estimatedTotal = elapsed / (progress / 100)
        const remaining = Math.max(0, estimatedTotal - elapsed)

        onProgress?.({
          stage: "compressing",
          progress: adjustedProgress,
          message: `Compressing image... ${Math.round(progress)}%`,
          estimatedTimeRemaining: remaining,
        })
      },
    }

    const compressedFile = await imageCompression(file, compressionOptions)

    onProgress?.({
      stage: "complete",
      progress: 100,
      message: "Compression complete!",
    })

    const compressionRatio = file.size / compressedFile.size

    return {
      success: true,
      file: compressedFile,
      originalSize: file.size,
      compressedSize: compressedFile.size,
      compressionRatio,
    }
  } catch (error) {
    console.error("Image compression error:", error)

    const errorMessage = error instanceof Error ? error.message : "Unknown compression error"

    onProgress?.({
      stage: "error",
      progress: 0,
      message: `Compression failed: ${errorMessage}`,
    })

    return {
      success: false,
      originalSize: file.size,
      error: errorMessage,
    }
  }
}

/**
 * Utility to format file sizes for display
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return "0 Bytes"

  const k = 1024
  const sizes = ["Bytes", "KB", "MB", "GB"]
  const i = Math.floor(Math.log(bytes) / Math.log(k))

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i]
}

/**
 * Utility to calculate compression savings
 */
export function getCompressionSavings(
  originalSize: number,
  compressedSize: number
): {
  savedBytes: number
  savedPercentage: number
  compressionRatio: number
} {
  const savedBytes = originalSize - compressedSize
  const savedPercentage = (savedBytes / originalSize) * 100
  const compressionRatio = originalSize / compressedSize

  return {
    savedBytes,
    savedPercentage,
    compressionRatio,
  }
}
