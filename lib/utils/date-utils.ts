/**
 * Date utility functions
 */

/**
 * Format a date to a readable string
 * @param date Date to format
 * @returns Formatted date string
 */
export function formatDate(date: Date): string {
  return date.toLocaleDateString("en-US", {
    year: "numeric",
    month: "long",
    day: "numeric",
  })
}

/**
 * Format a date range to a readable string
 * @param startDate Start date
 * @param endDate End date
 * @returns Formatted date range string
 */
export function formatDateRange(startDate: Date, endDate: Date): string {
  const start = formatDate(startDate)
  const end = formatDate(endDate)

  if (start === end) {
    return start
  }

  return `${start} - ${end}`
}

/**
 * Check if a date is today
 * @param date Date to check
 * @returns True if the date is today
 */
export function isToday(date: Date): boolean {
  const today = new Date()
  return (
    date.getDate() === today.getDate() &&
    date.getMonth() === today.getMonth() &&
    date.getFullYear() === today.getFullYear()
  )
}

/**
 * Check if a date is in the past
 * @param date Date to check
 * @returns True if the date is in the past
 */
export function isPast(date: Date): boolean {
  const today = new Date()
  today.setHours(0, 0, 0, 0)
  return date < today
}

/**
 * Get the number of days between two dates
 * @param startDate Start date
 * @param endDate End date
 * @returns Number of days between the dates
 */
export function getDaysBetween(startDate: Date, endDate: Date): number {
  const timeDiff = endDate.getTime() - startDate.getTime()
  return Math.ceil(timeDiff / (1000 * 3600 * 24))
}

/**
 * Normalize a date to UTC midnight
 * This ensures that dates are stored consistently regardless of user timezone
 * @param date Date to normalize (can be Date object or date string)
 * @returns Date object set to UTC midnight
 */
export function normalizeToUTCMidnight(date: Date | string): Date {
  const dateObj = typeof date === "string" ? new Date(date) : date

  // Create a new date in UTC using the local date components
  // This preserves the "calendar date" regardless of timezone
  const utcDate = new Date(
    Date.UTC(dateObj.getFullYear(), dateObj.getMonth(), dateObj.getDate(), 0, 0, 0, 0)
  )

  return utcDate
}

/**
 * Get today's date normalized to UTC midnight
 * @returns Today's date at UTC midnight
 */
export function getTodayUTC(): Date {
  const today = new Date()
  return normalizeToUTCMidnight(today)
}

/**
 * Compare two dates by their calendar date only (ignoring time and timezone)
 * @param date1 First date
 * @param date2 Second date
 * @returns -1 if date1 < date2, 0 if equal, 1 if date1 > date2
 */
export function compareDateOnly(date1: Date, date2: Date): number {
  const utc1 = normalizeToUTCMidnight(date1)
  const utc2 = normalizeToUTCMidnight(date2)

  if (utc1.getTime() < utc2.getTime()) return -1
  if (utc1.getTime() > utc2.getTime()) return 1
  return 0
}
