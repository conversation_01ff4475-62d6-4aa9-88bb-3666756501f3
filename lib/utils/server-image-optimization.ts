import sharp from "sharp"

export interface ServerOptimizationOptions {
  width?: number
  height?: number
  quality: number
  format: "webp" | "jpeg" | "png"
  fit?: "cover" | "contain" | "fill" | "inside" | "outside"
  background?: string
}

export interface OptimizationResult {
  success: boolean
  buffer?: Buffer
  originalSize: number
  optimizedSize?: number
  error?: string
  metadata?: {
    width: number
    height: number
    format: string
    size: number
  }
}

/**
 * Predefined optimization settings for different use cases
 */
export const OPTIMIZATION_PRESETS = {
  profilePicture: {
    width: 400,
    height: 400,
    quality: 85,
    format: "webp" as const,
    fit: "cover" as const,
    background: "#ffffff",
  },

  travelDetails: {
    width: 1200,
    quality: 80,
    format: "webp" as const,
    fit: "inside" as const,
  },

  thumbnail: {
    width: 300,
    height: 300,
    quality: 75,
    format: "webp" as const,
    fit: "cover" as const,
    background: "#ffffff",
  },
} as const

/**
 * Optimizes an image buffer using Sharp
 */
export async function optimizeImage(
  inputBuffer: <PERSON>uffer,
  options: ServerOptimizationOptions
): Promise<OptimizationResult> {
  try {
    const originalSize = inputBuffer.length

    // Create Sharp instance
    let sharpInstance = sharp(inputBuffer)

    // Get original metadata
    const metadata = await sharpInstance.metadata()

    // Apply resizing if dimensions are specified
    if (options.width || options.height) {
      sharpInstance = sharpInstance.resize({
        width: options.width,
        height: options.height,
        fit: options.fit || "inside",
        background: options.background || { r: 255, g: 255, b: 255, alpha: 1 },
      })
    }

    // Apply format and quality settings
    switch (options.format) {
      case "webp":
        sharpInstance = sharpInstance.webp({
          quality: options.quality,
          effort: 4, // Good balance of compression and speed
        })
        break
      case "jpeg":
        sharpInstance = sharpInstance.jpeg({
          quality: options.quality,
          progressive: true,
        })
        break
      case "png":
        sharpInstance = sharpInstance.png({
          quality: options.quality,
          compressionLevel: 6,
        })
        break
    }

    // Process the image
    const optimizedBuffer = await sharpInstance.toBuffer()
    const optimizedMetadata = await sharp(optimizedBuffer).metadata()

    return {
      success: true,
      buffer: optimizedBuffer,
      originalSize,
      optimizedSize: optimizedBuffer.length,
      metadata: {
        width: optimizedMetadata.width || 0,
        height: optimizedMetadata.height || 0,
        format: optimizedMetadata.format || options.format,
        size: optimizedBuffer.length,
      },
    }
  } catch (error) {
    console.error("Server image optimization error:", error)

    return {
      success: false,
      originalSize: inputBuffer.length,
      error: error instanceof Error ? error.message : "Unknown optimization error",
    }
  }
}

/**
 * Creates multiple optimized versions of an image
 */
export async function createImageVariants(
  inputBuffer: Buffer,
  variants: Record<string, ServerOptimizationOptions>
): Promise<Record<string, OptimizationResult>> {
  const results: Record<string, OptimizationResult> = {}

  for (const [variantName, options] of Object.entries(variants)) {
    results[variantName] = await optimizeImage(inputBuffer, options)
  }

  return results
}

/**
 * Validates image buffer and extracts metadata
 */
export async function validateImageBuffer(buffer: Buffer): Promise<{
  valid: boolean
  metadata?: sharp.Metadata
  error?: string
}> {
  try {
    const metadata = await sharp(buffer).metadata()

    // Check if it's a valid image
    if (!metadata.format || !metadata.width || !metadata.height) {
      return {
        valid: false,
        error: "Invalid image format or corrupted file",
      }
    }

    // Check for reasonable dimensions (not too large)
    const maxDimension = 10000 // 10k pixels max
    if (metadata.width > maxDimension || metadata.height > maxDimension) {
      return {
        valid: false,
        error: `Image dimensions too large. Maximum ${maxDimension}x${maxDimension} pixels.`,
      }
    }

    return {
      valid: true,
      metadata,
    }
  } catch (error) {
    return {
      valid: false,
      error: error instanceof Error ? error.message : "Failed to process image",
    }
  }
}

/**
 * Converts File to Buffer for server processing
 */
export async function fileToBuffer(file: File): Promise<Buffer> {
  const arrayBuffer = await file.arrayBuffer()
  return Buffer.from(arrayBuffer)
}

/**
 * Creates a File from optimized buffer
 */
export function bufferToFile(buffer: Buffer, filename: string, mimeType: string): File {
  const blob = new Blob([buffer], { type: mimeType })
  return new File([blob], filename, { type: mimeType })
}

/**
 * Utility to determine optimal format based on input
 */
export function getOptimalFormat(originalFormat?: string): "webp" | "jpeg" | "png" {
  // Default to WebP for best compression
  if (!originalFormat) return "webp"

  // Keep PNG for images that might need transparency
  if (originalFormat === "png") return "png"

  // Use WebP for everything else (JPEG, HEIC, etc.)
  return "webp"
}

/**
 * Calculates compression savings
 */
export function calculateSavings(
  originalSize: number,
  optimizedSize: number
): {
  savedBytes: number
  savedPercentage: number
  compressionRatio: number
} {
  const savedBytes = originalSize - optimizedSize
  const savedPercentage = (savedBytes / originalSize) * 100
  const compressionRatio = originalSize / optimizedSize

  return {
    savedBytes,
    savedPercentage,
    compressionRatio,
  }
}
