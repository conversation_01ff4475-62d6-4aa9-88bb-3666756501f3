import { BaseRealtimeService } from "../base/base.realtime.service"
import { User } from "./user.types"

/**
 * User real-time service for Firebase real-time operations
 */
export class UserRealtimeService {
  private static readonly COLLECTION = "users"

  /**
   * Subscribe to a user by ID
   * @param userId User ID
   * @param callback Callback function to handle user changes
   * @returns Unsubscribe function
   */
  static subscribeToUser(
    userId: string,
    callback: (user: User | null, error?: Error) => void
  ): () => void {
    return BaseRealtimeService.subscribeToDocument<User>(this.COLLECTION, userId, (data, error) => {
      if (error) {
        callback(null, error)
        return
      }

      if (data) {
        callback({ ...data, uid: userId } as User)
      } else {
        callback(null)
      }
    })
  }
}
