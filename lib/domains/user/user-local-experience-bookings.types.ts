/**
 * User Local Experience Bookings Types
 *
 * This file re-exports types from the local-experiences domain
 * and defines user-specific booking interfaces for the user domain.
 *
 * The user domain only handles read-only operations for user booking history.
 * All booking creation and management logic remains in the local-experiences domain.
 */

// Re-export all booking types from local-experiences domain
export {
  type BookingStatus,
  type PaymentStatus,
  type BookingGuest,
  type BookingPricing,
  type ExperienceBooking,
  type ExperienceBookingCreateData,
  type ExperienceBookingUpdateData,
  type BookingFormData,
  type BookingConfirmation,
  type BookingHistoryFilters,
  type BookingSearchResult,
} from "../local-experiences/local-experiences-booking.types"

// Import types for use in this file
import type {
  BookingStatus,
  ExperienceBooking,
} from "../local-experiences/local-experiences-booking.types"

/**
 * User booking history filters (user-specific)
 */
export interface UserBookingHistoryFilters {
  status?: BookingStatus[]
  dateRange?: {
    start: string
    end: string
  }
  experienceId?: string
  sortBy?: "date_desc" | "date_asc" | "status" | "amount"
}

/**
 * User booking search result
 */
export interface UserBookingSearchResult {
  bookings: ExperienceBooking[]
  total: number
  hasMore: boolean
  lastDoc?: any // For pagination
}
