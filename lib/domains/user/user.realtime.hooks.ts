"use client"

import { useEffect, useState } from "react"
import { User } from "./user.types"
import { UserRealtimeService } from "./user.realtime.service"

/**
 * Hook to get real-time updates for a specific user
 */
export const useRealtimeUser = (userId: string) => {
  const [user, setUser] = useState<User | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<Error | null>(null)

  useEffect(() => {
    if (!userId) {
      setUser(null)
      setLoading(false)
      return () => {}
    }

    setLoading(true)

    const unsubscribe = UserRealtimeService.subscribeToUser(userId, (data, err) => {
      if (err) {
        console.error("Error getting real-time user:", err)
        setError(err)
        setLoading(false)
        return
      }

      setUser(data)
      setLoading(false)
    })

    return () => unsubscribe()
  }, [userId])

  return { user, loading, error }
}
