import { useCallback, useEffect } from "react"
import { toast } from "sonner"
import { UserLocalExperienceBookingsService } from "./user-local-experience-bookings.service"
import { useUserLocalExperienceBookingsStore } from "./user-local-experience-bookings.store"
import { useUser } from "../auth/auth.hooks"
import { UserBookingHistoryFilters } from "./user-local-experience-bookings.types"

/**
 * Hook for managing user's local experience booking history
 */
export const useUserLocalExperienceBookings = () => {
  const user = useUser()
  const {
    userBookings,
    bookingHistory,
    isLoadingHistory,
    historyError,
    historyFilters,
    hasMoreBookings,
    lastDoc,
    setUserBookings,
    setBookingHistory,
    setIsLoadingHistory,
    setHistoryError,
    setHistoryFilters,
    resetHistoryFilters,
    appendUserBookings,
    setHasMoreBookings,
    setLastDoc,
    resetHistoryState,
  } = useUserLocalExperienceBookingsStore()

  /**
   * Load user booking history
   */
  const loadBookingHistory = useCallback(
    async (filters?: UserBookingHistoryFilters, loadMore: boolean = false) => {
      try {
        if (!user) {
          setHistoryError("User not authenticated")
          return
        }

        setIsLoadingHistory(true)
        setHistoryError(null)

        const currentLastDoc = loadMore ? lastDoc : undefined
        const response = await UserLocalExperienceBookingsService.getUserBookings(
          user.uid,
          filters,
          10,
          currentLastDoc
        )

        if (response.success && response.data) {
          const { bookings, hasMore, lastDoc: newLastDoc } = response.data

          if (loadMore) {
            appendUserBookings(bookings)
          } else {
            setUserBookings(bookings)
          }

          setBookingHistory(response.data)
          setHasMoreBookings(hasMore)
          setLastDoc(newLastDoc)
        } else {
          const errorMessage =
            response.error instanceof Error
              ? response.error.message
              : "Failed to load booking history"
          setHistoryError(errorMessage)
          toast.error(errorMessage)
        }
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : "Failed to load booking history"
        setHistoryError(errorMessage)
        toast.error(errorMessage)
      } finally {
        setIsLoadingHistory(false)
      }
    },
    [
      user,
      lastDoc,
      setIsLoadingHistory,
      setHistoryError,
      setUserBookings,
      setBookingHistory,
      appendUserBookings,
      setHasMoreBookings,
      setLastDoc,
    ]
  )

  /**
   * Update filters and reload
   */
  const updateFilters = useCallback(
    (filters: UserBookingHistoryFilters) => {
      setHistoryFilters(filters)
      loadBookingHistory(filters, false)
    },
    [setHistoryFilters, loadBookingHistory]
  )

  /**
   * Load more bookings (pagination)
   */
  const loadMoreBookings = useCallback(() => {
    if (hasMoreBookings && !isLoadingHistory) {
      loadBookingHistory(historyFilters, true)
    }
  }, [hasMoreBookings, isLoadingHistory, historyFilters, loadBookingHistory])

  /**
   * Refresh booking history
   */
  const refreshBookingHistory = useCallback(() => {
    loadBookingHistory(historyFilters, false)
  }, [loadBookingHistory, historyFilters])

  // Load initial data when user changes
  useEffect(() => {
    if (user) {
      loadBookingHistory(historyFilters, false)
    } else {
      resetHistoryState()
    }
  }, [user]) // Only depend on user, not on other dependencies to avoid infinite loops

  return {
    // State
    userBookings,
    bookingHistory,
    isLoadingHistory,
    historyError,
    historyFilters,
    hasMoreBookings,

    // Actions
    loadBookingHistory,
    updateFilters,
    resetFilters: resetHistoryFilters,
    loadMoreBookings,
    refreshBookingHistory,
    resetHistoryState,
  }
}

/**
 * Hook for managing individual booking details
 */
export const useUserLocalExperienceBooking = (bookingId?: string) => {
  const user = useUser()
  const {
    selectedBooking,
    isLoadingBooking,
    bookingError,
    setSelectedBooking,
    setIsLoadingBooking,
    setBookingError,
    resetBookingState,
  } = useUserLocalExperienceBookingsStore()

  /**
   * Load specific booking
   */
  const loadBooking = useCallback(
    async (id: string) => {
      try {
        if (!user) {
          setBookingError("User not authenticated")
          return
        }

        setIsLoadingBooking(true)
        setBookingError(null)

        const response = await UserLocalExperienceBookingsService.getUserBooking(user.uid, id)

        if (response.success && response.data) {
          setSelectedBooking(response.data)
        } else {
          const errorMessage =
            response.error instanceof Error ? response.error.message : "Failed to load booking"
          setBookingError(errorMessage)
          toast.error(errorMessage)
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : "Failed to load booking"
        setBookingError(errorMessage)
        toast.error(errorMessage)
      } finally {
        setIsLoadingBooking(false)
      }
    },
    [user, setIsLoadingBooking, setBookingError, setSelectedBooking]
  )

  // Load booking when bookingId changes
  useEffect(() => {
    if (user && bookingId) {
      loadBooking(bookingId)
    } else {
      resetBookingState()
    }
  }, [user, bookingId]) // Only depend on user and bookingId

  return {
    // State
    selectedBooking,
    isLoadingBooking,
    bookingError,

    // Actions
    loadBooking,
    resetBookingState,
  }
}

/**
 * Hook for managing user booking counts
 */
export const useUserBookingCounts = () => {
  const user = useUser()
  const {
    bookingCounts,
    isLoadingCounts,
    countsError,
    setBookingCounts,
    setIsLoadingCounts,
    setCountsError,
    resetCountsState,
  } = useUserLocalExperienceBookingsStore()

  /**
   * Load booking counts
   */
  const loadBookingCounts = useCallback(async () => {
    try {
      if (!user) {
        setCountsError("User not authenticated")
        return
      }

      setIsLoadingCounts(true)
      setCountsError(null)

      const response = await UserLocalExperienceBookingsService.getUserBookingCounts(user.uid)

      if (response.success && response.data) {
        setBookingCounts(response.data)
      } else {
        const errorMessage =
          response.error instanceof Error ? response.error.message : "Failed to load booking counts"
        setCountsError(errorMessage)
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Failed to load booking counts"
      setCountsError(errorMessage)
    } finally {
      setIsLoadingCounts(false)
    }
  }, [user, setIsLoadingCounts, setCountsError, setBookingCounts])

  // Load counts when user changes
  useEffect(() => {
    if (user) {
      loadBookingCounts()
    } else {
      resetCountsState()
    }
  }, [user]) // Only depend on user

  return {
    // State
    bookingCounts,
    isLoadingCounts,
    countsError,

    // Actions
    loadBookingCounts,
    resetCountsState,
  }
}

/**
 * Hook for managing upcoming bookings
 */
export const useUserUpcomingBookings = () => {
  const user = useUser()
  const {
    upcomingBookings,
    isLoadingUpcoming,
    upcomingError,
    setUpcomingBookings,
    setIsLoadingUpcoming,
    setUpcomingError,
    resetUpcomingState,
  } = useUserLocalExperienceBookingsStore()

  /**
   * Load upcoming bookings
   */
  const loadUpcomingBookings = useCallback(
    async (limit: number = 5) => {
      try {
        if (!user) {
          setUpcomingError("User not authenticated")
          return
        }

        setIsLoadingUpcoming(true)
        setUpcomingError(null)

        const response = await UserLocalExperienceBookingsService.getUserUpcomingBookings(
          user.uid,
          limit
        )

        if (response.success && response.data) {
          setUpcomingBookings(response.data)
        } else {
          const errorMessage =
            response.error instanceof Error
              ? response.error.message
              : "Failed to load upcoming bookings"
          setUpcomingError(errorMessage)
        }
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : "Failed to load upcoming bookings"
        setUpcomingError(errorMessage)
      } finally {
        setIsLoadingUpcoming(false)
      }
    },
    [user, setIsLoadingUpcoming, setUpcomingError, setUpcomingBookings]
  )

  // Load upcoming bookings when user changes
  useEffect(() => {
    if (user) {
      loadUpcomingBookings()
    } else {
      resetUpcomingState()
    }
  }, [user]) // Only depend on user

  return {
    // State
    upcomingBookings,
    isLoadingUpcoming,
    upcomingError,

    // Actions
    loadUpcomingBookings,
    resetUpcomingState,
  }
}
