import { db } from "@/lib/firebase"
import {
  collection,
  doc,
  getDoc,
  getDocs,
  query,
  where,
  orderBy,
  limit,
  startAfter,
  QueryDocumentSnapshot,
} from "firebase/firestore"
import { BaseService } from "../base/base.service"
import { ServiceResponse } from "../base/base.types"
import {
  ExperienceBooking,
  UserBookingHistoryFilters,
  UserBookingSearchResult,
  BookingStatus,
} from "./user-local-experience-bookings.types"

/**
 * User Local Experience Bookings Service
 *
 * This service handles read-only operations for user booking history.
 * It reads from the users/{userId}/localExperienceBookings collection.
 *
 * All booking creation and management logic remains in the local-experiences domain.
 */
export class UserLocalExperienceBookingsService extends BaseService {
  private static readonly USERS_COLLECTION = "users"
  private static readonly USER_BOOKINGS_SUBCOLLECTION = "localExperienceBookings"

  /**
   * Get a specific booking for a user
   */
  static async getUserBooking(
    userId: string,
    bookingId: string
  ): Promise<ServiceResponse<ExperienceBooking>> {
    try {
      const bookingRef = doc(
        db,
        this.USERS_COLLECTION,
        userId,
        this.USER_BOOKINGS_SUBCOLLECTION,
        bookingId
      )
      const bookingDoc = await getDoc(bookingRef)

      if (!bookingDoc.exists()) {
        return { success: false, error: "Booking not found" }
      }

      const booking = {
        id: bookingDoc.id,
        ...bookingDoc.data(),
      } as ExperienceBooking

      return { success: true, data: booking }
    } catch (error) {
      console.error("Error getting user booking:", error)
      return { success: false, error: "Failed to get booking" }
    }
  }

  /**
   * Get user's booking history with filters and pagination
   */
  static async getUserBookings(
    userId: string,
    filters?: UserBookingHistoryFilters,
    pageSize: number = 10,
    lastDoc?: QueryDocumentSnapshot
  ): Promise<ServiceResponse<UserBookingSearchResult>> {
    try {
      let bookingsQuery = query(
        collection(db, this.USERS_COLLECTION, userId, this.USER_BOOKINGS_SUBCOLLECTION)
      )

      // Apply status filter
      if (filters?.status && filters.status.length > 0) {
        bookingsQuery = query(bookingsQuery, where("status", "in", filters.status))
      }

      // Apply date range filter
      if (filters?.dateRange) {
        if (filters.dateRange.start) {
          bookingsQuery = query(bookingsQuery, where("date", ">=", filters.dateRange.start))
        }
        if (filters.dateRange.end) {
          bookingsQuery = query(bookingsQuery, where("date", "<=", filters.dateRange.end))
        }
      }

      // Apply experience filter
      if (filters?.experienceId) {
        bookingsQuery = query(bookingsQuery, where("experienceId", "==", filters.experienceId))
      }

      // Apply sorting
      const sortBy = filters?.sortBy || "date_desc"
      switch (sortBy) {
        case "date_asc":
          bookingsQuery = query(bookingsQuery, orderBy("date", "asc"))
          break
        case "date_desc":
          bookingsQuery = query(bookingsQuery, orderBy("date", "desc"))
          break
        case "status":
          bookingsQuery = query(bookingsQuery, orderBy("status", "asc"))
          break
        case "amount":
          bookingsQuery = query(bookingsQuery, orderBy("pricing.total", "desc"))
          break
        default:
          bookingsQuery = query(bookingsQuery, orderBy("bookedAt", "desc"))
      }

      // Apply pagination
      if (lastDoc) {
        bookingsQuery = query(bookingsQuery, startAfter(lastDoc))
      }
      bookingsQuery = query(bookingsQuery, limit(pageSize + 1)) // +1 to check if there are more

      const querySnapshot = await getDocs(bookingsQuery)
      const docs = querySnapshot.docs
      const hasMore = docs.length > pageSize

      // Remove the extra document if it exists
      const bookingDocs = hasMore ? docs.slice(0, pageSize) : docs
      const newLastDoc = bookingDocs.length > 0 ? bookingDocs[bookingDocs.length - 1] : undefined

      const bookings: ExperienceBooking[] = bookingDocs.map(
        (doc) =>
          ({
            id: doc.id,
            ...doc.data(),
          }) as ExperienceBooking
      )

      const result: UserBookingSearchResult = {
        bookings,
        total: bookings.length, // Note: This is not the total count, just current page count
        hasMore,
        lastDoc: newLastDoc,
      }

      return { success: true, data: result }
    } catch (error) {
      console.error("Error getting user bookings:", error)
      return { success: false, error: "Failed to get user bookings" }
    }
  }

  /**
   * Get user's booking count by status
   */
  static async getUserBookingCounts(
    userId: string
  ): Promise<ServiceResponse<Record<BookingStatus, number>>> {
    try {
      const bookingsQuery = query(
        collection(db, this.USERS_COLLECTION, userId, this.USER_BOOKINGS_SUBCOLLECTION)
      )

      const querySnapshot = await getDocs(bookingsQuery)
      const counts: Record<BookingStatus, number> = {
        pending: 0,
        confirmed: 0,
        cancelled: 0,
        completed: 0,
      }

      querySnapshot.docs.forEach((doc) => {
        const booking = doc.data() as ExperienceBooking
        if (booking.status && Object.hasOwn(counts, booking.status)) {
          counts[booking.status]++
        }
      })

      return { success: true, data: counts }
    } catch (error) {
      console.error("Error getting user booking counts:", error)
      return { success: false, error: "Failed to get booking counts" }
    }
  }

  /**
   * Get user's upcoming bookings (confirmed bookings with future dates)
   */
  static async getUserUpcomingBookings(
    userId: string,
    limitCount: number = 5
  ): Promise<ServiceResponse<ExperienceBooking[]>> {
    try {
      const today = new Date().toISOString().split("T")[0] // YYYY-MM-DD format

      let bookingsQuery = query(
        collection(db, this.USERS_COLLECTION, userId, this.USER_BOOKINGS_SUBCOLLECTION),
        where("status", "==", "confirmed"),
        where("date", ">=", today),
        orderBy("date", "asc"),
        limit(limitCount)
      )

      const querySnapshot = await getDocs(bookingsQuery)
      const bookings: ExperienceBooking[] = querySnapshot.docs.map(
        (doc) =>
          ({
            id: doc.id,
            ...doc.data(),
          }) as ExperienceBooking
      )

      return { success: true, data: bookings }
    } catch (error) {
      console.error("Error getting upcoming bookings:", error)
      return { success: false, error: "Failed to get upcoming bookings" }
    }
  }
}
