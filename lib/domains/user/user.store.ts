"use client"

import { create } from "zustand"
import { User } from "./user.types"
import { UserService } from "./user.service"
import { UserRealtimeService } from "./user.realtime.service"
import { toast } from "@/components/ui/use-toast"

/**
 * User store state interface
 */
interface UserState {
  // State
  users: Record<string, User>
  currentUser: User | null
  loading: boolean
  error: Error | null
  user: User | null
  isNewUser: boolean | null

  // Real-time subscriptions
  userSubscription: (() => void) | null

  // Actions
  fetchUser: (userId: string) => Promise<void>
  updateUser: (userId: string, userData: Partial<User>) => Promise<boolean>
  setCurrentUser: (user: User | null) => void
  setUser: (user: User | null) => void
  setIsNewUser: (isNewUser: boolean) => void
  setLoading: (loading: boolean) => void
  subscribeToUser: (userId: string) => void
  unsubscribeFromUser: () => void
}

/**
 * User store with Zustand
 */
export const useUserStore = create<UserState>((set, get) => ({
  // Initial state
  users: {},
  currentUser: null,
  loading: false,
  error: null,
  user: null,
  isNewUser: false,

  // Real-time subscriptions
  userSubscription: null,

  // Actions
  setUser: (user) => {
    set({ user })
  },
  setIsNewUser: (isNewUser) => {
    set({ isNewUser })
  },
  setLoading: (loading) => {
    set({ loading })
  },
  fetchUser: async (userId: string) => {
    try {
      set({ loading: true, error: null })
      const user = await UserService.getUser(userId)

      if (user) {
        set((state) => ({
          users: { ...state.users, [userId]: user },
          currentUser: user,
          loading: false,
        }))
      } else {
        set({ error: new Error("User not found"), loading: false })
      }
    } catch (error) {
      console.error("Error fetching user:", error)
      set({ error: error as Error, loading: false })
    }
  },

  updateUser: async (userId, userData) => {
    try {
      set({ loading: true, error: null })
      const result = await UserService.updateUser(userId, userData)

      // If successful and we have the current user loaded, update it
      if (result.success && get().currentUser?.uid === userId) {
        const updatedUser = await UserService.getUser(userId)
        if (updatedUser) {
          set((state) => ({
            users: { ...state.users, [userId]: updatedUser },
            currentUser: updatedUser,
          }))
        }
      }

      if (result.success) {
        toast({
          title: "Profile updated",
          description: "Your profile has been updated successfully.",
        })
      }

      set({ loading: false })
      return result.success
    } catch (error) {
      console.error("Error updating user:", error)
      set({ error: error as Error, loading: false })
      return false
    }
  },

  setCurrentUser: (user) => {
    set({ currentUser: user })
  },

  subscribeToUser: (userId: string) => {
    // Unsubscribe from any existing subscription
    const { unsubscribeFromUser } = get()
    unsubscribeFromUser()

    // Subscribe to the user
    const unsubscribe = UserRealtimeService.subscribeToUser(userId, (user, error) => {
      if (error) {
        set({ error, loading: false })
        return
      }

      if (user) {
        set((state) => ({
          users: { ...state.users, [userId]: user },
          currentUser: user,
          loading: false,
        }))
      } else {
        set({ error: new Error("User not found"), loading: false })
      }
    })

    set({ userSubscription: unsubscribe })
  },

  unsubscribeFromUser: () => {
    const { userSubscription } = get()
    if (userSubscription) {
      userSubscription()
      set({ userSubscription: null })
    }
  },
}))
