import { create } from "zustand"
import { devtools } from "zustand/middleware"
import {
  ExperienceBooking,
  UserBookingHistoryFilters,
  UserBookingSearchResult,
  BookingStatus,
} from "./user-local-experience-bookings.types"

/**
 * User Local Experience Bookings store state
 */
interface UserLocalExperienceBookingsState {
  // User booking history
  userBookings: ExperienceBooking[]
  bookingHistory: UserBookingSearchResult | null
  isLoadingHistory: boolean
  historyError: string | null
  historyFilters: UserBookingHistoryFilters

  // Individual booking
  selectedBooking: ExperienceBooking | null
  isLoadingBooking: boolean
  bookingError: string | null

  // Booking counts
  bookingCounts: Record<BookingStatus, number> | null
  isLoadingCounts: boolean
  countsError: string | null

  // Upcoming bookings
  upcomingBookings: ExperienceBooking[]
  isLoadingUpcoming: boolean
  upcomingError: string | null

  // Pagination
  hasMoreBookings: boolean
  lastDoc: any
}

/**
 * User Local Experience Bookings store actions
 */
interface UserLocalExperienceBookingsActions {
  // User booking history
  setUserBookings: (bookings: ExperienceBooking[]) => void
  setBookingHistory: (history: UserBookingSearchResult | null) => void
  setIsLoadingHistory: (isLoading: boolean) => void
  setHistoryError: (error: string | null) => void
  setHistoryFilters: (filters: UserBookingHistoryFilters) => void
  resetHistoryFilters: () => void
  appendUserBookings: (bookings: ExperienceBooking[]) => void

  // Individual booking
  setSelectedBooking: (booking: ExperienceBooking | null) => void
  setIsLoadingBooking: (isLoading: boolean) => void
  setBookingError: (error: string | null) => void

  // Booking counts
  setBookingCounts: (counts: Record<BookingStatus, number> | null) => void
  setIsLoadingCounts: (isLoading: boolean) => void
  setCountsError: (error: string | null) => void

  // Upcoming bookings
  setUpcomingBookings: (bookings: ExperienceBooking[]) => void
  setIsLoadingUpcoming: (isLoading: boolean) => void
  setUpcomingError: (error: string | null) => void

  // Pagination
  setHasMoreBookings: (hasMore: boolean) => void
  setLastDoc: (lastDoc: any) => void

  // Reset functions
  resetHistoryState: () => void
  resetBookingState: () => void
  resetCountsState: () => void
  resetUpcomingState: () => void
  resetAllState: () => void
}

/**
 * Combined store type
 */
type UserLocalExperienceBookingsStore = UserLocalExperienceBookingsState &
  UserLocalExperienceBookingsActions

/**
 * Initial state
 */
const initialState: UserLocalExperienceBookingsState = {
  // User booking history
  userBookings: [],
  bookingHistory: null,
  isLoadingHistory: false,
  historyError: null,
  historyFilters: {},

  // Individual booking
  selectedBooking: null,
  isLoadingBooking: false,
  bookingError: null,

  // Booking counts
  bookingCounts: null,
  isLoadingCounts: false,
  countsError: null,

  // Upcoming bookings
  upcomingBookings: [],
  isLoadingUpcoming: false,
  upcomingError: null,

  // Pagination
  hasMoreBookings: false,
  lastDoc: null,
}

/**
 * User Local Experience Bookings Zustand store
 */
export const useUserLocalExperienceBookingsStore = create<UserLocalExperienceBookingsStore>()(
  devtools(
    (set, get) => ({
      ...initialState,

      // User booking history actions
      setUserBookings: (bookings) => set({ userBookings: bookings }, false, "setUserBookings"),
      setBookingHistory: (history) => set({ bookingHistory: history }, false, "setBookingHistory"),
      setIsLoadingHistory: (isLoading) =>
        set({ isLoadingHistory: isLoading }, false, "setIsLoadingHistory"),
      setHistoryError: (error) => set({ historyError: error }, false, "setHistoryError"),
      setHistoryFilters: (filters) => set({ historyFilters: filters }, false, "setHistoryFilters"),
      resetHistoryFilters: () => set({ historyFilters: {} }, false, "resetHistoryFilters"),
      appendUserBookings: (bookings) => {
        const currentBookings = get().userBookings
        set({ userBookings: [...currentBookings, ...bookings] }, false, "appendUserBookings")
      },

      // Individual booking actions
      setSelectedBooking: (booking) =>
        set({ selectedBooking: booking }, false, "setSelectedBooking"),
      setIsLoadingBooking: (isLoading) =>
        set({ isLoadingBooking: isLoading }, false, "setIsLoadingBooking"),
      setBookingError: (error) => set({ bookingError: error }, false, "setBookingError"),

      // Booking counts actions
      setBookingCounts: (counts) => set({ bookingCounts: counts }, false, "setBookingCounts"),
      setIsLoadingCounts: (isLoading) =>
        set({ isLoadingCounts: isLoading }, false, "setIsLoadingCounts"),
      setCountsError: (error) => set({ countsError: error }, false, "setCountsError"),

      // Upcoming bookings actions
      setUpcomingBookings: (bookings) =>
        set({ upcomingBookings: bookings }, false, "setUpcomingBookings"),
      setIsLoadingUpcoming: (isLoading) =>
        set({ isLoadingUpcoming: isLoading }, false, "setIsLoadingUpcoming"),
      setUpcomingError: (error) => set({ upcomingError: error }, false, "setUpcomingError"),

      // Pagination actions
      setHasMoreBookings: (hasMore) =>
        set({ hasMoreBookings: hasMore }, false, "setHasMoreBookings"),
      setLastDoc: (lastDoc) => set({ lastDoc: lastDoc }, false, "setLastDoc"),

      // Reset functions
      resetHistoryState: () =>
        set(
          {
            userBookings: [],
            bookingHistory: null,
            isLoadingHistory: false,
            historyError: null,
            historyFilters: {},
            hasMoreBookings: false,
            lastDoc: null,
          },
          false,
          "resetHistoryState"
        ),
      resetBookingState: () =>
        set(
          {
            selectedBooking: null,
            isLoadingBooking: false,
            bookingError: null,
          },
          false,
          "resetBookingState"
        ),
      resetCountsState: () =>
        set(
          {
            bookingCounts: null,
            isLoadingCounts: false,
            countsError: null,
          },
          false,
          "resetCountsState"
        ),
      resetUpcomingState: () =>
        set(
          {
            upcomingBookings: [],
            isLoadingUpcoming: false,
            upcomingError: null,
          },
          false,
          "resetUpcomingState"
        ),
      resetAllState: () => set(initialState, false, "resetAllState"),
    }),
    {
      name: "user-local-experience-bookings-store",
    }
  )
)
