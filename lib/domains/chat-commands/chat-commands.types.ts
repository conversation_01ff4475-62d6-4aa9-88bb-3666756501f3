/**
 * Chat command types and enums for scalable command system
 */

/**
 * Available chat commands
 */
export enum ChatCommand {
  TOGEDA = "togeda",
  // Future commands can be added here:
  // HELP = "help",
  // POLL = "poll",
  // REMIND = "remind",
  // WEATHER = "weather",
}

/**
 * Command metadata for display and validation
 */
export interface ChatCommandInfo {
  command: ChatCommand
  displayName: string
  description: string
  usage: string
  requiresPro: boolean
  category: ChatCommandCategory
  icon?: string
}

/**
 * Command categories for organization
 */
export enum ChatCommandCategory {
  AI = "ai",
  UTILITY = "utility",
  SOCIAL = "social",
  PLANNING = "planning",
}

/**
 * Command suggestion item for UI display
 */
export interface CommandSuggestion {
  command: ChatCommand
  displayName: string
  description: string
  usage: string
  requiresPro: boolean
  category: ChatCommandCategory
  icon?: string
  isAvailable: boolean // Based on user subscription
}

/**
 * Available chat commands with their metadata
 */
export const CHAT_COMMANDS: Record<ChatCommand, ChatCommandInfo> = {
  [ChatCommand.TOGEDA]: {
    command: ChatCommand.TOGEDA,
    displayName: "/togeda",
    description: "Ask Togeda AI for trip planning assistance",
    usage: "/togeda [your question about the trip]",
    requiresPro: true,
    category: ChatCommandCategory.AI,
    icon: "🤖",
  },
  // Future commands:
  // [ChatCommand.HELP]: {
  //   command: ChatCommand.HELP,
  //   displayName: "/help",
  //   description: "Show available commands",
  //   usage: "/help",
  //   requiresPro: false,
  //   category: ChatCommandCategory.UTILITY,
  //   icon: "❓",
  // },
}

/**
 * Get all available commands for a user based on their subscription
 */
export function getAvailableCommands(isProUser: boolean): CommandSuggestion[] {
  return Object.values(CHAT_COMMANDS).map((cmd) => ({
    ...cmd,
    isAvailable: !cmd.requiresPro || isProUser,
  }))
}

/**
 * Get command suggestions based on partial input
 */
export function getCommandSuggestions(input: string, isProUser: boolean): CommandSuggestion[] {
  const trimmedInput = input.toLowerCase().trim()

  // Only show suggestions if input starts with "/"
  if (!trimmedInput.startsWith("/")) {
    return []
  }

  const searchTerm = trimmedInput.substring(1) // Remove the "/"
  const availableCommands = getAvailableCommands(isProUser)

  // If just "/", show all commands
  if (searchTerm === "") {
    return availableCommands
  }

  // Filter commands that match the search term
  return availableCommands.filter((cmd) => cmd.command.toLowerCase().startsWith(searchTerm))
}

/**
 * Parse command from user input
 */
export function parseCommand(input: string): {
  isCommand: boolean
  command?: ChatCommand
  args?: string
} {
  const trimmedInput = input.trim()

  if (!trimmedInput.startsWith("/")) {
    return { isCommand: false }
  }

  const parts = trimmedInput.split(/\s+/)
  const commandPart = parts[0].substring(1).toLowerCase() // Remove "/" and normalize
  const args = parts.slice(1).join(" ").trim()

  // Check if it's a valid command
  const command = Object.values(ChatCommand).find((cmd) => cmd === commandPart)

  if (!command) {
    return { isCommand: false }
  }

  return {
    isCommand: true,
    command,
    args: args || undefined,
  }
}

/**
 * Validate if user can execute a command
 */
export function canExecuteCommand(
  command: ChatCommand,
  isProUser: boolean
): { canExecute: boolean; reason?: string } {
  const commandInfo = CHAT_COMMANDS[command]

  if (!commandInfo) {
    return { canExecute: false, reason: "Unknown command" }
  }

  if (commandInfo.requiresPro && !isProUser) {
    return { canExecute: false, reason: "Pro subscription required" }
  }

  return { canExecute: true }
}
