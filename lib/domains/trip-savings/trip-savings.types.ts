import { Timestamp } from "firebase/firestore"
import { BaseEntity } from "../base/base.types"

/**
 * Trip savings entity
 */
export interface TripSavings extends BaseEntity {
  userId: string
  tripId: string
  name: string
  goalAmount: number
  savedAmount: number
  targetDate: Timestamp
  isPrivate: boolean
}

/**
 * Trip savings creation data
 */
export type TripSavingsCreateData = Omit<
  TripSavings,
  "id" | "createdAt" | "updatedAt" | "savedAmount"
>

/**
 * Trip savings update data
 */
export type TripSavingsUpdateData = Partial<TripSavings>

/**
 * Savings transaction entity
 */
export interface SavingsTransaction extends BaseEntity {
  savingsId: string
  userId: string
  amount: number
  date: Timestamp
  method: string
}

/**
 * Savings transaction creation data
 */
export type SavingsTransactionCreateData = Omit<SavingsTransaction, "id" | "createdAt">
