"use client"

import { create } from "zustand"
import {
  TripSavings,
  TripSavingsCreateData,
  TripSavingsUpdateData,
  SavingsTransaction,
  SavingsTransactionCreateData,
} from "./trip-savings.types"
import { TripSavingsService } from "./trip-savings.service"

/**
 * Trip Savings store state interface
 */
interface TripSavingsState {
  // State
  savingsGoals: TripSavings[]
  currentSavingsGoal: TripSavings | null
  transactions: SavingsTransaction[]
  loading: boolean
  error: Error | null

  // Actions
  fetchUserSavingsGoals: (userId: string) => Promise<void>
  fetchTripSavingsGoals: (tripId: string, includePrivate?: boolean) => Promise<void>
  fetchSavingsGoal: (savingsId: string) => Promise<void>
  fetchTransactions: (savingsId: string) => Promise<void>
  createSavingsGoal: (savingsData: TripSavingsCreateData) => Promise<string | null>
  updateSavingsGoal: (savingsId: string, savingsData: TripSavingsUpdateData) => Promise<boolean>
  addTransaction: (transactionData: SavingsTransactionCreateData) => Promise<string | null>
  clearSavingsGoals: () => void
  clearTransactions: () => void
}

/**
 * Trip Savings store with Zustand
 */
export const useTripSavingsStore = create<TripSavingsState>((set, get) => ({
  // Initial state
  savingsGoals: [],
  currentSavingsGoal: null,
  transactions: [],
  loading: false,
  error: null,

  // Actions
  fetchUserSavingsGoals: async (userId: string) => {
    try {
      set({ loading: true, error: null })
      const savingsGoals = await TripSavingsService.getUserTripSavings(userId)
      set({ savingsGoals, loading: false })
    } catch (error) {
      console.error("Error fetching user savings goals:", error)
      set({ error: error as Error, loading: false })
    }
  },

  fetchTripSavingsGoals: async (tripId: string, includePrivate = false) => {
    try {
      set({ loading: true, error: null })
      const savingsGoals = await TripSavingsService.getTripSavingsForTrip(tripId, includePrivate)
      set({ savingsGoals, loading: false })
    } catch (error) {
      console.error("Error fetching trip savings goals:", error)
      set({ error: error as Error, loading: false })
    }
  },

  fetchSavingsGoal: async (savingsId: string) => {
    try {
      set({ loading: true, error: null })
      const savingsGoal = await TripSavingsService.getTripSavings(savingsId)
      if (savingsGoal) {
        set({ currentSavingsGoal: savingsGoal, loading: false })
      } else {
        set({ error: new Error("Savings goal not found"), loading: false })
      }
    } catch (error) {
      console.error("Error fetching savings goal:", error)
      set({ error: error as Error, loading: false })
    }
  },

  fetchTransactions: async (savingsId: string) => {
    try {
      set({ loading: true, error: null })
      const transactions = await TripSavingsService.getSavingsTransactions(savingsId)
      set({ transactions, loading: false })
    } catch (error) {
      console.error("Error fetching savings transactions:", error)
      set({ error: error as Error, loading: false })
    }
  },

  createSavingsGoal: async (savingsData) => {
    try {
      set({ loading: true, error: null })
      const savingsId = await TripSavingsService.createTripSavings(savingsData)

      // If we're viewing savings goals for this user or trip, refresh the list
      const savingsGoals = get().savingsGoals
      if (savingsGoals.length > 0) {
        if (savingsGoals[0].userId === savingsData.userId) {
          const updatedGoals = await TripSavingsService.getUserTripSavings(savingsData.userId)
          set({ savingsGoals: updatedGoals })
        } else if (savingsGoals[0].tripId === savingsData.tripId) {
          const updatedGoals = await TripSavingsService.getTripSavingsForTrip(
            savingsData.tripId,
            true
          )
          set({ savingsGoals: updatedGoals })
        }
      }

      set({ loading: false })
      return savingsId
    } catch (error) {
      console.error("Error creating savings goal:", error)
      set({ error: error as Error, loading: false })
      return null
    }
  },

  updateSavingsGoal: async (savingsId, savingsData) => {
    try {
      set({ loading: true, error: null })
      const result = await TripSavingsService.updateTripSavings(savingsId, savingsData)

      if (result.success) {
        // Update the savings goal in the list
        const savingsGoals = get().savingsGoals
        const updatedGoals = await Promise.all(
          savingsGoals.map(async (goal) => {
            if (goal.id === savingsId) {
              const updatedGoal = await TripSavingsService.getTripSavings(savingsId)
              return updatedGoal || goal
            }
            return goal
          })
        )

        // Update current savings goal if it's the one being edited
        if (get().currentSavingsGoal?.id === savingsId) {
          const updatedGoal = await TripSavingsService.getTripSavings(savingsId)
          if (updatedGoal) {
            set({ currentSavingsGoal: updatedGoal })
          }
        }

        set({ savingsGoals: updatedGoals.filter(Boolean) as TripSavings[] })
      }

      set({ loading: false })
      return result.success
    } catch (error) {
      console.error("Error updating savings goal:", error)
      set({ error: error as Error, loading: false })
      return false
    }
  },

  addTransaction: async (transactionData) => {
    try {
      set({ loading: true, error: null })
      const transactionId = await TripSavingsService.addSavingsTransaction(transactionData)

      // Refresh transactions if we're viewing them for this savings goal
      if (
        get().transactions.length > 0 &&
        get().transactions[0].savingsId === transactionData.savingsId
      ) {
        const updatedTransactions = await TripSavingsService.getSavingsTransactions(
          transactionData.savingsId
        )
        set({ transactions: updatedTransactions })
      }

      // Update the current savings goal if it's the one being modified
      if (get().currentSavingsGoal?.id === transactionData.savingsId) {
        const updatedGoal = await TripSavingsService.getTripSavings(transactionData.savingsId)
        if (updatedGoal) {
          set({ currentSavingsGoal: updatedGoal })
        }
      }

      // Update the savings goal in the list
      const savingsGoals = get().savingsGoals
      const updatedGoals = await Promise.all(
        savingsGoals.map(async (goal) => {
          if (goal.id === transactionData.savingsId) {
            const updatedGoal = await TripSavingsService.getTripSavings(transactionData.savingsId)
            return updatedGoal || goal
          }
          return goal
        })
      )

      set({ savingsGoals: updatedGoals.filter(Boolean) as TripSavings[], loading: false })
      return transactionId
    } catch (error) {
      console.error("Error adding transaction:", error)
      set({ error: error as Error, loading: false })
      return null
    }
  },

  clearSavingsGoals: () => {
    set({ savingsGoals: [], currentSavingsGoal: null })
  },

  clearTransactions: () => {
    set({ transactions: [] })
  },
}))
