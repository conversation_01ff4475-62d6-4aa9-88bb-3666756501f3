"use client"

import { useEffect, useState } from "react"
import { useTripSavingsStore } from "./trip-savings.store"
import {
  TripSavings,
  TripSavingsCreateData,
  TripSavingsUpdateData,
  SavingsTransaction,
  SavingsTransactionCreateData,
} from "./trip-savings.types"
import { useUser } from "@/lib/domains/auth/auth.hooks"

/**
 * Hook to get all savings goals for the current user
 */
export const useUserSavingsGoals = () => {
  const user = useUser()
  const { savingsGoals, loading, error, fetchUserSavingsGoals } = useTripSavingsStore()

  useEffect(() => {
    if (user?.uid) {
      fetchUserSavingsGoals(user.uid)
    }
  }, [user, fetchUserSavingsGoals])

  return { savingsGoals, loading, error }
}

/**
 * Hook to get all savings goals for a trip
 */
export const useTripSavingsGoals = (tripId: string, includePrivate = false) => {
  const { savingsGoals, loading, error, fetchTripSavingsGoals } = useTripSavingsStore()

  useEffect(() => {
    if (tripId) {
      fetchTripSavingsGoals(tripId, includePrivate)
    }
  }, [tripId, includePrivate, fetchTripSavingsGoals])

  return { savingsGoals, loading, error }
}

/**
 * Hook to get a specific savings goal
 */
export const useSavingsGoal = (savingsId: string) => {
  const { currentSavingsGoal, loading, error, fetchSavingsGoal } = useTripSavingsStore()

  useEffect(() => {
    if (savingsId) {
      fetchSavingsGoal(savingsId)
    }
  }, [savingsId, fetchSavingsGoal])

  return { savingsGoal: currentSavingsGoal, loading, error }
}

/**
 * Hook to get transactions for a savings goal
 */
export const useSavingsTransactions = (savingsId: string) => {
  const { transactions, loading, error, fetchTransactions } = useTripSavingsStore()

  useEffect(() => {
    if (savingsId) {
      fetchTransactions(savingsId)
    }
  }, [savingsId, fetchTransactions])

  return { transactions, loading, error }
}

/**
 * Hook to create a savings goal
 */
export const useCreateSavingsGoal = () => {
  const [creating, setCreating] = useState(false)
  const [error, setError] = useState<Error | null>(null)
  const { createSavingsGoal } = useTripSavingsStore()

  const create = async (savingsData: TripSavingsCreateData) => {
    try {
      setCreating(true)
      setError(null)
      const savingsId = await createSavingsGoal(savingsData)
      setCreating(false)
      return savingsId
    } catch (err) {
      setError(err as Error)
      setCreating(false)
      return null
    }
  }

  return { create, creating, error }
}

/**
 * Hook to update a savings goal
 */
export const useUpdateSavingsGoal = (savingsId?: string) => {
  const [updating, setUpdating] = useState(false)
  const [error, setError] = useState<Error | null>(null)
  const { updateSavingsGoal } = useTripSavingsStore()

  const update = async (data: TripSavingsUpdateData, id?: string) => {
    const targetId = id || savingsId
    if (!targetId) {
      setError(new Error("No savings goal ID provided"))
      return false
    }

    try {
      setUpdating(true)
      setError(null)
      const success = await updateSavingsGoal(targetId, data)
      setUpdating(false)
      return success
    } catch (err) {
      setError(err as Error)
      setUpdating(false)
      return false
    }
  }

  return { update, updating, error }
}

/**
 * Hook to add a transaction to a savings goal
 */
export const useAddSavingsTransaction = (savingsId?: string) => {
  const user = useUser()
  const [adding, setAdding] = useState(false)
  const [error, setError] = useState<Error | null>(null)
  const { addTransaction } = useTripSavingsStore()

  const add = async (
    data: Omit<SavingsTransactionCreateData, "userId" | "savingsId">,
    id?: string
  ) => {
    const targetId = id || savingsId
    if (!targetId) {
      setError(new Error("No savings goal ID provided"))
      return null
    }

    if (!user?.uid) {
      setError(new Error("User not authenticated"))
      return null
    }

    try {
      setAdding(true)
      setError(null)
      const transactionId = await addTransaction({
        ...data,
        userId: user.uid,
        savingsId: targetId,
      })
      setAdding(false)
      return transactionId
    } catch (err) {
      setError(err as Error)
      setAdding(false)
      return null
    }
  }

  return { add, adding, error }
}
