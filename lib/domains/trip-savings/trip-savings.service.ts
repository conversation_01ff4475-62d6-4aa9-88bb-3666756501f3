import { db } from "@/lib/firebase"
import {
  collection,
  doc,
  getDoc,
  getDocs,
  updateDoc,
  query,
  where,
  serverTimestamp,
  setDoc,
  increment,
} from "firebase/firestore"
import { BaseService } from "../base/base.service"
import { ServiceResponse } from "../base/base.types"
import {
  TripSavings,
  TripSavingsCreateData,
  TripSavingsUpdateData,
  SavingsTransaction,
  SavingsTransactionCreateData,
} from "./trip-savings.types"

/**
 * Trip Savings service for Firebase operations
 */
export class TripSavingsService {
  private static readonly COLLECTION = "tripSavings"
  private static readonly TRANSACTIONS_COLLECTION = "savingsTransactions"

  /**
   * Create a new trip savings goal
   * @param savingsData Trip savings data
   * @returns The new trip savings ID
   */
  static async createTripSavings(savingsData: TripSavingsCreateData): Promise<string> {
    try {
      const savingsRef = doc(collection(db, this.COLLECTION))
      const savingsId = savingsRef.id

      const newSavings = {
        ...savingsData,
        id: savingsId,
        savedAmount: 0,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
      }

      await setDoc(savingsRef, newSavings)
      return savingsId
    } catch (error) {
      console.error("Error creating trip savings:", error)
      throw error
    }
  }

  /**
   * Get a trip savings goal by ID
   * @param savingsId Trip savings ID
   * @returns The trip savings data or null if not found
   */
  static async getTripSavings(savingsId: string): Promise<TripSavings | null> {
    try {
      const savingsDoc = await getDoc(doc(db, this.COLLECTION, savingsId))

      if (savingsDoc.exists()) {
        return { ...savingsDoc.data(), id: savingsId } as TripSavings
      }

      return null
    } catch (error) {
      console.error("Error getting trip savings:", error)
      throw error
    }
  }

  /**
   * Get trip savings goals for a user
   * @param userId User ID
   * @returns Array of trip savings goals
   */
  static async getUserTripSavings(userId: string): Promise<TripSavings[]> {
    try {
      const q = query(collection(db, this.COLLECTION), where("userId", "==", userId))
      const querySnapshot = await getDocs(q)

      return querySnapshot.docs.map((doc) => ({ ...doc.data(), id: doc.id }) as TripSavings)
    } catch (error) {
      console.error("Error getting user trip savings:", error)
      throw error
    }
  }

  /**
   * Get trip savings goals for a trip
   * @param tripId Trip ID
   * @param includePrivate Whether to include private savings goals
   * @returns Array of trip savings goals
   */
  static async getTripSavingsForTrip(
    tripId: string,
    includePrivate = false
  ): Promise<TripSavings[]> {
    try {
      let q

      if (includePrivate) {
        q = query(collection(db, this.COLLECTION), where("tripId", "==", tripId))
      } else {
        q = query(
          collection(db, this.COLLECTION),
          where("tripId", "==", tripId),
          where("isPrivate", "==", false)
        )
      }

      const querySnapshot = await getDocs(q)

      return querySnapshot.docs.map((doc) => ({ ...doc.data(), id: doc.id }) as TripSavings)
    } catch (error) {
      console.error("Error getting trip savings for trip:", error)
      throw error
    }
  }

  /**
   * Update a trip savings goal
   * @param savingsId Trip savings ID
   * @param savingsData Trip savings data to update
   * @returns Service response
   */
  static async updateTripSavings(
    savingsId: string,
    savingsData: TripSavingsUpdateData
  ): Promise<ServiceResponse> {
    try {
      await updateDoc(doc(db, this.COLLECTION, savingsId), {
        ...savingsData,
        updatedAt: serverTimestamp(),
      })

      return { success: true }
    } catch (error) {
      console.error("Error updating trip savings:", error)
      return { success: false, error }
    }
  }

  /**
   * Add a transaction to a savings goal
   * @param transactionData Transaction data
   * @returns The new transaction ID
   */
  static async addSavingsTransaction(
    transactionData: SavingsTransactionCreateData
  ): Promise<string> {
    try {
      const transactionRef = doc(collection(db, this.TRANSACTIONS_COLLECTION))
      const transactionId = transactionRef.id

      // Create the transaction
      await setDoc(transactionRef, {
        ...transactionData,
        id: transactionId,
        createdAt: serverTimestamp(),
      })

      // Update the savings goal's saved amount
      await updateDoc(doc(db, this.COLLECTION, transactionData.savingsId), {
        savedAmount: increment(transactionData.amount),
        updatedAt: serverTimestamp(),
      })

      return transactionId
    } catch (error) {
      console.error("Error adding savings transaction:", error)
      throw error
    }
  }

  /**
   * Get transactions for a savings goal
   * @param savingsId Savings goal ID
   * @returns Array of transactions
   */
  static async getSavingsTransactions(savingsId: string): Promise<SavingsTransaction[]> {
    try {
      const q = query(
        collection(db, this.TRANSACTIONS_COLLECTION),
        where("savingsId", "==", savingsId)
      )

      const querySnapshot = await getDocs(q)

      return querySnapshot.docs.map((doc) => ({ ...doc.data(), id: doc.id }) as SavingsTransaction)
    } catch (error) {
      console.error("Error getting savings transactions:", error)
      throw error
    }
  }
}
