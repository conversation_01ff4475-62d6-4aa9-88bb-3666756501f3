// Export all types and functions from the user-subscription domain

// Core types
export * from "./user-subscription.types"
export * from "./user-subscription.errors"

// New flat architecture services
export * from "./flat-subscription.service"
export * from "./flat-subscription.api"
export * from "./flat-subscription.realtime.hooks"
export * from "./flat-subscription-cron.service"
// Note: flat-subscription.webhooks is not exported to avoid bundling admin service in client code

// Migration and cleanup services
export * from "./clean-migration.service"

// Perk integration services
export * from "./perk-subscription.service"
export * from "./perk-aware-subscription.service"
export * from "./subscription-aggregation.service"

// Legacy services (deprecated but may still be used)
export * from "./user-subscription.service"
export * from "./user-subscription.store"
export * from "./user-subscription.hooks"
export * from "./user-subscription.realtime.hooks"
export * from "./user-subscription.realtime.service"
