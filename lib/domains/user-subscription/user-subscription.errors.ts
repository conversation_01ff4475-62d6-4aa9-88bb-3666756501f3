import { SUBSCRIPTION_LIMITS, SubscriptionErrorType } from "./user-subscription.types"

// Re-export SubscriptionErrorType for convenience
export { SubscriptionErrorType }
import { toast } from "@/components/ui/use-toast"
import { useRouter } from "next/navigation"

/**
 * Subscription Error Messages
 */
export const SUBSCRIPTION_ERROR_MESSAGES = {
  [SubscriptionErrorType.MAX_SQUADS_REACHED]: {
    free: {
      title: "Subscription limit reached",
      description: `You've reached the maximum number of squads (${SUBSCRIPTION_LIMITS.FREE.MAX_SQUADS}) for the free plan. Upgrade to get more squads.`,
    },
    pro: {
      title: "Subscription limit reached",
      description: `You've reached the maximum number of squads (${SUBSCRIPTION_LIMITS.PRO.MAX_SQUADS}) for the pro plan. Need more squads? Reach <NAME_EMAIL>`,
    },
  },
  [SubscriptionErrorType.MAX_TRIPS_PER_SQUAD_REACHED]: {
    free: {
      title: "Subscription limit reached",
      description: `You've reached the maximum number of trips (${SUBSCRIPTION_LIMITS.FREE.MAX_TRIPS_PER_SQUAD}) for this squad on the free plan. Upgrade to get more trips.`,
    },
    pro: {
      title: "Subscription limit reached",
      description: `You've reached the maximum number of trips (${SUBSCRIPTION_LIMITS.PRO.MAX_TRIPS_PER_SQUAD}) for this squad on the pro plan. Need more trips? Reach <NAME_EMAIL>`,
    },
  },
  [SubscriptionErrorType.DAILY_AI_LIMIT_REACHED]: {
    free: {
      title: "AI usage limit reached",
      description: `You've reached your limit of AI suggestions. Upgrade to Pro for unlimited suggestions.`,
    },
    pro: {
      title: "AI usage limit reached",
      description: `You've reached your limit of AI suggestions. Upgrade to Pro for unlimited suggestions.`,
    },
  },
  [SubscriptionErrorType.WEEKLY_AI_LIMIT_REACHED]: {
    free: {
      title: "AI usage limit reached",
      description: `You've reached your limit of AI suggestions. Upgrade to Pro for unlimited suggestions.`,
    },
    pro: {
      title: "AI usage limit reached",
      description: `You've reached your limit of AI suggestions. Upgrade to Pro for unlimited suggestions.`,
    },
  },
  [SubscriptionErrorType.TRIP_AI_LIMIT_REACHED]: {
    free: {
      title: "Trip suggestions limit reached",
      description: `You've reached your limit of trip suggestions. Upgrade to Pro for unlimited suggestions.`,
    },
    pro: {
      title: "Trip suggestions limit reached",
      description: `You've reached your limit of trip suggestions. Upgrade to Pro for unlimited suggestions.`,
    },
  },
  [SubscriptionErrorType.TASK_AI_LIMIT_REACHED]: {
    free: {
      title: "Task suggestions limit reached",
      description: `You've reached your limit of task suggestions. Upgrade to Pro for unlimited suggestions.`,
    },
    pro: {
      title: "Task suggestions limit reached",
      description: `You've reached your limit of task suggestions. Upgrade to Pro for unlimited suggestions.`,
    },
  },
  [SubscriptionErrorType.ITINERARY_AI_LIMIT_REACHED]: {
    free: {
      title: "Activity suggestions limit reached",
      description: `You've reached your limit of activity suggestions. Upgrade to Pro for unlimited suggestions.`,
    },
    pro: {
      title: "Activity suggestions limit reached",
      description: `You've reached your limit of activity suggestions. Upgrade to Pro for unlimited suggestions.`,
    },
  },
  [SubscriptionErrorType.GENERIC_ERROR]: {
    free: {
      title: "Subscription error",
      description:
        "There was an error with your subscription. Please try again or contact support.",
    },
    pro: {
      title: "Subscription error",
      description:
        "There was an error with your subscription. Please try again or contact support.",
    },
  },
}

/**
 * Shows a subscription error toast and redirects to the billing page
 */
export const handleSubscriptionError = (
  errorType: SubscriptionErrorType,
  isSubscribed: boolean = false,
  router?: ReturnType<typeof useRouter>
) => {
  const planType = isSubscribed ? "pro" : "free"
  const errorMessage = SUBSCRIPTION_ERROR_MESSAGES[errorType][planType]

  // Show toast with error message
  toast({
    title: errorMessage.title,
    description: errorMessage.description,
    variant: "destructive",
  })

  // Redirect to billing page if router is provided
  if (router) {
    router.push("/settings?tab=billing")
  }

  return false
}

/**
 * Creates an alert message for subscription errors
 */
export const getSubscriptionErrorAlert = (
  errorType: SubscriptionErrorType,
  isSubscribed: boolean = false
) => {
  console.log("getSubscriptionErrorAlert", {
    errorType,
    isSubscribed,
  })
  const planType = isSubscribed ? "pro" : "free"
  const errorMessage = SUBSCRIPTION_ERROR_MESSAGES[errorType][planType]

  return {
    title: errorMessage.title,
    description: errorMessage.description,
  }
}
