import { getFirestore } from "firebase-admin/firestore"
import { ServiceResponse } from "../base/base.types"
import {
  UserSubscription,
  UserSubscriptionEntry,
  createStripeSubscriptionEntry,
  createFreeSubscriptionEntry,
  StripeSubscriptionData,
  SUBSCRIPTION_PRECEDENCE,
} from "./user-subscription.types"

/**
 * Admin Migration Service for converting to flat subscription structure
 * Uses Firebase Admin SDK for server-side operations
 */
export class AdminMigrationService {
  private static readonly OLD_COLLECTION = "userSubscriptions"
  private static readonly NEW_COLLECTION = "userSubscriptions" // Same collection, different structure
  private static readonly BACKUP_COLLECTION = "userSubscriptionsBackup"
  private static readonly BATCH_SIZE = 50

  /**
   * Backup existing subscription data before migration
   */
  static async backupExistingData(): Promise<
    ServiceResponse<{
      backedUpCount: number
      errors: string[]
    }>
  > {
    try {
      console.log("🔄 Starting data backup...")
      const db = getFirestore()

      const subscriptionsSnapshot = await db.collection(this.OLD_COLLECTION).get()
      const errors: string[] = []
      let backedUpCount = 0

      const batch = db.batch()

      for (const subscriptionDoc of subscriptionsSnapshot.docs) {
        try {
          const subscriptionData = subscriptionDoc.data()

          // Create backup document
          const backupRef = db.collection(this.BACKUP_COLLECTION).doc(subscriptionDoc.id)
          batch.set(backupRef, {
            ...subscriptionData,
            backedUpAt: new Date(),
            originalId: subscriptionDoc.id,
          })

          backedUpCount++
        } catch (error) {
          const errorMsg = `Failed to backup subscription ${subscriptionDoc.id}: ${error}`
          console.error(errorMsg)
          errors.push(errorMsg)
        }
      }

      await batch.commit()
      console.log(`✅ Backup completed: ${backedUpCount} subscriptions backed up`)

      return {
        success: true,
        data: { backedUpCount, errors },
      }
    } catch (error) {
      console.error("❌ Backup failed:", error)
      return { success: false, error }
    }
  }

  /**
   * Migrate a single user's subscription to flat structure
   */
  static async migrateUserSubscription(userId: string): Promise<
    ServiceResponse<{
      migrated: boolean
      entriesCreated: number
      reason?: string
    }>
  > {
    try {
      const db = getFirestore()

      // Get existing subscription document
      const subscriptionDoc = await db.collection(this.OLD_COLLECTION).doc(userId).get()

      if (!subscriptionDoc.exists) {
        // No existing subscription - just create free entry
        await this.addFreeSubscription(userId)
        return {
          success: true,
          data: {
            migrated: true,
            entriesCreated: 1,
            reason: "Created free subscription for user without existing subscription",
          },
        }
      }

      const subscriptionData = subscriptionDoc.data() as UserSubscription
      let entriesCreated = 0

      // Use a transaction to ensure atomicity
      await db.runTransaction(async (transaction) => {
        let hasStripeSubscription = false

        // Create Stripe subscription entry if user has Stripe data (regardless of status)
        if (
          subscriptionData.stripeCustomerId &&
          subscriptionData.subscriptionId &&
          subscriptionData.subscriptionPlan !== "free"
        ) {
          const stripeEntry = createStripeSubscriptionEntry(userId, {
            customerId: subscriptionData.stripeCustomerId,
            subscriptionId: subscriptionData.subscriptionId,
            subscriptionStatus: subscriptionData.subscriptionStatus || "active",
            subscriptionPlan: subscriptionData.subscriptionPlan,
            currentPeriodEnd: subscriptionData.subscriptionCurrentPeriodEnd as any,
          } as StripeSubscriptionData)

          const stripeEntryRef = db.collection(this.NEW_COLLECTION).doc()
          transaction.set(stripeEntryRef, stripeEntry)
          entriesCreated++
          hasStripeSubscription = true

          console.log(
            `  🎯 Created Stripe entry for user ${userId} (${subscriptionData.subscriptionPlan})`
          )
        }

        // Create a free subscription entry as fallback
        // If user has Stripe subscription, set free entry as "pending" (lower precedence)
        const freeEntry = createFreeSubscriptionEntry(userId)
        if (hasStripeSubscription) {
          freeEntry.status = "pending"
        }

        const freeEntryRef = db.collection(this.NEW_COLLECTION).doc()
        transaction.set(freeEntryRef, freeEntry)
        entriesCreated++

        console.log(
          `  🆓 Created free entry for user ${userId} (${hasStripeSubscription ? "pending" : "applied"})`
        )

        // Delete old subscription document
        const oldDocRef = db.collection(this.OLD_COLLECTION).doc(userId)
        transaction.delete(oldDocRef)
      })

      return {
        success: true,
        data: {
          migrated: true,
          entriesCreated,
          reason: `Migrated ${entriesCreated} entries for user`,
        },
      }
    } catch (error) {
      console.error(`❌ Error migrating user ${userId}:`, error)
      return {
        success: false,
        error,
        data: {
          migrated: false,
          entriesCreated: 0,
          reason: error instanceof Error ? error.message : "Unknown error",
        },
      }
    }
  }

  /**
   * Add a free subscription entry for a user
   */
  private static async addFreeSubscription(userId: string): Promise<void> {
    const db = getFirestore()
    const freeEntry = createFreeSubscriptionEntry(userId)
    await db.collection(this.NEW_COLLECTION).add(freeEntry)
  }

  /**
   * Migrate all users to flat structure
   */
  static async migrateAllUsers(): Promise<
    ServiceResponse<{
      totalProcessed: number
      successfulMigrations: number
      totalEntriesCreated: number
      errors: string[]
    }>
  > {
    try {
      console.log("🚀 Starting complete migration to flat structure...")
      const db = getFirestore()

      // First, backup existing data
      const backupResult = await this.backupExistingData()
      if (!backupResult.success) {
        return { success: false, error: backupResult.error }
      }

      // Get all users and existing subscriptions
      const [usersSnapshot, subscriptionsSnapshot] = await Promise.all([
        db.collection("users").get(),
        db.collection(this.OLD_COLLECTION).get(),
      ])

      const errors: string[] = []
      let totalProcessed = 0
      let successfulMigrations = 0
      let totalEntriesCreated = 0

      // Get all user IDs (both with and without subscriptions)
      const allUserIds = usersSnapshot.docs.map((doc) => doc.id)
      const userIdsWithSubscriptions = new Set(subscriptionsSnapshot.docs.map((doc) => doc.id))

      console.log(`📊 Found ${allUserIds.length} total users`)
      console.log(`📊 Found ${userIdsWithSubscriptions.size} users with existing subscriptions`)
      console.log(
        `📊 Found ${allUserIds.length - userIdsWithSubscriptions.size} users without subscriptions`
      )

      // Process in batches to avoid overwhelming the database
      const userIds = allUserIds

      for (let i = 0; i < userIds.length; i += this.BATCH_SIZE) {
        const batch = userIds.slice(i, i + this.BATCH_SIZE)

        for (const userId of batch) {
          totalProcessed++

          try {
            const migrationResult = await this.migrateUserSubscription(userId)

            if (migrationResult.success) {
              successfulMigrations++
              totalEntriesCreated += migrationResult.data!.entriesCreated
              console.log(`✅ Migrated user ${userId}: ${migrationResult.data!.reason}`)
            } else {
              const errorMessage =
                typeof migrationResult.error === "string"
                  ? migrationResult.error
                  : (migrationResult.error as any)?.message || "Unknown error"
              const errorMsg = `Failed to migrate user ${userId}: ${errorMessage}`
              errors.push(errorMsg)
              console.error(`❌ ${errorMsg}`)
            }
          } catch (error) {
            const errorMsg = `Exception during migration for user ${userId}: ${error}`
            errors.push(errorMsg)
            console.error(`💥 ${errorMsg}`)
          }
        }

        // Add a small delay between batches to avoid rate limiting
        if (i + this.BATCH_SIZE < userIds.length) {
          await new Promise((resolve) => setTimeout(resolve, 100))
        }
      }

      console.log(`\n📊 Migration Summary:`)
      console.log(`  Total processed: ${totalProcessed}`)
      console.log(`  Successful: ${successfulMigrations}`)
      console.log(`  Entries created: ${totalEntriesCreated}`)
      console.log(`  Errors: ${errors.length}`)

      return {
        success: true,
        data: {
          totalProcessed,
          successfulMigrations,
          totalEntriesCreated,
          errors,
        },
      }
    } catch (error) {
      console.error("❌ Migration failed:", error)
      return { success: false, error }
    }
  }

  /**
   * Validate migration results
   */
  static async validateMigration(): Promise<
    ServiceResponse<{
      isValid: boolean
      issues: string[]
      statistics: {
        totalUsers: number
        usersWithSubscriptions: number
        totalEntries: number
        freeEntries: number
        stripeEntries: number
        perkEntries: number
        giveawayEntries: number
        oldStructureEntries: number
        usersWithoutFreeEntry: number
      }
    }>
  > {
    try {
      console.log("🔍 Validating migration results...")
      const db = getFirestore()

      const issues: string[] = []

      // Get all users
      const usersSnapshot = await db.collection("users").get()
      const totalUsers = usersSnapshot.size

      // Get all subscription entries
      const entriesSnapshot = await db.collection(this.NEW_COLLECTION).get()
      const totalEntries = entriesSnapshot.size

      let freeEntries = 0
      let stripeEntries = 0
      let perkEntries = 0
      let giveawayEntries = 0
      let oldStructureEntries = 0
      const userIdsWithSubscriptions = new Set<string>()

      for (const entryDoc of entriesSnapshot.docs) {
        const entryData = entryDoc.data()

        // Check if it's new flat structure (has 'source' field)
        if (entryData.source) {
          const entry = entryData as UserSubscriptionEntry
          userIdsWithSubscriptions.add(entry.userId)

          if (entry.source === "free") {
            freeEntries++
          } else if (entry.source === "stripe") {
            stripeEntries++
          } else if (entry.source === "perk") {
            perkEntries++
          } else if (entry.source === "giveaway") {
            giveawayEntries++
          }
        }
        // Check if it's old structure (document ID matches userId)
        else if (entryDoc.id === entryData.userId) {
          oldStructureEntries++
          userIdsWithSubscriptions.add(entryData.userId)
        }
      }

      const usersWithSubscriptions = userIdsWithSubscriptions.size

      // Check that every user has at least a subscription entry
      const usersWithoutFreeEntry = totalUsers - userIdsWithSubscriptions.size

      if (usersWithoutFreeEntry > 0) {
        issues.push(`${usersWithoutFreeEntry} users don't have any subscription entries`)
      }

      // Check if migration is complete (no old structure entries)
      if (oldStructureEntries > 0) {
        issues.push(
          `${oldStructureEntries} old structure entries still exist (migration not complete)`
        )
      }

      // Check for duplicate applied entries per user (only for new structure)
      const userAppliedCounts = new Map<string, number>()
      for (const entryDoc of entriesSnapshot.docs) {
        const entryData = entryDoc.data()

        // Only check new structure entries
        if (entryData.source && entryData.status === "applied") {
          const entry = entryData as UserSubscriptionEntry
          const count = userAppliedCounts.get(entry.userId) || 0
          userAppliedCounts.set(entry.userId, count + 1)
        }
      }

      for (const [userId, count] of userAppliedCounts.entries()) {
        if (count > 1) {
          issues.push(`User ${userId} has ${count} applied subscription entries (should be 1)`)
        }
      }

      const isValid = issues.length === 0

      return {
        success: true,
        data: {
          isValid,
          issues,
          statistics: {
            totalUsers,
            usersWithSubscriptions,
            totalEntries,
            freeEntries,
            stripeEntries,
            perkEntries,
            giveawayEntries,
            oldStructureEntries,
            usersWithoutFreeEntry,
          },
        },
      }
    } catch (error) {
      console.error("❌ Validation failed:", error)
      return { success: false, error }
    }
  }

  /**
   * Emergency rollback (restore from backup)
   */
  static async emergencyRollback(): Promise<
    ServiceResponse<{
      restoredCount: number
      deletedCount: number
      errors: string[]
    }>
  > {
    try {
      console.log("🚨 Starting emergency rollback...")
      const db = getFirestore()

      const errors: string[] = []
      let restoredCount = 0
      let deletedCount = 0

      // Get backup data
      const backupSnapshot = await db.collection(this.BACKUP_COLLECTION).get()

      // Delete all new entries
      const entriesSnapshot = await db.collection(this.NEW_COLLECTION).get()
      const batch = db.batch()

      for (const entryDoc of entriesSnapshot.docs) {
        batch.delete(entryDoc.ref)
        deletedCount++
      }

      // Restore original documents
      for (const backupDoc of backupSnapshot.docs) {
        try {
          const backupData = backupDoc.data()
          const { backedUpAt, originalId, ...originalData } = backupData

          const originalRef = db.collection(this.OLD_COLLECTION).doc(originalId)
          batch.set(originalRef, originalData)
          restoredCount++
        } catch (error) {
          const errorMsg = `Failed to restore backup ${backupDoc.id}: ${error}`
          console.error(errorMsg)
          errors.push(errorMsg)
        }
      }

      await batch.commit()

      console.log(`✅ Rollback completed:`)
      console.log(`  📦 Restored: ${restoredCount} original subscriptions`)
      console.log(`  🗑️  Deleted: ${deletedCount} flat entries`)

      return {
        success: true,
        data: { restoredCount, deletedCount, errors },
      }
    } catch (error) {
      console.error("❌ Rollback failed:", error)
      return { success: false, error }
    }
  }
}
