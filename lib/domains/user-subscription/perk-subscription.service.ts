import { ServiceResponse } from "../base/base.types"
import { PerkService } from "../perk/perk.service"
import { PerkSubscriptionData, UserSubscriptionEntry } from "./user-subscription.types"
import { FlatSubscriptionService } from "./flat-subscription.service"

/**
 * Perk application result interface
 */
interface PerkApplicationResult {
  success: boolean
  error?: string
  perkId?: string
  appliedAt?: Date
  expiresAt?: Date
}

/**
 * Service for integrating perk system with multi-entry subscription system
 */
export class PerkSubscriptionService {
  /**
   * Apply a subscription perk by creating a subscription entry
   */
  static async applySubscriptionPerk(
    userId: string,
    perkId: string
  ): Promise<PerkApplicationResult> {
    try {
      // Get the user perk
      const userPerks = await PerkService.getUserPerks(userId)
      const userPerk = userPerks.find((p) => p.perkId === perkId)

      if (!userPerk) {
        return {
          success: false,
          error: "User perk not found",
        }
      }

      if (userPerk.status !== "unlocked") {
        return {
          success: false,
          error: "Perk is not in unlocked status",
        }
      }

      if (userPerk.perkDetails.perkType !== "subscription") {
        return {
          success: false,
          error: "Perk is not a subscription perk",
        }
      }

      // Check if perk is already applied
      const existingSubscriptions = await FlatSubscriptionService.getUserSubscriptions(userId)
      const existingPerkEntry = existingSubscriptions.find(
        (entry: UserSubscriptionEntry) =>
          entry.source === "perk" &&
          (entry.subscriptionData as PerkSubscriptionData).perkId === perkId
      )

      if (existingPerkEntry) {
        return {
          success: false,
          error: "Perk subscription already applied",
        }
      }

      // Get perk duration from perk value
      const perkValue = userPerk.perkDetails.perkValue
      const duration = perkValue.duration || 30 // Default to 30 days if not specified

      // Create perk subscription data
      const perkData: PerkSubscriptionData = {
        perkId,
        appliedAt: new Date() as any,
        duration,
      }

      // Create subscription entry using flat service
      const createResult = await FlatSubscriptionService.addPerkSubscription(userId, perkData)

      if (!createResult.success) {
        return {
          success: false,
          error:
            typeof createResult.error === "string"
              ? createResult.error
              : (createResult.error as any)?.message || "Failed to create perk subscription entry",
        }
      }

      // Update perk status to applied
      await PerkService.updateUserPerk(userId, perkId, {
        status: "applied",
        appliedAt: new Date() as any,
      })

      console.log(`Applied subscription perk ${perkId} for user ${userId}`)

      return {
        success: true,
        perkId,
        appliedAt: new Date(),
        expiresAt: new Date(Date.now() + duration * 24 * 60 * 60 * 1000),
      }
    } catch (error) {
      console.error("Error applying subscription perk:", error)
      return {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error",
      }
    }
  }

  /**
   * Check for expired perk subscriptions and update their status
   */
  static async processExpiredPerkSubscriptions(userId: string): Promise<{
    expiredCount: number
    errors: string[]
  }> {
    try {
      const allSubscriptions = await FlatSubscriptionService.getUserSubscriptions(userId)
      const perkEntries = allSubscriptions.filter((entry) => entry.source === "perk")
      const errors: string[] = []
      let expiredCount = 0

      for (const entry of perkEntries) {
        if (entry.status !== "expired" && entry.endDate) {
          const endDate = entry.endDate instanceof Date ? entry.endDate : entry.endDate.toDate()
          const now = new Date()

          if (endDate <= now) {
            // Mark entry as expired
            const updateResult = await FlatSubscriptionService.updateSubscriptionEntry(entry.id, {
              status: "expired",
            })

            if (updateResult.success) {
              expiredCount++

              // Update corresponding perk status
              const perkData = entry.subscriptionData as PerkSubscriptionData
              if (perkData?.perkId) {
                await PerkService.updateUserPerk(userId, perkData.perkId, {
                  status: "expired",
                })
              }
            } else {
              errors.push(`Failed to expire perk subscription entry ${entry.id}`)
            }
          }
        }
      }

      return { expiredCount, errors }
    } catch (error) {
      console.error("Error processing expired perk subscriptions:", error)
      return { expiredCount: 0, errors: ["Failed to process expired perk subscriptions"] }
    }
  }

  /**
   * Get all active perk subscriptions for a user
   */
  static async getActivePerkSubscriptions(userId: string): Promise<
    Array<{
      entryId: string
      perkId: string
      perkName: string
      appliedAt: Date
      expiresAt: Date | null
      status: string
    }>
  > {
    try {
      const allSubscriptions = await FlatSubscriptionService.getUserSubscriptions(userId)
      const perkEntries = allSubscriptions.filter((entry) => entry.source === "perk")
      const activePerkSubscriptions = []

      for (const entry of perkEntries) {
        const perkData = entry.subscriptionData as PerkSubscriptionData
        if (perkData && entry.status !== "expired") {
          const appliedAt =
            perkData.appliedAt instanceof Date ? perkData.appliedAt : perkData.appliedAt.toDate()

          const expiresAt = entry.endDate
            ? entry.endDate instanceof Date
              ? entry.endDate
              : entry.endDate.toDate()
            : null

          // Get perk details
          const userPerks = await PerkService.getUserPerks(userId)
          const userPerk = userPerks.find((p) => p.perkId === perkData.perkId)
          const perkName = userPerk?.perkDetails.name || "Unknown Perk"

          activePerkSubscriptions.push({
            entryId: entry.id,
            perkId: perkData.perkId,
            perkName,
            appliedAt,
            expiresAt,
            status: entry.status,
          })
        }
      }

      return activePerkSubscriptions
    } catch (error) {
      console.error("Error getting active perk subscriptions:", error)
      return []
    }
  }

  /**
   * Calculate perk enhancements for subscription limits
   */
  static async calculatePerkEnhancements(userId: string): Promise<{
    additionalSquads: number
    additionalTripsPerSquad: number
    additionalDailyAI: number
    additionalWeeklyAI: number
    activePerkIds: string[]
  }> {
    try {
      const userPerks = await PerkService.getUserPerks(userId)
      const appliedPerks = userPerks.filter((perk) => perk.status === "applied")

      let additionalSquads = 0
      let additionalTripsPerSquad = 0
      let additionalDailyAI = 0
      let additionalWeeklyAI = 0
      const activePerkIds: string[] = []

      for (const perk of appliedPerks) {
        activePerkIds.push(perk.perkId)

        // Add perk value enhancements
        const perkValue = perk.perkDetails.perkValue

        if (perkValue.maxSquads) {
          additionalSquads += perkValue.maxSquads
        }

        if (perkValue.maxTripsPerSquad) {
          additionalTripsPerSquad += perkValue.maxTripsPerSquad
        }

        if (perkValue.additionalDailyAI) {
          additionalDailyAI += perkValue.additionalDailyAI
        }

        if (perkValue.additionalWeeklyAI) {
          additionalWeeklyAI += perkValue.additionalWeeklyAI
        }
      }

      return {
        additionalSquads,
        additionalTripsPerSquad,
        additionalDailyAI,
        additionalWeeklyAI,
        activePerkIds,
      }
    } catch (error) {
      console.error("Error calculating perk enhancements:", error)
      return {
        additionalSquads: 0,
        additionalTripsPerSquad: 0,
        additionalDailyAI: 0,
        additionalWeeklyAI: 0,
        activePerkIds: [],
      }
    }
  }

  /**
   * Check if a user has any active subscription perks
   */
  static async hasActiveSubscriptionPerks(userId: string): Promise<boolean> {
    try {
      const allSubscriptions = await FlatSubscriptionService.getUserSubscriptions(userId)
      const perkEntries = allSubscriptions.filter((entry) => entry.source === "perk")
      return perkEntries.some((entry: UserSubscriptionEntry) => entry.status === "applied")
    } catch (error) {
      console.error("Error checking active subscription perks:", error)
      return false
    }
  }

  /**
   * Get subscription perk summary
   */
  static async getSubscriptionPerkSummary(userId: string): Promise<{
    totalPerks: number
    activePerks: number
    expiredPerks: number
    upcomingExpirations: Array<{
      perkId: string
      perkName: string
      expiresAt: Date
    }>
  }> {
    try {
      const activePerkSubscriptions = await this.getActivePerkSubscriptions(userId)
      const totalPerks = activePerkSubscriptions.length
      const activePerks = activePerkSubscriptions.filter((p) => p.status === "applied").length
      const expiredPerks = activePerkSubscriptions.filter((p) => p.status === "expired").length

      // Get upcoming expirations (within 7 days)
      const sevenDaysFromNow = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000)
      const upcomingExpirations = activePerkSubscriptions
        .filter((p) => p.expiresAt && p.expiresAt <= sevenDaysFromNow && p.status === "applied")
        .map((p) => ({
          perkId: p.perkId,
          perkName: p.perkName,
          expiresAt: p.expiresAt!,
        }))
        .sort((a, b) => a.expiresAt.getTime() - b.expiresAt.getTime())

      return {
        totalPerks,
        activePerks,
        expiredPerks,
        upcomingExpirations,
      }
    } catch (error) {
      console.error("Error getting subscription perk summary:", error)
      return {
        totalPerks: 0,
        activePerks: 0,
        expiredPerks: 0,
        upcomingExpirations: [],
      }
    }
  }

  /**
   * Cleanup expired perk subscription entries
   */
  static async cleanupExpiredPerkEntries(userId: string): Promise<ServiceResponse> {
    try {
      const allSubscriptions = await FlatSubscriptionService.getUserSubscriptions(userId)
      const perkEntries = allSubscriptions.filter((entry) => entry.source === "perk")
      const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
      let cleanedCount = 0

      for (const entry of perkEntries) {
        if (entry.status === "expired") {
          const updatedAt =
            entry.updatedAt instanceof Date ? entry.updatedAt : entry.updatedAt?.toDate()

          if (updatedAt && updatedAt < thirtyDaysAgo) {
            const deleteResult = await FlatSubscriptionService.deleteSubscriptionEntry(entry.id)

            if (deleteResult.success) {
              cleanedCount++
            }
          }
        }
      }

      console.log(`Cleaned up ${cleanedCount} expired perk subscription entries for user ${userId}`)
      return { success: true }
    } catch (error) {
      console.error("Error cleaning up expired perk entries:", error)
      return { success: false, error }
    }
  }
}
