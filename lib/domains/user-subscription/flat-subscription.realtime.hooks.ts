"use client"

import { useEffect, useState, useCallback } from "react"
import {
  collection,
  onSnapshot,
  query,
  where,
  orderBy,
  limit,
  Unsubscribe,
} from "firebase/firestore"
import { db } from "@/lib/firebase"
import { UserSubscriptionEntry } from "./user-subscription.types"
import { FlatSubscriptionService } from "./flat-subscription.service"

/**
 * Hook for real-time current subscription (applied entry only)
 */
export function useCurrentSubscriptionRealtime(userId: string | null) {
  const [currentSubscription, setCurrentSubscription] = useState<UserSubscriptionEntry | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<Error | null>(null)

  useEffect(() => {
    if (!userId) {
      setCurrentSubscription(null)
      setLoading(false)
      setError(null)
      return
    }

    setLoading(true)
    setError(null)

    const subscriptionsRef = collection(db, "userSubscriptions")
    const q = query(
      subscriptionsRef,
      where("userId", "==", userId),
      where("status", "==", "applied"),
      limit(1)
    )

    const unsubscribe = onSnapshot(
      q,
      (snapshot) => {
        try {
          if (snapshot.empty) {
            setCurrentSubscription(null)
          } else {
            const doc = snapshot.docs[0]
            setCurrentSubscription({
              id: doc.id,
              ...doc.data(),
            } as UserSubscriptionEntry)
          }
          setLoading(false)
          setError(null)
        } catch (err) {
          console.error("Error processing current subscription snapshot:", err)
          setError(err as Error)
          setLoading(false)
        }
      },
      (err) => {
        console.error("Error in current subscription subscription:", err)
        setError(err as Error)
        setLoading(false)
      }
    )

    return () => unsubscribe()
  }, [userId])

  return { currentSubscription, loading, error }
}

/**
 * Hook for real-time all user subscriptions
 */
export function useAllSubscriptionsRealtime(userId: string | null) {
  const [allSubscriptions, setAllSubscriptions] = useState<UserSubscriptionEntry[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<Error | null>(null)

  useEffect(() => {
    if (!userId) {
      setAllSubscriptions([])
      setLoading(false)
      setError(null)
      return
    }

    setLoading(true)
    setError(null)

    const subscriptionsRef = collection(db, "userSubscriptions")
    const q = query(subscriptionsRef, where("userId", "==", userId), orderBy("precedence", "asc"))

    const unsubscribe = onSnapshot(
      q,
      (snapshot) => {
        try {
          const subscriptions = snapshot.docs.map((doc) => ({
            id: doc.id,
            ...doc.data(),
          })) as UserSubscriptionEntry[]

          setAllSubscriptions(subscriptions)
          setLoading(false)
          setError(null)
        } catch (err) {
          console.error("Error processing all subscriptions snapshot:", err)
          setError(err as Error)
          setLoading(false)
        }
      },
      (err) => {
        console.error("Error in all subscriptions subscription:", err)
        setError(err as Error)
        setLoading(false)
      }
    )

    return () => unsubscribe()
  }, [userId])

  return { allSubscriptions, loading, error }
}

/**
 * Hook for real-time subscription summary
 */
export function useSubscriptionSummaryRealtime(userId: string | null) {
  const [summary, setSummary] = useState<any>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<Error | null>(null)

  const refreshSummary = useCallback(async () => {
    if (!userId) {
      setSummary(null)
      setLoading(false)
      return
    }

    try {
      setLoading(true)
      const summaryData = await FlatSubscriptionService.getSubscriptionSummary(userId)
      setSummary(summaryData)
      setError(null)
    } catch (err) {
      console.error("Error fetching subscription summary:", err)
      setError(err as Error)
    } finally {
      setLoading(false)
    }
  }, [userId])

  // Subscribe to subscription changes and refresh summary
  const { allSubscriptions } = useAllSubscriptionsRealtime(userId)

  useEffect(() => {
    refreshSummary()
  }, [refreshSummary, allSubscriptions])

  return {
    summary,
    loading,
    error,
    refresh: refreshSummary,
  }
}

/**
 * Hook for real-time subscription limits
 */
export function useSubscriptionLimitsRealtime(userId: string | null) {
  const [limits, setLimits] = useState<any>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<Error | null>(null)

  const refreshLimits = useCallback(async () => {
    if (!userId) {
      setLimits(null)
      setLoading(false)
      return
    }

    try {
      setLoading(true)
      const limitsData = await FlatSubscriptionService.getSubscriptionLimits(userId)
      setLimits(limitsData)
      setError(null)
    } catch (err) {
      console.error("Error fetching subscription limits:", err)
      setError(err as Error)
    } finally {
      setLoading(false)
    }
  }, [userId])

  // Subscribe to current subscription changes and refresh limits
  const { currentSubscription } = useCurrentSubscriptionRealtime(userId)

  useEffect(() => {
    refreshLimits()
  }, [refreshLimits, currentSubscription])

  return {
    limits,
    loading,
    error,
    refresh: refreshLimits,
  }
}

/**
 * Hook for multi-user subscriptions (perfect for squad member badges)
 */
export function useMultiUserSubscriptions(userIds: string[]) {
  const [subscriptions, setSubscriptions] = useState<UserSubscriptionEntry[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<Error | null>(null)

  const fetchSubscriptions = useCallback(async () => {
    if (userIds.length === 0) {
      setSubscriptions([])
      setLoading(false)
      return
    }

    try {
      setLoading(true)
      const multiUserSubscriptions =
        await FlatSubscriptionService.getMultiUserSubscriptions(userIds)
      setSubscriptions(multiUserSubscriptions)
      setError(null)
    } catch (err) {
      console.error("Error fetching multi-user subscriptions:", err)
      setError(err as Error)
    } finally {
      setLoading(false)
    }
  }, [userIds])

  useEffect(() => {
    fetchSubscriptions()
  }, [fetchSubscriptions])

  return {
    subscriptions,
    loading,
    error,
    refresh: fetchSubscriptions,
  }
}

/**
 * Comprehensive hook that combines all subscription data for a single user
 */
export function useComprehensiveSubscriptionRealtime(userId: string | null) {
  const {
    currentSubscription,
    loading: currentLoading,
    error: currentError,
  } = useCurrentSubscriptionRealtime(userId)
  const {
    allSubscriptions,
    loading: allLoading,
    error: allError,
  } = useAllSubscriptionsRealtime(userId)
  const {
    summary,
    loading: summaryLoading,
    error: summaryError,
  } = useSubscriptionSummaryRealtime(userId)
  const {
    limits,
    loading: limitsLoading,
    error: limitsError,
  } = useSubscriptionLimitsRealtime(userId)

  const loading = currentLoading || allLoading || summaryLoading || limitsLoading
  const error = currentError || allError || summaryError || limitsError

  return {
    // Core subscription data
    currentSubscription,
    allSubscriptions,
    summary,
    limits,
    loading,
    error,

    // Computed values
    isSubscribed: currentSubscription?.source !== "free",
    subscriptionPlan:
      currentSubscription?.source === "stripe"
        ? (currentSubscription.subscriptionData as any).subscriptionPlan
        : currentSubscription?.source || "free",
    currentSource: currentSubscription?.source || null,

    // Limits (with defaults)
    maxSquads: limits?.maxSquads || 1,
    maxTripsPerSquad: limits?.maxTripsPerSquad || 2,
    maxDailyAIRequests: limits?.maxDailyAIRequests || 10,
    maxWeeklyAIRequests: limits?.maxWeeklyAIRequests || 50,
    hasTripChat: limits?.hasTripChat || false,

    // Summary data
    totalSubscriptions: summary?.totalSubscriptions || 0,
    activeSubscriptions: summary?.activeSubscriptions || 0,
    pausedSubscriptions: summary?.pausedSubscriptions || 0,
    expiredSubscriptions: summary?.expiredSubscriptions || 0,
  }
}

/**
 * Hook for checking feature access in real-time
 */
export function useFeatureAccessRealtime(userId: string | null, feature: string) {
  const [hasAccess, setHasAccess] = useState(false)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<Error | null>(null)

  const checkAccess = useCallback(async () => {
    if (!userId) {
      setHasAccess(false)
      setLoading(false)
      return
    }

    try {
      setLoading(true)
      const access = await FlatSubscriptionService.hasFeatureAccess(userId, feature)
      setHasAccess(access)
      setError(null)
    } catch (err) {
      console.error("Error checking feature access:", err)
      setError(err as Error)
      setHasAccess(false)
    } finally {
      setLoading(false)
    }
  }, [userId, feature])

  // Subscribe to current subscription changes and recheck access
  const { currentSubscription } = useCurrentSubscriptionRealtime(userId)

  useEffect(() => {
    checkAccess()
  }, [checkAccess, currentSubscription])

  return {
    hasAccess,
    loading,
    error,
    refresh: checkAccess,
  }
}
