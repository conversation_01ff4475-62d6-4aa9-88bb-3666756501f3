"use client"

import { useState, useEffect } from "react"
import { UserSubscriptionEntry, UserSubscriptionsMap } from "./user-subscription.types"
import { UserSubscriptionRealtimeService } from "./user-subscription.realtime.service"
import { useUser } from "../auth/auth.hooks"

/**
 * Hook to get real-time updates for a user's subscription (flat structure)
 */
export const useRealtimeUserSubscription = () => {
  const user = useUser()
  const userId = user?.uid || ""

  const [subscription, setSubscription] = useState<UserSubscriptionEntry | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<Error | null>(null)

  useEffect(() => {
    if (!userId) {
      setSubscription(null)
      setLoading(false)
      return () => {}
    }

    setLoading(true)

    const unsubscribe = UserSubscriptionRealtimeService.subscribeToUserSubscription(
      userId,
      (data, err) => {
        if (err) {
          console.error("Error getting real-time user subscription:", err)
          setError(err)
          setLoading(false)
          return
        }

        setSubscription(data)
        setLoading(false)
      }
    )

    return () => unsubscribe()
  }, [userId])

  return { subscription, loading, error }
}

/**
 * Hook to get real-time updates for multiple users' subscriptions
 * Returns a map of user IDs to subscription status (true if subscribed)
 */
export const useRealtimeUserSubscriptions = (userIds?: string[]) => {
  const [subscriptions, setSubscriptions] = useState<UserSubscriptionsMap>({})
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<Error | null>(null)

  // If userIds is not provided, use an empty array
  const userIdsToUse = userIds || []

  useEffect(() => {
    if (!userIdsToUse.length) {
      setSubscriptions({})
      setLoading(false)
      return () => {}
    }

    setLoading(true)

    const unsubscribe = UserSubscriptionRealtimeService.subscribeToUserSubscriptions(
      userIdsToUse,
      (data, err) => {
        if (err) {
          console.error("Error getting real-time user subscriptions:", err)
          setError(err)
          setLoading(false)
          return
        }

        setSubscriptions(data)
        setLoading(false)
      }
    )

    return () => unsubscribe()
  }, [userIdsToUse.join(",")]) // Use join to create a stable dependency

  return { subscriptions, loading, error }
}
