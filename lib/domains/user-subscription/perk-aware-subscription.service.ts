import { PerkService } from "../perk/perk.service"
import { PerkSubscriptionService } from "./perk-subscription.service"
import { AI_USAGE_LIMITS } from "../user-ai-usage/user-ai-usage.types"
import { SubscriptionAggregationService } from "./subscription-aggregation.service"

/**
 * Enhanced subscription service that considers both subscription status and active perks
 */
export class PerkAwareSubscriptionService {
  /**
   * Get enhanced user limits considering both subscription and perks
   * @deprecated Use SubscriptionAggregationService.getEnhancedSubscriptionLimits instead
   */
  static async getEnhancedUserLimits(userId: string) {
    // Simply delegate to the new service
    return await SubscriptionAggregationService.getEnhancedSubscriptionLimits(userId)
  }

  /**
   * Check if user can create more squads (perk-aware)
   */
  static async canCreateMoreSquads(userId: string, currentSquadCount?: number): Promise<boolean> {
    try {
      const limits = await this.getEnhancedUserLimits(userId)

      // If we have infinite squads, return true
      if (limits.maxSquads === Infinity) return true

      // If we already have the count, use it
      if (typeof currentSquadCount === "number") {
        return currentSquadCount < limits.maxSquads
      }

      // Otherwise, fetch the current count
      const { SquadService } = await import("../squad/squad.service")
      const userSquads = await SquadService.getUserSquads(userId)
      return userSquads.length < limits.maxSquads
    } catch (error) {
      console.error("Error checking if user can create more squads:", error)
      return false
    }
  }

  /**
   * Check if user can create more trips in a squad (perk-aware)
   */
  static async canCreateMoreTripsInSquad(
    userId: string,
    squadId: string,
    currentTripCount?: number
  ): Promise<boolean> {
    try {
      const limits = await this.getEnhancedUserLimits(userId)

      // If we have infinite trips, return true
      if (limits.maxTripsPerSquad === Infinity) return true

      // If we already have the count, use it
      if (typeof currentTripCount === "number") {
        return currentTripCount < limits.maxTripsPerSquad
      }

      // Otherwise, fetch the current count
      const { TripService } = await import("../trip/trip.service")
      const squadTrips = await TripService.getSquadTrips(squadId)

      // Only count non-completed trips (planning, upcoming, active)
      const activeTrips = squadTrips.filter(
        (trip) =>
          trip.status === "planning" || trip.status === "upcoming" || trip.status === "active"
      )

      return activeTrips.length < limits.maxTripsPerSquad
    } catch (error) {
      console.error("Error checking if user can create more trips in squad:", error)
      return false
    }
  }

  /**
   * Get enhanced AI usage limits (perk-aware)
   */
  static async getEnhancedAILimits(userId: string) {
    try {
      const limits = await this.getEnhancedUserLimits(userId)

      return {
        dailyLimit: limits.maxDailyAIRequests,
        weeklyLimit: limits.maxWeeklyAIRequests,
        perkEnhancements: limits.perkEnhancements,
      }
    } catch (error) {
      console.error("Error getting enhanced AI limits:", error)
      return {
        dailyLimit: AI_USAGE_LIMITS.FREE.DAILY,
        weeklyLimit: AI_USAGE_LIMITS.FREE.WEEKLY,
        perkEnhancements: {
          additionalDailyAI: 0,
          additionalWeeklyAI: 0,
          activePerkIds: [],
        },
      }
    }
  }

  /**
   * Apply subscription perks on login
   * FIXED: Only apply perks that users have actually earned and unlocked
   */
  static async applySubscriptionPerksOnLogin(userId: string): Promise<void> {
    try {
      // Get user's subscription perks that need processing
      const userPerks = await PerkService.getUserPerks(userId)

      // CRITICAL FIX: Only process perks that are actually unlocked by the user
      // and have valid unlocked/applied status with proper timestamps
      const validSubscriptionPerks = userPerks.filter((perk) => {
        // Must be a subscription perk
        if (
          perk.perkDetails.perkType !== "subscription" ||
          !perk.perkDetails.tags.includes("subscription")
        ) {
          return false
        }

        // Must have valid status
        if (perk.status !== "unlocked" && perk.status !== "applied") {
          return false
        }

        // Must have been properly unlocked (has unlockedAt timestamp)
        if (!perk.unlockedAt) {
          console.warn(
            `Perk ${perk.perkId} has status ${perk.status} but no unlockedAt timestamp - skipping`
          )
          return false
        }

        // For applied perks, must have appliedAt timestamp
        if (perk.status === "applied" && !perk.appliedAt) {
          console.warn(
            `Perk ${perk.perkId} has applied status but no appliedAt timestamp - skipping`
          )
          return false
        }

        return true
      })

      console.log(
        `Found ${validSubscriptionPerks.length} valid subscription perks to process for user ${userId} (filtered from ${userPerks.length} total perks)`
      )

      // If no valid perks, exit early
      if (validSubscriptionPerks.length === 0) {
        return
      }

      // Check which perks need flat subscription entries
      const { FlatSubscriptionService } = await import("./flat-subscription.service")
      const existingSubscriptions = await FlatSubscriptionService.getUserSubscriptions(userId)

      for (const perk of validSubscriptionPerks) {
        try {
          // Check if this perk already has a flat subscription entry
          const existingPerkEntry = existingSubscriptions.find(
            (entry: any) =>
              entry.source === "perk" && entry.subscriptionData?.perkId === perk.perkId
          )

          if (existingPerkEntry) {
            console.log(`Perk ${perk.perkId} already has flat subscription entry, skipping`)
            continue
          }

          // Apply the perk (this will create the flat subscription entry)
          if (perk.status === "unlocked") {
            const result = await PerkSubscriptionService.applySubscriptionPerk(userId, perk.perkId)
            if (result.success) {
              console.log(`Applied subscription perk ${perk.perkId} for user ${userId}`)
              console.log(`Subscription perk expires at: ${result.expiresAt}`)
            } else {
              console.error(`Failed to apply subscription perk ${perk.perkId}:`, result.error)
            }
          } else if (perk.status === "applied") {
            // For already "applied" perks, create the flat subscription entry directly
            console.log(`Creating flat subscription entry for already applied perk ${perk.perkId}`)

            const perkValue = perk.perkDetails.perkValue
            const duration = perkValue.duration || 30

            const perkData = {
              perkId: perk.perkId,
              appliedAt: perk.appliedAt || (new Date() as any),
              duration,
            }

            const createResult = await FlatSubscriptionService.addPerkSubscription(userId, perkData)
            if (createResult.success) {
              console.log(`Created flat subscription entry for perk ${perk.perkId}`)
            } else {
              console.error(
                `Failed to create flat subscription entry for perk ${perk.perkId}:`,
                createResult.error
              )
            }
          }
        } catch (error) {
          console.error(`Error processing subscription perk ${perk.perkId}:`, error)
        }
      }
    } catch (error) {
      console.error("Error applying subscription perks on login:", error)
    }
  }
}
