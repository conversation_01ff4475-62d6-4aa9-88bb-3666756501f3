import { getFirestore, FieldValue } from "firebase-admin/firestore"
import { ServiceResponse } from "../base/base.types"
import {
  UserSubscriptionEntry,
  UserSubscriptionEntryUpdateData,
  SubscriptionEntryStatus,
  StripeSubscriptionData,
  createStripeSubscriptionEntry,
  validateSubscriptionEntry,
  SUBSCRIPTION_PRECEDENCE,
} from "./user-subscription.types"

/**
 * Admin version of flat subscription service using Firebase Admin SDK
 * Used for server-side operations like webhooks
 */
export class FlatSubscriptionAdminService {
  private static readonly COLLECTION = "userSubscriptions"

  /**
   * Get current active subscription for a user
   */
  static async getCurrentSubscription(userId: string): Promise<UserSubscriptionEntry | null> {
    try {
      const db = getFirestore()
      const snapshot = await db
        .collection(this.COLLECTION)
        .where("userId", "==", userId)
        .where("status", "==", "applied")
        .orderBy("precedence", "asc") // Order by precedence to get highest priority first
        .limit(1)
        .get()

      if (snapshot.empty) return null

      const doc = snapshot.docs[0]
      return { id: doc.id, ...doc.data() } as UserSubscriptionEntry
    } catch (error) {
      console.error("Error getting current subscription:", error)
      return null
    }
  }

  /**
   * Get all subscriptions for a user
   */
  static async getUserSubscriptions(userId: string): Promise<UserSubscriptionEntry[]> {
    try {
      const db = getFirestore()
      const snapshot = await db.collection(this.COLLECTION).where("userId", "==", userId).get()

      const subscriptions = snapshot.docs.map((doc) => ({
        id: doc.id,
        ...doc.data(),
      })) as UserSubscriptionEntry[]

      // Sort by precedence in memory to avoid index requirement
      return subscriptions.sort((a, b) => a.precedence - b.precedence)
    } catch (error) {
      console.error("Error getting user subscriptions:", error)
      return []
    }
  }

  /**
   * Add a Stripe subscription entry
   */
  static async addStripeSubscription(
    userId: string,
    stripeData: StripeSubscriptionData
  ): Promise<ServiceResponse<UserSubscriptionEntry>> {
    try {
      const db = getFirestore()

      // Check if subscription already exists
      const existingSubscriptions = await this.getUserSubscriptions(userId)
      const existingStripeEntry = existingSubscriptions.find(
        (sub) =>
          sub.source === "stripe" &&
          (sub.subscriptionData as StripeSubscriptionData).subscriptionId ===
            stripeData.subscriptionId
      )

      if (existingStripeEntry) {
        console.log(
          `⚠️  Subscription ${stripeData.subscriptionId} already exists for user ${userId}, updating instead of creating`
        )
        return await this.updateSubscriptionEntry(existingStripeEntry.id, {
          subscriptionData: stripeData,
        })
      }

      // Create the subscription entry
      const subscriptionEntry = createStripeSubscriptionEntry(userId, stripeData)

      // Validate the entry
      const validationErrors = validateSubscriptionEntry(subscriptionEntry)
      if (validationErrors.length > 0) {
        return {
          success: false,
          error: new Error(`Invalid subscription entry: ${validationErrors.join(", ")}`),
        }
      }

      // Check if user has higher precedence subscriptions
      const hasHigherPrecedence = existingSubscriptions.some(
        (sub) => sub.status === "applied" && sub.precedence < SUBSCRIPTION_PRECEDENCE.STRIPE
      )

      // Set status based on precedence
      const status: SubscriptionEntryStatus = hasHigherPrecedence ? "paused" : "applied"
      const finalEntry = { ...subscriptionEntry, status }

      // If this subscription should be applied, pause existing applied subscriptions
      if (status === "applied") {
        const batch = db.batch()

        // Pause existing applied subscriptions
        for (const existingSub of existingSubscriptions) {
          if (existingSub.status === "applied") {
            const existingRef = db.collection(this.COLLECTION).doc(existingSub.id)
            batch.update(existingRef, {
              status: "paused",
              pausedAt: FieldValue.serverTimestamp(),
              updatedAt: FieldValue.serverTimestamp(),
            })
          }
        }

        // Add new subscription
        const cleanedEntry = this.removeUndefinedValues(finalEntry)
        const newRef = db.collection(this.COLLECTION).doc()
        batch.set(newRef, {
          ...cleanedEntry,
          id: newRef.id,
          createdAt: FieldValue.serverTimestamp(),
          updatedAt: FieldValue.serverTimestamp(),
        })

        await batch.commit()

        return {
          success: true,
          data: {
            ...cleanedEntry,
            id: newRef.id,
            createdAt: new Date() as any,
            updatedAt: new Date() as any,
          },
        }
      } else {
        // Just add the paused subscription
        // Clean the data to remove any undefined values
        const cleanedEntry = this.removeUndefinedValues(finalEntry)

        const newRef = db.collection(this.COLLECTION).doc()
        await newRef.set({
          ...cleanedEntry,
          id: newRef.id,
          createdAt: FieldValue.serverTimestamp(),
          updatedAt: FieldValue.serverTimestamp(),
        })

        return {
          success: true,
          data: {
            ...cleanedEntry,
            id: newRef.id,
            createdAt: new Date() as any,
            updatedAt: new Date() as any,
          },
        }
      }
    } catch (error) {
      console.error("Error creating subscription entry:", error)
      return {
        success: false,
        error: error instanceof Error ? error : new Error("Unknown error"),
      }
    }
  }

  /**
   * Update a subscription entry
   */
  static async updateSubscriptionEntry(
    subscriptionId: string,
    updateData: UserSubscriptionEntryUpdateData
  ): Promise<ServiceResponse<UserSubscriptionEntry>> {
    try {
      const db = getFirestore()
      const subscriptionRef = db.collection(this.COLLECTION).doc(subscriptionId)

      // Get current subscription
      const currentDoc = await subscriptionRef.get()
      if (!currentDoc.exists) {
        return {
          success: false,
          error: new Error("Subscription entry not found"),
        }
      }

      const currentData = currentDoc.data() as UserSubscriptionEntry

      // Prepare update data
      const updatePayload = {
        ...updateData,
        updatedAt: FieldValue.serverTimestamp(),
      }

      // Update the subscription
      await subscriptionRef.update(updatePayload)

      // Return updated subscription
      const updatedData = { ...currentData, ...updateData, id: subscriptionId }
      return {
        success: true,
        data: updatedData,
      }
    } catch (error) {
      console.error("Error updating subscription entry:", error)
      return {
        success: false,
        error: error instanceof Error ? error : new Error("Unknown error"),
      }
    }
  }

  /**
   * Delete a subscription entry
   */
  static async deleteSubscriptionEntry(subscriptionId: string): Promise<ServiceResponse<void>> {
    try {
      const db = getFirestore()
      const subscriptionRef = db.collection(this.COLLECTION).doc(subscriptionId)

      // Get current subscription to check if it was applied
      const currentDoc = await subscriptionRef.get()
      if (!currentDoc.exists) {
        return {
          success: false,
          error: new Error("Subscription entry not found"),
        }
      }

      const currentData = currentDoc.data() as UserSubscriptionEntry
      const wasApplied = currentData.status === "applied"

      // Delete the subscription
      await subscriptionRef.delete()

      // If this was an applied subscription, reactivate the next highest precedence subscription
      if (wasApplied) {
        await this.processExpiredSubscriptions(currentData.userId)
      }

      return { success: true, data: undefined }
    } catch (error) {
      console.error("Error deleting subscription entry:", error)
      return {
        success: false,
        error: error instanceof Error ? error : new Error("Unknown error"),
      }
    }
  }

  /**
   * Process expired subscriptions and reactivate the next highest precedence subscription
   */
  static async processExpiredSubscriptions(userId: string): Promise<ServiceResponse<void>> {
    try {
      const db = getFirestore()

      await db.runTransaction(async (transaction) => {
        // Get all user subscriptions
        const subscriptionsSnapshot = await transaction.get(
          db.collection(this.COLLECTION).where("userId", "==", userId)
        )

        const subscriptions = subscriptionsSnapshot.docs.map((doc) => ({
          id: doc.id,
          ...doc.data(),
        })) as UserSubscriptionEntry[]

        // Find pending subscriptions sorted by precedence
        const pendingSubscriptions = subscriptions
          .filter((sub) => sub.status === "pending" || sub.status === "paused")
          .sort((a, b) => a.precedence - b.precedence)

        if (pendingSubscriptions.length > 0) {
          const nextSubscription = pendingSubscriptions[0]
          const subscriptionRef = db.collection(this.COLLECTION).doc(nextSubscription.id)

          transaction.update(subscriptionRef, {
            status: "applied",
            updatedAt: FieldValue.serverTimestamp(),
          })
        }
      })

      return { success: true, data: undefined }
    } catch (error) {
      console.error("Error processing expired subscriptions:", error)
      return {
        success: false,
        error: error instanceof Error ? error : new Error("Unknown error"),
      }
    }
  }

  /**
   * Helper method to remove undefined values from an object
   */
  private static removeUndefinedValues(obj: any): any {
    if (obj === null || obj === undefined) {
      return obj
    }

    if (Array.isArray(obj)) {
      return obj.map((item) => this.removeUndefinedValues(item))
    }

    if (obj instanceof Date) {
      return obj // Keep Date objects as-is
    }

    if (typeof obj === "object") {
      const cleaned: any = {}
      for (const [key, value] of Object.entries(obj)) {
        if (value !== undefined) {
          cleaned[key] = this.removeUndefinedValues(value)
        }
      }
      return cleaned
    }

    return obj
  }
}
