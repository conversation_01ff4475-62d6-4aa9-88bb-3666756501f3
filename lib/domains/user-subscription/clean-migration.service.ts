import {
  collection,
  doc,
  getDocs,
  getDoc,
  setDoc,
  deleteDoc,
  runTransaction,
  serverTimestamp,
  writeBatch,
} from "firebase/firestore"
import { db } from "@/lib/firebase"
import { ServiceResponse } from "../base/base.types"
import {
  UserSubscription,
  UserSubscriptionEntry,
  createStripeSubscriptionEntry,
  createFreeSubscriptionEntry,
  StripeSubscriptionData,
} from "./user-subscription.types"
import { FlatSubscriptionService } from "./flat-subscription.service"

/**
 * Clean migration service for converting to flat subscription structure
 * No backward compatibility - complete migration approach
 */
export class CleanMigrationService {
  private static readonly OLD_COLLECTION = "userSubscriptions"
  private static readonly NEW_COLLECTION = "userSubscriptions" // Same collection, different structure
  private static readonly BACKUP_COLLECTION = "userSubscriptionsBackup"
  private static readonly BATCH_SIZE = 50

  /**
   * Backup existing subscription data before migration
   */
  static async backupExistingData(): Promise<
    ServiceResponse<{
      backedUpCount: number
      errors: string[]
    }>
  > {
    try {
      console.log("🔄 Starting data backup...")

      const subscriptionsSnapshot = await getDocs(collection(db, this.OLD_COLLECTION))
      const errors: string[] = []
      let backedUpCount = 0

      const batch = writeBatch(db)

      for (const subscriptionDoc of subscriptionsSnapshot.docs) {
        try {
          const subscriptionData = subscriptionDoc.data()

          // Create backup document
          const backupRef = doc(db, this.BACKUP_COLLECTION, subscriptionDoc.id)
          batch.set(backupRef, {
            ...subscriptionData,
            backedUpAt: serverTimestamp(),
            originalId: subscriptionDoc.id,
          })

          backedUpCount++
        } catch (error) {
          const errorMsg = `Failed to backup subscription ${subscriptionDoc.id}: ${error}`
          console.error(errorMsg)
          errors.push(errorMsg)
        }
      }

      await batch.commit()
      console.log(`✅ Backup completed: ${backedUpCount} subscriptions backed up`)

      return {
        success: true,
        data: { backedUpCount, errors },
      }
    } catch (error) {
      console.error("❌ Backup failed:", error)
      return { success: false, error }
    }
  }

  /**
   * Migrate a single user's subscription to flat structure
   */
  static async migrateUserSubscription(userId: string): Promise<
    ServiceResponse<{
      migrated: boolean
      entriesCreated: number
      reason?: string
    }>
  > {
    try {
      // Get existing subscription document
      const subscriptionDoc = await getDoc(doc(db, this.OLD_COLLECTION, userId))

      if (!subscriptionDoc.exists()) {
        // No existing subscription - just create free entry
        await FlatSubscriptionService.addFreeSubscription(userId)
        return {
          success: true,
          data: {
            migrated: true,
            entriesCreated: 1,
            reason: "Created free subscription for user without existing subscription",
          },
        }
      }

      const subscriptionData = subscriptionDoc.data() as UserSubscription
      let entriesCreated = 0

      return await runTransaction(db, async (transaction) => {
        // Create Stripe entry if there's valid Stripe data
        if (
          subscriptionData.stripeCustomerId &&
          subscriptionData.subscriptionId &&
          subscriptionData.subscriptionPlan !== "free"
        ) {
          const stripeData: StripeSubscriptionData = {
            customerId: subscriptionData.stripeCustomerId,
            subscriptionId: subscriptionData.subscriptionId,
            subscriptionStatus: subscriptionData.subscriptionStatus || "active",
            subscriptionPlan: subscriptionData.subscriptionPlan as "monthly" | "yearly",
            currentPeriodEnd: subscriptionData.subscriptionCurrentPeriodEnd || Date.now(),
          }

          const stripeEntryData = createStripeSubscriptionEntry(userId, stripeData)
          const stripeEntryRef = doc(collection(db, this.NEW_COLLECTION))

          transaction.set(stripeEntryRef, {
            ...stripeEntryData,
            createdAt: serverTimestamp(),
            updatedAt: serverTimestamp(),
          })

          entriesCreated++
        }

        // Always create a free entry (will be paused if Stripe is active)
        const freeEntryData = createFreeSubscriptionEntry(userId)
        // If we created a Stripe entry, the free entry should be paused
        if (entriesCreated > 0) {
          freeEntryData.status = "paused"
        }

        const freeEntryRef = doc(collection(db, this.NEW_COLLECTION))
        transaction.set(freeEntryRef, {
          ...freeEntryData,
          createdAt: serverTimestamp(),
          updatedAt: serverTimestamp(),
        })

        entriesCreated++

        // Delete old subscription document
        const oldDocRef = doc(db, this.OLD_COLLECTION, userId)
        transaction.delete(oldDocRef)

        return {
          success: true,
          data: {
            migrated: true,
            entriesCreated,
            reason: `Migrated ${entriesCreated} entries for user`,
          },
        }
      })
    } catch (error) {
      console.error(`❌ Error migrating user ${userId}:`, error)
      return {
        success: false,
        error,
        data: {
          migrated: false,
          entriesCreated: 0,
          reason: error instanceof Error ? error.message : "Unknown error",
        },
      }
    }
  }

  /**
   * Migrate all users to flat structure
   */
  static async migrateAllUsers(): Promise<
    ServiceResponse<{
      totalProcessed: number
      successfulMigrations: number
      totalEntriesCreated: number
      errors: string[]
    }>
  > {
    try {
      console.log("🚀 Starting complete migration to flat structure...")

      // First, backup existing data
      const backupResult = await this.backupExistingData()
      if (!backupResult.success) {
        return { success: false, error: backupResult.error }
      }

      const subscriptionsSnapshot = await getDocs(collection(db, this.OLD_COLLECTION))
      const errors: string[] = []
      let totalProcessed = 0
      let successfulMigrations = 0
      let totalEntriesCreated = 0

      // Process in batches to avoid overwhelming the database
      const userIds = subscriptionsSnapshot.docs.map((doc) => doc.id)

      for (let i = 0; i < userIds.length; i += this.BATCH_SIZE) {
        const batch = userIds.slice(i, i + this.BATCH_SIZE)

        for (const userId of batch) {
          totalProcessed++

          try {
            const migrationResult = await this.migrateUserSubscription(userId)

            if (migrationResult.success) {
              successfulMigrations++
              totalEntriesCreated += migrationResult.data!.entriesCreated
              console.log(`✅ Migrated user ${userId}: ${migrationResult.data!.reason}`)
            } else {
              const errorMessage =
                typeof migrationResult.error === "string"
                  ? migrationResult.error
                  : (migrationResult.error as any)?.message || "Unknown error"
              const errorMsg = `Failed to migrate user ${userId}: ${errorMessage}`
              errors.push(errorMsg)
              console.error(`❌ ${errorMsg}`)
            }
          } catch (error) {
            const errorMsg = `Exception during migration for user ${userId}: ${error}`
            errors.push(errorMsg)
            console.error(`💥 ${errorMsg}`)
          }
        }

        // Small delay between batches
        if (i + this.BATCH_SIZE < userIds.length) {
          await new Promise((resolve) => setTimeout(resolve, 100))
        }
      }

      console.log(
        `🎉 Migration completed: ${successfulMigrations}/${totalProcessed} users migrated, ${totalEntriesCreated} entries created`
      )

      return {
        success: true,
        data: {
          totalProcessed,
          successfulMigrations,
          totalEntriesCreated,
          errors,
        },
      }
    } catch (error) {
      console.error("💥 Migration failed:", error)
      return { success: false, error }
    }
  }

  /**
   * Add transaction logic for new user creation
   */
  static async createUserWithFreeSubscription(
    userId: string,
    userData: any
  ): Promise<ServiceResponse> {
    try {
      return await runTransaction(db, async (transaction) => {
        // Create user document
        const userRef = doc(db, "users", userId)
        transaction.set(userRef, {
          ...userData,
          createdAt: serverTimestamp(),
          updatedAt: serverTimestamp(),
        })

        // Create free subscription entry
        const freeEntryData = createFreeSubscriptionEntry(userId)
        const freeEntryRef = doc(collection(db, this.NEW_COLLECTION))

        transaction.set(freeEntryRef, {
          ...freeEntryData,
          createdAt: serverTimestamp(),
          updatedAt: serverTimestamp(),
        })

        return { success: true }
      })
    } catch (error) {
      console.error("Error creating user with free subscription:", error)
      return { success: false, error }
    }
  }

  /**
   * Validate migration results
   */
  static async validateMigration(): Promise<
    ServiceResponse<{
      isValid: boolean
      issues: string[]
      statistics: {
        totalUsers: number
        usersWithSubscriptions: number
        totalEntries: number
        freeEntries: number
        stripeEntries: number
        usersWithoutFreeEntry: number
      }
    }>
  > {
    try {
      console.log("🔍 Validating migration results...")

      const issues: string[] = []

      // Get all users
      const usersSnapshot = await getDocs(collection(db, "users"))
      const totalUsers = usersSnapshot.size

      // Get all subscription entries
      const entriesSnapshot = await getDocs(collection(db, this.NEW_COLLECTION))
      const totalEntries = entriesSnapshot.size

      const userSubscriptionMap = new Map<string, UserSubscriptionEntry[]>()
      let freeEntries = 0
      let stripeEntries = 0

      // Group entries by user
      for (const entryDoc of entriesSnapshot.docs) {
        const entry = { id: entryDoc.id, ...entryDoc.data() } as UserSubscriptionEntry
        const userId = entry.userId

        if (!userSubscriptionMap.has(userId)) {
          userSubscriptionMap.set(userId, [])
        }
        userSubscriptionMap.get(userId)!.push(entry)

        if (entry.source === "free") freeEntries++
        if (entry.source === "stripe") stripeEntries++
      }

      const usersWithSubscriptions = userSubscriptionMap.size
      let usersWithoutFreeEntry = 0

      // Validate each user
      for (const userDoc of usersSnapshot.docs) {
        const userId = userDoc.id
        const userEntries = userSubscriptionMap.get(userId) || []

        // Check if user has a free entry
        const hasFreeEntry = userEntries.some((entry) => entry.source === "free")
        if (!hasFreeEntry) {
          usersWithoutFreeEntry++
          issues.push(`User ${userId} does not have a free subscription entry`)
        }

        // Check if user has multiple applied entries
        const appliedEntries = userEntries.filter((entry) => entry.status === "applied")
        if (appliedEntries.length > 1) {
          issues.push(`User ${userId} has ${appliedEntries.length} applied entries (should be 1)`)
        }

        // Check if user has no applied entries
        if (appliedEntries.length === 0 && userEntries.length > 0) {
          issues.push(`User ${userId} has subscription entries but none are applied`)
        }
      }

      const isValid = issues.length === 0

      console.log(
        `🔍 Validation completed: ${isValid ? "PASSED" : "FAILED"} with ${issues.length} issues`
      )

      return {
        success: true,
        data: {
          isValid,
          issues,
          statistics: {
            totalUsers,
            usersWithSubscriptions,
            totalEntries,
            freeEntries,
            stripeEntries,
            usersWithoutFreeEntry,
          },
        },
      }
    } catch (error) {
      console.error("❌ Validation failed:", error)
      return { success: false, error }
    }
  }

  /**
   * Emergency rollback (restore from backup)
   */
  static async emergencyRollback(): Promise<
    ServiceResponse<{
      restoredCount: number
      deletedCount: number
      errors: string[]
    }>
  > {
    try {
      console.log("🚨 Starting emergency rollback...")

      const errors: string[] = []
      let restoredCount = 0
      let deletedCount = 0

      // Get backup data
      const backupSnapshot = await getDocs(collection(db, this.BACKUP_COLLECTION))

      // Delete all new entries
      const entriesSnapshot = await getDocs(collection(db, this.NEW_COLLECTION))
      const batch = writeBatch(db)

      for (const entryDoc of entriesSnapshot.docs) {
        batch.delete(entryDoc.ref)
        deletedCount++
      }

      // Restore original subscription documents
      for (const backupDoc of backupSnapshot.docs) {
        const backupData = backupDoc.data()
        const { backedUpAt, originalId, ...originalData } = backupData

        const originalRef = doc(db, this.OLD_COLLECTION, originalId)
        batch.set(originalRef, {
          ...originalData,
          restoredAt: serverTimestamp(),
        })
        restoredCount++
      }

      await batch.commit()

      console.log(`🔄 Rollback completed: ${restoredCount} restored, ${deletedCount} deleted`)

      return {
        success: true,
        data: { restoredCount, deletedCount, errors },
      }
    } catch (error) {
      console.error("💥 Rollback failed:", error)
      return { success: false, error }
    }
  }
}
