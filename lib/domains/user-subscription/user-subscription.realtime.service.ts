import { BaseRealtimeService } from "../base/base.realtime.service"
import { UserSubscription, UserSubscriptionEntry } from "./user-subscription.types"
import { FlatSubscriptionService } from "./flat-subscription.service"
import { query, where, collection } from "firebase/firestore"
import { db } from "@/lib/firebase"

/**
 * User subscription real-time service for Firebase real-time operations
 * Updated to work with flat subscription structure
 */
export class UserSubscriptionRealtimeService {
  private static readonly COLLECTION = "userSubscriptions"

  /**
   * Subscribe to a user's current subscription (flat structure)
   * @param userId User ID
   * @param callback Callback function to handle subscription changes
   * @returns Unsubscribe function
   */
  static subscribeToUserSubscription(
    userId: string,
    callback: (subscription: UserSubscriptionEntry | null, error?: Error) => void
  ): () => void {
    // Subscribe to the user's applied subscription entries
    const q = query(
      collection(db, this.COLLECTION),
      where("userId", "==", userId),
      where("status", "==", "applied")
    )

    return BaseRealtimeService.subscribeToCollection<UserSubscriptionEntry>(q, (entries, error) => {
      if (error) {
        callback(null, error)
        return
      }

      // Get the highest precedence entry (should be only one with status "applied")
      const currentSubscription = entries.length > 0 ? entries[0] : null
      callback(currentSubscription)
    })
  }

  /**
   * Subscribe to all user subscription entries
   * @param callback Callback function to handle subscription entries changes
   * @returns Unsubscribe function
   */
  static subscribeToAllUserSubscriptions(
    callback: (subscriptions: UserSubscriptionEntry[], error?: Error) => void
  ): () => void {
    return BaseRealtimeService.subscribeToCollection<UserSubscriptionEntry>(
      this.COLLECTION,
      callback
    )
  }

  /**
   * Subscribe to multiple user subscriptions by user IDs (for squad member badges)
   * @param userIds Array of user IDs
   * @param callback Callback function to handle subscriptions map
   * @returns Unsubscribe function
   */
  static subscribeToUserSubscriptions(
    userIds: string[],
    callback: (subscriptionsMap: Record<string, boolean>, error?: Error) => void
  ): () => void {
    if (!userIds.length) {
      callback({})
      return () => {}
    }

    // Create a map to track subscriptions
    const subscriptionsMap: Record<string, boolean> = {}
    const unsubscribeFunctions: (() => void)[] = []

    // Subscribe to each user's subscription
    userIds.forEach((userId) => {
      const unsubscribe = this.subscribeToUserSubscription(userId, (subscription, error) => {
        if (error) {
          console.error(`Error getting subscription for user ${userId}:`, error)
          subscriptionsMap[userId] = false
        } else {
          // Check if the user has an active subscription (not free)
          const isSubscribed = subscription?.source !== "free"
          subscriptionsMap[userId] = isSubscribed
        }

        // Call the callback with the updated map
        callback({ ...subscriptionsMap })
      })

      unsubscribeFunctions.push(unsubscribe)
    })

    // Return a function that unsubscribes from all subscriptions
    return () => {
      unsubscribeFunctions.forEach((unsubscribe) => unsubscribe())
    }
  }
}
