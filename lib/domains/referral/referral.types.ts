import { Timestamp } from "firebase/firestore"
import { BaseEntity } from "../base/base.types"

/**
 * Referral code entity stored at referral/{referralCode}
 */
export interface ReferralCode extends BaseEntity {
  userId: string // Owner of the referral code
  totalReferrals: number // Number of successful referrals
  isActive: boolean // Whether the code is active
}

/**
 * User referral tracking entity stored at users/{userId}/referrals/{referralId}
 */
export interface UserReferral extends BaseEntity {
  referredUserId: string // ID of the user who was referred
  referredUserEmail: string // Email of the user who was referred
  referralCode: string // The referral code that was used
  status: "completed" // Status of the referral
}

/**
 * Referral code creation data
 */
export type ReferralCodeCreateData = Omit<ReferralCode, "id" | "createdAt" | "updatedAt">

/**
 * Referral code update data
 */
export type ReferralCodeUpdateData = Partial<Omit<ReferralCode, "id" | "userId" | "createdAt">>

/**
 * User referral creation data
 */
export type UserReferralCreateData = Omit<UserReferral, "id" | "createdAt" | "updatedAt">

/**
 * User referral update data
 */
export type UserReferralUpdateData = Partial<Omit<UserReferral, "id" | "createdAt">>

/**
 * Referral code generation options
 */
export interface ReferralCodeGenerationOptions {
  length?: number // Default: 8
  maxRetries?: number // Default: 10
}

/**
 * Referral processing result
 */
export interface ReferralProcessingResult {
  success: boolean
  referralId?: string
  perksUnlocked?: string[] // Array of perk IDs that were unlocked
  error?: string
}

/**
 * Referral validation result
 */
export interface ReferralValidationResult {
  isValid: boolean
  exists: boolean
  isActive: boolean
  ownerId?: string
  error?: string
}
