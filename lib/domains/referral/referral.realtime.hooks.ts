"use client"

import { useEffect, useState } from "react"
import { useUser } from "../auth/auth.hooks"
import { ReferralRealtimeService } from "./referral.realtime.service"
import { ReferralCode, UserReferral } from "./referral.types"

/**
 * Hook for real-time user referral code updates
 */
export const useUserReferralCodeRealtime = () => {
  const user = useUser()
  const [referralCode, setReferralCode] = useState<ReferralCode | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<Error | null>(null)

  useEffect(() => {
    if (!user?.uid) {
      setReferralCode(null)
      setLoading(false)
      return
    }

    setLoading(true)
    setError(null)

    const unsubscribe = ReferralRealtimeService.subscribeToUserReferralCode(
      user.uid,
      (referralCode) => {
        setReferralCode(referralCode)
        setLoading(false)
      },
      (error) => {
        setError(error)
        setLoading(false)
      }
    )

    return unsubscribe
  }, [user?.uid])

  return { referralCode, loading, error }
}

/**
 * Hook for real-time user referrals updates
 */
export const useUserReferralsRealtime = () => {
  const user = useUser()
  const [referrals, setReferrals] = useState<UserReferral[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<Error | null>(null)

  useEffect(() => {
    if (!user?.uid) {
      setReferrals([])
      setLoading(false)
      return
    }

    setLoading(true)
    setError(null)

    const unsubscribe = ReferralRealtimeService.subscribeToUserReferrals(
      user.uid,
      (referrals) => {
        setReferrals(referrals)
        setLoading(false)
      },
      (error) => {
        setError(error)
        setLoading(false)
      }
    )

    return unsubscribe
  }, [user?.uid])

  return { referrals, loading, error }
}

/**
 * Hook for real-time referral code updates (by code)
 */
export const useReferralCodeRealtime = (code: string | null) => {
  const [referralCode, setReferralCode] = useState<ReferralCode | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<Error | null>(null)

  useEffect(() => {
    if (!code) {
      setReferralCode(null)
      setLoading(false)
      return
    }

    setLoading(true)
    setError(null)

    const unsubscribe = ReferralRealtimeService.subscribeToReferralCode(
      code,
      (referralCode) => {
        setReferralCode(referralCode)
        setLoading(false)
      },
      (error) => {
        setError(error)
        setLoading(false)
      }
    )

    return unsubscribe
  }, [code])

  return { referralCode, loading, error }
}

/**
 * Combined hook for referral data with real-time updates
 */
export const useReferralDataRealtime = () => {
  const user = useUser()
  const { referralCode, loading: codeLoading, error: codeError } = useUserReferralCodeRealtime()
  const { referrals, loading: referralsLoading, error: referralsError } = useUserReferralsRealtime()

  const loading = codeLoading || referralsLoading
  const error = codeError || referralsError

  // Computed values
  const totalReferrals = referralCode?.totalReferrals || 0
  const hasReferralCode = !!referralCode

  const getReferralProgress = (targetCount: number) => ({
    current: totalReferrals,
    target: targetCount,
    percentage: Math.min((totalReferrals / targetCount) * 100, 100),
  })

  // Referral goals
  const goals = [
    {
      id: "squad_perk",
      name: "1 free squad",
      description: "5 referrals = 1 free squad",
      targetReferrals: 5,
      perkType: "squad",
      progress: getReferralProgress(5),
      isCompleted: totalReferrals >= 5,
    },
    {
      id: "subscription_perk",
      name: "PRO for 2 months",
      description: "10 referrals = PRO for 2 months",
      targetReferrals: 10,
      perkType: "subscription",
      progress: getReferralProgress(10),
      isCompleted: totalReferrals >= 10,
    },
  ]

  return {
    user,
    referralCode,
    referrals,
    loading,
    error,
    totalReferrals,
    hasReferralCode,
    goals,
    getReferralProgress,
  }
}
