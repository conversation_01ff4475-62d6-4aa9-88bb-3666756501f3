import { collection, getDocs, doc, setDoc, serverTimestamp } from "firebase/firestore"
import { db } from "@/lib/firebase"
import { ServiceResponse } from "../base/base.types"
import { ReferralService } from "./referral.service"
import { PerkService } from "../perk/perk.service"

/**
 * Health monitoring service for the referral system
 */
export class ReferralHealthService {
  private static readonly HEALTH_LOG_COLLECTION = "referralHealthLogs"

  /**
   * Perform comprehensive health check of the referral system
   */
  static async performHealthCheck(): Promise<
    ServiceResponse<{
      totalReferralCodes: number
      totalUsers: number
      usersWithIssues: number
      commonIssues: Array<{
        issue: string
        count: number
      }>
      systemHealth: "healthy" | "warning" | "critical"
      perkSystemHealth: {
        totalPerks: number
        activePerks: number
        expiredPerks: number
      }
    }>
  > {
    try {
      console.log("🏥 Performing referral system health check...")

      const issueMap = new Map<string, number>()
      let usersWithIssues = 0

      // Check referral codes integrity
      const referralCodesSnapshot = await getDocs(collection(db, "referral"))
      const totalReferralCodes = referralCodesSnapshot.size

      // Check users collection
      const usersSnapshot = await getDocs(collection(db, "users"))
      const totalUsers = usersSnapshot.size

      // Sample users for detailed health check
      const sampleSize = Math.min(50, totalUsers)
      const sampleUsers = usersSnapshot.docs.slice(0, sampleSize)

      for (const userDoc of sampleUsers) {
        try {
          const issues = await this.checkUserReferralHealth(userDoc.id)
          if (issues.length > 0) {
            usersWithIssues++
            issues.forEach((issue) => {
              const count = issueMap.get(issue) || 0
              issueMap.set(issue, count + 1)
            })
          }
        } catch (error) {
          usersWithIssues++
          const issue = "Health check failed for user"
          const count = issueMap.get(issue) || 0
          issueMap.set(issue, count + 1)
        }
      }

      // Check perk system health
      const perkSystemHealth = await this.checkPerkSystemHealth()

      // Determine overall system health
      const issueRate = usersWithIssues / sampleUsers.length
      let systemHealth: "healthy" | "warning" | "critical"

      if (issueRate < 0.05) {
        systemHealth = "healthy"
      } else if (issueRate < 0.2) {
        systemHealth = "warning"
      } else {
        systemHealth = "critical"
      }

      const commonIssues = Array.from(issueMap.entries())
        .map(([issue, count]) => ({ issue, count }))
        .sort((a, b) => b.count - a.count)

      // Log health check results
      await this.logHealthCheck({
        totalReferralCodes,
        totalUsers,
        sampleSize,
        usersWithIssues,
        issueRate,
        systemHealth,
        perkSystemHealth,
      })

      console.log(
        `🏥 Referral health check completed: ${systemHealth} (${usersWithIssues}/${sampleSize} users with issues)`
      )

      return {
        success: true,
        data: {
          totalReferralCodes,
          totalUsers,
          usersWithIssues,
          commonIssues,
          systemHealth,
          perkSystemHealth,
        },
      }
    } catch (error) {
      console.error("❌ Referral health check failed:", error)
      return { success: false, error }
    }
  }

  /**
   * Check referral system health for a single user
   */
  private static async checkUserReferralHealth(userId: string): Promise<string[]> {
    const issues: string[] = []

    try {
      // Check if user has a referral code
      const userReferralCode = await ReferralService.getUserReferralCode(userId)
      if (!userReferralCode.success || !userReferralCode.data) {
        issues.push("User missing referral code")
      }

      // Check user's referrals
      const userReferrals = await ReferralService.getUserReferrals(userId)
      if (userReferrals.success && userReferrals.data) {
        // Check for inconsistent referral counts
        const referralCount = userReferrals.data.length
        if (userReferralCode.success && userReferralCode.data) {
          const codeData = userReferralCode.data
          if (codeData.totalReferrals !== referralCount) {
            issues.push("Inconsistent referral count")
          }
        }
      }

      // Check user's perks
      const userPerks = await PerkService.getUserPerks(userId)
      const referralCount = userReferrals.success ? userReferrals.data?.length || 0 : 0

      // Check if user should have unlocked perks based on referral count
      const eligibilityResults = await PerkService.checkPerkEligibility(userId, referralCount)
      for (const result of eligibilityResults) {
        if (result.isEligible && !result.alreadyUnlocked) {
          issues.push("Missing eligible perk")
        }
      }

      // Check for expired perks that should be marked as expired
      for (const perk of userPerks) {
        if (perk.expiresAt && perk.expiresAt.toMillis() < Date.now() && perk.status !== "expired") {
          issues.push("Perk should be expired")
        }
      }
    } catch (error) {
      issues.push("Failed to check user referral health")
    }

    return issues
  }

  /**
   * Check perk system health
   */
  private static async checkPerkSystemHealth(): Promise<{
    totalPerks: number
    activePerks: number
    expiredPerks: number
  }> {
    try {
      // Get all global perks
      const globalPerks = await PerkService.getGlobalPerks()
      const totalPerks = globalPerks.length
      const activePerks = globalPerks.filter((perk) => perk.isActive).length

      // Sample user perks to check expiration status
      const usersSnapshot = await getDocs(collection(db, "users"))
      let expiredPerks = 0

      // Check a sample of users for expired perks
      const sampleUsers = usersSnapshot.docs.slice(0, 20)
      for (const userDoc of sampleUsers) {
        try {
          const userPerks = await PerkService.getUserPerks(userDoc.id)
          expiredPerks += userPerks.filter((perk) => perk.status === "expired").length
        } catch (error) {
          // Skip users with errors
        }
      }

      return {
        totalPerks,
        activePerks,
        expiredPerks,
      }
    } catch (error) {
      console.error("Error checking perk system health:", error)
      return {
        totalPerks: 0,
        activePerks: 0,
        expiredPerks: 0,
      }
    }
  }

  /**
   * Log health check execution
   */
  private static async logHealthCheck(data: any): Promise<void> {
    try {
      const logDoc = doc(collection(db, this.HEALTH_LOG_COLLECTION))
      await setDoc(logDoc, {
        executedAt: serverTimestamp(),
        ...data,
      })
    } catch (error) {
      console.error("Error logging referral health check:", error)
    }
  }
}
