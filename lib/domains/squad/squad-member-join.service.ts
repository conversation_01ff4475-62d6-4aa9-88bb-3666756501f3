import {
  collection,
  addDoc,
  getDocs,
  query,
  where,
  limit,
  serverTimestamp,
} from "firebase/firestore"
import { db } from "@/lib/firebase"
import { SquadMemberJoin } from "./squad.types"
import { ServiceResponse } from "../base/base.types"

/**
 * Service for tracking squad member joins
 */
export class SquadMemberJoinService {
  private static readonly COLLECTION = "squadMemberJoins"

  /**
   * Track a new member joining a squad
   */
  static async trackMemberJoin(data: {
    squadId: string
    userId: string
    userEmail: string
    userName: string
    joinMethod: "email_invitation" | "shareable_link"
    invitationId?: string
    invitedBy?: string
  }): Promise<ServiceResponse<string>> {
    try {
      const joinData = {
        ...data,
        joinedAt: serverTimestamp(),
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
      }

      const docRef = await addDoc(collection(db, this.COLLECTION), joinData)
      return { success: true, data: docRef.id }
    } catch (error) {
      console.error("Error tracking member join:", error)
      return { success: false, error }
    }
  }

  /**
   * Get join history for a squad (requires squad membership validation)
   */
  static async getSquadJoinHistory(
    squadId: string,
    currentUserId: string,
    limitCount: number = 50
  ): Promise<ServiceResponse<SquadMemberJoin[]>> {
    try {
      // Validate that the current user is a squad member
      const { SquadService } = await import("./squad.service")
      const squad = await SquadService.getSquad(squadId)

      if (!squad) {
        return { success: false, error: new Error("Squad not found") }
      }

      // Check if user is a squad member (leader or regular member)
      const isMember = await SquadService.isUserSquadMember(currentUserId, squadId)
      if (!isMember) {
        return {
          success: false,
          error: new Error("Access denied: Only squad members can view join history"),
        }
      }

      // Use a simpler query to avoid composite index requirement
      const q = query(
        collection(db, this.COLLECTION),
        where("squadId", "==", squadId),
        limit(limitCount)
      )

      const snapshot = await getDocs(q)
      let joins = snapshot.docs.map((doc) => ({
        id: doc.id,
        ...doc.data(),
      })) as SquadMemberJoin[]

      // Sort in memory to avoid composite index requirement
      joins = joins.sort((a, b) => {
        const aTime = a.joinedAt?.toDate ? a.joinedAt.toDate().getTime() : 0
        const bTime = b.joinedAt?.toDate ? b.joinedAt.toDate().getTime() : 0
        return bTime - aTime // desc order
      })

      return { success: true, data: joins }
    } catch (error) {
      console.error("Error getting squad join history:", error)
      return { success: false, error }
    }
  }

  /**
   * Get join record for a specific user in a squad
   */
  static async getUserJoinRecord(
    squadId: string,
    userId: string
  ): Promise<ServiceResponse<SquadMemberJoin | null>> {
    try {
      // Use a simpler query to avoid composite index requirement
      const q = query(
        collection(db, this.COLLECTION),
        where("squadId", "==", squadId),
        where("userId", "==", userId),
        limit(10) // Get a few records and sort in memory
      )

      const snapshot = await getDocs(q)
      if (snapshot.empty) {
        return { success: true, data: null }
      }

      // Get all records and sort in memory to find the most recent
      const joins = snapshot.docs.map((doc) => ({
        id: doc.id,
        ...doc.data(),
      })) as SquadMemberJoin[]

      // Sort by joinedAt desc and take the first one
      const sortedJoins = joins.sort((a, b) => {
        const aTime = a.joinedAt?.toDate ? a.joinedAt.toDate().getTime() : 0
        const bTime = b.joinedAt?.toDate ? b.joinedAt.toDate().getTime() : 0
        return bTime - aTime // desc order
      })

      return { success: true, data: sortedJoins[0] || null }
    } catch (error) {
      console.error("Error getting user join record:", error)
      return { success: false, error }
    }
  }

  /**
   * Get join statistics for a squad
   */
  static async getSquadJoinStats(
    squadId: string,
    currentUserId: string
  ): Promise<
    ServiceResponse<{
      totalJoins: number
      emailInvitations: number
      shareableLinkJoins: number
    }>
  > {
    try {
      // Validate that the current user is a squad member
      const { SquadService } = await import("./squad.service")
      const squad = await SquadService.getSquad(squadId)

      if (!squad) {
        return { success: false, error: new Error("Squad not found") }
      }

      // Check if user is a squad member (leader or regular member)
      const isMember = await SquadService.isUserSquadMember(currentUserId, squadId)
      if (!isMember) {
        return {
          success: false,
          error: new Error("Access denied: Only squad members can view join statistics"),
        }
      }

      const q = query(collection(db, this.COLLECTION), where("squadId", "==", squadId))

      const snapshot = await getDocs(q)
      const joins = snapshot.docs.map((doc) => doc.data()) as SquadMemberJoin[]

      const stats = {
        totalJoins: joins.length,
        emailInvitations: joins.filter((j) => j.joinMethod === "email_invitation").length,
        shareableLinkJoins: joins.filter((j) => j.joinMethod === "shareable_link").length,
      }

      return { success: true, data: stats }
    } catch (error) {
      console.error("Error getting squad join stats:", error)
      return { success: false, error }
    }
  }
}
