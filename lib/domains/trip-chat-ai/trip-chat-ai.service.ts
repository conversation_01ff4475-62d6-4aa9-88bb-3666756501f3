import { TRIP_CHAT_LIMITS } from "../user-ai-usage/user-ai-usage.types"

/**
 * Trip context snapshot for AI requests
 */
export interface TripContextSnapshot {
  destination: string
  startDate: string
  endDate: string
  budget: string
  attendeeCount: number
  tripStatus: string
  description: string
}

/**
 * AI request validation result
 */
export interface AIRequestValidation {
  canMake: boolean
  reason?: "not_pro" | "daily_limit" | "rate_limit" | "invalid_prompt"
  dailyUsage: number
  dailyLimit: number
  remainingRequests: number
}

/**
 * Trip Chat AI Service for client-side operations only
 * Server-side operations are handled by TripChatAIServerService
 */
export class TripChatAIService {
  /**
   * Client-side validation for AI requests (basic validation only)
   * Real validation happens on the server
   */
  static validateAIRequestClient(prompt: string): { isValid: boolean; reason?: string } {
    const trimmedPrompt = prompt.trim()
    if (!trimmedPrompt) {
      return { isValid: false, reason: "Prompt cannot be empty" }
    }

    if (trimmedPrompt.length > TRIP_CHAT_LIMITS.MAX_RESPONSE_LENGTH) {
      return { isValid: false, reason: "Prompt is too long" }
    }

    return { isValid: true }
  }

  /**
   * Build trip context from trip data for AI request
   */
  static buildTripContext(trip: any): TripContextSnapshot {
    return {
      destination: trip.destination || "",
      startDate: trip.startDate?.toDate?.()?.toISOString()?.split("T")[0] || "",
      endDate: trip.endDate?.toDate?.()?.toISOString()?.split("T")[0] || "",
      budget: trip.budget || "",
      attendeeCount: trip.attendees?.length || 0,
      tripStatus: trip.status || "planning",
      description: trip.description || "",
    }
  }
}
