"use client"

import { useState, useCallback, useEffect } from "react"
import { useAuthStatus } from "../auth/auth.hooks"
import { useIsUserSubscribed } from "../user-subscription/user-subscription.hooks"
import { useRealtimeUserAIUsage } from "../user-ai-usage/user-ai-usage.realtime.hooks"
import { AIUsageCategory, TRIP_CHAT_LIMITS } from "../user-ai-usage/user-ai-usage.types"
import { AIRequestValidation, TripChatAIService } from "./trip-chat-ai.service"
import { MessageService } from "../message/message.service"
import { TripService } from "../trip/trip.service"
import { auth } from "@/lib/firebase"
import { toast } from "@/components/ui/use-toast"

/**
 * Hook for managing trip chat AI requests
 */
export const useTripChatAI = (tripId: string) => {
  const { user } = useAuthStatus()
  const isUserSubscribed = useIsUserSubscribed()
  const { getCategoryUsage } = useRealtimeUserAIUsage(isUserSubscribed)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Get trip chat usage data from the real-time hook
  const tripChatUsage = getCategoryUsage(AIUsageCategory.TRIP_CHAT)

  // Calculate validation based on current usage data
  const validation: AIRequestValidation | null =
    user?.uid && isUserSubscribed !== undefined
      ? {
          canMake: isUserSubscribed
            ? (tripChatUsage?.count || 0) < TRIP_CHAT_LIMITS.DAILY_LIMIT
            : false,
          reason: !isUserSubscribed
            ? "not_pro"
            : (tripChatUsage?.count || 0) >= TRIP_CHAT_LIMITS.DAILY_LIMIT
              ? "daily_limit"
              : undefined,
          dailyUsage: tripChatUsage?.count || 0,
          dailyLimit: isUserSubscribed ? TRIP_CHAT_LIMITS.DAILY_LIMIT : 0,
          remainingRequests: isUserSubscribed
            ? Math.max(0, TRIP_CHAT_LIMITS.DAILY_LIMIT - (tripChatUsage?.count || 0))
            : 0,
        }
      : null

  const sendAIRequest = useCallback(
    async (prompt: string): Promise<{ promptMessageId: string; aiResponse: string }> => {
      if (!user?.uid || !user.displayName) {
        throw new Error("User not authenticated")
      }

      if (!tripId) {
        throw new Error("Trip ID is required")
      }

      // Step 1: Client-side validation
      const clientValidation = TripChatAIService.validateAIRequestClient(prompt)
      if (!clientValidation.isValid) {
        throw new Error(clientValidation.reason || "Invalid prompt")
      }

      // Check if user can make request based on real-time data
      if (!validation?.canMake) {
        const reason = validation?.reason
        let errorMessage = "Cannot make AI request"
        switch (reason) {
          case "not_pro":
            errorMessage = "AI chat is available for Pro users only"
            break
          case "daily_limit":
            errorMessage = "Daily AI request limit reached"
            break
          case "rate_limit":
            errorMessage = "Please wait before making another request"
            break
        }
        throw new Error(errorMessage)
      }

      setLoading(true)
      setError(null)

      try {
        // Step 2: Send the actual prompt first in the chat with AI prompt identification
        // Include the full /togeda command in the message content for visibility
        const fullCommand = `/togeda ${prompt.trim()}`
        const promptMessageId = await MessageService.createMessage(tripId, {
          tripId,
          content: fullCommand,
          senderId: user.uid,
          senderName: user.displayName,
          senderPhotoURL: user.photoURL || undefined,
          mentionedUserIds: [],
          messageType: "user", // This is a user message that contains an AI prompt
        })

        // Step 3: Build trip context and parameters on client-side, set loading state
        const trip = await TripService.getTrip(tripId)
        const tripContext = TripChatAIService.buildTripContext(trip)

        // Get Firebase auth token
        const currentUser = auth.currentUser
        if (!currentUser) {
          throw new Error("User not authenticated")
        }

        const token = await currentUser.getIdToken()

        // Call server-side function with all built params
        const response = await fetch("/api/ai", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
          body: JSON.stringify({
            type: "trip_chat",
            data: {
              tripId,
              prompt: prompt.trim(),
              userId: user.uid,
              userDisplayName: user.displayName,
              tripContext,
              maxResponseLength: TRIP_CHAT_LIMITS.MAX_RESPONSE_LENGTH,
            },
          }),
        })

        if (!response.ok) {
          const errorData = await response.json()
          throw new Error(errorData.error || "Failed to process AI request")
        }

        const data = await response.json()
        const aiResponse = data.result

        // Step 4: Save AI response using client-side message service with proper tagging and succession linking
        await MessageService.createMessage(tripId, {
          tripId,
          content: aiResponse,
          senderId: "ai-assistant", // Special sender ID for AI
          senderName: "Togeda AI",
          mentionedUserIds: [],
          isAIResponse: true, // Mark this as an AI response
          aiPrompt: prompt.trim(), // Store the original prompt for analytics
          messageType: "ai_response", // Mark as AI response
        })

        return { promptMessageId, aiResponse }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : "Failed to process AI request"
        setError(errorMessage)

        // Show toast for user-friendly errors
        toast({
          title: "AI Request Failed",
          description: errorMessage,
          variant: "destructive",
        })

        throw error
      } finally {
        setLoading(false)
      }
    },
    [user?.uid, user?.displayName, user?.photoURL, tripId, validation]
  )

  return {
    sendAIRequest,
    loading,
    error,
    validation,
    canMakeRequest: validation?.canMake || false,
    dailyUsage: validation?.dailyUsage || 0,
    dailyLimit: validation?.dailyLimit || 20,
    remainingRequests: validation?.remainingRequests || 0,
  }
}

/**
 * Hook for managing AI usage warning dialog
 */
export const useAIUsageWarning = () => {
  const [hasShownWarning, setHasShownWarning] = useState(false)

  // Check localStorage for warning state
  useEffect(() => {
    const warningShown = localStorage.getItem("togeda-ai-warning-shown")
    setHasShownWarning(warningShown === "true")
  }, [])

  const showWarning = !hasShownWarning

  const dismissWarning = useCallback(() => {
    setHasShownWarning(true)
    localStorage.setItem("togeda-ai-warning-shown", "true")
  }, [])

  const resetWarning = useCallback(() => {
    setHasShownWarning(false)
    localStorage.removeItem("togeda-ai-warning-shown")
  }, [])

  return {
    showWarning,
    dismissWarning,
    resetWarning,
  }
}

/**
 * Hook for detecting /togeda commands in input
 */
export const useTogedaCommand = () => {
  const detectCommand = useCallback((input: string): { isCommand: boolean; prompt: string } => {
    const trimmedInput = input.trim()

    if (trimmedInput.toLowerCase().startsWith("/togeda")) {
      const prompt = trimmedInput.substring(7).trim() // Remove "/togeda" and trim
      return {
        isCommand: true,
        prompt,
      }
    }

    return {
      isCommand: false,
      prompt: "",
    }
  }, [])

  const formatCommand = useCallback((prompt: string): string => {
    return `/togeda ${prompt.trim()}`
  }, [])

  return {
    detectCommand,
    formatCommand,
  }
}

/**
 * Hook for managing AI request state and preventing concurrent requests
 */
export const useAIRequestState = () => {
  const [isProcessing, setIsProcessing] = useState(false)
  const [lastRequestTime, setLastRequestTime] = useState<Date | null>(null)

  const startProcessing = useCallback(() => {
    setIsProcessing(true)
    setLastRequestTime(new Date())
  }, [])

  const stopProcessing = useCallback(() => {
    setIsProcessing(false)
  }, [])

  const canMakeNewRequest = useCallback(() => {
    // Prevent new requests while processing
    if (isProcessing) return false

    // Add a small delay between requests (1 second minimum)
    if (lastRequestTime) {
      const timeSinceLastRequest = Date.now() - lastRequestTime.getTime()
      return timeSinceLastRequest >= 1000
    }

    return true
  }, [isProcessing, lastRequestTime])

  return {
    isProcessing,
    lastRequestTime,
    startProcessing,
    stopProcessing,
    canMakeNewRequest,
  }
}
