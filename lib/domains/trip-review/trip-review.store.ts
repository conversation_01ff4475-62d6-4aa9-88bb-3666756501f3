import { create } from "zustand"
import { TripReview, TripReviewAggregate } from "./trip-review.types"

/**
 * Trip review state interface
 */
interface TripReviewState {
  // Reviews data
  reviews: Record<string, TripReview[]> // tripId -> reviews
  userReviews: Record<string, TripReview | null> // tripId -> user's review
  aggregates: Record<string, TripReviewAggregate> // tripId -> aggregate data

  // Loading states
  loading: boolean
  submitting: boolean
  loadingReviews: Record<string, boolean> // tripId -> loading state

  // Error states
  error: Error | null
  submitError: Error | null

  // Actions
  setReviews: (tripId: string, reviews: TripReview[]) => void
  addReview: (tripId: string, review: TripReview) => void
  setUserReview: (tripId: string, review: TripReview | null) => void
  setAggregate: (tripId: string, aggregate: TripReviewAggregate) => void
  setLoading: (loading: boolean) => void
  setSubmitting: (submitting: boolean) => void
  setLoadingReviews: (tripId: string, loading: boolean) => void
  setError: (error: Error | null) => void
  setSubmitError: (error: Error | null) => void
  clearTripData: (tripId: string) => void
  reset: () => void
}

/**
 * Trip review store
 */
export const useTripReviewStore = create<TripReviewState>((set, get) => ({
  // Initial state
  reviews: {},
  userReviews: {},
  aggregates: {},
  loading: false,
  submitting: false,
  loadingReviews: {},
  error: null,
  submitError: null,

  // Actions
  setReviews: (tripId, reviews) =>
    set((state) => ({
      reviews: { ...state.reviews, [tripId]: reviews },
    })),

  addReview: (tripId, review) =>
    set((state) => ({
      reviews: {
        ...state.reviews,
        [tripId]: [review, ...(state.reviews[tripId] ?? [])],
      },
    })),

  setUserReview: (tripId, review) =>
    set((state) => ({
      userReviews: { ...state.userReviews, [tripId]: review },
    })),

  setAggregate: (tripId, aggregate) =>
    set((state) => ({
      aggregates: { ...state.aggregates, [tripId]: aggregate },
    })),

  setLoading: (loading) =>
    set(() => ({
      loading,
    })),

  setSubmitting: (submitting) =>
    set(() => ({
      submitting,
    })),

  setLoadingReviews: (tripId, loading) =>
    set((state) => ({
      loadingReviews: { ...state.loadingReviews, [tripId]: loading },
    })),

  setError: (error) =>
    set(() => ({
      error,
    })),

  setSubmitError: (error) =>
    set(() => ({
      submitError: error,
    })),

  clearTripData: (tripId) =>
    set((state) => {
      const newReviews = { ...state.reviews }
      const newUserReviews = { ...state.userReviews }
      const newAggregates = { ...state.aggregates }
      const newLoadingReviews = { ...state.loadingReviews }

      delete newReviews[tripId]
      delete newUserReviews[tripId]
      delete newAggregates[tripId]
      delete newLoadingReviews[tripId]

      return {
        reviews: newReviews,
        userReviews: newUserReviews,
        aggregates: newAggregates,
        loadingReviews: newLoadingReviews,
      }
    }),

  reset: () =>
    set(() => ({
      reviews: {},
      userReviews: {},
      aggregates: {},
      loading: false,
      submitting: false,
      loadingReviews: {},
      error: null,
      submitError: null,
    })),
}))

/**
 * Selectors for trip review store
 */
// Create stable empty array to prevent infinite re-renders
const EMPTY_REVIEWS: TripReview[] = []

export const selectTripReviews = (tripId: string) => (state: TripReviewState) =>
  state.reviews[tripId] ?? EMPTY_REVIEWS

export const selectUserReview = (tripId: string) => (state: TripReviewState) =>
  state.userReviews[tripId] ?? null

export const selectTripAggregate = (tripId: string) => (state: TripReviewState) =>
  state.aggregates[tripId] ?? null

export const selectIsLoadingReviews = (tripId: string) => (state: TripReviewState) =>
  state.loadingReviews[tripId] ?? false
