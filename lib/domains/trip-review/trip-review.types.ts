import { Timestamp } from "firebase/firestore"
import { BaseEntity } from "../base/base.types"

/**
 * Trip review entity
 */
export interface TripReview extends BaseEntity {
  tripId: string
  userId: string
  rating: number // 1-5 stars
  feedback: string // max 250 characters
  reviewDate: Timestamp
  // User display information stored directly in the review
  userDisplayName?: string | null
  userPhotoURL?: string | null
}

/**
 * Trip review creation data
 */
export type TripReviewCreateData = Omit<TripReview, "id" | "createdAt" | "updatedAt" | "reviewDate">

/**
 * Trip review update data - reviews are immutable after creation
 */
export type TripReviewUpdateData = never

/**
 * Trip review aggregate data
 */
export interface TripReviewAggregate {
  tripId: string
  averageRating: number
  totalReviews: number
  ratingDistribution: {
    1: number
    2: number
    3: number
    4: number
    5: number
  }
}

/**
 * Trip review form data
 */
export interface TripReviewFormData {
  rating: number
  feedback: string
}

/**
 * Trip review validation constraints
 */
export const TRIP_REVIEW_CONSTRAINTS = {
  FEEDBACK_MAX_LENGTH: 250,
  FEEDBACK_MIN_LENGTH: 10,
  RATING_MIN: 1,
  RATING_MAX: 5,
} as const
