"use client"

import { use<PERSON><PERSON>back, useMemo, useState, useEffect } from "react"
import { useUser } from "../auth/auth.hooks"
import { TripReviewService } from "./trip-review.service"
import {
  TripReviewCreateData,
  TripReviewFormData,
  TripReviewAggregate,
  TRIP_REVIEW_CONSTRAINTS,
} from "./trip-review.types"
import { Trip } from "../trip/trip.types"
import {
  useTripReviewStore,
  selectTripReviews,
  selectUserReview,
  selectTripAggregate,
  selectIsLoadingReviews,
} from "./trip-review.store"

/**
 * Trip with review data interface
 */
interface TripWithReviewData extends Trip {
  reviewAggregate?: TripReviewAggregate
  userHasReviewed?: boolean
}

/**
 * Hook to get trip reviews
 */
export const useTripReviews = (tripId: string) => {
  const reviewsSelector = useMemo(() => selectTripReviews(tripId), [tripId])
  const loadingSelector = useMemo(() => selectIsLoadingReviews(tripId), [tripId])

  const reviews = useTripReviewStore(reviewsSelector)
  const loading = useTripReviewStore(loadingSelector)
  const error = useTripReviewStore((state) => state.error)

  return { reviews, loading, error }
}

/**
 * Hook to get user's review for a trip
 */
export const useUserTripReview = (tripId: string) => {
  const userReviewSelector = useMemo(() => selectUserReview(tripId), [tripId])

  const userReview = useTripReviewStore(userReviewSelector)
  const loading = useTripReviewStore((state) => state.loading)
  const error = useTripReviewStore((state) => state.error)

  return { userReview, loading, error }
}

/**
 * Hook to get trip review aggregate data
 */
export const useTripReviewAggregate = (tripId: string) => {
  const aggregateSelector = useMemo(() => selectTripAggregate(tripId), [tripId])

  const aggregate = useTripReviewStore(aggregateSelector)
  const loading = useTripReviewStore((state) => state.loading)
  const error = useTripReviewStore((state) => state.error)

  return { aggregate, loading, error }
}

/**
 * Hook to create a trip review
 */
export const useCreateTripReview = () => {
  const user = useUser()
  const submitting = useTripReviewStore((state) => state.submitting)
  const submitError = useTripReviewStore((state) => state.submitError)
  const { setSubmitting, setSubmitError, addReview, setUserReview } = useTripReviewStore()

  const createReview = useCallback(
    async (tripId: string, formData: TripReviewFormData) => {
      if (!user) {
        throw new Error("User must be authenticated to create a review")
      }

      // Validate form data
      if (
        formData.rating < TRIP_REVIEW_CONSTRAINTS.RATING_MIN ||
        formData.rating > TRIP_REVIEW_CONSTRAINTS.RATING_MAX
      ) {
        throw new Error(
          `Rating must be between ${TRIP_REVIEW_CONSTRAINTS.RATING_MIN} and ${TRIP_REVIEW_CONSTRAINTS.RATING_MAX}`
        )
      }

      if (formData.feedback.length < TRIP_REVIEW_CONSTRAINTS.FEEDBACK_MIN_LENGTH) {
        throw new Error(
          `Feedback must be at least ${TRIP_REVIEW_CONSTRAINTS.FEEDBACK_MIN_LENGTH} characters`
        )
      }

      if (formData.feedback.length > TRIP_REVIEW_CONSTRAINTS.FEEDBACK_MAX_LENGTH) {
        throw new Error(
          `Feedback must be no more than ${TRIP_REVIEW_CONSTRAINTS.FEEDBACK_MAX_LENGTH} characters`
        )
      }

      setSubmitting(true)
      setSubmitError(null)

      try {
        // Check if user has already reviewed this trip
        const existingReview = await TripReviewService.hasUserReviewed(tripId, user.uid)
        if (existingReview) {
          throw new Error("You have already reviewed this trip")
        }

        const reviewData: TripReviewCreateData = {
          tripId,
          userId: user.uid,
          rating: formData.rating,
          feedback: formData.feedback.trim(),
          userDisplayName: user.displayName,
          userPhotoURL: user.photoURL,
        }

        const reviewId = await TripReviewService.createReview(tripId, reviewData)

        // Create the full review object for store update
        const newReview = {
          ...reviewData,
          id: reviewId,
          createdAt: new Date() as any, // Will be replaced by server timestamp
          reviewDate: new Date() as any, // Will be replaced by server timestamp
        }

        // Update store
        addReview(tripId, newReview)
        setUserReview(tripId, newReview)

        return reviewId
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : "Failed to create review"
        setSubmitError(new Error(errorMessage))
        throw error
      } finally {
        setSubmitting(false)
      }
    },
    [user, setSubmitting, setSubmitError, addReview, setUserReview]
  )

  return { createReview, submitting, submitError }
}

/**
 * Hook to load trip reviews
 */
export const useLoadTripReviews = () => {
  const { setReviews, setLoadingReviews, setError } = useTripReviewStore()

  const loadReviews = useCallback(
    async (tripId: string) => {
      setLoadingReviews(tripId, true)
      setError(null)

      try {
        const reviews = await TripReviewService.getTripReviews(tripId)
        setReviews(tripId, reviews)
        return reviews
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : "Failed to load reviews"
        setError(new Error(errorMessage))
        throw error
      } finally {
        setLoadingReviews(tripId, false)
      }
    },
    [setReviews, setLoadingReviews, setError]
  )

  return { loadReviews }
}

/**
 * Hook to load user's review for a trip
 */
export const useLoadUserTripReview = () => {
  const user = useUser()
  const { setUserReview, setLoading, setError } = useTripReviewStore()

  const loadUserReview = useCallback(
    async (tripId: string) => {
      if (!user) {
        setUserReview(tripId, null)
        return null
      }

      setLoading(true)
      setError(null)

      try {
        const review = await TripReviewService.getUserReview(tripId, user.uid)
        setUserReview(tripId, review)
        return review
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : "Failed to load user review"
        setError(new Error(errorMessage))
        throw error
      } finally {
        setLoading(false)
      }
    },
    [user, setUserReview, setLoading, setError]
  )

  return { loadUserReview }
}

/**
 * Hook to load trip review aggregate
 */
export const useLoadTripReviewAggregate = () => {
  const { setAggregate, setLoading, setError } = useTripReviewStore()

  const loadAggregate = useCallback(
    async (tripId: string) => {
      setLoading(true)
      setError(null)

      try {
        const aggregate = await TripReviewService.getTripReviewAggregate(tripId)
        setAggregate(tripId, aggregate)
        return aggregate
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : "Failed to load review aggregate"
        setError(new Error(errorMessage))
        throw error
      } finally {
        setLoading(false)
      }
    },
    [setAggregate, setLoading, setError]
  )

  return { loadAggregate }
}

/**
 * Hook to get squad average rating from recent completed trips
 */
export const useSquadAverageRating = (squadId: string, limitTrips: number = 10) => {
  const [averageRating, setAverageRating] = useState<number | null>(null)
  const [tripCount, setTripCount] = useState<number>(0)
  const [loading, setLoading] = useState<boolean>(true)
  const [error, setError] = useState<Error | null>(null)

  const loadSquadAverageRating = useCallback(async () => {
    if (!squadId) {
      setAverageRating(null)
      setTripCount(0)
      setLoading(false)
      return
    }

    setLoading(true)
    setError(null)

    try {
      const result = await TripReviewService.getSquadAverageRating(squadId, limitTrips)

      if (result) {
        setAverageRating(result.averageRating)
        setTripCount(result.tripCount)
      } else {
        setAverageRating(null)
        setTripCount(0)
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to load squad average rating"
      setError(new Error(errorMessage))
      setAverageRating(null)
      setTripCount(0)
    } finally {
      setLoading(false)
    }
  }, [squadId, limitTrips])

  useEffect(() => {
    loadSquadAverageRating()
  }, [loadSquadAverageRating])

  return {
    averageRating,
    tripCount,
    loading,
    error,
    refetch: loadSquadAverageRating,
  }
}

/**
 * Hook for paginated past trips with review data
 */
export const usePaginatedPastTripsWithReviews = (
  pastTrips: Trip[],
  user: any,
  pageSize: number = 5
) => {
  const [tripsWithReviews, setTripsWithReviews] = useState<TripWithReviewData[]>([])
  const [currentPage, setCurrentPage] = useState(0)
  const [loading, setLoading] = useState(false)
  const [hasMore, setHasMore] = useState(true)
  const [error, setError] = useState<Error | null>(null)

  // Reset when pastTrips change
  useEffect(() => {
    setTripsWithReviews([])
    setCurrentPage(0)
    setHasMore(pastTrips.length > 0)
    setError(null)
  }, [pastTrips.length, pastTrips.map((trip) => trip.id).join(",")])

  const loadMoreTrips = useCallback(async () => {
    if (loading || !hasMore || !user) return

    setLoading(true)
    setError(null)

    try {
      const startIndex = currentPage * pageSize
      const endIndex = startIndex + pageSize
      const tripsToLoad = pastTrips.slice(startIndex, endIndex)

      if (tripsToLoad.length === 0) {
        setHasMore(false)
        setLoading(false)
        return
      }

      // Filter only completed trips to reduce unnecessary API calls
      const completedTrips = tripsToLoad.filter((trip) => trip.status === "completed")
      const nonCompletedTrips = tripsToLoad.filter((trip) => trip.status !== "completed")

      // Process completed trips with review data using Promise.allSettled and cached aggregates
      const completedTripsResults = await Promise.allSettled(
        completedTrips.map(async (trip) => {
          const [reviewAggregateResult, userReviewResult] = await Promise.allSettled([
            TripReviewService.getTripReviewAggregate(trip.id),
            TripReviewService.getUserReview(trip.id, user.uid),
          ])

          const reviewAggregate =
            reviewAggregateResult.status === "fulfilled" ? reviewAggregateResult.value : undefined
          const userReview = userReviewResult.status === "fulfilled" ? userReviewResult.value : null

          if (reviewAggregateResult.status === "rejected") {
            console.error(
              `Error loading review aggregate for trip ${trip.id}:`,
              reviewAggregateResult.reason
            )
          }
          if (userReviewResult.status === "rejected") {
            console.error(`Error loading user review for trip ${trip.id}:`, userReviewResult.reason)
          }

          return {
            ...trip,
            reviewAggregate,
            userHasReviewed: userReview !== null,
          }
        })
      )

      const completedTripsWithReviews = completedTripsResults
        .filter((result) => result.status === "fulfilled")
        .map((result) => result.value)

      // Combine completed trips with review data and non-completed trips without review data
      const newTripsWithReviews = [
        ...completedTripsWithReviews,
        ...nonCompletedTrips.map((trip) => ({
          ...trip,
          reviewAggregate: undefined,
          userHasReviewed: false,
        })),
      ]

      // Sort by original order (by date)
      const sortedNewTrips = newTripsWithReviews.sort((a, b) => {
        const aIndex = pastTrips.findIndex((trip) => trip.id === a.id)
        const bIndex = pastTrips.findIndex((trip) => trip.id === b.id)
        return aIndex - bIndex
      })

      // Prevent duplicates by filtering out trips that already exist
      setTripsWithReviews((prev) => {
        const existingIds = new Set(prev.map((trip) => trip.id))
        const newUniqueTrips = sortedNewTrips.filter((trip) => !existingIds.has(trip.id))
        return [...prev, ...newUniqueTrips]
      })
      setCurrentPage((prev) => prev + 1)
      setHasMore(endIndex < pastTrips.length)
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Failed to load trips"
      setError(new Error(errorMessage))
    } finally {
      setLoading(false)
    }
  }, [pastTrips, user, currentPage, pageSize, loading, hasMore])

  // Load initial trips
  useEffect(() => {
    if (pastTrips.length > 0 && tripsWithReviews.length === 0 && !loading) {
      loadMoreTrips()
    }
  }, [pastTrips, tripsWithReviews.length, loading, loadMoreTrips])

  return {
    tripsWithReviews,
    loading,
    hasMore,
    error,
    loadMore: loadMoreTrips,
  }
}
