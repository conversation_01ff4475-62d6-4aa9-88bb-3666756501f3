import { collection, query, orderBy, onSnapshot, where } from "firebase/firestore"
import { db } from "@/lib/firebase"
import { TripReview } from "./trip-review.types"

/**
 * Trip review real-time service for Firebase subscriptions
 */
export class TripReviewRealtimeService {
  private static readonly TRIPS_COLLECTION = "trips"
  private static readonly REVIEWS_SUBCOLLECTION = "reviews"

  /**
   * Subscribe to all reviews for a trip
   * @param tripId Trip ID
   * @param callback Callback function to handle reviews changes
   * @returns Unsubscribe function
   */
  static subscribeToTripReviews(
    tripId: string,
    callback: (reviews: TripReview[], error?: Error) => void
  ): () => void {
    try {
      const reviewsRef = collection(db, this.TRIPS_COLLECTION, tripId, this.REVIEWS_SUBCOLLECTION)
      const q = query(reviewsRef, orderBy("reviewDate", "desc"))

      return onSnapshot(
        q,
        (querySnapshot) => {
          try {
            const reviews: TripReview[] = querySnapshot.docs.map((doc) => ({
              ...doc.data(),
              id: doc.id,
            })) as TripReview[]

            callback(reviews)
          } catch (error) {
            console.error("Error processing trip reviews snapshot:", error)
            callback([], error instanceof Error ? error : new Error("Unknown error"))
          }
        },
        (error) => {
          console.error("Error in trip reviews subscription:", error)
          callback([], error)
        }
      )
    } catch (error) {
      console.error("Error setting up trip reviews subscription:", error)
      callback([], error instanceof Error ? error : new Error("Failed to set up subscription"))
      return () => {} // Return empty unsubscribe function
    }
  }

  /**
   * Subscribe to a specific user's review for a trip
   * @param tripId Trip ID
   * @param userId User ID
   * @param callback Callback function to handle review changes
   * @returns Unsubscribe function
   */
  static subscribeToUserTripReview(
    tripId: string,
    userId: string,
    callback: (review: TripReview | null, error?: Error) => void
  ): () => void {
    try {
      const reviewsRef = collection(db, this.TRIPS_COLLECTION, tripId, this.REVIEWS_SUBCOLLECTION)
      const q = query(reviewsRef, where("userId", "==", userId))

      return onSnapshot(
        q,
        (querySnapshot) => {
          try {
            if (querySnapshot.empty) {
              callback(null)
              return
            }

            // Should only be one review per user per trip
            const doc = querySnapshot.docs[0]
            const review: TripReview = {
              ...doc.data(),
              id: doc.id,
            } as TripReview

            callback(review)
          } catch (error) {
            console.error("Error processing user trip review snapshot:", error)
            callback(null, error instanceof Error ? error : new Error("Unknown error"))
          }
        },
        (error) => {
          console.error("Error in user trip review subscription:", error)
          callback(null, error)
        }
      )
    } catch (error) {
      console.error("Error setting up user trip review subscription:", error)
      callback(null, error instanceof Error ? error : new Error("Failed to set up subscription"))
      return () => {} // Return empty unsubscribe function
    }
  }

  /**
   * Subscribe to review count for a trip (for performance when only count is needed)
   * @param tripId Trip ID
   * @param callback Callback function to handle count changes
   * @returns Unsubscribe function
   */
  static subscribeToTripReviewCount(
    tripId: string,
    callback: (count: number, error?: Error) => void
  ): () => void {
    try {
      const reviewsRef = collection(db, this.TRIPS_COLLECTION, tripId, this.REVIEWS_SUBCOLLECTION)

      return onSnapshot(
        reviewsRef,
        (querySnapshot) => {
          try {
            callback(querySnapshot.size)
          } catch (error) {
            console.error("Error processing trip review count snapshot:", error)
            callback(0, error instanceof Error ? error : new Error("Unknown error"))
          }
        },
        (error) => {
          console.error("Error in trip review count subscription:", error)
          callback(0, error)
        }
      )
    } catch (error) {
      console.error("Error setting up trip review count subscription:", error)
      callback(0, error instanceof Error ? error : new Error("Failed to set up subscription"))
      return () => {} // Return empty unsubscribe function
    }
  }
}
