"use client"

import { useEffect, useMemo, useState } from "react"
import { useUser } from "../auth/auth.hooks"
import { TripReviewRealtimeService } from "./trip-review.realtime.service"
import { useTripReviewStore, selectTripReviews, selectIsLoadingReviews } from "./trip-review.store"
import { TripReview } from "./trip-review.types"

/**
 * Hook to get real-time updates for trip reviews
 */
export const useRealtimeTripReviews = (tripId: string) => {
  // Use memoized selectors to prevent infinite re-renders
  const reviewsSelector = useMemo(() => selectTripReviews(tripId), [tripId])
  const loadingSelector = useMemo(() => selectIsLoadingReviews(tripId), [tripId])

  const reviews = useTripReviewStore(reviewsSelector)
  const loading = useTripReviewStore(loadingSelector)
  const error = useTripReviewStore((state) => state.error)

  // Memoize store actions to prevent infinite re-renders
  const setReviews = useTripReviewStore((state) => state.setReviews)
  const setLoadingReviews = useTripReviewStore((state) => state.setLoadingReviews)
  const setError = useTripReviewStore((state) => state.setError)

  // Memoize dependencies to prevent infinite re-renders
  const memoizedTripId = useMemo(() => tripId, [tripId])

  useEffect(() => {
    if (!memoizedTripId) return

    setLoadingReviews(memoizedTripId, true)
    setError(null)

    const unsubscribe = TripReviewRealtimeService.subscribeToTripReviews(
      memoizedTripId,
      (reviews, error) => {
        if (error) {
          console.error("Error in real-time trip reviews:", error)
          setError(error)
        } else {
          setReviews(memoizedTripId, reviews)
        }
        setLoadingReviews(memoizedTripId, false)
      }
    )

    return () => {
      unsubscribe()
    }
  }, [memoizedTripId, setReviews, setLoadingReviews, setError])

  return { reviews, loading, error }
}

/**
 * Hook to get real-time updates for user's trip review
 */
export const useRealtimeUserTripReview = (tripId: string) => {
  const user = useUser()

  // Use local state to avoid store conflicts
  const [userReview, setUserReview] = useState<TripReview | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<Error | null>(null)

  // Memoize dependencies to prevent infinite re-renders
  const memoizedTripId = useMemo(() => tripId, [tripId])
  const memoizedUserId = useMemo(() => user?.uid, [user?.uid])

  useEffect(() => {
    if (!memoizedTripId || !memoizedUserId) {
      setUserReview(null)
      setLoading(false)
      return
    }

    setLoading(true)
    setError(null)

    const unsubscribe = TripReviewRealtimeService.subscribeToUserTripReview(
      memoizedTripId,
      memoizedUserId,
      (review, error) => {
        if (error) {
          console.error("Error in real-time user trip review:", error)
          setError(error)
        } else {
          setUserReview(review)
        }
        setLoading(false)
      }
    )

    return () => {
      unsubscribe()
    }
  }, [memoizedTripId, memoizedUserId])

  return { userReview, loading, error }
}

/**
 * Hook to get real-time review count for a trip
 */
export const useRealtimeTripReviewCount = (tripId: string) => {
  // Use memoized selectors to prevent infinite re-renders
  const reviewsSelector = useMemo(() => selectTripReviews(tripId), [tripId])
  const loadingSelector = useMemo(() => selectIsLoadingReviews(tripId), [tripId])

  const reviews = useTripReviewStore(reviewsSelector)
  const loading = useTripReviewStore(loadingSelector)
  const error = useTripReviewStore((state) => state.error)

  // Calculate count from existing reviews to avoid additional subscription
  const count = reviews.length

  return { count, loading, error }
}

/**
 * Hook to get real-time review aggregate data
 */
export const useRealtimeTripReviewAggregate = (tripId: string) => {
  // Use memoized selectors to prevent infinite re-renders
  const reviewsSelector = useMemo(() => selectTripReviews(tripId), [tripId])
  const loadingSelector = useMemo(() => selectIsLoadingReviews(tripId), [tripId])

  const reviews = useTripReviewStore(reviewsSelector)
  const loading = useTripReviewStore(loadingSelector)
  const error = useTripReviewStore((state) => state.error)

  // Calculate aggregate from existing reviews to avoid additional processing
  const aggregate = useMemo(() => {
    if (reviews.length === 0) {
      return {
        tripId,
        averageRating: 0,
        totalReviews: 0,
        ratingDistribution: { 1: 0, 2: 0, 3: 0, 4: 0, 5: 0 },
      }
    }

    const totalRating = reviews.reduce((sum, review) => sum + review.rating, 0)
    const averageRating = totalRating / reviews.length

    const ratingDistribution = { 1: 0, 2: 0, 3: 0, 4: 0, 5: 0 }
    reviews.forEach((review) => {
      ratingDistribution[review.rating as keyof typeof ratingDistribution]++
    })

    return {
      tripId,
      averageRating: Math.round(averageRating * 10) / 10, // Round to 1 decimal place
      totalReviews: reviews.length,
      ratingDistribution,
    }
  }, [reviews, tripId])

  return { aggregate, loading, error }
}
