"use client"

import { useState, useCallback } from "react"
import { useIsUserSubscribed } from "../user-subscription/user-subscription.hooks"
import { useRealtimeUserAIUsage } from "../user-ai-usage/user-ai-usage.realtime.hooks"
import { UserAIUsageService } from "../user-ai-usage/user-ai-usage.service"
import { AIUsageCategory } from "../user-ai-usage/user-ai-usage.types"
import { useAISuggestionsCache } from "./ai-suggestions-cache.hooks"
import { CachedTaskSuggestion } from "./ai-suggestions-cache.service"
import { TaskSuggestionsHookReturn } from "./ai-suggestions.types"
import { SubscriptionErrorType } from "../user-subscription/user-subscription.types"
import { generateTaskSuggestions } from "@/lib/api-client"
import { Trip } from "../trip/trip.types"
import { Task } from "../task/task.types"
import { User } from "../user/user.types"
import { useUserPreferences } from "../user-preferences/user-preferences.hooks"
import { findMultipleAffiliateLinks } from "@/lib/affiliate-links-map"
import { toast } from "@/components/ui/use-toast"

/**
 * Hook for AI task suggestions
 * @param trip The trip to generate suggestions for
 * @param tasks Existing tasks to avoid duplicates
 * @param userId The current user ID
 * @returns Task suggestions hook return object
 */
export function useAITaskSuggestions(
  trip: Trip,
  tasks: (Task & { assignee?: User })[],
  userId: string
): TaskSuggestionsHookReturn {
  const isSubscribed = useIsUserSubscribed()
  const { usage, getCategoryUsage } = useRealtimeUserAIUsage(isSubscribed)
  const { hasCachedSuggestions, getTaskSuggestions, saveTaskSuggestions } = useAISuggestionsCache()
  const { preferences } = useUserPreferences(userId)

  // State for suggestions
  const [suggestions, setSuggestions] = useState<CachedTaskSuggestion[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [showAiSuggestions, setShowAiSuggestions] = useState(false)
  const [usingCachedSuggestions, setUsingCachedSuggestions] = useState(false)
  const [usageError, setUsageError] = useState<TaskSuggestionsHookReturn["usageError"]>(null)

  /**
   * Check if the user can make an AI request
   * @param category AI usage category
   * @returns Whether the user can make a request
   */
  const canMakeRequest = useCallback(
    async (category: AIUsageCategory): Promise<boolean> => {
      if (!userId) return false

      // If user has a subscription, they can always make requests
      if (isSubscribed) return true

      // Check if the user has reached their limit
      const categoryUsage = getCategoryUsage(category)
      if (!categoryUsage) return true

      // If they've reached the limit, show an error
      if (categoryUsage.count >= categoryUsage.limit) {
        setUsageError({
          show: true,
          errorType: SubscriptionErrorType.TASK_AI_LIMIT_REACHED,
          usageData: {
            daily: usage?.daily || 0,
            weekly: usage?.weekly || 0,
            dailyLimit: usage?.dailyLimit || 10,
            weeklyLimit: usage?.weeklyLimit || 50,
            categoryCount: categoryUsage.count,
            categoryLimit: categoryUsage.limit,
          },
        })
        return false
      }

      return true
    },
    [userId, isSubscribed, getCategoryUsage, usage, setUsageError]
  )

  /**
   * Load task suggestions
   * @param refresh Whether to force a refresh (bypass cache)
   */
  const loadSuggestions = useCallback(
    async (refresh: boolean = false): Promise<void> => {
      try {
        setLoading(true)

        if (suggestions.length > 0 && !refresh) {
          setShowAiSuggestions(true)
          setLoading(false)
          return
        }

        // Check if we have cached suggestions and it's not a refresh request
        if (!refresh && hasCachedSuggestions(AIUsageCategory.TASK, trip.id, userId)) {
          // Get cached suggestions
          const cachedSuggestions = getTaskSuggestions(trip.id, userId)

          if (cachedSuggestions && cachedSuggestions.length > 0) {
            // Use cached suggestions
            setSuggestions(cachedSuggestions)
            setShowAiSuggestions(true)
            setUsingCachedSuggestions(true)
            setLoading(false)
            return
          }
        }

        // Check if the user can make a request
        const canMakeAIRequest = await canMakeRequest(AIUsageCategory.TASK)
        if (!canMakeAIRequest) {
          setLoading(false)
          return
        }

        // Get user preferences from the hook
        const userPrefs = preferences
          ? {
              travelPreferences: preferences.travelPreferences || [],
              budgetRange: preferences.budgetRange || [500, 2000],
              preferredTravelSeasons: preferences.preferredTravelSeasons || [],
              availabilityPreferences: preferences.availabilityPreferences || [],
            }
          : {}

        // Extract existing task titles to avoid similar suggestions
        const existingTaskTitles = tasks.map((task) => task.title)

        // Generate AI task suggestions
        const aiSuggestions = await generateTaskSuggestions(
          trip,
          userPrefs,
          undefined,
          existingTaskTitles
        )

        // Process suggestions to add affiliate links (up to 2 per suggestion)
        const processedSuggestions = aiSuggestions.map((suggestion) => {
          // Find multiple matching affiliate links using tags if available, otherwise fallback to text matching
          const affiliateLinks = findMultipleAffiliateLinks(
            suggestion.title,
            suggestion.description,
            2, // max 2 links per suggestion
            trip.destination,
            suggestion.tags // pass AI-generated tags for exact matching
          )
          return {
            ...suggestion,
            affiliateLinks,
          }
        })

        // Filter to suggestions with affiliate links
        const filteredAiSuggestions = processedSuggestions.filter((suggestion) => {
          return suggestion.affiliateLinks && suggestion.affiliateLinks.length > 0
        })

        // Limit to 3 suggestions
        const finalSuggestions = filteredAiSuggestions.slice(0, 3)

        // Convert to cached format
        const cachedSuggestions: CachedTaskSuggestion[] = finalSuggestions.map((suggestion) => ({
          title: suggestion.title,
          description: suggestion.description,
          category: suggestion.category,
          priority: suggestion.priority,
          tags: suggestion.tags,
          affiliateLinks: suggestion.affiliateLinks,
        }))

        // Cache the suggestions
        saveTaskSuggestions(trip.id, userId, cachedSuggestions)

        // Only increment the user's AI usage counter if we successfully generated and processed suggestions
        // and we're not using cached suggestions
        if (!usingCachedSuggestions) {
          await UserAIUsageService.incrementAIUsage(userId, AIUsageCategory.TASK)
        }

        setSuggestions(cachedSuggestions)
        setShowAiSuggestions(true)
        setUsingCachedSuggestions(false)
      } catch (err) {
        console.error("Error loading AI task suggestions:", err)
        setError("Failed to load AI task suggestions. Please try again.")
        toast({
          title: "Error",
          description: "Failed to load AI task suggestions. Please try again.",
          variant: "destructive",
        })
      } finally {
        setLoading(false)
      }
    },
    [
      trip,
      tasks,
      userId,
      preferences,
      suggestions,
      hasCachedSuggestions,
      getTaskSuggestions,
      saveTaskSuggestions,
      canMakeRequest,
      usingCachedSuggestions,
    ]
  )

  return {
    suggestions,
    loading,
    error,
    usageError,
    showAiSuggestions,
    usingCachedSuggestions,
    loadSuggestions,
    canMakeRequest,
  }
}
