"use client"

import { useState, useCallback } from "react"
import { AIUsageCategory } from "../user-ai-usage/user-ai-usage.types"
import {
  AISuggestionsCacheService,
  CachedTaskSuggestion,
  CachedTripSuggestion,
  CachedItinerarySuggestion,
  SuggestionHistoryEntry,
} from "./ai-suggestions-cache.service"

/**
 * Hook for using the AI suggestions cache
 */
export function useAISuggestionsCache() {
  const [loading, setLoading] = useState(false)

  /**
   * Save task suggestions to cache
   */
  const saveTaskSuggestions = useCallback(
    (tripId: string, userId: string, suggestions: CachedTaskSuggestion[]) => {
      try {
        AISuggestionsCacheService.saveSuggestions<CachedTaskSuggestion>(
          AIUsageCategory.TASK,
          tripId,
          userId,
          suggestions
        )
        return true
      } catch (error) {
        console.error("Error saving task suggestions to cache:", error)
        return false
      }
    },
    []
  )

  /**
   * Get task suggestions from cache
   */
  const getTaskSuggestions = useCallback(
    (tripId: string, userId: string): CachedTaskSuggestion[] | null => {
      try {
        setLoading(true)
        return AISuggestionsCacheService.getSuggestions<CachedTaskSuggestion>(
          AIUsageCategory.TASK,
          tripId,
          userId
        )
      } catch (error) {
        console.error("Error getting task suggestions from cache:", error)
        return null
      } finally {
        setLoading(false)
      }
    },
    []
  )

  /**
   * Save trip suggestions to cache
   */
  const saveTripSuggestions = useCallback(
    (squadId: string, userId: string, suggestions: CachedTripSuggestion[]) => {
      try {
        AISuggestionsCacheService.saveSuggestions<CachedTripSuggestion>(
          AIUsageCategory.TRIP,
          squadId,
          userId,
          suggestions
        )
        return true
      } catch (error) {
        console.error("Error saving trip suggestions to cache:", error)
        return false
      }
    },
    []
  )

  /**
   * Get trip suggestions from cache
   */
  const getTripSuggestions = useCallback(
    (squadId: string, userId: string): CachedTripSuggestion[] | null => {
      try {
        setLoading(true)
        return AISuggestionsCacheService.getSuggestions<CachedTripSuggestion>(
          AIUsageCategory.TRIP,
          squadId,
          userId
        )
      } catch (error) {
        console.error("Error getting trip suggestions from cache:", error)
        return null
      } finally {
        setLoading(false)
      }
    },
    []
  )

  /**
   * Save itinerary suggestions to cache
   */
  const saveItinerarySuggestions = useCallback(
    (tripId: string, userId: string, suggestions: CachedItinerarySuggestion[]) => {
      try {
        // Use just the tripId as the context ID to make suggestions available for all days
        AISuggestionsCacheService.saveSuggestions<CachedItinerarySuggestion>(
          AIUsageCategory.ITINERARY,
          tripId,
          userId,
          suggestions
        )
        return true
      } catch (error) {
        console.error("Error saving itinerary suggestions to cache:", error)
        return false
      }
    },
    []
  )

  /**
   * Get itinerary suggestions from cache
   */
  const getItinerarySuggestions = useCallback(
    (tripId: string, userId: string): CachedItinerarySuggestion[] | null => {
      try {
        setLoading(true)
        // Use just the tripId as the context ID to make suggestions available for all days
        return AISuggestionsCacheService.getSuggestions<CachedItinerarySuggestion>(
          AIUsageCategory.ITINERARY,
          tripId,
          userId
        )
      } catch (error) {
        console.error("Error getting itinerary suggestions from cache:", error)
        return null
      } finally {
        setLoading(false)
      }
    },
    []
  )

  /**
   * Check if cached suggestions exist
   */
  const hasCachedSuggestions = useCallback(
    (category: AIUsageCategory, contextId: string, userId: string): boolean => {
      try {
        // Use contextId directly for all categories
        return AISuggestionsCacheService.hasCachedSuggestions(category, contextId, userId)
      } catch (error) {
        console.error("Error checking for cached suggestions:", error)
        return false
      }
    },
    []
  )

  /**
   * Clear cached suggestions
   */
  const clearCachedSuggestions = useCallback(
    (category: AIUsageCategory, contextId: string): void => {
      try {
        // Use contextId directly for all categories
        AISuggestionsCacheService.clearSuggestions(category, contextId)
      } catch (error) {
        console.error("Error clearing cached suggestions:", error)
      }
    },
    []
  )

  // New User Trip Cache Methods

  /**
   * Save new user trip suggestions to cache
   */
  const saveNewUserTripSuggestions = useCallback(
    (userId: string, suggestions: CachedTripSuggestion[]) => {
      try {
        AISuggestionsCacheService.saveNewUserTripSuggestions(userId, suggestions)
        return true
      } catch (error) {
        console.error("Error saving new user trip suggestions to cache:", error)
        return false
      }
    },
    []
  )

  /**
   * Get new user trip suggestions from cache
   */
  const getNewUserTripSuggestions = useCallback((userId: string): CachedTripSuggestion[] | null => {
    try {
      setLoading(true)
      return AISuggestionsCacheService.getNewUserTripSuggestions(userId)
    } catch (error) {
      console.error("Error getting new user trip suggestions from cache:", error)
      return null
    } finally {
      setLoading(false)
    }
  }, [])

  /**
   * Check if new user trip suggestions exist
   */
  const hasNewUserTripSuggestions = useCallback((userId: string): boolean => {
    try {
      return AISuggestionsCacheService.hasNewUserTripSuggestions(userId)
    } catch (error) {
      console.error("Error checking for new user trip suggestions:", error)
      return false
    }
  }, [])

  /**
   * Clear new user trip suggestions
   */
  const clearNewUserTripSuggestions = useCallback((userId: string) => {
    try {
      AISuggestionsCacheService.clearNewUserTripSuggestions(userId)
      return true
    } catch (error) {
      console.error("Error clearing new user trip suggestions:", error)
      return false
    }
  }, [])

  /**
   * Transfer new user trip suggestions to squad-bound cache
   */
  const transferNewUserTripSuggestionsToSquad = useCallback(
    (userId: string, squadId: string): CachedTripSuggestion[] | null => {
      try {
        return AISuggestionsCacheService.transferNewUserTripSuggestionsToSquad(userId, squadId)
      } catch (error) {
        console.error("Error transferring new user trip suggestions to squad:", error)
        return null
      }
    },
    []
  )

  // Suggestion History Methods

  /**
   * Add suggestions to history for deduplication
   */
  const addSuggestionsToHistory = useCallback(
    (tripId: string, suggestions: string[], day?: number) => {
      try {
        AISuggestionsCacheService.addSuggestionsToHistory(tripId, suggestions, day)
        return true
      } catch (error) {
        console.error("Error adding suggestions to history:", error)
        return false
      }
    },
    []
  )

  /**
   * Get recent suggestion titles for deduplication
   */
  const getRecentSuggestionTitles = useCallback(
    (tripId: string, day?: number, count: number = 3): string[] => {
      try {
        return AISuggestionsCacheService.getRecentSuggestionTitles(tripId, day, count)
      } catch (error) {
        console.error("Error getting recent suggestion titles:", error)
        return []
      }
    },
    []
  )

  /**
   * Get suggestion history
   */
  const getSuggestionHistory = useCallback(
    (tripId: string, day?: number): SuggestionHistoryEntry[] => {
      try {
        return AISuggestionsCacheService.getSuggestionHistory(tripId, day)
      } catch (error) {
        console.error("Error getting suggestion history:", error)
        return []
      }
    },
    []
  )

  /**
   * Clear suggestion history
   */
  const clearSuggestionHistory = useCallback((tripId: string, day?: number) => {
    try {
      AISuggestionsCacheService.clearSuggestionHistory(tripId, day)
      return true
    } catch (error) {
      console.error("Error clearing suggestion history:", error)
      return false
    }
  }, [])

  return {
    loading,
    saveTaskSuggestions,
    getTaskSuggestions,
    saveTripSuggestions,
    getTripSuggestions,
    saveItinerarySuggestions,
    getItinerarySuggestions,
    hasCachedSuggestions,
    clearCachedSuggestions,
    // New user cache methods
    saveNewUserTripSuggestions,
    getNewUserTripSuggestions,
    hasNewUserTripSuggestions,
    clearNewUserTripSuggestions,
    transferNewUserTripSuggestionsToSquad,
    // Suggestion history methods
    addSuggestionsToHistory,
    getRecentSuggestionTitles,
    getSuggestionHistory,
    clearSuggestionHistory,
  }
}
