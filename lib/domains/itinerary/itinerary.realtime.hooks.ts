"use client"

import { useEffect, useState } from "react"
import { ItineraryRealtimeService } from "./itinerary.realtime.service"
import { ItineraryItem } from "./itinerary.types"

/**
 * Hook to get real-time updates for a specific itinerary item
 */
export const useRealtimeItineraryItem = (itemId: string) => {
  const [item, setItem] = useState<ItineraryItem | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<Error | null>(null)

  useEffect(() => {
    if (!itemId) {
      setItem(null)
      setLoading(false)
      return () => {}
    }

    setLoading(true)

    const unsubscribe = ItineraryRealtimeService.subscribeToItineraryItem(itemId, (data, err) => {
      if (err) {
        console.error("Error getting real-time itinerary item:", err)
        setError(err)
        setLoading(false)
        return
      }

      setItem(data)
      setLoading(false)
    })

    return () => unsubscribe()
  }, [itemId])

  return { item, loading, error }
}

/**
 * Hook to get real-time updates for trip itinerary items
 */
export const useRealtimeItineraryItems = (tripId: string) => {
  const [items, setItems] = useState<ItineraryItem[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<Error | null>(null)

  useEffect(() => {
    if (!tripId) {
      setItems([])
      setLoading(false)
      return () => {}
    }

    setLoading(true)

    const unsubscribe = ItineraryRealtimeService.subscribeToTripItineraryItems(
      tripId,
      (data, err) => {
        if (err) {
          console.error("Error getting real-time itinerary items:", err)
          setError(err)
          setLoading(false)
          return
        }

        // Sort items by startTime
        const sortedItems = [...data].sort((a, b) => {
          return a.startTime.seconds - b.startTime.seconds
        })

        setItems(sortedItems)
        setLoading(false)
      }
    )

    return () => unsubscribe()
  }, [tripId])

  return { items, loading, error }
}
