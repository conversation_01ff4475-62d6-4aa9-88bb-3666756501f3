"use client"

import { useCallback, useEffect, useState } from "react"
import { useItineraryStore } from "./itinerary.store"
import { ItineraryItem, ItineraryItemCreateData, ItineraryItemUpdateData } from "./itinerary.types"
import { useUser } from "@/lib/domains/auth/auth.hooks"
import { ItineraryService } from "./itinerary.service"
import { useToast } from "@/hooks/use-toast"

// Export real-time hooks
export * from "./itinerary.realtime.hooks"

/**
 * Hook to get all itinerary items for a trip
 */
export const useTripItinerary = (tripId: string) => {
  const { itineraryItems, loading, error, setItineraryItems, setLoading, setError } =
    useItineraryStore()

  const fetchTripItinerary = useCallback(async () => {
    if (!tripId) return

    try {
      setLoading(true)
      setError(null)
      const items = await ItineraryService.getTripItinerary(tripId)
      setItineraryItems(items)
      setLoading(false)
    } catch (error) {
      console.error("Error fetching trip itinerary:", error)
      setError(error as Error)
      setLoading(false)
    }
  }, [tripId, setItineraryItems, setLoading, setError])

  useEffect(() => {
    if (tripId) {
      fetchTripItinerary()
    }
  }, [tripId, fetchTripItinerary])

  return { itineraryItems, loading, error, refetch: fetchTripItinerary }
}

/**
 * Hook to get itinerary items for a specific day
 */
export const useItineraryForDay = (tripId: string, date: Date) => {
  const { itineraryItems, loading, error, setItineraryItems, setLoading, setError } =
    useItineraryStore()

  const fetchItineraryForDay = useCallback(async () => {
    if (!tripId || !date) return

    try {
      setLoading(true)
      setError(null)
      const items = await ItineraryService.getItineraryForDay(tripId, date)
      setItineraryItems(items)
      setLoading(false)
    } catch (error) {
      console.error("Error fetching itinerary for day:", error)
      setError(error as Error)
      setLoading(false)
    }
  }, [tripId, date, setItineraryItems, setLoading, setError])

  useEffect(() => {
    if (tripId && date) {
      fetchItineraryForDay()
    }
  }, [tripId, date, fetchItineraryForDay])

  return { itineraryItems, loading, error, refetch: fetchItineraryForDay }
}

/**
 * Hook to get a specific itinerary item
 */
export const useItineraryItem = (itemId: string) => {
  const { currentItem, loading, error, setCurrentItem, setLoading, setError } = useItineraryStore()

  const fetchItineraryItem = useCallback(async () => {
    if (!itemId) return

    try {
      setLoading(true)
      setError(null)
      const item = await ItineraryService.getItineraryItem(itemId)
      if (item) {
        setCurrentItem(item)
      } else {
        setError(new Error("Itinerary item not found"))
      }
      setLoading(false)
    } catch (error) {
      console.error("Error fetching itinerary item:", error)
      setError(error as Error)
      setLoading(false)
    }
  }, [itemId, setCurrentItem, setLoading, setError])

  useEffect(() => {
    if (itemId) {
      fetchItineraryItem()
    }
  }, [itemId, fetchItineraryItem])

  return { item: currentItem, loading, error, refetch: fetchItineraryItem }
}

/**
 * Hook to create an itinerary item
 */
export const useCreateItineraryItem = () => {
  const user = useUser()
  const { toast } = useToast()
  const [creating, setCreating] = useState(false)
  const [error, setError] = useState<Error | null>(null)
  const { addItineraryItem } = useItineraryStore()

  const create = async (data: Omit<ItineraryItemCreateData, "createdBy">) => {
    if (!user?.uid) {
      const authError = new Error("User not authenticated")
      setError(authError)
      toast({
        title: "Authentication Error",
        description: "You must be logged in to create an itinerary item",
        variant: "destructive",
      })
      return null
    }

    try {
      setCreating(true)
      setError(null)

      // Create the item in the database
      const itemId = await ItineraryService.createItineraryItem({
        ...data,
        createdBy: user.uid,
      })

      // Get the created item
      const createdItem = await ItineraryService.getItineraryItem(itemId)

      // Update the store
      if (createdItem) {
        addItineraryItem(createdItem)
      }

      toast({
        title: "Itinerary item created",
        description: "New itinerary item has been added successfully",
      })

      setCreating(false)
      return itemId
    } catch (err) {
      console.error("Error creating itinerary item:", err)
      setError(err as Error)
      setCreating(false)

      toast({
        title: "Error",
        description: "Failed to create itinerary item",
        variant: "destructive",
      })

      return null
    }
  }

  return { create, creating, error }
}

/**
 * Hook to update an itinerary item
 */
export const useUpdateItineraryItem = (itemId?: string) => {
  const { toast } = useToast()
  const [updating, setUpdating] = useState(false)
  const [error, setError] = useState<Error | null>(null)
  const { updateItineraryItemInStore } = useItineraryStore()

  const update = async (data: ItineraryItemUpdateData, id?: string) => {
    const targetId = id || itemId
    if (!targetId) {
      const idError = new Error("No itinerary item ID provided")
      setError(idError)
      toast({
        title: "Error",
        description: "No itinerary item ID provided",
        variant: "destructive",
      })
      return false
    }

    try {
      setUpdating(true)
      setError(null)

      // Update the item in the database
      const result = await ItineraryService.updateItineraryItem(targetId, data)

      if (result.success) {
        // Get the updated item
        const updatedItem = await ItineraryService.getItineraryItem(targetId)

        // Update the store
        if (updatedItem) {
          updateItineraryItemInStore(updatedItem)
        }

        toast({
          title: "Itinerary item updated",
          description: "Itinerary item has been updated successfully",
        })
      } else {
        throw new Error("Failed to update itinerary item")
      }

      setUpdating(false)
      return result.success
    } catch (err) {
      console.error("Error updating itinerary item:", err)
      setError(err as Error)
      setUpdating(false)

      toast({
        title: "Error",
        description: "Failed to update itinerary item",
        variant: "destructive",
      })

      return false
    }
  }

  return { update, updating, error }
}

/**
 * Hook to delete an itinerary item
 */
export const useDeleteItineraryItem = () => {
  const { toast } = useToast()
  const [deleting, setDeleting] = useState(false)
  const [error, setError] = useState<Error | null>(null)
  const { removeItineraryItem } = useItineraryStore()

  const remove = async (itemId: string) => {
    if (!itemId) {
      const idError = new Error("No itinerary item ID provided")
      setError(idError)
      toast({
        title: "Error",
        description: "No itinerary item ID provided",
        variant: "destructive",
      })
      return false
    }

    try {
      setDeleting(true)
      setError(null)

      // Delete the item from the database
      const result = await ItineraryService.deleteItineraryItem(itemId)

      if (result.success) {
        // Update the store
        removeItineraryItem(itemId)

        toast({
          title: "Itinerary item deleted",
          description: "Itinerary item has been deleted successfully",
        })
      } else {
        throw new Error("Failed to delete itinerary item")
      }

      setDeleting(false)
      return result.success
    } catch (err) {
      console.error("Error deleting itinerary item:", err)
      setError(err as Error)
      setDeleting(false)

      toast({
        title: "Error",
        description: "Failed to delete itinerary item",
        variant: "destructive",
      })

      return false
    }
  }

  return { remove, deleting, error }
}

/**
 * Hook to group itinerary items by day
 */
export const useItineraryByDay = (items: ItineraryItem[]) => {
  const [itemsByDay, setItemsByDay] = useState<Record<string, ItineraryItem[]>>({})

  useEffect(() => {
    const groupedItems: Record<string, ItineraryItem[]> = {}

    items.forEach((item) => {
      const date = item.startTime.toDate()
      const dateString = date.toISOString().split("T")[0]

      if (!groupedItems[dateString]) {
        groupedItems[dateString] = []
      }

      groupedItems[dateString].push(item)
    })

    // Sort items within each day by start time
    Object.keys(groupedItems).forEach((date) => {
      groupedItems[date].sort((a, b) => a.startTime.toMillis() - b.startTime.toMillis())
    })

    setItemsByDay(groupedItems)
  }, [items])

  return itemsByDay
}
