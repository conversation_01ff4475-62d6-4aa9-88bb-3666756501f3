"use client"

import { create } from "zustand"
import { ItineraryItem } from "./itinerary.types"

/**
 * Itinerary store state interface
 */
interface ItineraryState {
  // State
  itineraryItems: ItineraryItem[]
  currentItem: ItineraryItem | null
  loading: boolean
  error: Error | null

  // State setters
  setItineraryItems: (items: ItineraryItem[]) => void
  setCurrentItem: (item: ItineraryItem | null) => void
  setLoading: (loading: boolean) => void
  setError: (error: Error | null) => void

  // Store actions
  addItineraryItem: (item: ItineraryItem) => void
  updateItineraryItemInStore: (updatedItem: ItineraryItem) => void
  removeItineraryItem: (itemId: string) => void
  clearItinerary: () => void
}

/**
 * Itinerary store with Zustand
 */
export const useItineraryStore = create<ItineraryState>((set, get) => ({
  // Initial state
  itineraryItems: [],
  currentItem: null,
  loading: false,
  error: null,

  // State setters
  setItineraryItems: (items: ItineraryItem[]) => set({ itineraryItems: items }),
  setCurrentItem: (item: ItineraryItem | null) => set({ currentItem: item }),
  setLoading: (loading: boolean) => set({ loading }),
  setError: (error: Error | null) => set({ error }),

  // Store actions
  addItineraryItem: (item: ItineraryItem) => {
    const items = get().itineraryItems

    // Only add the item if it's for the trip we're currently viewing
    if (items.length > 0 && items[0].tripId === item.tripId) {
      set({ itineraryItems: [...items, item] })
    }

    // If this is the item we're currently viewing, update it
    if (get().currentItem?.id === item.id) {
      set({ currentItem: item })
    }
  },

  updateItineraryItemInStore: (updatedItem: ItineraryItem) => {
    const items = get().itineraryItems

    // Update the item in the list if it exists
    const updatedItems = items.map((item) => (item.id === updatedItem.id ? updatedItem : item))

    // Update current item if it's the one being edited
    if (get().currentItem?.id === updatedItem.id) {
      set({ currentItem: updatedItem })
    }

    set({ itineraryItems: updatedItems })
  },

  removeItineraryItem: (itemId: string) => {
    const items = get().itineraryItems

    // Remove the item from the list
    const updatedItems = items.filter((item) => item.id !== itemId)

    // Clear current item if it's the one being deleted
    if (get().currentItem?.id === itemId) {
      set({ currentItem: null })
    }

    set({ itineraryItems: updatedItems })
  },

  clearItinerary: () => {
    set({ itineraryItems: [], currentItem: null })
  },
}))
