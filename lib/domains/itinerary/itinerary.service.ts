import { db } from "@/lib/firebase"
import {
  collection,
  doc,
  getDoc,
  getDocs,
  updateDoc,
  query,
  where,
  serverTimestamp,
  setDoc,
  deleteDoc,
  orderBy,
} from "firebase/firestore"
import { BaseService } from "../base/base.service"
import { ServiceResponse } from "../base/base.types"
import { ItineraryItem, ItineraryItemCreateData, ItineraryItemUpdateData } from "./itinerary.types"

/**
 * Itinerary service for Firebase operations
 */
export class ItineraryService {
  private static readonly COLLECTION = "tripItineraries"

  /**
   * Create a new itinerary item
   * @param itemData Itinerary item data
   * @returns The new itinerary item ID
   */
  static async createItineraryItem(itemData: ItineraryItemCreateData): Promise<string> {
    try {
      const itemRef = doc(collection(db, this.COLLECTION))
      const itemId = itemRef.id

      const newItem = {
        ...itemData,
        id: itemId,
        isBooked: itemData.isBooked || false,
        createdAt: serverTimestamp(),
      }

      await setDoc(itemRef, newItem)
      return itemId
    } catch (error) {
      console.error("Error creating itinerary item:", error)
      throw error
    }
  }

  /**
   * Get an itinerary item by ID
   * @param itemId Itinerary item ID
   * @returns The itinerary item data or null if not found
   */
  static async getItineraryItem(itemId: string): Promise<ItineraryItem | null> {
    try {
      const itemDoc = await getDoc(doc(db, this.COLLECTION, itemId))

      if (itemDoc.exists()) {
        return { ...itemDoc.data(), id: itemId } as ItineraryItem
      }

      return null
    } catch (error) {
      console.error("Error getting itinerary item:", error)
      throw error
    }
  }

  /**
   * Get itinerary items for a trip
   * @param tripId Trip ID
   * @returns Array of itinerary items
   */
  static async getTripItinerary(tripId: string): Promise<ItineraryItem[]> {
    try {
      const q = query(
        collection(db, this.COLLECTION),
        where("tripId", "==", tripId),
        orderBy("startTime")
      )

      const querySnapshot = await getDocs(q)

      return querySnapshot.docs.map((doc) => ({ ...doc.data(), id: doc.id }) as ItineraryItem)
    } catch (error) {
      console.error("Error getting trip itinerary:", error)
      throw error
    }
  }

  /**
   * Update an itinerary item
   * @param itemId Itinerary item ID
   * @param itemData Itinerary item data to update
   * @returns Service response
   */
  static async updateItineraryItem(
    itemId: string,
    itemData: ItineraryItemUpdateData
  ): Promise<ServiceResponse> {
    try {
      await updateDoc(doc(db, this.COLLECTION, itemId), {
        ...itemData,
        updatedAt: serverTimestamp(),
      })

      return { success: true }
    } catch (error) {
      console.error("Error updating itinerary item:", error)
      return { success: false, error }
    }
  }

  /**
   * Delete an itinerary item
   * @param itemId Itinerary item ID
   * @returns Service response
   */
  static async deleteItineraryItem(itemId: string): Promise<ServiceResponse> {
    try {
      await deleteDoc(doc(db, this.COLLECTION, itemId))
      return { success: true }
    } catch (error) {
      console.error("Error deleting itinerary item:", error)
      return { success: false, error }
    }
  }

  /**
   * Get itinerary items for a specific day
   * @param tripId Trip ID
   * @param date Date to filter by
   * @returns Array of itinerary items
   */
  static async getItineraryForDay(tripId: string, date: Date): Promise<ItineraryItem[]> {
    try {
      // Get all itinerary items for the trip
      const items = await this.getTripItinerary(tripId)

      // Filter items that fall on the specified date
      const startOfDay = new Date(date)
      startOfDay.setHours(0, 0, 0, 0)

      const endOfDay = new Date(date)
      endOfDay.setHours(23, 59, 59, 999)

      return items.filter((item) => {
        const itemStartTime = item.startTime.toDate()
        return itemStartTime >= startOfDay && itemStartTime <= endOfDay
      })
    } catch (error) {
      console.error("Error getting itinerary for day:", error)
      throw error
    }
  }
}
