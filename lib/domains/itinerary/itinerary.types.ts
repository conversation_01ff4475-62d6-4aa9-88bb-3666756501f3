import { Timestamp } from "firebase/firestore"
import { BaseEntity } from "../base/base.types"

/**
 * Itinerary item type
 */
export type ItineraryItemType = "activity" | "transportation" | "accommodation" | "meal" | "other"

/**
 * Itinerary item entity
 */
export interface ItineraryItem extends BaseEntity {
  tripId: string
  title: string
  description?: string
  type: ItineraryItemType
  startTime: Timestamp
  endTime: Timestamp
  location?: string
  locationDetails?: {
    placeId?: string
    address?: string
    latitude?: number
    longitude?: number
  }
  cost?: number
  currency?: string
  isBooked: boolean
  bookingReference?: string
  bookingUrl?: string
  notes?: string
  createdBy: string
  // Additional fields to support component needs
  day?: number
  time?: string
}

/**
 * Itinerary item creation data
 */
export type ItineraryItemCreateData = Omit<ItineraryItem, "id" | "createdAt" | "isBooked"> & {
  isBooked?: boolean
}

/**
 * Itinerary item update data
 */
export type ItineraryItemUpdateData = Partial<ItineraryItem>
