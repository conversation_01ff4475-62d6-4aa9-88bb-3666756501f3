import { BaseRealtimeService } from "../base/base.realtime.service"
import { ItineraryItem } from "./itinerary.types"

/**
 * Itinerary real-time service for Firebase real-time operations
 */
export class ItineraryRealtimeService {
  private static readonly COLLECTION = "tripItineraries"

  /**
   * Subscribe to an itinerary item by ID
   * @param itemId Itinerary item ID
   * @param callback Callback function to handle itinerary item changes
   * @returns Unsubscribe function
   */
  static subscribeToItineraryItem(
    itemId: string,
    callback: (item: ItineraryItem | null, error?: Error) => void
  ): () => void {
    return BaseRealtimeService.subscribeToDocument<ItineraryItem>(this.COLLECTION, itemId, callback)
  }

  /**
   * Subscribe to itinerary items for a trip
   * @param tripId Trip ID
   * @param callback Callback function to handle itinerary items changes
   * @returns Unsubscribe function
   */
  static subscribeToTripItineraryItems(
    tripId: string,
    callback: (items: ItineraryItem[], error?: Error) => void
  ): () => void {
    return BaseRealtimeService.subscribeToQuery<ItineraryItem>(
      this.COLLECTION,
      [["tripId", "==", tripId]],
      callback
    )
  }
}
