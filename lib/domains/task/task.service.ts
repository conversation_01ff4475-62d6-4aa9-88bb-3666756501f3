import { db } from "@/lib/firebase"
import {
  collection,
  doc,
  getDoc,
  getDocs,
  updateDoc,
  query,
  where,
  serverTimestamp,
  setDoc,
  deleteDoc,
} from "firebase/firestore"
import { ServiceResponse } from "../base/base.types"
import { Task, TaskCreateData, TaskUpdateData } from "./task.types"
import { TripService } from "../trip/trip.service"

/**
 * Task service for Firebase operations
 */
export class TaskService {
  private static readonly COLLECTION = "tripTasks"

  /**
   * Create a new task
   * @param taskData Task data
   * @returns The new task ID
   */
  static async createTask(taskData: TaskCreateData): Promise<string> {
    try {
      const taskRef = doc(collection(db, this.COLLECTION))
      const taskId = taskRef.id

      // Get assignee display information if assigneeId is provided
      let assigneeDisplayName = null
      let assigneePhotoURL = null

      if (taskData.assigneeId) {
        try {
          const userDoc = await getDoc(doc(db, "users", taskData.assigneeId))
          if (userDoc.exists()) {
            const userData = userDoc.data()
            assigneeDisplayName = userData.displayName || null
            assigneePhotoURL = userData.photoURL || null
          }
        } catch (err) {
          console.error("Error getting assignee details:", err)
          // Continue with task creation even if we can't get user details
        }
      }

      const newTask = {
        ...taskData,
        id: taskId,
        completed: false,
        createdAt: serverTimestamp(),
        // Store user display information directly in the task
        assigneeDisplayName,
        assigneePhotoURL,
      }

      await setDoc(taskRef, newTask)

      // Update trip task count
      const tripRef = doc(db, "trips", taskData.tripId)
      const tripDoc = await getDoc(tripRef)

      if (tripDoc.exists()) {
        const tripData = tripDoc.data()
        const totalTasks = (tripData.totalTasks || 0) + 1

        await updateDoc(tripRef, { totalTasks })
      }

      return taskId
    } catch (error) {
      console.error("Error creating task:", error)
      throw error
    }
  }

  /**
   * Get tasks for a trip
   * @param tripId Trip ID
   * @returns Array of tasks
   */
  static async getTripTasks(tripId: string): Promise<Task[]> {
    try {
      const q = query(collection(db, this.COLLECTION), where("tripId", "==", tripId))
      const querySnapshot = await getDocs(q)
      return querySnapshot.docs.map((doc) => ({ ...doc.data(), id: doc.id }) as Task)
    } catch (error) {
      console.error("Error getting trip tasks:", error)
      throw error
    }
  }

  /**
   * Get a task by ID
   * @param taskId Task ID
   * @returns The task data or null if not found
   */
  static async getTask(taskId: string): Promise<Task | null> {
    try {
      const taskDoc = await getDoc(doc(db, this.COLLECTION, taskId))

      if (taskDoc.exists()) {
        return { ...taskDoc.data(), id: taskId } as Task
      }

      return null
    } catch (error) {
      console.error("Error getting task:", error)
      throw error
    }
  }

  /**
   * Update a task
   * @param taskId Task ID
   * @param taskData Task data to update
   * @returns Service response
   */
  static async updateTask(taskId: string, taskData: TaskUpdateData): Promise<ServiceResponse> {
    try {
      const taskRef = doc(db, this.COLLECTION, taskId)
      const taskDoc = await getDoc(taskRef)

      if (!taskDoc.exists()) {
        throw new Error("Task not found")
      }

      const oldTask = taskDoc.data() as Task
      const wasCompleted = oldTask.completed
      const willBeCompleted = taskData.completed !== undefined ? taskData.completed : wasCompleted

      // If assignee is changing, get the new assignee's display information
      let updateData = { ...taskData, updatedAt: serverTimestamp() }

      if (taskData.assigneeId && taskData.assigneeId !== oldTask.assigneeId) {
        try {
          const userDoc = await getDoc(doc(db, "users", taskData.assigneeId))
          if (userDoc.exists()) {
            const userData = userDoc.data()
            updateData.assigneeDisplayName = userData.displayName || null
            updateData.assigneePhotoURL = userData.photoURL || null
          } else {
            // If user not found, clear the display info
            updateData.assigneeDisplayName = null
            updateData.assigneePhotoURL = null
          }
        } catch (err) {
          console.error("Error getting assignee details:", err)
          // Continue with task update even if we can't get user details
        }
      }

      await updateDoc(taskRef, updateData)

      // If completion status changed, update trip task completion count
      if (wasCompleted !== willBeCompleted) {
        const tripRef = doc(db, "trips", oldTask.tripId)
        const tripDoc = await getDoc(tripRef)

        if (tripDoc.exists()) {
          const tripData = tripDoc.data()
          const tasksCompleted = (tripData.tasksCompleted || 0) + (willBeCompleted ? 1 : -1)
          await updateDoc(tripRef, { tasksCompleted })
        }
      }

      return { success: true }
    } catch (error) {
      console.error("Error updating task:", error)
      return { success: false, error }
    }
  }

  /**
   * Delete a task
   * @param taskId Task ID
   * @returns Service response
   */
  static async deleteTask(taskId: string): Promise<ServiceResponse> {
    try {
      const taskRef = doc(db, this.COLLECTION, taskId)
      const taskDoc = await getDoc(taskRef)

      if (!taskDoc.exists()) {
        throw new Error("Task not found")
      }

      const task = taskDoc.data() as Task

      await deleteDoc(taskRef)

      // Update trip task counts
      const tripRef = doc(db, "trips", task.tripId)
      const tripDoc = await getDoc(tripRef)

      if (tripDoc.exists()) {
        const tripData = tripDoc.data()
        const totalTasks = Math.max(0, (tripData.totalTasks || 0) - 1)
        const tasksCompleted = Math.max(
          0,
          (tripData.tasksCompleted || 0) - (task.completed ? 1 : 0)
        )

        await updateDoc(tripRef, {
          totalTasks,
          tasksCompleted,
        })
      }

      return { success: true }
    } catch (error) {
      console.error("Error deleting task:", error)
      return { success: false, error }
    }
  }

  /**
   * Check if a user can manage a task (is trip leader or task assignee)
   * @param userId User ID
   * @param taskId Task ID
   * @returns True if the user can manage the task
   */
  static async canUserManageTask(userId: string, taskId: string): Promise<boolean> {
    try {
      const taskRef = doc(db, this.COLLECTION, taskId)
      const taskDoc = await getDoc(taskRef)

      if (!taskDoc.exists()) {
        return false
      }

      const task = taskDoc.data() as Task

      // Check if user is the task assignee
      if (task.assigneeId === userId) {
        return true
      }

      // Check if user is the task creator
      if (task.createdBy === userId) {
        return true
      }

      // Get the trip data
      const trip = await TripService.getTrip(task.tripId)

      if (!trip) {
        return false
      }

      // Check if user is the trip leader
      return trip.leaderId === userId
    } catch (error) {
      console.error("Error checking if user can manage task:", error)
      return false
    }
  }
}
