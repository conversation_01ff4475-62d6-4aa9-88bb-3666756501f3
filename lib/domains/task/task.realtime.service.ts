import { BaseRealtimeService } from "../base/base.realtime.service"
import { Task } from "./task.types"

/**
 * Task real-time service for Firebase real-time operations
 */
export class TaskRealtimeService {
  private static readonly COLLECTION = "tripTasks"

  /**
   * Subscribe to a task by ID
   * @param taskId Task ID
   * @param callback Callback function to handle task changes
   * @returns Unsubscribe function
   */
  static subscribeToTask(
    taskId: string,
    callback: (task: Task | null, error?: Error) => void
  ): () => void {
    return BaseRealtimeService.subscribeToDocument<Task>(this.COLLECTION, taskId, callback)
  }

  /**
   * Subscribe to tasks for a trip
   * @param tripId Trip ID
   * @param callback Callback function to handle tasks changes
   * @returns Unsubscribe function
   */
  static subscribeToTripTasks(
    tripId: string,
    callback: (tasks: Task[], error?: Error) => void
  ): () => void {
    return BaseRealtimeService.subscribeToQuery<Task>(
      this.COLLECTION,
      [["tripId", "==", tripId]],
      callback
    )
  }

  /**
   * Subscribe to tasks assigned to a user
   * @param userId User ID
   * @param callback Callback function to handle tasks changes
   * @returns Unsubscribe function
   */
  static subscribeToUserTasks(
    userId: string,
    callback: (tasks: Task[], error?: Error) => void
  ): () => void {
    return BaseRealtimeService.subscribeToQuery<Task>(
      this.COLLECTION,
      [["assigneeId", "==", userId]],
      callback
    )
  }
}
