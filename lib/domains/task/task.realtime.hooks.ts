"use client"

import { useEffect, useState } from "react"
import { Task } from "./task.types"
import { TaskRealtimeService } from "./task.realtime.service"
import { useUser } from "@/lib/domains/auth/auth.hooks"

/**
 * Hook to get real-time updates for a specific task
 */
export const useRealtimeTask = (taskId: string) => {
  const [task, setTask] = useState<Task | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<Error | null>(null)

  useEffect(() => {
    if (!taskId) {
      setTask(null)
      setLoading(false)
      return () => {}
    }

    setLoading(true)

    const unsubscribe = TaskRealtimeService.subscribeToTask(taskId, (data, err) => {
      if (err) {
        console.error("Error getting real-time task:", err)
        setError(err)
        setLoading(false)
        return
      }

      setTask(data)
      setLoading(false)
    })

    return () => unsubscribe()
  }, [taskId])

  return { task, loading, error }
}

/**
 * Hook to get real-time updates for trip tasks
 */
export const useRealtimeTripTasks = (tripId: string) => {
  const [tasks, setTasks] = useState<Task[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<Error | null>(null)

  useEffect(() => {
    if (!tripId) {
      setTasks([])
      setLoading(false)
      return () => {}
    }

    setLoading(true)

    const unsubscribe = TaskRealtimeService.subscribeToTripTasks(tripId, (data, err) => {
      if (err) {
        console.error("Error getting real-time trip tasks:", err)
        setError(err)
        setLoading(false)
        return
      }

      setTasks(data)
      setLoading(false)
    })

    return () => unsubscribe()
  }, [tripId])

  return { tasks, loading, error }
}

/**
 * Hook to get real-time updates for user tasks
 */
export const useRealtimeUserTasks = () => {
  const user = useUser()
  const [tasks, setTasks] = useState<Task[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<Error | null>(null)

  useEffect(() => {
    if (!user?.uid) {
      setTasks([])
      setLoading(false)
      return () => {}
    }

    setLoading(true)

    const unsubscribe = TaskRealtimeService.subscribeToUserTasks(user.uid, (data, err) => {
      if (err) {
        console.error("Error getting real-time user tasks:", err)
        setError(err)
        setLoading(false)
        return
      }

      setTasks(data)
      setLoading(false)
    })

    return () => unsubscribe()
  }, [user])

  return { tasks, loading, error }
}
