"use client"

import { useEffect, useState, useCallback } from "react"
import { useTaskStore } from "./task.store"
import { Task, TaskCreateData, TaskUpdateData, TaskWithAssignee } from "./task.types"
import { TaskService } from "./task.service"
import { useUser } from "@/lib/domains/auth/auth.hooks"
import { useToast } from "@/components/ui/use-toast"
import { Timestamp } from "firebase/firestore"

// Export real-time hooks
export * from "./task.realtime.hooks"

/**
 * Hook to get all tasks for a trip
 */
export const useTripTasks = (tripId: string) => {
  // Otherwise use the regular store
  const { tasks, loading, error, fetchTripTasks } = useTaskStore()

  useEffect(() => {
    if (tripId) {
      fetchTripTasks(tripId)
    }
  }, [tripId, fetchTripTasks])

  return { tasks, loading, error }
}

/**
 * Hook to get a specific task
 */
export const useTask = (taskId: string) => {
  // Otherwise use the regular store
  const { currentTask, loading, error, fetchTask } = useTaskStore()

  useEffect(() => {
    if (taskId) {
      fetchTask(taskId)
    }
  }, [taskId, fetchTask])

  return { task: currentTask, loading, error }
}

/**
 * Hook for creating a task
 */
export const useCreateTask = () => {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<Error | null>(null)
  const { toast } = useToast()
  const { addTask } = useTaskStore()
  const user = useUser()

  const createTask = useCallback(
    async (taskData: Omit<TaskCreateData, "createdBy">) => {
      if (!user) {
        toast({
          title: "Error",
          description: "You must be logged in to create a task",
          variant: "destructive",
        })
        return false
      }

      // Validate required fields
      const validationErrors = []

      if (!taskData.title || !taskData.title.trim()) {
        validationErrors.push("Title is required")
      }

      if (!taskData.assigneeId) {
        validationErrors.push("Assignee is required")
      }

      if (validationErrors.length > 0) {
        toast({
          title: "Validation Error",
          description: validationErrors.join(", "),
          variant: "destructive",
        })
        return false
      }

      setLoading(true)
      setError(null)

      try {
        // Format the task data
        const formattedTaskData: TaskCreateData = {
          ...taskData,
          createdBy: user.uid,
          // Convert string date to Timestamp if it exists
          dueDate:
            taskData.dueDate && typeof taskData.dueDate === "string"
              ? Timestamp.fromDate(new Date(taskData.dueDate))
              : (taskData.dueDate as Timestamp | null),
        }

        // Create the task in the database
        const taskId = await TaskService.createTask(formattedTaskData)

        // Get the created task
        const createdTask = await TaskService.getTask(taskId)

        // Update the store
        if (createdTask) {
          addTask(createdTask)
        }

        toast({
          title: "Task created",
          description: "New task has been added successfully",
        })

        return true
      } catch (err) {
        console.error("Error creating task:", err)
        setError(err as Error)
        toast({
          title: "Error",
          description: "Failed to create task",
          variant: "destructive",
        })
        return false
      } finally {
        setLoading(false)
      }
    },
    [user, addTask, toast]
  )

  return { createTask, loading, error }
}

/**
 * Hook for updating a task
 */
export const useUpdateTask = () => {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<Error | null>(null)
  const { toast } = useToast()
  const { updateTaskInStore } = useTaskStore()

  const updateTask = useCallback(
    async (taskId: string, taskData: TaskUpdateData) => {
      // Validate required fields
      const validationErrors = []

      if (taskData.title !== undefined && (!taskData.title || !taskData.title.trim())) {
        validationErrors.push("Title is required")
      }

      if (taskData.assigneeId !== undefined && !taskData.assigneeId) {
        validationErrors.push("Assignee is required")
      }

      if (validationErrors.length > 0) {
        toast({
          title: "Validation Error",
          description: validationErrors.join(", "),
          variant: "destructive",
        })
        return false
      }

      setLoading(true)
      setError(null)

      try {
        // Format the task data
        const formattedTaskData: TaskUpdateData = {
          ...taskData,
          // Convert string date to Timestamp if it exists and is a string
          dueDate:
            taskData.dueDate && typeof taskData.dueDate === "string"
              ? Timestamp.fromDate(new Date(taskData.dueDate))
              : taskData.dueDate,
        }

        // Update the task in the database
        const result = await TaskService.updateTask(taskId, formattedTaskData)

        if (result.success) {
          // Get the updated task
          const updatedTask = await TaskService.getTask(taskId)

          // Update the store
          if (updatedTask) {
            updateTaskInStore(updatedTask)
          }

          toast({
            title: "Task updated",
            description: "Task has been updated successfully",
          })
          return true
        } else {
          throw new Error("Failed to update task")
        }
      } catch (err) {
        console.error("Error updating task:", err)
        setError(err as Error)
        toast({
          title: "Error",
          description: "Failed to update task",
          variant: "destructive",
        })
        return false
      } finally {
        setLoading(false)
      }
    },
    [updateTaskInStore, toast]
  )

  return { updateTask, loading, error }
}

/**
 * Hook for deleting a task
 */
export const useDeleteTask = () => {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<Error | null>(null)
  const { toast } = useToast()
  const { removeTask } = useTaskStore()

  const deleteTask = useCallback(
    async (taskId: string) => {
      setLoading(true)
      setError(null)

      try {
        // Delete the task from the database
        const result = await TaskService.deleteTask(taskId)

        if (result.success) {
          // Update the store
          removeTask(taskId)

          toast({
            title: "Task deleted",
            description: "Task has been deleted successfully",
          })
          return true
        } else {
          throw new Error("Failed to delete task")
        }
      } catch (err) {
        console.error("Error deleting task:", err)
        setError(err as Error)
        toast({
          title: "Error",
          description: "Failed to delete task",
          variant: "destructive",
        })
        return false
      } finally {
        setLoading(false)
      }
    },
    [removeTask, toast]
  )

  return { deleteTask, loading, error }
}

/**
 * Hook for toggling task completion
 */
export const useToggleTaskCompletion = () => {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<Error | null>(null)
  const { toast } = useToast()
  const { updateTaskInStore } = useTaskStore()
  const user = useUser()

  const toggleTaskCompletion = useCallback(
    async (
      taskId: string,
      currentCompleted: boolean,
      _tripId: string, // Not used but kept for API compatibility
      isLeader: boolean,
      assigneeId: string
    ) => {
      if (!user) {
        toast({
          title: "Error",
          description: "You must be logged in to update a task",
          variant: "destructive",
        })
        return false
      }

      // Check if user can complete this task (is the assignee or trip leader)
      const isTaskOwner = assigneeId === user.uid

      if (!isTaskOwner && !isLeader) {
        toast({
          title: "Permission denied",
          description: "Only the task owner or trip leader can complete this task",
          variant: "destructive",
        })
        return false
      }

      setLoading(true)
      setError(null)

      try {
        const newCompleted = !currentCompleted

        // Update the task in the database
        const result = await TaskService.updateTask(taskId, { completed: newCompleted })

        if (result.success) {
          // Get the updated task
          const updatedTask = await TaskService.getTask(taskId)

          // Update the store
          if (updatedTask) {
            updateTaskInStore(updatedTask)
          }

          toast({
            title: currentCompleted ? "Task marked as incomplete" : "Task completed",
            description: "Task status has been updated",
          })
          return true
        } else {
          throw new Error("Failed to update task status")
        }
      } catch (err) {
        console.error("Error updating task completion:", err)
        setError(err as Error)
        toast({
          title: "Error",
          description: "Failed to update task status",
          variant: "destructive",
        })
        return false
      } finally {
        setLoading(false)
      }
    },
    [user, updateTaskInStore, toast]
  )

  return { toggleTaskCompletion, loading, error }
}

/**
 * Hook to create a task
 */
// export const useCreateTask = () => {
//   const [creating, setCreating] = useState(false)
//   const [error, setError] = useState<Error | null>(null)
//   const { createTask } = useTaskStore()

//   const create = async (taskData: TaskCreateData) => {
//     try {
//       setCreating(true)
//       setError(null)
//       const taskId = await createTask(taskData)
//       setCreating(false)
//       return taskId
//     } catch (err) {
//       setError(err as Error)
//       setCreating(false)
//       return null
//     }
//   }

//   return { create, creating, error }
// }

// /**
//  * Hook to update a task
//  */
// export const useUpdateTask = (taskId?: string) => {
//   const [updating, setUpdating] = useState(false)
//   const [error, setError] = useState<Error | null>(null)
//   const { updateTask } = useTaskStore()

//   const update = async (data: TaskUpdateData, id?: string) => {
//     const targetId = id || taskId
//     if (!targetId) {
//       setError(new Error("No task ID provided"))
//       return false
//     }

//     try {
//       setUpdating(true)
//       setError(null)
//       const success = await updateTask(targetId, data)
//       setUpdating(false)
//       return success
//     } catch (err) {
//       setError(err as Error)
//       setUpdating(false)
//       return false
//     }
//   }

//   return { update, updating, error }
// }

// /**
//  * Hook to delete a task
//  */
// export const useDeleteTask = () => {
//   const [deleting, setDeleting] = useState(false)
//   const [error, setError] = useState<Error | null>(null)
//   const { deleteTask } = useTaskStore()

//   const remove = async (taskId: string) => {
//     try {
//       setDeleting(true)
//       setError(null)
//       const success = await deleteTask(taskId)
//       setDeleting(false)
//       return success
//     } catch (err) {
//       setError(err as Error)
//       setDeleting(false)
//       return false
//     }
//   }

//   return { remove, deleting, error }
// }

/**
 * Hook to check if a user can manage a task
 */
export const useCanManageTask = (taskId: string) => {
  const user = useUser()
  const [canManage, setCanManage] = useState(false)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const checkManagePermission = async () => {
      if (user?.uid && taskId) {
        setLoading(true)
        try {
          const result = await TaskService.canUserManageTask(user.uid, taskId)
          setCanManage(result)
        } catch (error) {
          console.error("Error checking task management permission:", error)
          setCanManage(false)
        } finally {
          setLoading(false)
        }
      } else {
        setCanManage(false)
        setLoading(false)
      }
    }

    checkManagePermission()
  }, [user, taskId])

  return { canManage, loading }
}
