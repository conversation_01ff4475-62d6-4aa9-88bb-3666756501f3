"use client"

import { create } from "zustand"
import { Task } from "./task.types"
import { TaskService } from "./task.service"

/**
 * Task store state interface
 */
interface TaskState {
  // State
  tasks: Task[]
  currentTask: Task | null
  loading: boolean
  error: Error | null

  // Actions
  fetchTripTasks: (tripId: string) => Promise<void>
  fetchTask: (taskId: string) => Promise<void>

  // Store update methods
  addTask: (task: Task) => void
  updateTaskInStore: (task: Task) => void
  removeTask: (taskId: string) => void
  clearTasks: () => void
  setCurrentTask: (task: Task | null) => void
  setTasks: (tasks: Task[]) => void
}

/**
 * Task store with Zustand
 */
export const useTaskStore = create<TaskState>((set, get) => ({
  // Initial state
  tasks: [],
  currentTask: null,
  loading: false,
  error: null,

  // Actions
  fetchTripTasks: async (tripId: string) => {
    try {
      set({ loading: true, error: null })
      const tasks = await TaskService.getTripTasks(tripId)
      set({ tasks, loading: false })
    } catch (error) {
      console.error("Error fetching trip tasks:", error)
      set({ error: error as Error, loading: false })
    }
  },

  fetchTask: async (taskId: string) => {
    try {
      set({ loading: true, error: null })
      const task = await TaskService.getTask(taskId)
      if (task) {
        set({ currentTask: task, loading: false })
      } else {
        set({ error: new Error("Task not found"), loading: false })
      }
    } catch (error) {
      console.error("Error fetching task:", error)
      set({ error: error as Error, loading: false })
    }
  },

  // Store update methods
  addTask: (task: Task) => {
    const tasks = get().tasks

    // Only add if it doesn't already exist
    if (!tasks.some((t) => t.id === task.id)) {
      set({ tasks: [...tasks, task] })
    }
  },

  updateTaskInStore: (task: Task) => {
    const tasks = get().tasks
    const updatedTasks = tasks.map((t) => (t.id === task.id ? task : t))

    // Update current task if it's the one being edited
    if (get().currentTask?.id === task.id) {
      set({ currentTask: task })
    }

    set({ tasks: updatedTasks })
  },

  removeTask: (taskId: string) => {
    const tasks = get().tasks
    const updatedTasks = tasks.filter((task) => task.id !== taskId)

    // Clear current task if it's the one being deleted
    if (get().currentTask?.id === taskId) {
      set({ currentTask: null })
    }

    set({ tasks: updatedTasks })
  },

  clearTasks: () => {
    set({ tasks: [], currentTask: null })
  },

  setCurrentTask: (task) => {
    set({ currentTask: task })
  },

  setTasks: (tasks) => {
    set({ tasks })
  },
}))
