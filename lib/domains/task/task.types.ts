import { Timestamp } from "firebase/firestore"
import { BaseEntity } from "../base/base.types"

/**
 * Task category type
 */
export type TaskCategory = "planning" | "booking" | "preparation" | "coordination" | "during-trip"

/**
 * Task entity
 */
export interface Task extends BaseEntity {
  tripId: string
  title: string
  description?: string
  category: TaskCategory
  assigneeId: string
  dueDate: Timestamp | null
  completed: boolean
  createdBy: string
  tags?: string[] // Added tags for affiliate link matching
  // User display information stored directly in the task
  assigneeDisplayName?: string | null
  assigneePhotoURL?: string | null
}

/**
 * Task creation data
 */
export type TaskCreateData = Omit<Task, "id" | "createdAt" | "completed">

/**
 * Task update data
 */
export type TaskUpdateData = Partial<Task>

/**
 * Task with assignee information
 */
export interface TaskWithAssignee extends Task {
  assignee?: {
    uid: string
    displayName: string
    photoURL?: string
    email: string
  }
}
