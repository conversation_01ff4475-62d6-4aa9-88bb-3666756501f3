"use client"

import { getUserPreferences, updateUserTheme } from "@/lib/firebase/user-preferences-service"
import { ThemeType } from "./theme.types"

// Local storage key for theme
export const THEME_STORAGE_KEY = "togeda-theme"

// Helper functions for localStorage
export const getLocalStorageTheme = (): ThemeType | null => {
  if (typeof window === "undefined") return null
  const theme = window.localStorage.getItem(THEME_STORAGE_KEY)
  return (theme as ThemeType) || null
}

export const setLocalStorageTheme = (theme: ThemeType) => {
  if (typeof window === "undefined") return
  window.localStorage.setItem(THEME_STORAGE_KEY, theme)
}

/**
 * Theme service
 */
export const ThemeService = {
  /**
   * Get user theme from database
   */
  getUserTheme: async (userId: string): Promise<ThemeType | null> => {
    try {
      const userPreferences = await getUserPreferences(userId)
      return userPreferences?.theme || null
    } catch (error) {
      console.error("Error getting user theme:", error)
      return null
    }
  },

  /**
   * Update user theme in database
   */
  updateUserTheme: async (userId: string, theme: ThemeType): Promise<boolean> => {
    try {
      await updateUserTheme(userId, theme)
      return true
    } catch (error) {
      console.error("Error updating user theme:", error)
      return false
    }
  },
}
