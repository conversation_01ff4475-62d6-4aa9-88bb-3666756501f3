"use client"

import { useState, useCallback, useMemo } from "react"
import { useIsUserSubscribed } from "../user-subscription/user-subscription.hooks"
import { searchPlaces, EnhancedPlaceResult, PlacesSearchOptions } from "@/lib/google-places"

/**
 * Enhanced Google Places search hook with subscription-aware features
 */
export const useEnhancedPlacesSearch = () => {
  const isSubscribed = useIsUserSubscribed()
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Memoize subscription status to prevent re-renders
  const memoizedIsSubscribed = useMemo(() => isSubscribed, [isSubscribed])

  /**
   * Search places with enhanced features for Pro users
   */
  const searchPlacesWithEnhancements = useCallback(
    async (query: string, options?: PlacesSearchOptions): Promise<EnhancedPlaceResult[]> => {
      setLoading(true)
      setError(null)

      try {
        // Prepare search options based on subscription status
        const searchOptions: PlacesSearchOptions = {
          ...options,
          // Only include enhanced features for Pro users
          location: memoizedIsSubscribed ? options?.location : undefined,
          radius: memoizedIsSubscribed ? options?.radius : undefined,
          includeRatings: memoizedIsSubscribed ? (options?.includeRatings ?? true) : false,
        }

        const results = await searchPlaces(query, searchOptions)
        setLoading(false)
        return results
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : "Failed to search places"
        setError(errorMessage)
        setLoading(false)
        throw err
      }
    },
    [memoizedIsSubscribed]
  )

  /**
   * Search places with basic functionality (for free users)
   */
  const searchPlacesBasic = useCallback(
    async (query: string): Promise<EnhancedPlaceResult[]> => {
      return searchPlacesWithEnhancements(query, {
        includeRatings: false,
      })
    },
    [searchPlacesWithEnhancements]
  )

  /**
   * Search places with ratings and radius (Pro only)
   */
  const searchPlacesWithRatingsAndRadius = useCallback(
    async (
      query: string,
      location?: { lat: number; lng: number },
      radius?: number,
      type?: string
    ): Promise<EnhancedPlaceResult[]> => {
      if (!memoizedIsSubscribed) {
        // Fall back to basic search for free users
        return searchPlacesBasic(query)
      }

      return searchPlacesWithEnhancements(query, {
        location,
        radius: radius || 5000, // Default 5km radius
        type,
        includeRatings: true,
      })
    },
    [memoizedIsSubscribed, searchPlacesWithEnhancements, searchPlacesBasic]
  )

  /**
   * Get places by type with enhanced filtering (Pro only)
   */
  const searchPlacesByType = useCallback(
    async (
      query: string,
      type: string,
      location?: { lat: number; lng: number },
      radius?: number
    ): Promise<EnhancedPlaceResult[]> => {
      return searchPlacesWithRatingsAndRadius(query, location, radius, type)
    },
    [searchPlacesWithRatingsAndRadius]
  )

  return {
    loading,
    error,
    isSubscribed: memoizedIsSubscribed,
    searchPlacesWithEnhancements,
    searchPlacesBasic,
    searchPlacesWithRatingsAndRadius,
    searchPlacesByType,
  }
}

/**
 * Hook for restaurant search with activity preferences integration
 */
export const useRestaurantSearch = () => {
  const { searchPlacesByType, ...rest } = useEnhancedPlacesSearch()

  const searchRestaurants = useCallback(
    async (
      location: string,
      cuisinePreferences?: string[],
      diningExperience?: string[],
      dietaryNeeds?: string[],
      userLocation?: { lat: number; lng: number },
      radius?: number
    ): Promise<EnhancedPlaceResult[]> => {
      // Build search query with specific preferences
      let query = `restaurants in ${location}`

      if (cuisinePreferences && cuisinePreferences.length > 0) {
        // Use specific cuisine types for better results
        const cuisineQuery = cuisinePreferences
          .map((cuisine) => {
            // Map to more searchable terms
            switch (cuisine.toLowerCase()) {
              case "asian fusion":
                return "asian fusion restaurant"
              case "street food":
                return "street food vendor food truck"
              default:
                return `${cuisine} restaurant`
            }
          })
          .join(" OR ")
        query = `${cuisineQuery} in ${location}`
      }

      // Add dining experience modifiers
      if (diningExperience && diningExperience.length > 0) {
        const experienceModifiers = diningExperience
          .map((exp) => {
            switch (exp.toLowerCase()) {
              case "fine dining":
                return "fine dining upscale"
              case "local gems":
                return "local favorite hidden gem"
              case "budget-friendly":
                return "cheap affordable"
              case "rooftop / scenic views":
                return "rooftop view scenic"
              case "fast casual":
                return "fast casual quick"
              default:
                return exp.toLowerCase()
            }
          })
          .join(" ")
        query += ` ${experienceModifiers}`
      }

      // Add dietary needs
      if (dietaryNeeds && dietaryNeeds.length > 0 && !dietaryNeeds.includes("None")) {
        const dietaryQuery = dietaryNeeds.join(" ")
        query += ` ${dietaryQuery} friendly`
      }

      return searchPlacesByType(query, "restaurant", userLocation, radius)
    },
    [searchPlacesByType]
  )

  return {
    ...rest,
    searchRestaurants,
  }
}

/**
 * Hook for shopping search with activity preferences integration
 */
export const useShoppingSearch = () => {
  const { searchPlacesByType, ...rest } = useEnhancedPlacesSearch()

  const searchShopping = useCallback(
    async (
      location: string,
      shoppingStyle?: string[],
      budget?: string,
      focusAreas?: string[],
      userLocation?: { lat: number; lng: number },
      radius?: number
    ): Promise<EnhancedPlaceResult[]> => {
      // Build search query with specific shopping preferences
      let query = `shopping in ${location}`

      if (shoppingStyle && shoppingStyle.length > 0) {
        const styleQuery = shoppingStyle
          .map((style) => {
            switch (style.toLowerCase()) {
              case "boutiques":
                return "boutique shops"
              case "high-end / luxury":
                return "luxury shopping high-end stores"
              case "local markets":
                return "local market artisan shops"
              case "malls":
                return "shopping mall center"
              case "thrift / vintage":
                return "thrift store vintage shop"
              default:
                return style
            }
          })
          .join(" OR ")
        query = `${styleQuery} in ${location}`
      }

      // Add budget modifiers
      if (budget) {
        switch (budget.toLowerCase()) {
          case "budget":
            query += " affordable cheap discount"
            break
          case "high-end":
            query += " luxury premium upscale"
            break
          // mid-range doesn't need modifiers
        }
      }

      // Add focus area modifiers
      if (focusAreas && focusAreas.length > 0) {
        const focusQuery = focusAreas
          .map((area) => {
            switch (area.toLowerCase()) {
              case "tech & gadgets":
                return "electronics technology store"
              case "art & decor":
                return "art gallery home decor"
              default:
                return area.toLowerCase()
            }
          })
          .join(" ")
        query += ` ${focusQuery}`
      }

      return searchPlacesByType(query, "shopping_mall", userLocation, radius)
    },
    [searchPlacesByType]
  )

  return {
    ...rest,
    searchShopping,
  }
}

/**
 * Hook for entertainment search with activity preferences integration
 */
export const useEntertainmentSearch = () => {
  const { searchPlacesByType, ...rest } = useEnhancedPlacesSearch()

  const searchEntertainment = useCallback(
    async (
      location: string,
      venues?: string[],
      vibe?: string[],
      interests?: string[],
      userLocation?: { lat: number; lng: number },
      radius?: number
    ): Promise<EnhancedPlaceResult[]> => {
      // Build search query with specific entertainment preferences
      let query = `entertainment in ${location}`

      if (venues && venues.length > 0) {
        const venueQuery = venues
          .map((venue) => {
            switch (venue.toLowerCase()) {
              case "live music":
                return "live music venue concert hall"
              case "theater / performing arts":
                return "theater performing arts venue"
              case "comedy clubs":
                return "comedy club stand-up"
              case "nightclubs":
                return "nightclub dance club"
              case "outdoor events":
                return "outdoor venue amphitheater"
              default:
                return venue
            }
          })
          .join(" OR ")
        query = `${venueQuery} in ${location}`
      }

      // Add vibe modifiers
      if (vibe && vibe.length > 0) {
        const vibeModifiers = vibe
          .map((v) => {
            switch (v.toLowerCase()) {
              case "chill & relaxing":
                return "relaxed casual laid-back"
              case "trendy & upscale":
                return "trendy upscale sophisticated"
              case "high-energy":
                return "energetic lively exciting"
              case "family-friendly":
                return "family-friendly all-ages"
              default:
                return v.toLowerCase()
            }
          })
          .join(" ")
        query += ` ${vibeModifiers}`
      }

      // Add interest modifiers
      if (interests && interests.length > 0) {
        const interestQuery = interests
          .map((interest) => {
            switch (interest.toLowerCase()) {
              case "local shows":
                return "local performance show"
              case "cultural performances":
                return "cultural show traditional"
              case "dj sets":
                return "DJ electronic music"
              case "festivals":
                return "festival event"
              case "trivia / game nights":
                return "trivia game night bar"
              default:
                return interest.toLowerCase()
            }
          })
          .join(" ")
        query += ` ${interestQuery}`
      }

      return searchPlacesByType(query, "tourist_attraction", userLocation, radius)
    },
    [searchPlacesByType]
  )

  return {
    ...rest,
    searchEntertainment,
  }
}
