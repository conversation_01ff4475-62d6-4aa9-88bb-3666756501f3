import { Timestamp } from "firebase/firestore"
import { BaseEntity } from "../base/base.types"

/**
 * AI usage categories
 */
export enum AIUsageCategory {
  TRIP = "trip",
  TASK = "task",
  ITINERARY = "itinerary",
  NEW_USER_ONBOARDING = "new_user_onboarding",
  TRIP_CHAT = "trip_chat",
}

/**
 * Category usage period tracking
 */
export interface CategoryUsagePeriod {
  count: number
  lastReset: Timestamp | null
}

/**
 * Category usage tracking
 */
export interface CategoryUsageTracking {
  count: number
  lastReset: Timestamp | null
  daily?: CategoryUsagePeriod
  weekly?: CategoryUsagePeriod
  monthly?: CategoryUsagePeriod
}

/**
 * Trip chat specific usage tracking with rate limiting
 */
export interface TripChatUsageTracking extends CategoryUsageTracking {
  lastMinuteRequests: Timestamp[] // Track last requests for rate limiting (max 3 per minute)
  isProcessing: boolean // Prevent concurrent requests
  lastRequestTime: Timestamp | null
}

/**
 * User AI usage entity
 */
export interface UserAIUsage extends BaseEntity {
  userId: string

  // Legacy fields (kept for backward compatibility)
  aiUsageToday: number
  aiUsageThisWeek: number
  aiUsageLastReset: Timestamp | null
  aiUsageWeekStart: Timestamp | null

  // Total usage across all categories
  totalUsage?: {
    count: number
    lastReset: Timestamp | null
    daily?: CategoryUsagePeriod
    weekly?: CategoryUsagePeriod
    monthly?: CategoryUsagePeriod
  }

  // Category-specific usage tracking
  categoryUsage?: {
    [AIUsageCategory.TRIP]?: CategoryUsageTracking
    [AIUsageCategory.TASK]?: CategoryUsageTracking
    [AIUsageCategory.ITINERARY]?: CategoryUsageTracking
    [AIUsageCategory.NEW_USER_ONBOARDING]?: CategoryUsageTracking
    [AIUsageCategory.TRIP_CHAT]?: TripChatUsageTracking
  }
}

/**
 * AI usage limits
 */
export const AI_USAGE_LIMITS = {
  FREE: {
    // Legacy limits
    DAILY: 10,
    WEEKLY: 50,

    // Category-specific limits
    CATEGORIES: {
      [AIUsageCategory.TRIP]: 3,
      [AIUsageCategory.TASK]: 3,
      [AIUsageCategory.ITINERARY]: 3,
      [AIUsageCategory.NEW_USER_ONBOARDING]: Infinity, // No limits for new user onboarding
      [AIUsageCategory.TRIP_CHAT]: 0, // Free users cannot use trip chat
    },
  },
  PRO: {
    // Legacy limits
    DAILY: Infinity,
    WEEKLY: Infinity,

    // Category-specific limits
    CATEGORIES: {
      [AIUsageCategory.TRIP]: Infinity,
      [AIUsageCategory.TASK]: Infinity,
      [AIUsageCategory.ITINERARY]: Infinity,
      [AIUsageCategory.NEW_USER_ONBOARDING]: Infinity, // No limits for new user onboarding
      [AIUsageCategory.TRIP_CHAT]: 20, // Pro users get 20 trip chat requests per day
    },
  },
}

/**
 * Category usage data
 */
export interface CategoryUsageData {
  count: number
  limit: number
  canMakeRequest: boolean
}

/**
 * Period usage data
 */
export interface PeriodUsageData {
  count: number
  limit: number
}

/**
 * Total usage data
 */
export interface TotalUsageData {
  count: number
  daily?: PeriodUsageData
  weekly?: PeriodUsageData
  monthly?: PeriodUsageData
}

/**
 * Enhanced category usage data
 */
export interface EnhancedCategoryUsageData extends CategoryUsageData {
  daily?: PeriodUsageData
  weekly?: PeriodUsageData
  monthly?: PeriodUsageData
}

/**
 * AI usage summary
 */
export interface AIUsageSummary {
  // Legacy fields
  daily: number
  weekly: number
  dailyLimit: number
  weeklyLimit: number
  canMakeRequest: boolean

  // Total usage across all categories
  totalUsage?: TotalUsageData

  // New category-specific fields
  categories?: {
    [AIUsageCategory.TRIP]?: EnhancedCategoryUsageData
    [AIUsageCategory.TASK]?: EnhancedCategoryUsageData
    [AIUsageCategory.ITINERARY]?: EnhancedCategoryUsageData
    [AIUsageCategory.NEW_USER_ONBOARDING]?: EnhancedCategoryUsageData
    [AIUsageCategory.TRIP_CHAT]?: EnhancedCategoryUsageData
  }
}

/**
 * Trip chat AI usage limits and rate limiting constants
 */
export const TRIP_CHAT_LIMITS = {
  DAILY_LIMIT: 20, // Pro users only
  RATE_LIMIT_WINDOW_MS: 60 * 1000, // 1 minute
  MAX_REQUESTS_PER_MINUTE: 3,
  MAX_RESPONSE_LENGTH: 750, // characters
} as const
