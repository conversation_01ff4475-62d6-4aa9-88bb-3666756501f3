"use client"

import { create } from "zustand"
import { AIUsageSummary, AIUsageCategory } from "./user-ai-usage.types"
import { UserAIUsageService } from "./user-ai-usage.service"
import { UserAIUsageRealtimeService } from "./user-ai-usage.realtime.service"

/**
 * User AI usage store state interface
 */
interface UserAIUsageState {
  // State
  usage: AIUsageSummary | null
  loading: boolean
  error: Error | null

  // Real-time subscriptions
  usageSubscription: (() => void) | null

  // Actions
  fetchAIUsage: (userId: string, hasSubscription: boolean) => Promise<void>
  incrementAIUsage: (userId: string, category?: AIUsageCategory) => Promise<boolean>
  canMakeAIRequest: (
    userId: string,
    hasSubscription: boolean,
    category?: AIUsageCategory
  ) => Promise<boolean>
  subscribeToAIUsage: (userId: string, hasSubscription: boolean) => void
  unsubscribeFromAIUsage: () => void
}

/**
 * User AI usage store with Zustand
 */
export const useUserAIUsageStore = create<UserAIUsageState>((set, get) => ({
  // Initial state
  usage: null,
  loading: false,
  error: null,

  // Real-time subscriptions
  usageSubscription: null,

  // Actions
  fetchAIUsage: async (userId: string, hasSubscription: boolean) => {
    try {
      set({ loading: true, error: null })
      const usage = await UserAIUsageService.getAIUsage(userId, hasSubscription)
      set({ usage, loading: false })
    } catch (error) {
      console.error("Error fetching AI usage:", error)
      set({ error: error as Error, loading: false })
    }
  },

  incrementAIUsage: async (userId: string, category?: AIUsageCategory) => {
    try {
      set({ loading: true, error: null })
      const result = await UserAIUsageService.incrementAIUsage(userId, category)
      set({ loading: false })
      return result.success
    } catch (error) {
      console.error("Error incrementing AI usage:", error)
      set({ error: error as Error, loading: false })
      return false
    }
  },

  canMakeAIRequest: async (
    userId: string,
    hasSubscription: boolean,
    category?: AIUsageCategory
  ) => {
    try {
      set({ loading: true, error: null })
      const canMakeRequest = await UserAIUsageService.canMakeAIRequest(
        userId,
        hasSubscription,
        category
      )
      set({ loading: false })
      return canMakeRequest
    } catch (error) {
      console.error("Error checking if user can make AI request:", error)
      set({ error: error as Error, loading: false })
      return false
    }
  },

  subscribeToAIUsage: (userId: string, hasSubscription: boolean) => {
    // Unsubscribe from any existing subscription
    const { unsubscribeFromAIUsage } = get()
    unsubscribeFromAIUsage()

    // Subscribe to the AI usage
    const unsubscribe = UserAIUsageRealtimeService.subscribeToUserAIUsage(
      userId,
      hasSubscription,
      (usage, error) => {
        if (error) {
          set({ error, loading: false })
          return
        }

        set({ usage, loading: false })
      }
    )

    set({ usageSubscription: unsubscribe })
  },

  unsubscribeFromAIUsage: () => {
    const { usageSubscription } = get()
    if (usageSubscription) {
      usageSubscription()
      set({ usageSubscription: null })
    }
  },
}))
