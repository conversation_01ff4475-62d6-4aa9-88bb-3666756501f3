"use client"

import { useEffect } from "react"
import { useUserAIUsageStore } from "./user-ai-usage.store"
import { useUser } from "../auth/auth.hooks"
import { AIUsageCategory } from "./user-ai-usage.types"

// Export real-time hooks
export * from "./user-ai-usage.realtime.hooks"

/**
 * Hook to get AI usage for the current user
 */
export const useUserAIUsage = (
  hasSubscription: boolean | undefined,
  useRealtime: boolean = false
) => {
  const user = useUser()
  const userId = user?.uid || ""

  const {
    usage,
    loading,
    error,
    fetchAIUsage,
    incrementAIUsage,
    canMakeAIRequest,
    subscribeToAIUsage,
    unsubscribeFromAIUsage,
  } = useUserAIUsageStore()

  useEffect(() => {
    if (userId) {
      // Default to false (free user) if subscription status is undefined
      const subscriptionStatus = hasSubscription ?? false

      if (useRealtime) {
        // Use real-time subscription
        subscribeToAIUsage(userId, subscriptionStatus)

        // Cleanup on unmount
        return () => {
          unsubscribeFromAIUsage()
        }
      } else {
        // Use regular fetch
        fetchAIUsage(userId, subscriptionStatus)
      }
    }

    // Return empty cleanup function for TypeScript
    return () => {}
  }, [userId, hasSubscription, useRealtime])

  // Create convenience functions
  const incrementUsage = async (category?: AIUsageCategory) => {
    if (!userId) return false
    return incrementAIUsage(userId, category)
  }

  const checkCanMakeRequest = async (category?: AIUsageCategory) => {
    if (!userId) return false
    // Default to false (free user) if subscription status is undefined
    const subscriptionStatus = hasSubscription ?? false
    return canMakeAIRequest(userId, subscriptionStatus, category)
  }

  // Get category-specific usage data
  const getCategoryUsage = (category: AIUsageCategory) => {
    if (!usage || !usage.categories) return null
    return usage.categories[category]
  }

  return {
    usage,
    loading,
    error,
    incrementUsage,
    canMakeRequest: checkCanMakeRequest,
    getCategoryUsage,
  }
}
