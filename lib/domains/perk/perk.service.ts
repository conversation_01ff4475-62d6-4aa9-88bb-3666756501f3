import { db } from "@/lib/firebase"
import {
  collection,
  doc,
  getDoc,
  getDocs,
  setDoc,
  updateDoc,
  query,
  where,
  orderBy,
  serverTimestamp,
  runTransaction,
  Timestamp,
} from "firebase/firestore"
import { BaseService } from "../base/base.service"
import { ServiceResponse } from "../base/base.types"
import {
  GlobalPerk,
  GlobalPerkCreateData,
  GlobalPerkUpdateData,
  UserPerk,
  UserPerkCreateData,
  UserPerkUpdateData,
  PerkApplicationResult,
  UserPerkSummary,
  PerkEligibilityResult,
  PerkStatus,
} from "./perk.types"

/**
 * Perk service for Firebase operations
 */
export class PerkService extends BaseService {
  private static readonly GLOBAL_PERKS_PATH = "perks"
  private static readonly USER_PERKS_SUBCOLLECTION = "perks"

  /**
   * Get all global perk definitions
   */
  static async getGlobalPerks(): Promise<GlobalPerk[]> {
    try {
      const perksRef = collection(db, this.GLOBAL_PERKS_PATH)
      const querySnapshot = await getDocs(perksRef)

      return querySnapshot.docs.map((doc) => ({ id: doc.id, ...doc.data() }) as GlobalPerk)
    } catch (error) {
      console.error("Error getting global perks:", error)
      return []
    }
  }

  /**
   * Get a specific global perk
   */
  static async getGlobalPerk(perkId: string): Promise<GlobalPerk | null> {
    try {
      const perkRef = doc(db, this.GLOBAL_PERKS_PATH, perkId)
      const perkDoc = await getDoc(perkRef)

      if (perkDoc.exists()) {
        return { id: perkDoc.id, ...perkDoc.data() } as GlobalPerk
      }

      return null
    } catch (error) {
      console.error("Error getting global perk:", error)
      return null
    }
  }

  /**
   * Create a global perk definition
   */
  static async createGlobalPerk(
    perkData: GlobalPerkCreateData
  ): Promise<ServiceResponse<GlobalPerk>> {
    try {
      const perkRef = doc(collection(db, this.GLOBAL_PERKS_PATH))

      await setDoc(perkRef, {
        ...perkData,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
      })

      return {
        success: true,
        id: perkRef.id,
        data: {
          id: perkRef.id,
          ...perkData,
          createdAt: null, // Will be set by server
          updatedAt: null,
        } as GlobalPerk,
      }
    } catch (error) {
      console.error("Error creating global perk:", error)
      return { success: false, error }
    }
  }

  /**
   * Update a global perk definition
   */
  static async updateGlobalPerk(
    perkId: string,
    updateData: GlobalPerkUpdateData
  ): Promise<ServiceResponse> {
    try {
      const perkRef = doc(db, this.GLOBAL_PERKS_PATH, perkId)

      await updateDoc(perkRef, {
        ...updateData,
        updatedAt: serverTimestamp(),
      })

      return { success: true }
    } catch (error) {
      console.error("Error updating global perk:", error)
      return { success: false, error }
    }
  }

  /**
   * Get user's perks
   */
  static async getUserPerks(userId: string): Promise<UserPerk[]> {
    try {
      const perksRef = collection(db, "users", userId, this.USER_PERKS_SUBCOLLECTION)
      const q = query(perksRef, orderBy("unlockedAt", "desc"))
      const querySnapshot = await getDocs(q)

      return querySnapshot.docs.map((doc) => ({ id: doc.id, ...doc.data() }) as UserPerk)
    } catch (error) {
      console.error("Error getting user perks:", error)
      return []
    }
  }

  /**
   * Get a specific user perk
   */
  static async getUserPerk(userId: string, perkId: string): Promise<UserPerk | null> {
    try {
      const perkRef = doc(db, "users", userId, this.USER_PERKS_SUBCOLLECTION, perkId)
      const perkDoc = await getDoc(perkRef)

      if (perkDoc.exists()) {
        return { id: perkDoc.id, ...perkDoc.data() } as UserPerk
      }

      return null
    } catch (error) {
      console.error("Error getting user perk:", error)
      return null
    }
  }

  /**
   * Create a user perk instance
   */
  static async createUserPerk(
    userId: string,
    perkData: UserPerkCreateData
  ): Promise<ServiceResponse<UserPerk>> {
    try {
      const perkRef = doc(db, "users", userId, this.USER_PERKS_SUBCOLLECTION, perkData.perkId)

      await setDoc(perkRef, {
        ...perkData,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
      })

      return {
        success: true,
        id: perkRef.id,
        data: {
          id: perkRef.id,
          ...perkData,
          createdAt: null, // Will be set by server
          updatedAt: null,
        } as UserPerk,
      }
    } catch (error) {
      console.error("Error creating user perk:", error)
      return { success: false, error }
    }
  }

  /**
   * Update a user perk instance
   */
  static async updateUserPerk(
    userId: string,
    perkId: string,
    updateData: UserPerkUpdateData
  ): Promise<ServiceResponse> {
    try {
      const perkRef = doc(db, "users", userId, this.USER_PERKS_SUBCOLLECTION, perkId)

      await updateDoc(perkRef, {
        ...updateData,
        updatedAt: serverTimestamp(),
      })

      return { success: true }
    } catch (error) {
      console.error("Error updating user perk:", error)
      return { success: false, error }
    }
  }

  /**
   * Check which perks a user is eligible for based on referral count
   */
  static async checkPerkEligibility(
    userId: string,
    referralCount: number
  ): Promise<PerkEligibilityResult[]> {
    try {
      // Get all global perks
      const globalPerks = await this.getGlobalPerks()

      // Get user's existing perks
      const userPerks = await this.getUserPerks(userId)
      const unlockedPerkIds = new Set(userPerks.map((p) => p.perkId))

      const eligibilityResults: PerkEligibilityResult[] = []

      for (const globalPerk of globalPerks) {
        if (!globalPerk.isActive) continue

        const isEligible = referralCount >= globalPerk.referralCount
        const alreadyUnlocked = unlockedPerkIds.has(globalPerk.id)

        eligibilityResults.push({
          isEligible,
          perkId: globalPerk.id,
          currentReferrals: referralCount,
          requiredReferrals: globalPerk.referralCount,
          alreadyUnlocked,
        })
      }

      return eligibilityResults
    } catch (error) {
      console.error("Error checking perk eligibility:", error)
      return []
    }
  }

  /**
   * Unlock perks for a user based on their referral count
   */
  static async unlockEligiblePerks(userId: string, referralCount: number): Promise<string[]> {
    try {
      const eligibilityResults = await this.checkPerkEligibility(userId, referralCount)
      const newlyUnlockedPerks: string[] = []

      for (const result of eligibilityResults) {
        if (result.isEligible && !result.alreadyUnlocked) {
          // Get the global perk definition
          const globalPerk = await this.getGlobalPerk(result.perkId)
          if (!globalPerk) continue

          // Create user perk instance
          const userPerkData: UserPerkCreateData = {
            perkId: globalPerk.id,
            unlockedAt: serverTimestamp() as Timestamp,
            appliedAt: null,
            expiresAt: null, // Will be set when applied for non-permanent perks
            status: "unlocked",
            perkDetails: globalPerk,
          }

          const createResult = await this.createUserPerk(userId, userPerkData)
          if (createResult.success) {
            newlyUnlockedPerks.push(globalPerk.id)
          }
        }
      }

      return newlyUnlockedPerks
    } catch (error) {
      console.error("Error unlocking eligible perks:", error)
      return []
    }
  }

  /**
   * Process expired perks for all users (cron job)
   */
  static async processExpiredPerks(): Promise<{
    processedUsers: number
    expiredPerks: number
    errors: string[]
    duration: number
  }> {
    const startTime = Date.now()
    const errors: string[] = []
    let processedUsers = 0
    let expiredPerks = 0

    try {
      console.log("🕐 Starting perk expiration processing...")

      // Get all users with perks
      const usersWithPerks = await this.getUsersWithPerks()
      console.log(`📊 Found ${usersWithPerks.length} users with perks`)

      // Process users in batches
      const BATCH_SIZE = 50
      for (let i = 0; i < usersWithPerks.length; i += BATCH_SIZE) {
        const batch = usersWithPerks.slice(i, i + BATCH_SIZE)

        for (const userId of batch) {
          try {
            const userExpiredCount = await this.processExpiredPerksForUser(userId)
            expiredPerks += userExpiredCount
            processedUsers++
          } catch (error) {
            const errorMsg = `Failed to process perks for user ${userId}: ${error}`
            console.error(errorMsg)
            errors.push(errorMsg)
          }
        }

        // Small delay between batches
        if (i + BATCH_SIZE < usersWithPerks.length) {
          await new Promise((resolve) => setTimeout(resolve, 100))
        }
      }

      const duration = Date.now() - startTime
      console.log(`✅ Perk expiration processing completed in ${duration}ms`)
      console.log(`📊 Processed: ${processedUsers} users, ${expiredPerks} expired perks`)

      return {
        processedUsers,
        expiredPerks,
        errors,
        duration,
      }
    } catch (error) {
      const duration = Date.now() - startTime
      console.error("❌ Perk expiration processing failed:", error)
      return {
        processedUsers,
        expiredPerks,
        errors: [...errors, error instanceof Error ? error.message : "Unknown error"],
        duration,
      }
    }
  }

  /**
   * Process expired perks for a single user
   */
  static async processExpiredPerksForUser(userId: string): Promise<number> {
    try {
      const userPerks = await this.getUserPerks(userId)
      const now = new Date()
      let expiredCount = 0

      for (const perk of userPerks) {
        // Skip already expired perks
        if (perk.status === "expired") continue

        // Skip permanent perks (they never expire)
        if (perk.perkDetails.tags.includes("permanent")) {
          console.log(`Skipping permanent perk ${perk.perkId} for user ${userId}`)
          continue
        }

        // Check if perk has expired (only for non-permanent perks with expiration date)
        if (perk.expiresAt && perk.expiresAt.toMillis() < now.getTime()) {
          await this.updateUserPerk(userId, perk.perkId, { status: "expired" })
          expiredCount++
          console.log(
            `Expired ${perk.perkDetails.perkType} perk ${perk.perkId} for user ${userId} (expired at: ${perk.expiresAt.toDate()})`
          )
        } else if (!perk.expiresAt && !perk.perkDetails.tags.includes("permanent")) {
          // Log warning for non-permanent perks without expiration date
          console.warn(
            `Non-permanent perk ${perk.perkId} for user ${userId} has no expiration date. This may be a data inconsistency.`
          )
        }
      }

      return expiredCount
    } catch (error) {
      console.error(`Error processing expired perks for user ${userId}:`, error)
      throw error
    }
  }

  /**
   * Get list of users with perks
   */
  private static async getUsersWithPerks(): Promise<string[]> {
    try {
      // Get all users and check if they have perks subcollection
      const usersSnapshot = await getDocs(collection(db, "users"))
      const userIds: string[] = []

      for (const userDoc of usersSnapshot.docs) {
        const perksSnapshot = await getDocs(
          collection(db, "users", userDoc.id, this.USER_PERKS_SUBCOLLECTION)
        )
        if (!perksSnapshot.empty) {
          userIds.push(userDoc.id)
        }
      }

      return userIds
    } catch (error) {
      console.error("Error getting users with perks:", error)
      return []
    }
  }

  /**
   * Apply a subscription perk (add duration to user's subscription)
   */
  static async applySubscriptionPerk(
    userId: string,
    perkId: string
  ): Promise<PerkApplicationResult> {
    try {
      return await runTransaction(db, async (transaction) => {
        // Get the user perk
        const userPerkRef = doc(db, "users", userId, this.USER_PERKS_SUBCOLLECTION, perkId)
        const userPerkDoc = await transaction.get(userPerkRef)

        if (!userPerkDoc.exists()) {
          throw new Error("User perk not found")
        }

        const userPerk = userPerkDoc.data() as UserPerk

        if (userPerk.status !== "unlocked") {
          throw new Error("Perk is not in unlocked status")
        }

        if (userPerk.perkDetails.perkType !== "subscription") {
          throw new Error("Perk is not a subscription perk")
        }

        const now = serverTimestamp() as Timestamp

        // Update the perk status to applied
        transaction.update(userPerkRef, {
          status: "applied" as PerkStatus,
          appliedAt: now,
          updatedAt: now,
        })

        return {
          success: true,
          perkId,
          appliedAt: now,
        }
      })
    } catch (error) {
      console.error("Error applying subscription perk:", error)
      return {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error",
      }
    }
  }

  /**
   * Calculate user's perk summary for limit calculations
   */
  static async calculateUserPerkSummary(userId: string): Promise<UserPerkSummary> {
    try {
      const userPerks = await this.getUserPerks(userId)

      let additionalSquads = 0
      let additionalTrips = 0
      let additionalDailyAI = 0
      let additionalWeeklyAI = 0
      let subscriptionDaysAdded = 0
      const activePerkIds: string[] = []

      for (const perk of userPerks) {
        // Only count unlocked or applied perks
        if (perk.status === "expired") continue

        // Check if perk has expired (for non-permanent perks)
        if (perk.expiresAt && perk.expiresAt.toMillis() < Date.now()) {
          // Mark as expired
          await this.updateUserPerk(userId, perk.perkId, { status: "expired" })
          continue
        }

        activePerkIds.push(perk.perkId)

        // Add perk benefits based on type and tags
        const { perkValue, tags } = perk.perkDetails

        if (tags.includes("squad") && perkValue.maxSquads) {
          additionalSquads += perkValue.maxSquads
        }

        if (tags.includes("trip") && perkValue.maxTrips) {
          additionalTrips += perkValue.maxTrips
        }

        if (perkValue.dailyRequests) {
          additionalDailyAI += perkValue.dailyRequests
        }

        if (perkValue.weeklyRequests) {
          additionalWeeklyAI += perkValue.weeklyRequests
        }

        // For subscription perks that have been applied
        if (perk.status === "applied" && tags.includes("subscription") && perkValue.duration) {
          subscriptionDaysAdded += perkValue.duration
        }
      }

      return {
        additionalSquads,
        additionalTrips,
        additionalDailyAI,
        additionalWeeklyAI,
        subscriptionDaysAdded,
        activePerkIds,
      }
    } catch (error) {
      console.error("Error calculating user perk summary:", error)
      return {
        additionalSquads: 0,
        additionalTrips: 0,
        additionalDailyAI: 0,
        additionalWeeklyAI: 0,
        subscriptionDaysAdded: 0,
        activePerkIds: [],
      }
    }
  }
}
