import { db } from "@/lib/firebase"
import { collection, doc, onSnapshot, query, orderBy, Unsubscribe } from "firebase/firestore"
import { BaseRealtimeService } from "../base/base.realtime.service"
import { GlobalPerk, UserPerk } from "./perk.types"

/**
 * Perk realtime service for Firebase operations
 */
export class PerkRealtimeService extends BaseRealtimeService {
  private static readonly GLOBAL_PERKS_PATH = "perks"
  private static readonly USER_PERKS_SUBCOLLECTION = "perks"

  /**
   * Subscribe to global perks changes
   */
  static subscribeToGlobalPerks(
    callback: (perks: GlobalPerk[]) => void,
    onError?: (error: Error) => void
  ): Unsubscribe {
    try {
      const perksRef = collection(db, this.GLOBAL_PERKS_PATH)

      const q = query(perksRef, orderBy("referralCount", "asc"))

      return onSnapshot(
        q,
        (snapshot) => {
          try {
            const perks: GlobalPerk[] = snapshot.docs.map(
              (doc) =>
                ({
                  id: doc.id,
                  ...doc.data(),
                }) as GlobalPerk
            )

            callback(perks)
          } catch (error) {
            console.error("Error processing global perks snapshot:", error)
            onError?.(error as Error)
          }
        },
        (error) => {
          console.error("Error in global perks subscription:", error)
          onError?.(error)
        }
      )
    } catch (error) {
      console.error("Error setting up global perks subscription:", error)
      onError?.(error as Error)
      return () => {} // Return empty unsubscribe function
    }
  }

  /**
   * Subscribe to user's perks changes
   */
  static subscribeToUserPerks(
    userId: string,
    callback: (perks: UserPerk[]) => void,
    onError?: (error: Error) => void
  ): Unsubscribe {
    try {
      const perksRef = collection(db, "users", userId, this.USER_PERKS_SUBCOLLECTION)
      const q = query(perksRef, orderBy("unlockedAt", "desc"))

      return onSnapshot(
        q,
        (snapshot) => {
          try {
            const perks: UserPerk[] = snapshot.docs.map(
              (doc) =>
                ({
                  id: doc.id,
                  ...doc.data(),
                }) as UserPerk
            )

            callback(perks)
          } catch (error) {
            console.error("Error processing user perks snapshot:", error)
            onError?.(error as Error)
          }
        },
        (error) => {
          console.error("Error in user perks subscription:", error)
          onError?.(error)
        }
      )
    } catch (error) {
      console.error("Error setting up user perks subscription:", error)
      onError?.(error as Error)
      return () => {} // Return empty unsubscribe function
    }
  }

  /**
   * Subscribe to a specific user perk
   */
  static subscribeToUserPerk(
    userId: string,
    perkId: string,
    callback: (perk: UserPerk | null) => void,
    onError?: (error: Error) => void
  ): Unsubscribe {
    try {
      const perkRef = doc(db, "users", userId, this.USER_PERKS_SUBCOLLECTION, perkId)

      return onSnapshot(
        perkRef,
        (snapshot) => {
          try {
            if (snapshot.exists()) {
              const perk: UserPerk = {
                id: snapshot.id,
                ...snapshot.data(),
              } as UserPerk

              callback(perk)
            } else {
              callback(null)
            }
          } catch (error) {
            console.error("Error processing user perk snapshot:", error)
            onError?.(error as Error)
          }
        },
        (error) => {
          console.error("Error in user perk subscription:", error)
          onError?.(error)
        }
      )
    } catch (error) {
      console.error("Error setting up user perk subscription:", error)
      onError?.(error as Error)
      return () => {} // Return empty unsubscribe function
    }
  }

  /**
   * Subscribe to a specific global perk
   */
  static subscribeToGlobalPerk(
    perkId: string,
    callback: (perk: GlobalPerk | null) => void,
    onError?: (error: Error) => void
  ): Unsubscribe {
    try {
      const perkRef = doc(db, this.GLOBAL_PERKS_PATH, perkId)

      return onSnapshot(
        perkRef,
        (snapshot) => {
          try {
            if (snapshot.exists()) {
              const perk: GlobalPerk = {
                id: snapshot.id,
                ...snapshot.data(),
              } as GlobalPerk

              callback(perk)
            } else {
              callback(null)
            }
          } catch (error) {
            console.error("Error processing global perk snapshot:", error)
            onError?.(error as Error)
          }
        },
        (error) => {
          console.error("Error in global perk subscription:", error)
          onError?.(error)
        }
      )
    } catch (error) {
      console.error("Error setting up global perk subscription:", error)
      onError?.(error as Error)
      return () => {} // Return empty unsubscribe function
    }
  }
}
