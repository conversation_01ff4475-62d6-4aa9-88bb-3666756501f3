"use client"

import { create } from "zustand"
import { persist } from "zustand/middleware"
import { GlobalPerk, UserPerk, UserPerkSummary } from "./perk.types"
import { PerkService } from "./perk.service"
import { PerkSubscriptionService } from "../user-subscription/perk-subscription.service"

/**
 * Perk store state interface
 */
interface PerkState {
  // State
  globalPerks: GlobalPerk[]
  userPerks: UserPerk[]
  userPerkSummary: UserPerkSummary | null
  loading: boolean
  error: Error | null

  // Actions
  setGlobalPerks: (perks: GlobalPerk[]) => void
  setUserPerks: (perks: UserPerk[]) => void
  setUserPerkSummary: (summary: UserPerkSummary | null) => void
  setLoading: (loading: boolean) => void
  setError: (error: Error | null) => void

  // Async actions
  fetchGlobalPerks: () => Promise<void>
  fetchUserPerks: (userId: string) => Promise<void>
  fetchUserPerkSummary: (userId: string) => Promise<void>
  unlockEligiblePerks: (userId: string, referralCount: number) => Promise<string[]>
  applySubscriptionPerk: (userId: string, perkId: string) => Promise<boolean>

  // Computed values
  getUnlockedPerks: () => UserPerk[]
  getAppliedPerks: () => UserPerk[]
  getExpiredPerks: () => UserPerk[]
  hasUnlockedPerk: (perkId: string) => boolean
  getAdditionalSquadLimit: () => number
  getAdditionalTripLimit: () => number
}

/**
 * Perk store with Zustand
 */
export const usePerkStore = create<PerkState>()(
  persist(
    (set, get) => ({
      // Initial state
      globalPerks: [],
      userPerks: [],
      userPerkSummary: null,
      loading: false,
      error: null,

      // Actions
      setGlobalPerks: (perks) => set({ globalPerks: perks }),
      setUserPerks: (perks) => set({ userPerks: perks }),
      setUserPerkSummary: (summary) => set({ userPerkSummary: summary }),
      setLoading: (loading) => set({ loading }),
      setError: (error) => set({ error }),

      // Async actions
      fetchGlobalPerks: async () => {
        try {
          set({ loading: true, error: null })
          const perks = await PerkService.getGlobalPerks()
          set({ globalPerks: perks })
        } catch (error) {
          console.error("Error fetching global perks:", error)
          set({ error: error as Error })
        } finally {
          set({ loading: false })
        }
      },

      fetchUserPerks: async (userId: string) => {
        try {
          set({ loading: true, error: null })
          const perks = await PerkService.getUserPerks(userId)
          set({ userPerks: perks })
        } catch (error) {
          console.error("Error fetching user perks:", error)
          set({ error: error as Error })
        } finally {
          set({ loading: false })
        }
      },

      fetchUserPerkSummary: async (userId: string) => {
        try {
          set({ loading: true, error: null })
          const summary = await PerkService.calculateUserPerkSummary(userId)
          set({ userPerkSummary: summary })
        } catch (error) {
          console.error("Error fetching user perk summary:", error)
          set({ error: error as Error })
        } finally {
          set({ loading: false })
        }
      },

      unlockEligiblePerks: async (userId: string, referralCount: number) => {
        try {
          set({ loading: true, error: null })
          const unlockedPerkIds = await PerkService.unlockEligiblePerks(userId, referralCount)

          // Refresh user perks and summary
          const perks = await PerkService.getUserPerks(userId)
          const summary = await PerkService.calculateUserPerkSummary(userId)

          set({ userPerks: perks, userPerkSummary: summary })

          return unlockedPerkIds
        } catch (error) {
          console.error("Error unlocking eligible perks:", error)
          set({ error: error as Error })
          return []
        } finally {
          set({ loading: false })
        }
      },

      applySubscriptionPerk: async (userId: string, perkId: string) => {
        try {
          set({ loading: true, error: null })
          const result = await PerkSubscriptionService.applySubscriptionPerk(userId, perkId)

          if (result.success) {
            // Refresh user perks and summary
            const perks = await PerkService.getUserPerks(userId)
            const summary = await PerkService.calculateUserPerkSummary(userId)

            set({ userPerks: perks, userPerkSummary: summary })
          }

          return result.success
        } catch (error) {
          console.error("Error applying subscription perk:", error)
          set({ error: error as Error })
          return false
        } finally {
          set({ loading: false })
        }
      },

      // Computed values
      getUnlockedPerks: () => {
        const { userPerks } = get()
        return userPerks.filter((perk) => perk.status === "unlocked")
      },

      getAppliedPerks: () => {
        const { userPerks } = get()
        return userPerks.filter((perk) => perk.status === "applied")
      },

      getExpiredPerks: () => {
        const { userPerks } = get()
        return userPerks.filter((perk) => perk.status === "expired")
      },

      hasUnlockedPerk: (perkId: string) => {
        const { userPerks } = get()
        return userPerks.some((perk) => perk.perkId === perkId && perk.status !== "expired")
      },

      getAdditionalSquadLimit: () => {
        const { userPerkSummary } = get()
        return userPerkSummary?.additionalSquads || 0
      },

      getAdditionalTripLimit: () => {
        const { userPerkSummary } = get()
        return userPerkSummary?.additionalTrips || 0
      },
    }),
    {
      name: "perk-store",
      partialize: (state) => ({
        globalPerks: state.globalPerks,
        userPerks: state.userPerks,
        userPerkSummary: state.userPerkSummary,
      }),
    }
  )
)
