"use client"

import { useEffect, useState } from "react"
import { useUser } from "../auth/auth.hooks"
import { PerkRealtimeService } from "./perk.realtime.service"
import { GlobalPerk, UserPerk } from "./perk.types"

/**
 * Hook for real-time global perks updates
 */
export const useGlobalPerksRealtime = () => {
  const [globalPerks, setGlobalPerks] = useState<GlobalPerk[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<Error | null>(null)

  useEffect(() => {
    setLoading(true)
    setError(null)

    const unsubscribe = PerkRealtimeService.subscribeToGlobalPerks(
      (perks) => {
        setGlobalPerks(perks)
        setLoading(false)
      },
      (error) => {
        setError(error)
        setLoading(false)
      }
    )

    return unsubscribe
  }, [])

  return { globalPerks, loading, error }
}

/**
 * Hook for real-time user perks updates
 */
export const useUserPerksRealtime = () => {
  const user = useUser()
  const [userPerks, setUserPerks] = useState<UserPerk[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<Error | null>(null)

  useEffect(() => {
    if (!user?.uid) {
      setUserPerks([])
      setLoading(false)
      return
    }

    setLoading(true)
    setError(null)

    const unsubscribe = PerkRealtimeService.subscribeToUserPerks(
      user.uid,
      (perks) => {
        setUserPerks(perks)
        setLoading(false)
      },
      (error) => {
        setError(error)
        setLoading(false)
      }
    )

    return unsubscribe
  }, [user?.uid])

  return { userPerks, loading, error }
}

/**
 * Hook for real-time specific user perk updates
 */
export const useUserPerkRealtime = (perkId: string | null) => {
  const user = useUser()
  const [userPerk, setUserPerk] = useState<UserPerk | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<Error | null>(null)

  useEffect(() => {
    if (!user?.uid || !perkId) {
      setUserPerk(null)
      setLoading(false)
      return
    }

    setLoading(true)
    setError(null)

    const unsubscribe = PerkRealtimeService.subscribeToUserPerk(
      user.uid,
      perkId,
      (perk) => {
        setUserPerk(perk)
        setLoading(false)
      },
      (error) => {
        setError(error)
        setLoading(false)
      }
    )

    return unsubscribe
  }, [user?.uid, perkId])

  return { userPerk, loading, error }
}

/**
 * Hook for real-time specific global perk updates
 */
export const useGlobalPerkRealtime = (perkId: string | null) => {
  const [globalPerk, setGlobalPerk] = useState<GlobalPerk | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<Error | null>(null)

  useEffect(() => {
    if (!perkId) {
      setGlobalPerk(null)
      setLoading(false)
      return
    }

    setLoading(true)
    setError(null)

    const unsubscribe = PerkRealtimeService.subscribeToGlobalPerk(
      perkId,
      (perk) => {
        setGlobalPerk(perk)
        setLoading(false)
      },
      (error) => {
        setError(error)
        setLoading(false)
      }
    )

    return unsubscribe
  }, [perkId])

  return { globalPerk, loading, error }
}

/**
 * Combined hook for all perk data with real-time updates
 */
export const usePerksRealtime = () => {
  const user = useUser()
  const { globalPerks, loading: globalLoading, error: globalError } = useGlobalPerksRealtime()
  const { userPerks, loading: userLoading, error: userError } = useUserPerksRealtime()

  const loading = globalLoading || userLoading
  const error = globalError || userError

  // Computed values
  const unlockedPerks = userPerks.filter((perk) => perk.status === "unlocked")
  const appliedPerks = userPerks.filter((perk) => perk.status === "applied")
  const expiredPerks = userPerks.filter((perk) => perk.status === "expired")

  const hasUnlockedPerk = (perkId: string) => {
    return userPerks.some((perk) => perk.perkId === perkId && perk.status !== "expired")
  }

  // Calculate additional limits from perks
  const calculateAdditionalLimits = () => {
    let additionalSquads = 0
    let additionalTrips = 0
    let additionalDailyAI = 0
    let additionalWeeklyAI = 0

    for (const perk of userPerks) {
      if (perk.status === "expired") continue

      // Check if perk has expired (for non-permanent perks)
      if (perk.expiresAt && perk.expiresAt.toMillis() < Date.now()) {
        continue
      }

      const { perkValue, tags } = perk.perkDetails

      if (tags.includes("squad") && perkValue.maxSquads) {
        additionalSquads += perkValue.maxSquads
      }

      if (tags.includes("trip") && perkValue.maxTrips) {
        additionalTrips += perkValue.maxTrips
      }

      if (perkValue.dailyRequests) {
        additionalDailyAI += perkValue.dailyRequests
      }

      if (perkValue.weeklyRequests) {
        additionalWeeklyAI += perkValue.weeklyRequests
      }
    }

    return {
      additionalSquads,
      additionalTrips,
      additionalDailyAI,
      additionalWeeklyAI,
    }
  }

  const additionalLimits = calculateAdditionalLimits()

  return {
    user,
    globalPerks,
    userPerks,
    loading,
    error,
    unlockedPerks,
    appliedPerks,
    expiredPerks,
    hasUnlockedPerk,
    additionalLimits,
  }
}
