import { BaseRealtimeService } from "../base/base.realtime.service"
import { ActivityPreferences } from "./activity-preferences.types"

/**
 * Activity preferences real-time service for Firebase real-time operations
 */
export class ActivityPreferencesRealtimeService {
  private static readonly COLLECTION = "activityPreferences"

  /**
   * Subscribe to activity preferences by user ID
   * @param userId User ID
   * @param callback Callback function to handle preferences changes
   * @returns Unsubscribe function
   */
  static subscribeToActivityPreferences(
    userId: string,
    callback: (preferences: ActivityPreferences | null, error?: Error) => void
  ): () => void {
    return BaseRealtimeService.subscribeToDocument<ActivityPreferences>(
      this.COLLECTION,
      userId,
      callback
    )
  }
}
