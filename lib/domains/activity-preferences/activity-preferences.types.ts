import { Timestamp } from "firebase/firestore"
import { BaseEntity } from "../base/base.types"

/**
 * Eateries preferences
 */
export interface EateriesPreferences {
  cuisineTypes: string[]
  diningExperience: string[]
  dietaryNeeds: string[]
}

/**
 * Shopping preferences
 */
export interface ShoppingPreferences {
  style: string[]
  budget: string
  focusAreas: string[]
}

/**
 * Entertainment preferences
 */
export interface EntertainmentPreferences {
  venues: string[]
  vibe: string[]
  interests: string[]
}

/**
 * Activity preferences entity
 */
export interface ActivityPreferences extends BaseEntity {
  userId: string
  eateries: EateriesPreferences
  shopping: ShoppingPreferences
  entertainment: EntertainmentPreferences
}

/**
 * Activity preferences create data (without BaseEntity fields)
 */
export interface ActivityPreferencesCreateData {
  userId: string
  eateries: EateriesPreferences
  shopping: ShoppingPreferences
  entertainment: EntertainmentPreferences
}

/**
 * Activity preferences update data (partial)
 */
export interface ActivityPreferencesUpdateData {
  eateries?: Partial<EateriesPreferences>
  shopping?: Partial<ShoppingPreferences>
  entertainment?: Partial<EntertainmentPreferences>
}

/**
 * Default activity preferences structure
 */
export const DEFAULT_ACTIVITY_PREFERENCES: Omit<ActivityPreferences, keyof BaseEntity | "userId"> =
  {
    eateries: {
      cuisineTypes: [],
      diningExperience: [],
      dietaryNeeds: [],
    },
    shopping: {
      style: [],
      budget: "mid-range",
      focusAreas: [],
    },
    entertainment: {
      venues: [],
      vibe: [],
      interests: [],
    },
  }

/**
 * Available preference options with affiliate link tag mappings
 */
export const PREFERENCE_OPTIONS = {
  eateries: {
    cuisineTypes: [
      "Mexican",
      "Italian",
      "Vegan",
      "Seafood",
      "Street Food",
      "Asian Fusion",
      "American",
    ],
    diningExperience: [
      "Fine Dining",
      "Local Gems",
      "Budget-Friendly",
      "Rooftop / Scenic Views",
      "Fast Casual",
    ],
    dietaryNeeds: ["Vegetarian", "Gluten-Free", "Halal", "Kosher", "None"],
  },
  shopping: {
    style: ["Boutiques", "High-End / Luxury", "Local Markets", "Malls", "Thrift / Vintage"],
    budget: ["budget", "mid-range", "high-end"],
    focusAreas: ["Fashion", "Tech & Gadgets", "Souvenirs", "Home Goods", "Art & Decor"],
  },
  entertainment: {
    venues: [
      "Live Music",
      "Theater / Performing Arts",
      "Comedy Clubs",
      "Nightclubs",
      "Outdoor Events",
    ],
    vibe: ["Chill & Relaxing", "Trendy & Upscale", "High-Energy", "Family-Friendly"],
    interests: [
      "Local Shows",
      "Cultural Performances",
      "DJ Sets",
      "Festivals",
      "Trivia / Game Nights",
    ],
  },
} as const

/**
 * Mapping of activity preferences to affiliate link tags
 * This ensures proper tagging for future affiliate link integration
 */
export const ACTIVITY_PREFERENCE_TAG_MAPPING = {
  eateries: {
    // All eateries preferences map to dining/restaurant tags
    baseTags: ["dining", "restaurant", "food"],
    cuisineTypes: {
      Mexican: ["mexican", "dining", "restaurant"],
      Italian: ["italian", "dining", "restaurant"],
      Vegan: ["vegan", "dining", "restaurant"],
      Seafood: ["seafood", "dining", "restaurant"],
      "Street Food": ["street food", "local", "dining"],
      "Asian Fusion": ["asian", "dining", "restaurant"],
      American: ["american", "dining", "restaurant"],
    },
    diningExperience: {
      "Fine Dining": ["fine dining", "restaurant", "dining"],
      "Local Gems": ["local", "dining", "restaurant"],
      "Budget-Friendly": ["budget", "dining", "restaurant"],
      "Rooftop / Scenic Views": ["scenic", "dining", "restaurant"],
      "Fast Casual": ["casual", "dining", "restaurant"],
    },
    dietaryNeeds: {
      Vegetarian: ["vegetarian", "dining", "restaurant"],
      "Gluten-Free": ["gluten-free", "dining", "restaurant"],
      Halal: ["halal", "dining", "restaurant"],
      Kosher: ["kosher", "dining", "restaurant"],
      None: ["dining", "restaurant"],
    },
  },
  shopping: {
    // All shopping preferences map to shopping/market tags
    baseTags: ["shopping", "market"],
    style: {
      Boutiques: ["boutique", "shopping"],
      "High-End / Luxury": ["luxury", "shopping"],
      "Local Markets": ["local", "market", "shopping"],
      Malls: ["mall", "shopping"],
      "Thrift / Vintage": ["vintage", "shopping"],
    },
    budget: {
      budget: ["budget", "shopping"],
      "mid-range": ["shopping"],
      "high-end": ["luxury", "shopping"],
    },
    focusAreas: {
      Fashion: ["fashion", "shopping", "clothing"],
      "Tech & Gadgets": ["tech", "shopping", "equipment"],
      Souvenirs: ["souvenirs", "shopping"],
      "Home Goods": ["home", "shopping"],
      "Art & Decor": ["art", "shopping"],
    },
  },
  entertainment: {
    // All entertainment preferences map to entertainment/activity tags
    baseTags: ["entertainment", "activity"],
    venues: {
      "Live Music": ["music", "entertainment", "show"],
      "Theater / Performing Arts": ["theater", "entertainment", "show"],
      "Comedy Clubs": ["comedy", "entertainment", "nightlife"],
      Nightclubs: ["nightlife", "entertainment", "club"],
      "Outdoor Events": ["outdoor", "entertainment", "activity"],
    },
    vibe: {
      "Chill & Relaxing": ["relaxation", "entertainment"],
      "Trendy & Upscale": ["trendy", "entertainment"],
      "High-Energy": ["high-energy", "entertainment"],
      "Family-Friendly": ["family", "entertainment"],
    },
    interests: {
      "Local Shows": ["local", "show", "entertainment"],
      "Cultural Performances": ["cultural", "entertainment", "show"],
      "DJ Sets": ["music", "entertainment", "nightlife"],
      Festivals: ["festival", "entertainment", "activity"],
      "Trivia / Game Nights": ["games", "entertainment", "nightlife"],
    },
  },
} as const

/**
 * Generate affiliate link tags from activity preferences
 * @param preferences The user's activity preferences
 * @returns Array of tags for affiliate link matching
 */
export function generateAffiliateTagsFromPreferences(preferences: ActivityPreferences): string[] {
  const tags = new Set<string>()

  // Add eateries tags
  if (preferences.eateries) {
    tags.add(...ACTIVITY_PREFERENCE_TAG_MAPPING.eateries.baseTags)

    preferences.eateries.cuisineTypes?.forEach((cuisine) => {
      const cuisineTags =
        ACTIVITY_PREFERENCE_TAG_MAPPING.eateries.cuisineTypes[
          cuisine as keyof typeof ACTIVITY_PREFERENCE_TAG_MAPPING.eateries.cuisineTypes
        ]
      if (cuisineTags) tags.add(...cuisineTags)
    })

    preferences.eateries.diningExperience?.forEach((experience) => {
      const experienceTags =
        ACTIVITY_PREFERENCE_TAG_MAPPING.eateries.diningExperience[
          experience as keyof typeof ACTIVITY_PREFERENCE_TAG_MAPPING.eateries.diningExperience
        ]
      if (experienceTags) tags.add(...experienceTags)
    })

    preferences.eateries.dietaryNeeds?.forEach((dietary) => {
      const dietaryTags =
        ACTIVITY_PREFERENCE_TAG_MAPPING.eateries.dietaryNeeds[
          dietary as keyof typeof ACTIVITY_PREFERENCE_TAG_MAPPING.eateries.dietaryNeeds
        ]
      if (dietaryTags) tags.add(...dietaryTags)
    })
  }

  // Add shopping tags
  if (preferences.shopping) {
    tags.add(...ACTIVITY_PREFERENCE_TAG_MAPPING.shopping.baseTags)

    preferences.shopping.style?.forEach((style) => {
      const styleTags =
        ACTIVITY_PREFERENCE_TAG_MAPPING.shopping.style[
          style as keyof typeof ACTIVITY_PREFERENCE_TAG_MAPPING.shopping.style
        ]
      if (styleTags) tags.add(...styleTags)
    })

    if (preferences.shopping.budget) {
      const budgetTags =
        ACTIVITY_PREFERENCE_TAG_MAPPING.shopping.budget[
          preferences.shopping
            .budget as keyof typeof ACTIVITY_PREFERENCE_TAG_MAPPING.shopping.budget
        ]
      if (budgetTags) tags.add(...budgetTags)
    }

    preferences.shopping.focusAreas?.forEach((focus) => {
      const focusTags =
        ACTIVITY_PREFERENCE_TAG_MAPPING.shopping.focusAreas[
          focus as keyof typeof ACTIVITY_PREFERENCE_TAG_MAPPING.shopping.focusAreas
        ]
      if (focusTags) tags.add(...focusTags)
    })
  }

  // Add entertainment tags
  if (preferences.entertainment) {
    tags.add(...ACTIVITY_PREFERENCE_TAG_MAPPING.entertainment.baseTags)

    preferences.entertainment.venues?.forEach((venue) => {
      const venueTags =
        ACTIVITY_PREFERENCE_TAG_MAPPING.entertainment.venues[
          venue as keyof typeof ACTIVITY_PREFERENCE_TAG_MAPPING.entertainment.venues
        ]
      if (venueTags) tags.add(...venueTags)
    })

    preferences.entertainment.vibe?.forEach((vibe) => {
      const vibeTags =
        ACTIVITY_PREFERENCE_TAG_MAPPING.entertainment.vibe[
          vibe as keyof typeof ACTIVITY_PREFERENCE_TAG_MAPPING.entertainment.vibe
        ]
      if (vibeTags) tags.add(...vibeTags)
    })

    preferences.entertainment.interests?.forEach((interest) => {
      const interestTags =
        ACTIVITY_PREFERENCE_TAG_MAPPING.entertainment.interests[
          interest as keyof typeof ACTIVITY_PREFERENCE_TAG_MAPPING.entertainment.interests
        ]
      if (interestTags) tags.add(...interestTags)
    })
  }

  return Array.from(tags)
}
