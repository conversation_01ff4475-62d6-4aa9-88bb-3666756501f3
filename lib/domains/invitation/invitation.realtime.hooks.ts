"use client"

import { useEffect, useState, useMemo } from "react"
import { Invitation, InvitationLink, InvitationSend } from "./invitation.types"
import { InvitationRealtimeService } from "./invitation.realtime.service"
import { useUser, useUserData } from "@/lib/domains/auth/auth.hooks"

/**
 * Hook to get real-time updates for a specific invitation
 */
export const useRealtimeInvitation = (invitationId: string) => {
  const [invitation, setInvitation] = useState<Invitation | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<Error | null>(null)

  useEffect(() => {
    if (!invitationId) {
      setInvitation(null)
      setLoading(false)
      return () => {}
    }

    setLoading(true)

    const unsubscribe = InvitationRealtimeService.subscribeToInvitation(
      invitationId,
      (data, err) => {
        if (err) {
          console.error("Error getting real-time invitation:", err)
          setError(err)
          setLoading(false)
          return
        }

        setInvitation(data)
        setLoading(false)
      }
    )

    return () => unsubscribe()
  }, [invitationId])

  return { invitation, loading, error }
}

/**
 * Hook to get real-time updates for squad invitations
 */
export const useRealtimeSquadInvitations = (squadId: string) => {
  const [invitations, setInvitations] = useState<Invitation[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<Error | null>(null)

  useEffect(() => {
    if (!squadId) {
      setInvitations([])
      setLoading(false)
      return () => {}
    }

    setLoading(true)

    const unsubscribe = InvitationRealtimeService.subscribeToSquadInvitations(
      squadId,
      (data, err) => {
        if (err) {
          console.error("Error getting real-time squad invitations:", err)
          setError(err)
          setLoading(false)
          return
        }

        setInvitations(data)
        setLoading(false)
      }
    )

    return () => unsubscribe()
  }, [squadId])

  return { invitations, loading, error }
}

/**
 * Hook to get real-time updates for user invitations
 */
export const useRealtimeUserInvitations = () => {
  const userData = useUserData()
  const [invitations, setInvitations] = useState<Invitation[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<Error | null>(null)

  useEffect(() => {
    if (!userData?.email) {
      setInvitations([])
      setLoading(false)
      return () => {}
    }

    setLoading(true)

    const unsubscribe = InvitationRealtimeService.subscribeToUserInvitations(
      userData.email,
      (data, err) => {
        if (err) {
          console.error("Error getting real-time user invitations:", err)
          setError(err)
          setLoading(false)
          return
        }

        setInvitations(data)
        setLoading(false)
      }
    )

    return () => unsubscribe()
  }, [userData])

  return { invitations, loading, error }
}

/**
 * Hook to get real-time updates for an invitation link
 */
export const useRealtimeInvitationLink = (invitationId: string) => {
  const [invitationLink, setInvitationLink] = useState<InvitationLink | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<Error | null>(null)

  // Memoize dependencies to prevent infinite re-renders
  const memoizedInvitationId = useMemo(() => invitationId, [invitationId])

  useEffect(() => {
    if (!memoizedInvitationId) {
      setInvitationLink(null)
      setLoading(false)
      return () => {}
    }

    setLoading(true)

    const unsubscribe = InvitationRealtimeService.subscribeToInvitationLink(
      memoizedInvitationId,
      (data, err) => {
        if (err) {
          console.error("Error getting real-time invitation link:", err)
          setError(err)
          setLoading(false)
          return
        }

        setInvitationLink(data)
        setLoading(false)
      }
    )

    return () => unsubscribe()
  }, [memoizedInvitationId])

  return { invitationLink, loading, error }
}

/**
 * Hook to get real-time updates for squad invitation sends
 */
export const useRealtimeSquadInvitationSends = (squadId: string) => {
  const [invitationSends, setInvitationSends] = useState<InvitationSend[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<Error | null>(null)

  // Memoize dependencies to prevent infinite re-renders
  const memoizedSquadId = useMemo(() => squadId, [squadId])

  useEffect(() => {
    if (!memoizedSquadId) {
      setInvitationSends([])
      setLoading(false)
      return () => {}
    }

    setLoading(true)

    const unsubscribe = InvitationRealtimeService.subscribeToSquadInvitationSends(
      memoizedSquadId,
      (data, err) => {
        if (err) {
          console.error("Error getting real-time squad invitation sends:", err)
          setError(err)
          setLoading(false)
          return
        }

        setInvitationSends(data)
        setLoading(false)
      }
    )

    return () => unsubscribe()
  }, [memoizedSquadId])

  return { invitationSends, loading, error }
}

/**
 * Hook to get real-time updates for invitation sends for a specific invitation
 */
export const useRealtimeInvitationSends = (squadId: string, invitationId: string) => {
  const [invitationSends, setInvitationSends] = useState<InvitationSend[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<Error | null>(null)

  // Memoize dependencies to prevent infinite re-renders
  const memoizedDependencies = useMemo(() => ({ squadId, invitationId }), [squadId, invitationId])

  useEffect(() => {
    if (!memoizedDependencies.squadId || !memoizedDependencies.invitationId) {
      setInvitationSends([])
      setLoading(false)
      return () => {}
    }

    setLoading(true)

    const unsubscribe = InvitationRealtimeService.subscribeToInvitationSends(
      memoizedDependencies.squadId,
      memoizedDependencies.invitationId,
      (data, err) => {
        if (err) {
          console.error("Error getting real-time invitation sends:", err)
          setError(err)
          setLoading(false)
          return
        }

        setInvitationSends(data)
        setLoading(false)
      }
    )

    return () => unsubscribe()
  }, [memoizedDependencies])

  return { invitationSends, loading, error }
}
