"use client"

import { create } from "zustand"
import {
  Invitation,
  InvitationCreateData,
  InvitationStatus,
  InvitationLink,
} from "./invitation.types"
import { InvitationService } from "./invitation.service"

/**
 * Invitation store state interface
 */
interface InvitationState {
  // State
  invitations: Invitation[]
  currentInvitation: Invitation | null
  invitationLinks: Record<string, InvitationLink | null> // squadId -> InvitationLink
  loading: boolean
  error: Error | null

  // Actions
  fetchSquadInvitations: (squadId: string) => Promise<void>
  fetchUserInvitations: (email: string) => Promise<void>
  fetchInvitation: (invitationId: string) => Promise<void>
  createInvitation: (invitationData: InvitationCreateData) => Promise<string | null>
  updateInvitationStatus: (
    invitationId: string,
    status: InvitationStatus,
    userId?: string
  ) => Promise<boolean>
  deleteInvitation: (invitationId: string) => Promise<boolean>
  resendInvitation: (invitationId: string) => Promise<boolean>
  clearInvitations: () => void

  // Invitation Link Actions
  fetchInvitationLink: (squadId: string) => Promise<void>
  setInvitationLink: (squadId: string, invitationLink: InvitationLink | null) => void
  clearInvitationLinks: () => void
}

/**
 * Invitation store with Zustand
 */
export const useInvitationStore = create<InvitationState>((set, get) => ({
  // Initial state
  invitations: [],
  currentInvitation: null,
  invitationLinks: {},
  loading: false,
  error: null,

  // Actions
  fetchSquadInvitations: async (squadId: string) => {
    try {
      set({ loading: true, error: null })
      const invitations = await InvitationService.getSquadInvitations(squadId)
      set({ invitations, loading: false })
    } catch (error) {
      console.error("Error fetching squad invitations:", error)
      set({ error: error as Error, loading: false })
    }
  },

  fetchUserInvitations: async (email: string) => {
    try {
      set({ loading: true, error: null })
      const invitations = await InvitationService.getUserInvitations(email)
      set({ invitations, loading: false })
    } catch (error) {
      console.error("Error fetching user invitations:", error)
      set({ error: error as Error, loading: false })
    }
  },

  fetchInvitation: async (invitationId: string) => {
    try {
      set({ loading: true, error: null })
      const invitation = await InvitationService.getInvitation(invitationId)
      if (invitation) {
        set({ currentInvitation: invitation, loading: false })
      } else {
        set({ error: new Error("Invitation not found"), loading: false })
      }
    } catch (error) {
      console.error("Error fetching invitation:", error)
      set({ error: error as Error, loading: false })
    }
  },

  createInvitation: async (invitationData) => {
    try {
      set({ loading: true, error: null })
      const invitationId = await InvitationService.createInvitation(invitationData)

      // If we're viewing invitations for this squad, refresh the list
      const invitations = get().invitations
      if (invitations.length > 0 && invitations[0].squadId === invitationData.squadId) {
        const updatedInvitations = await InvitationService.getSquadInvitations(
          invitationData.squadId
        )
        set({ invitations: updatedInvitations })
      }

      set({ loading: false })
      return invitationId
    } catch (error) {
      console.error("Error creating invitation:", error)
      set({ error: error as Error, loading: false })
      return null
    }
  },

  updateInvitationStatus: async (invitationId, status, userId) => {
    try {
      set({ loading: true, error: null })
      const result = await InvitationService.updateInvitationStatus(invitationId, status, userId)

      if (result.success) {
        // Update the invitation in the list
        const invitations = get().invitations
        const updatedInvitations = invitations.map((invitation) =>
          invitation.id === invitationId ? { ...invitation, status } : invitation
        )

        // Update current invitation if it's the one being updated
        if (get().currentInvitation?.id === invitationId) {
          set({
            currentInvitation: {
              ...get().currentInvitation!,
              status,
              lastUpdated: new Date() as any,
            },
          })
        }

        set({ invitations: updatedInvitations })
      }

      set({ loading: false })
      return result.success
    } catch (error) {
      console.error("Error updating invitation status:", error)
      set({ error: error as Error, loading: false })
      return false
    }
  },

  deleteInvitation: async (invitationId) => {
    try {
      set({ loading: true, error: null })
      const result = await InvitationService.deleteInvitation(invitationId)

      if (result.success) {
        // Remove the invitation from the list
        const invitations = get().invitations
        const updatedInvitations = invitations.filter(
          (invitation) => invitation.id !== invitationId
        )

        // Clear current invitation if it's the one being deleted
        if (get().currentInvitation?.id === invitationId) {
          set({ currentInvitation: null })
        }

        set({ invitations: updatedInvitations })
      }

      set({ loading: false })
      return result.success
    } catch (error) {
      console.error("Error deleting invitation:", error)
      set({ error: error as Error, loading: false })
      return false
    }
  },

  resendInvitation: async (invitationId) => {
    try {
      set({ loading: true, error: null })
      const result = await InvitationService.resendInvitation(invitationId)

      if (result.success) {
        // Update the invitation in the list
        const invitations = get().invitations
        const updatedInvitations = invitations.map((invitation) =>
          invitation.id === invitationId
            ? { ...invitation, lastResent: new Date() as any }
            : invitation
        )

        // Update current invitation if it's the one being resent
        if (get().currentInvitation?.id === invitationId) {
          set({
            currentInvitation: {
              ...get().currentInvitation!,
              lastResent: new Date() as any,
            },
          })
        }

        set({ invitations: updatedInvitations })
      }

      set({ loading: false })
      return result.success
    } catch (error) {
      console.error("Error resending invitation:", error)
      set({ error: error as Error, loading: false })
      return false
    }
  },

  clearInvitations: () => {
    set({ invitations: [], currentInvitation: null })
  },

  // Invitation Link Actions
  fetchInvitationLink: async (squadId: string) => {
    try {
      set({ loading: true, error: null })
      const invitationLink = await InvitationService.getActiveInvitationLink(squadId)

      // Update the invitation links record
      const currentLinks = get().invitationLinks
      set({
        invitationLinks: { ...currentLinks, [squadId]: invitationLink },
        loading: false,
      })
    } catch (error) {
      console.error("Error fetching invitation link:", error)
      set({ error: error as Error, loading: false })
    }
  },

  setInvitationLink: (squadId: string, invitationLink: InvitationLink | null) => {
    const currentLinks = get().invitationLinks
    set({
      invitationLinks: { ...currentLinks, [squadId]: invitationLink },
    })
  },

  clearInvitationLinks: () => {
    set({ invitationLinks: {} })
  },
}))
