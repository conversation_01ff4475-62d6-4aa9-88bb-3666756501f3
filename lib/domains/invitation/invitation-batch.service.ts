import { db } from "@/lib/firebase"
import {
  collection,
  doc,
  getDocs,
  query,
  where,
  serverTimestamp,
  writeBatch,
  getDoc,
} from "firebase/firestore"
import { InvitationSend, InvitationSendCreateData, InvitationSendStatus } from "./invitation.types"

/**
 * Batch service for invitation operations to optimize database performance
 */
export class InvitationBatchService {
  /**
   * Get collection path for invitation sends
   * @param squadId Squad ID
   * @returns Collection path
   */
  private static getCollectionPath(squadId: string): string {
    return `squads/${squadId}/invitation-sends`
  }

  /**
   * Create multiple invitation sends in a single batch operation
   * @param squadId Squad ID
   * @param invitationSendsData Array of invitation send data
   * @returns Array of created invitation send IDs
   */
  static async createBatchInvitationSends(
    squadId: string,
    invitationSendsData: InvitationSendCreateData[]
  ): Promise<string[]> {
    try {
      const batch = writeBatch(db)
      const collectionPath = this.getCollectionPath(squadId)
      const sendIds: string[] = []

      for (const invitationSendData of invitationSendsData) {
        const invitationSendRef = doc(collection(db, collectionPath))
        const invitationSendId = invitationSendRef.id

        batch.set(invitationSendRef, {
          ...invitationSendData,
          id: invitationSendId,
          sentAt: serverTimestamp(),
          status: "sent" as InvitationSendStatus,
          createdAt: serverTimestamp(),
        })

        sendIds.push(invitationSendId)
      }

      await batch.commit()
      return sendIds
    } catch (error) {
      console.error("Error creating batch invitation sends:", error)
      throw error
    }
  }

  /**
   * Update multiple invitation send statuses in a single batch operation
   * @param squadId Squad ID
   * @param updates Array of {id, status} updates
   */
  static async updateBatchInvitationSendStatuses(
    squadId: string,
    updates: Array<{ id: string; status: InvitationSendStatus }>
  ): Promise<void> {
    try {
      const batch = writeBatch(db)
      const collectionPath = this.getCollectionPath(squadId)

      for (const update of updates) {
        const invitationSendRef = doc(db, collectionPath, update.id)
        batch.update(invitationSendRef, {
          status: update.status,
          lastUpdated: serverTimestamp(),
        })
      }

      await batch.commit()
    } catch (error) {
      console.error("Error updating batch invitation send statuses:", error)
      throw error
    }
  }

  /**
   * Get invitation sends for multiple emails in a single query
   * @param squadId Squad ID
   * @param emails Array of emails to check
   * @returns Map of email to invitation sends
   */
  static async getBatchInvitationSendsByEmails(
    squadId: string,
    emails: string[]
  ): Promise<Map<string, InvitationSend[]>> {
    try {
      const collectionPath = this.getCollectionPath(squadId)
      const invitationSendsRef = collection(db, collectionPath)

      // Firestore 'in' queries are limited to 10 items, so we need to batch them
      const emailBatches: string[][] = []
      for (let i = 0; i < emails.length; i += 10) {
        emailBatches.push(emails.slice(i, i + 10))
      }

      const resultMap = new Map<string, InvitationSend[]>()

      // Initialize map with empty arrays for all emails
      emails.forEach((email) => {
        resultMap.set(email.toLowerCase(), [])
      })

      // Query each batch
      for (const emailBatch of emailBatches) {
        const normalizedEmails = emailBatch.map((email) => email.toLowerCase())
        const q = query(invitationSendsRef, where("email", "in", normalizedEmails))
        const querySnapshot = await getDocs(q)

        querySnapshot.docs.forEach((doc) => {
          const invitationSend = { ...doc.data(), id: doc.id } as InvitationSend
          const email = invitationSend.email.toLowerCase()
          const existing = resultMap.get(email) || []
          existing.push(invitationSend)
          resultMap.set(email, existing)
        })
      }

      return resultMap
    } catch (error) {
      console.error("Error getting batch invitation sends by emails:", error)
      throw error
    }
  }

  /**
   * Check if multiple users are squad members in batch
   * @param userIds Array of user IDs to check
   * @param squadId Squad ID
   * @returns Map of userId to boolean indicating membership
   */
  static async checkBatchSquadMembership(
    userIds: string[],
    squadId: string
  ): Promise<Map<string, boolean>> {
    try {
      const resultMap = new Map<string, boolean>()

      // Initialize all as false
      userIds.forEach((userId) => {
        resultMap.set(userId, false)
      })

      // Check squad members collection
      const membersRef = collection(db, `squads/${squadId}/members`)

      // Firestore 'in' queries are limited to 10 items
      const userIdBatches: string[][] = []
      for (let i = 0; i < userIds.length; i += 10) {
        userIdBatches.push(userIds.slice(i, i + 10))
      }

      for (const userIdBatch of userIdBatches) {
        const q = query(membersRef, where("userId", "in", userIdBatch))
        const querySnapshot = await getDocs(q)

        querySnapshot.docs.forEach((doc) => {
          const memberData = doc.data()
          if (memberData.userId) {
            resultMap.set(memberData.userId, true)
          }
        })
      }

      return resultMap
    } catch (error) {
      console.error("Error checking batch squad membership:", error)
      throw error
    }
  }

  /**
   * Get multiple users by email in batch
   * @param emails Array of emails
   * @returns Map of email to user data (if exists)
   */
  static async getBatchUsersByEmail(
    emails: string[]
  ): Promise<Map<string, { uid: string; email: string; displayName?: string } | null>> {
    try {
      const resultMap = new Map<
        string,
        { uid: string; email: string; displayName?: string } | null
      >()

      // Initialize all as null
      emails.forEach((email) => {
        resultMap.set(email.toLowerCase(), null)
      })

      // Query users collection
      const usersRef = collection(db, "users")

      // Firestore 'in' queries are limited to 10 items
      const emailBatches: string[][] = []
      for (let i = 0; i < emails.length; i += 10) {
        emailBatches.push(emails.slice(i, i + 10))
      }

      for (const emailBatch of emailBatches) {
        const normalizedEmails = emailBatch.map((email) => email.toLowerCase())
        const q = query(usersRef, where("email", "in", normalizedEmails))
        const querySnapshot = await getDocs(q)

        querySnapshot.docs.forEach((doc) => {
          const userData = doc.data()
          if (userData.email) {
            resultMap.set(userData.email.toLowerCase(), {
              uid: doc.id,
              email: userData.email,
              displayName: userData.displayName,
            })
          }
        })
      }

      return resultMap
    } catch (error) {
      console.error("Error getting batch users by email:", error)
      throw error
    }
  }

  /**
   * Get squad data once for caching
   * @param squadId Squad ID
   * @returns Squad data with member count
   */
  static async getSquadData(squadId: string): Promise<{
    memberCount: number
    name: string
    leaderId: string
  } | null> {
    try {
      const squadRef = doc(db, "squads", squadId)
      const squadDoc = await getDoc(squadRef)

      if (squadDoc.exists()) {
        const data = squadDoc.data()
        return {
          memberCount: data.memberCount || 1,
          name: data.name || "",
          leaderId: data.leaderId || "",
        }
      }

      return null
    } catch (error) {
      console.error("Error getting squad data:", error)
      return null
    }
  }
}
