"use client"

import { useEffect, useState } from "react"
import { User as FirebaseUser } from "firebase/auth"
import { AuthRealtimeService } from "./auth.realtime.service"

/**
 * Hook to get real-time updates for auth state
 */
export const useRealtimeAuth = () => {
  const [user, setUser] = useState<FirebaseUser | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<Error | null>(null)

  useEffect(() => {
    setLoading(true)

    const unsubscribe = AuthRealtimeService.subscribeToAuthState((userData, err) => {
      if (err) {
        console.error("Error getting real-time auth state:", err)
        setError(err)
        setLoading(false)
        return
      }

      setUser(userData)
      setLoading(false)
    })

    return () => unsubscribe()
  }, [])

  return { user, loading, error }
}
