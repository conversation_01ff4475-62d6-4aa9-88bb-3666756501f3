import { User as FirebaseUser } from "firebase/auth"
import type { ProviderMetadata } from "./providers/base-provider"

/**
 * Admin status interface
 */
export interface AdminStatus {
  isAdmin: boolean
}

/**
 * Auth user interface
 */
export interface AuthUser {
  user: FirebaseUser | null
  isAdmin: boolean
  loading: boolean
  error: Error | null
}

/**
 * Authentication method types (OAuth providers only)
 */
export type AuthMethod = "google" | "github" | "apple" | "facebook"

/**
 * Authentication provider status
 */
export interface AuthProviderStatus {
  id: string
  isConfigured: boolean
  isEnabled: boolean
  metadata: ProviderMetadata
}

/**
 * Authentication state
 */
export interface AuthState {
  user: FirebaseUser | null
  loading: boolean
  error: Error | null
  isAdmin: boolean
  availableProviders: AuthProviderStatus[]
  currentProvider?: string
}

/**
 * Sign-in options
 */
export interface SignInOptions {
  providerId: string
  redirectUrl?: string
  linkAccount?: boolean
}

/**
 * Account linking status
 */
export interface AccountLinkingStatus {
  isLinked: boolean
  linkedProviders: string[]
  availableForLinking: string[]
}

/**
 * Public routes that don't require authentication
 */
export const publicRoutes = ["/", "/login", "/signup", "/forgot-password", "/terms", "/privacy"]

/**
 * Routes that require Firebase Auth but not necessarily application user profile
 */
export const authOnlyRoutes = ["/complete-profile"]

/**
 * Re-export provider types for convenience
 */
export type { ProviderMetadata, SignInResult, LinkAccountResult } from "./providers/base-provider"
