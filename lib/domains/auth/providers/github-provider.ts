import {
  GithubAuthProvider,
  signInWithPopup,
  linkWithCredential,
  type User as FirebaseUser,
} from "firebase/auth"
import { auth } from "@/lib/firebase"
import {
  BaseAuthProvider,
  type ProviderConfig,
  type ProviderMetadata,
  type SignInResult,
  type LinkAccountResult,
} from "./base-provider"
import type { ServiceResponse } from "../../base/base.types"

/**
 * GitHub OAuth provider implementation
 * NOTE: This is a template for future implementation
 */
export class GitHubProvider extends BaseAuthProvider {
  private provider: GithubAuthProvider

  constructor(config: ProviderConfig) {
    const metadata: ProviderMetadata = {
      id: "github",
      name: "github",
      displayName: "GitHub",
      iconName: "github",
      brandColor: "#24292e",
      description: "Sign in with your GitHub account",
      isEnabled: false, // Disabled by default until configured
    }

    super(config, metadata)
    this.provider = new GithubAuthProvider()
    this.setupProvider()
  }

  /**
   * Setup GitHub provider with configuration
   */
  private setupProvider(): void {
    // Add default scopes
    this.provider.addScope("user:email")

    // Add additional scopes if configured
    if (this.config.scopes) {
      this.config.scopes.forEach((scope) => {
        this.provider.addScope(scope)
      })
    }

    // Add custom parameters if configured
    if (this.config.customParameters) {
      this.provider.setCustomParameters(this.config.customParameters)
    }
  }

  /**
   * Check if provider is properly configured
   */
  isConfigured(): boolean {
    // GitHub provider requires client ID configuration
    return !!this.config.clientId
  }

  /**
   * Sign in with GitHub
   */
  async signIn(): Promise<ServiceResponse<SignInResult>> {
    try {
      const result = await signInWithPopup(auth, this.provider)
      const credential = GithubAuthProvider.credentialFromResult(result)

      return {
        success: true,
        data: {
          user: result.user,
          credential,
        },
      }
    } catch (error: any) {
      console.error("GitHub sign-in error:", error)

      let errorMessage = "Failed to sign in with GitHub"

      switch (error.code) {
        case "auth/popup-closed-by-user":
          errorMessage = "Sign-in was cancelled"
          break
        case "auth/popup-blocked":
          errorMessage = "Pop-up was blocked by your browser"
          break
        case "auth/account-exists-with-different-credential":
          errorMessage =
            "An account already exists with this email using a different sign-in method"
          break
        default:
          errorMessage = error.message || "Failed to sign in with GitHub"
      }

      return { success: false, error: new Error(errorMessage) }
    }
  }

  /**
   * Link existing account with GitHub
   */
  async linkAccount(): Promise<ServiceResponse<LinkAccountResult>> {
    try {
      const currentUser = auth.currentUser
      if (!currentUser) {
        return { success: false, error: new Error("No user currently signed in") }
      }

      const result = await signInWithPopup(auth, this.provider)
      const credential = GithubAuthProvider.credentialFromResult(result)

      if (credential) {
        await linkWithCredential(currentUser, credential)

        return {
          success: true,
          data: {
            success: true,
            linked: true,
            user: currentUser,
          },
        }
      }

      return { success: false, error: new Error("Failed to get GitHub credential") }
    } catch (error: any) {
      console.error("GitHub account linking error:", error)
      return { success: false, error }
    }
  }

  /**
   * Handle account linking during sign-in
   */
  async handleAccountLinking(user: FirebaseUser): Promise<ServiceResponse<LinkAccountResult>> {
    // Implementation similar to Google provider
    return {
      success: true,
      data: { success: true, linked: false, user },
    }
  }

  /**
   * Get access token for GitHub
   */
  async getAccessToken(): Promise<ServiceResponse<string>> {
    return {
      success: false,
      error: new Error("Access token retrieval not implemented for GitHub provider"),
    }
  }

  /**
   * Refresh access token
   */
  async refreshAccessToken(): Promise<ServiceResponse<string>> {
    return { success: false, error: new Error("Token refresh not implemented for GitHub provider") }
  }

  /**
   * Sign out from GitHub
   */
  async signOut(): Promise<ServiceResponse> {
    // Firebase Auth handles GitHub sign-out automatically
    return { success: true }
  }
}
