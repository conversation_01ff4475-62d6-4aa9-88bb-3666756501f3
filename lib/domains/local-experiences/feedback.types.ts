import { Timestamp } from "firebase/firestore"
import { BaseEntity } from "../base/base.types"

/**
 * Feedback rating (1-5 stars)
 */
export type FeedbackRating = 1 | 2 | 3 | 4 | 5

/**
 * User feedback for an experience
 */
export interface UserFeedback extends BaseEntity {
  // Booking reference
  bookingId: string
  experienceId: string
  userId: string
  userEmail: string
  userName: string

  // Experience details for context
  experienceTitle: string
  experienceDate: string
  experienceTime: string

  // Feedback fields
  howDidYouHear: string // How did you hear about Togeda.ai
  expectations: string // What are your expectations before going
  whatWentWell: string // What went well with the experience
  whatDidntGoWell: string // What didn't go well
  overallRating: FeedbackRating // Overall rating 1-5 stars

  // Metadata
  submittedAt: Timestamp
}

/**
 * Host feedback for an experience with a customer
 */
export interface HostFeedback extends BaseEntity {
  // Booking reference
  bookingId: string
  experienceId: string

  // Customer details (from booking)
  customerId: string
  customerEmail: string
  customerName: string

  // Experience details for context
  experienceTitle: string
  experienceDate: string
  experienceTime: string

  // Feedback fields
  whatWentWell: string // What went well with the experience
  whatDidntGoWell: string // What didn't go well
  overallRating: FeedbackRating // Overall rating for the experience with the customer 1-5 stars

  // Metadata
  submittedAt: Timestamp
}

/**
 * Combined feedback data for display
 */
export interface ExperienceFeedback {
  bookingId: string
  experienceId: string
  experienceTitle: string
  experienceDate: string
  experienceTime: string
  userFeedback?: UserFeedback
  hostFeedback?: HostFeedback
  createdAt: Timestamp
}

/**
 * User feedback form data
 */
export interface UserFeedbackFormData {
  howDidYouHear: string
  expectations: string
  whatWentWell: string
  whatDidntGoWell: string
  overallRating: FeedbackRating
}

/**
 * Host feedback form data
 */
export interface HostFeedbackFormData {
  whatWentWell: string
  whatDidntGoWell: string
  overallRating: FeedbackRating
}

/**
 * Feedback creation data for users
 */
export interface UserFeedbackCreateData {
  bookingId: string
  experienceId: string
  userId: string
  userEmail: string
  userName: string
  experienceTitle: string
  experienceDate: string
  experienceTime: string
  howDidYouHear: string
  expectations: string
  whatWentWell: string
  whatDidntGoWell: string
  overallRating: FeedbackRating
}

/**
 * Feedback creation data for hosts
 */
export interface HostFeedbackCreateData {
  bookingId: string
  experienceId: string
  customerId: string
  customerEmail: string
  customerName: string
  experienceTitle: string
  experienceDate: string
  experienceTime: string
  whatWentWell: string
  whatDidntGoWell: string
  overallRating: FeedbackRating
}
