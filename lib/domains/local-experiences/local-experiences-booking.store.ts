import { create } from "zustand"
import { devtools } from "zustand/middleware"
import {
  ExperienceBooking,
  BookingFormData,
  BookingConfirmation,
  BookingHistoryFilters,
  BookingSearchResult,
  BookingGuest,
} from "./local-experiences-booking.types"

/**
 * Experience Booking store state
 */
interface LocalExperiencesBookingState {
  // Current booking process
  currentBooking: ExperienceBooking | null
  bookingForm: BookingFormData
  isCreatingBooking: boolean
  bookingError: string | null

  // Booking confirmation
  bookingConfirmation: BookingConfirmation | null
  isProcessingPayment: boolean
  paymentError: string | null

  // User booking history
  userBookings: ExperienceBooking[]
  bookingHistory: BookingSearchResult | null
  isLoadingHistory: boolean
  historyError: string | null
  historyFilters: BookingHistoryFilters

  // Experience bookings (for hosts/admin)
  experienceBookings: ExperienceBooking[]
  isLoadingExperienceBookings: boolean
  experienceBookingsError: string | null

  // UI state
  isBookingFormValid: boolean
}

/**
 * Experience Booking store actions
 */
interface LocalExperiencesBookingActions {
  // Current booking process
  setCurrentBooking: (booking: ExperienceBooking | null) => void
  setBookingForm: (form: Partial<BookingFormData>) => void
  resetBookingForm: () => void
  setIsCreatingBooking: (isCreating: boolean) => void
  setBookingError: (error: string | null) => void

  // Booking confirmation
  setBookingConfirmation: (confirmation: BookingConfirmation | null) => void
  setIsProcessingPayment: (isProcessing: boolean) => void
  setPaymentError: (error: string | null) => void

  // User booking history
  setUserBookings: (bookings: ExperienceBooking[]) => void
  setBookingHistory: (history: BookingSearchResult | null) => void
  setIsLoadingHistory: (isLoading: boolean) => void
  setHistoryError: (error: string | null) => void
  setHistoryFilters: (filters: BookingHistoryFilters) => void

  // Experience bookings
  setExperienceBookings: (bookings: ExperienceBooking[]) => void
  setIsLoadingExperienceBookings: (isLoading: boolean) => void
  setExperienceBookingsError: (error: string | null) => void

  // Form validation
  validateBookingForm: () => boolean
  setIsBookingFormValid: (isValid: boolean) => void

  // Guest management
  addGuest: () => void
  removeGuest: (index: number) => void
  updateGuest: (index: number, guest: Partial<BookingGuest>) => void

  // Reset functions
  resetBookingState: () => void
  resetPaymentState: () => void
  resetHistoryState: () => void
  resetAllState: () => void
}

/**
 * Combined store type
 */
type LocalExperiencesBookingStore = LocalExperiencesBookingState & LocalExperiencesBookingActions

/**
 * Initial booking form data
 */
const initialBookingForm: BookingFormData = {
  date: null,
  time: "",
  guests: 1,
  guestDetails: [{ name: "", email: "", phone: "" }],
  specialRequests: "",
}

/**
 * Initial state
 */
const initialState: LocalExperiencesBookingState = {
  // Current booking process
  currentBooking: null,
  bookingForm: initialBookingForm,
  isCreatingBooking: false,
  bookingError: null,

  // Booking confirmation
  bookingConfirmation: null,
  isProcessingPayment: false,
  paymentError: null,

  // User booking history
  userBookings: [],
  bookingHistory: null,
  isLoadingHistory: false,
  historyError: null,
  historyFilters: {},

  // Experience bookings
  experienceBookings: [],
  isLoadingExperienceBookings: false,
  experienceBookingsError: null,

  // UI state
  isBookingFormValid: false,
}

/**
 * Experience Booking Zustand store
 */
export const useLocalExperiencesBookingStore = create<LocalExperiencesBookingStore>()(
  devtools(
    (set, get) => ({
      ...initialState,

      // Current booking process actions
      setCurrentBooking: (booking) => set({ currentBooking: booking }, false, "setCurrentBooking"),

      setBookingForm: (form) => {
        const currentForm = get().bookingForm
        const updatedForm = { ...currentForm, ...form }

        // Update guest details array based on guests count
        if (form.guests !== undefined) {
          const guestDetails = [...updatedForm.guestDetails]
          if (form.guests > guestDetails.length) {
            // Add more guests
            for (let i = guestDetails.length; i < form.guests; i++) {
              guestDetails.push({ name: "", email: "", phone: "" })
            }
          } else if (form.guests < guestDetails.length) {
            // Remove excess guests
            guestDetails.splice(form.guests)
          }
          updatedForm.guestDetails = guestDetails
        }

        set({ bookingForm: updatedForm }, false, "setBookingForm")
        get().validateBookingForm()
      },

      resetBookingForm: () => {
        set({ bookingForm: initialBookingForm }, false, "resetBookingForm")
        get().validateBookingForm()
      },

      setIsCreatingBooking: (isCreating) =>
        set({ isCreatingBooking: isCreating }, false, "setIsCreatingBooking"),

      setBookingError: (error) => set({ bookingError: error }, false, "setBookingError"),

      // Booking confirmation actions
      setBookingConfirmation: (confirmation) =>
        set({ bookingConfirmation: confirmation }, false, "setBookingConfirmation"),

      setIsProcessingPayment: (isProcessing) =>
        set({ isProcessingPayment: isProcessing }, false, "setIsProcessingPayment"),

      setPaymentError: (error) => set({ paymentError: error }, false, "setPaymentError"),

      // User booking history actions
      setUserBookings: (bookings) => set({ userBookings: bookings }, false, "setUserBookings"),

      setBookingHistory: (history) => set({ bookingHistory: history }, false, "setBookingHistory"),

      setIsLoadingHistory: (isLoading) =>
        set({ isLoadingHistory: isLoading }, false, "setIsLoadingHistory"),

      setHistoryError: (error) => set({ historyError: error }, false, "setHistoryError"),

      setHistoryFilters: (filters) => set({ historyFilters: filters }, false, "setHistoryFilters"),

      // Experience bookings actions
      setExperienceBookings: (bookings) =>
        set({ experienceBookings: bookings }, false, "setExperienceBookings"),

      setIsLoadingExperienceBookings: (isLoading) =>
        set({ isLoadingExperienceBookings: isLoading }, false, "setIsLoadingExperienceBookings"),

      setExperienceBookingsError: (error) =>
        set({ experienceBookingsError: error }, false, "setExperienceBookingsError"),

      // Form validation
      validateBookingForm: () => {
        const { bookingForm } = get()
        const isValid = !!(
          bookingForm.date &&
          bookingForm.time &&
          bookingForm.guests > 0 &&
          bookingForm.guestDetails.length === bookingForm.guests &&
          bookingForm.guestDetails.every((guest) => guest.name.trim())
        )

        set({ isBookingFormValid: isValid }, false, "validateBookingForm")
        return isValid
      },

      setIsBookingFormValid: (isValid) =>
        set({ isBookingFormValid: isValid }, false, "setIsBookingFormValid"),

      // Guest management
      addGuest: () => {
        const { bookingForm } = get()
        const updatedForm = {
          ...bookingForm,
          guests: bookingForm.guests + 1,
          guestDetails: [...bookingForm.guestDetails, { name: "", email: "", phone: "" }],
        }
        set({ bookingForm: updatedForm }, false, "addGuest")
        get().validateBookingForm()
      },

      removeGuest: (index) => {
        const { bookingForm } = get()
        if (bookingForm.guests > 1) {
          const updatedGuestDetails = bookingForm.guestDetails.filter((_, i) => i !== index)
          const updatedForm = {
            ...bookingForm,
            guests: bookingForm.guests - 1,
            guestDetails: updatedGuestDetails,
          }
          set({ bookingForm: updatedForm }, false, "removeGuest")
          get().validateBookingForm()
        }
      },

      updateGuest: (index, guest) => {
        const { bookingForm } = get()
        const updatedGuestDetails = bookingForm.guestDetails.map((g, i) =>
          i === index ? { ...g, ...guest } : g
        )
        const updatedForm = {
          ...bookingForm,
          guestDetails: updatedGuestDetails,
        }
        set({ bookingForm: updatedForm }, false, "updateGuest")
        get().validateBookingForm()
      },

      // Reset functions
      resetBookingState: () =>
        set(
          {
            currentBooking: null,
            bookingForm: initialBookingForm,
            isCreatingBooking: false,
            bookingError: null,
            isBookingFormValid: false,
          },
          false,
          "resetBookingState"
        ),

      resetPaymentState: () =>
        set(
          {
            bookingConfirmation: null,
            isProcessingPayment: false,
            paymentError: null,
          },
          false,
          "resetPaymentState"
        ),

      resetHistoryState: () =>
        set(
          {
            userBookings: [],
            bookingHistory: null,
            isLoadingHistory: false,
            historyError: null,
            historyFilters: {},
          },
          false,
          "resetHistoryState"
        ),

      resetAllState: () => set({ ...initialState }, false, "resetAllState"),
    }),
    {
      name: "local-experiences-booking-store",
    }
  )
)
