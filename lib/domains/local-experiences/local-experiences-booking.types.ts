import { Timestamp } from "firebase/firestore"
import { BaseEntity } from "../base/base.types"

/**
 * Booking status
 */
export type BookingStatus = "pending" | "confirmed" | "cancelled" | "completed"

/**
 * Payment status
 */
export type PaymentStatus = "pending" | "paid" | "failed" | "refunded"

/**
 * Email notification status for experience reminders
 */
export interface EmailNotificationStatus {
  "7d": boolean // 7 days before reminder sent
  "3d": boolean // 3 days before reminder sent
  "1d": boolean // 1 day before reminder sent
  "2h": boolean // 2 hours before reminder sent
}

/**
 * Guest information for booking
 */
export interface BookingGuest {
  name: string
  email?: string
  phone?: string
}

/**
 * Booking pricing breakdown
 */
export interface BookingPricing {
  basePrice: number // Price per person
  guests: number
  subtotal: number // basePrice * guests
  taxes: number
  fees: number
  total: number
  currency: string
}

/**
 * Experience Booking entity
 */
export interface ExperienceBooking extends BaseEntity {
  // Experience details
  experienceId: string
  experienceTitle: string
  experienceLocation: string
  experienceHost: string

  // Booking details
  userId: string
  userEmail: string
  userName: string
  date: string // YYYY-MM-DD format
  time: string // HH:MM format
  availabilityId: string // e.g., "slot-09-00" - references the availability slot
  guests: number
  guestDetails?: BookingGuest[]
  specialRequests?: string

  // Pricing
  pricing: BookingPricing

  // Status
  status: BookingStatus
  paymentStatus: PaymentStatus

  // Payment details
  stripeSessionId?: string
  stripePaymentIntentId?: string
  stripeCustomerId?: string

  // Timestamps
  bookedAt: Timestamp
  confirmedAt?: Timestamp
  cancelledAt?: Timestamp
  completedAt?: Timestamp

  // Cancellation
  cancellationReason?: string
  refundAmount?: number
  refundedAt?: Timestamp

  // Email notifications
  emailNotifications?: EmailNotificationStatus
}

/**
 * Booking creation data
 */
export interface ExperienceBookingCreateData {
  experienceId: string
  experienceTitle: string
  experienceLocation: string
  experienceHost: string
  userId: string
  userEmail: string
  userName: string
  date: string
  time: string
  availabilityId: string
  guests: number
  guestDetails?: BookingGuest[]
  specialRequests?: string
  pricing: BookingPricing
}

/**
 * Booking update data
 */
export type ExperienceBookingUpdateData = Partial<
  Omit<ExperienceBooking, "id" | "createdAt" | "experienceId" | "userId">
>

/**
 * Booking form data (for UI)
 */
export interface BookingFormData {
  date: Date | null
  time: string
  guests: number
  guestDetails: BookingGuest[]
  specialRequests: string
}

/**
 * Booking confirmation data
 */
export interface BookingConfirmation {
  booking: ExperienceBooking
  paymentUrl?: string
  sessionId?: string
}

/**
 * User booking history filters
 */
export interface BookingHistoryFilters {
  status?: BookingStatus[]
  dateRange?: {
    start: string
    end: string
  }
  experienceId?: string
}

/**
 * Booking search result
 */
export interface BookingSearchResult {
  bookings: ExperienceBooking[]
  total: number
  hasMore: boolean
}
