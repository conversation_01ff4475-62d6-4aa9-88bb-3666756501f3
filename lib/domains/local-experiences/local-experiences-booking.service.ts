import { db } from "@/lib/firebase"
import {
  collection,
  collectionGroup,
  doc,
  getDoc,
  getDocs,
  query,
  where,
  orderBy,
  limit,
  QueryDocumentSnapshot,
  serverTimestamp,
  deleteDoc,
  Timestamp,
  runTransaction,
} from "firebase/firestore"
import { BaseService } from "../base/base.service"
import { ServiceResponse } from "../base/base.types"
import {
  ExperienceBooking,
  ExperienceBookingCreateData,
  ExperienceBookingUpdateData,
  BookingHistoryFilters,
  BookingSearchResult,
  BookingStatus,
} from "./local-experiences-booking.types"

/**
 * Experience Booking service for Firebase operations
 */
export class LocalExperiencesBookingService extends BaseService {
  private static readonly EXPERIENCES_COLLECTION = "localExperiences"
  private static readonly BOOKINGS_SUBCOLLECTION = "bookings"

  constructor() {
    super()
  }

  /**
   * Create a new booking
   *
   * This method creates a booking in both collections using a Firestore transaction:
   * 1. localExperiences/{experienceId}/bookings/{bookingId}
   * 2. users/{userId}/localExperienceBookings/{bookingId}
   *
   * Both records are created atomically or not at all.
   * Validates availability before creating the booking.
   */
  static async createBooking(
    experienceId: string,
    bookingData: ExperienceBookingCreateData
  ): Promise<ServiceResponse<string>> {
    try {
      // First, validate availability
      const { LocalExperiencesService } = await import("./local-experiences.service")
      const availabilityCheck = await LocalExperiencesService.checkTimeSlotAvailability(
        experienceId,
        bookingData.date,
        bookingData.availabilityId,
        bookingData.guests
      )

      if (!availabilityCheck.success) {
        return { success: false, error: availabilityCheck.error || "Failed to check availability" }
      }

      if (!availabilityCheck.data?.available) {
        const remainingSpots = availabilityCheck.data
          ? availabilityCheck.data.maxGuests - availabilityCheck.data.currentBookings
          : 0
        return {
          success: false,
          error: `This time slot is no longer available. Only ${remainingSpots} spots remaining.`,
        }
      }

      // Create references for both collections
      const experienceBookingRef = doc(
        collection(db, this.EXPERIENCES_COLLECTION, experienceId, this.BOOKINGS_SUBCOLLECTION)
      )
      const bookingId = experienceBookingRef.id

      const userBookingRef = doc(
        collection(db, "users", bookingData.userId, "localExperienceBookings"),
        bookingId
      )

      const newBooking: ExperienceBooking = {
        ...bookingData,
        id: bookingId,
        createdAt: serverTimestamp() as Timestamp,
        status: "pending",
        paymentStatus: "pending",
        bookedAt: serverTimestamp() as Timestamp,
      }

      // Use transaction to ensure both writes succeed or both fail
      await runTransaction(db, async (transaction) => {
        // Double-check availability within the transaction to prevent race conditions
        const currentBookingsQuery = query(
          collection(db, this.EXPERIENCES_COLLECTION, experienceId, this.BOOKINGS_SUBCOLLECTION),
          where("date", "==", bookingData.date),
          where("availabilityId", "==", bookingData.availabilityId),
          where("status", "==", "confirmed")
        )

        const currentBookingsSnapshot = await getDocs(currentBookingsQuery)
        const totalConfirmedGuests = currentBookingsSnapshot.docs.reduce((sum, doc) => {
          const booking = doc.data() as ExperienceBooking
          return sum + booking.guests
        }, 0)

        if (!availabilityCheck.data) {
          throw new Error("Availability data missing")
        }

        const availableSpots = availabilityCheck.data.maxGuests - totalConfirmedGuests
        if (availableSpots < bookingData.guests) {
          throw new Error(`Not enough spots available. Only ${availableSpots} spots remaining.`)
        }

        // Write to experience bookings collection
        transaction.set(experienceBookingRef, newBooking)

        // Write to user bookings collection (full duplication)
        transaction.set(userBookingRef, newBooking)
      })

      return { success: true, data: bookingId }
    } catch (error) {
      console.error("Error creating booking:", error)
      const errorMessage = error instanceof Error ? error.message : "Failed to create booking"
      return { success: false, error: errorMessage }
    }
  }

  /**
   * Get booking by ID using collection group query
   * This method searches across all experiences to find the booking
   */
  static async getBooking(bookingId: string): Promise<ServiceResponse<ExperienceBooking>>
  static async getBooking(
    experienceId: string,
    bookingId: string
  ): Promise<ServiceResponse<ExperienceBooking>>
  static async getBooking(
    experienceIdOrBookingId: string,
    bookingId?: string
  ): Promise<ServiceResponse<ExperienceBooking>> {
    // If only one parameter is provided, treat it as bookingId and use collection group query
    if (!bookingId) {
      return this.getBookingById(experienceIdOrBookingId)
    }

    // If both parameters are provided, use the direct path
    const experienceId = experienceIdOrBookingId
    try {
      const bookingRef = doc(
        db,
        this.EXPERIENCES_COLLECTION,
        experienceId,
        this.BOOKINGS_SUBCOLLECTION,
        bookingId
      )
      const bookingSnap = await getDoc(bookingRef)

      if (!bookingSnap.exists()) {
        return { success: false, error: "Booking not found" }
      }

      const booking = { id: bookingSnap.id, ...bookingSnap.data() } as ExperienceBooking
      return { success: true, data: booking }
    } catch (error) {
      console.error("Error getting booking:", error)
      return { success: false, error: "Failed to get booking" }
    }
  }

  /**
   * Get booking by ID using collection group query
   * This searches across all experiences to find the booking
   */
  private static async getBookingById(
    bookingId: string
  ): Promise<ServiceResponse<ExperienceBooking>> {
    try {
      const bookingsQuery = query(
        collectionGroup(db, "bookings"),
        where("__name__", "==", bookingId),
        limit(1)
      )

      const querySnapshot = await getDocs(bookingsQuery)

      if (querySnapshot.empty) {
        return {
          success: false,
          error: "Booking not found",
        }
      }

      const bookingDoc = querySnapshot.docs[0]
      const bookingData = bookingDoc.data()
      const booking = {
        id: bookingDoc.id,
        ...bookingData,
      } as ExperienceBooking

      return {
        success: true,
        data: booking,
      }
    } catch (error) {
      console.error("Error getting booking by ID:", error)
      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to get booking",
      }
    }
  }

  /**
   * Update booking
   *
   * This method updates a booking in both collections using a Firestore transaction:
   * 1. localExperiences/{experienceId}/bookings/{bookingId}
   * 2. users/{userId}/localExperienceBookings/{bookingId}
   *
   * Both records are updated atomically or not at all.
   */
  static async updateBooking(
    experienceId: string,
    bookingId: string,
    updateData: ExperienceBookingUpdateData
  ): Promise<ServiceResponse<void>> {
    try {
      const experienceBookingRef = doc(
        db,
        this.EXPERIENCES_COLLECTION,
        experienceId,
        this.BOOKINGS_SUBCOLLECTION,
        bookingId
      )

      // Use transaction to ensure both updates succeed or both fail
      await runTransaction(db, async (transaction) => {
        // First, get the current booking to extract userId
        const experienceBookingDoc = await transaction.get(experienceBookingRef)

        if (!experienceBookingDoc.exists()) {
          throw new Error("Booking not found")
        }

        const currentBooking = experienceBookingDoc.data() as ExperienceBooking
        const userId = currentBooking.userId

        if (!userId) {
          throw new Error("Booking does not have a valid userId")
        }

        const userBookingRef = doc(db, "users", userId, "localExperienceBookings", bookingId)

        // Update both collections
        transaction.update(experienceBookingRef, updateData)
        transaction.update(userBookingRef, updateData)
      })

      return { success: true }
    } catch (error) {
      console.error("Error updating booking:", error)
      return { success: false, error: "Failed to update booking" }
    }
  }

  /**
   * Confirm booking (after successful payment)
   */
  static async confirmBooking(
    experienceId: string,
    bookingId: string,
    paymentDetails: {
      stripeSessionId?: string
      stripePaymentIntentId?: string
      stripeCustomerId?: string
    }
  ): Promise<ServiceResponse<void>> {
    try {
      const updateData: ExperienceBookingUpdateData = {
        status: "confirmed",
        paymentStatus: "paid",
        confirmedAt: serverTimestamp() as Timestamp,
        ...paymentDetails,
      }

      return await this.updateBooking(experienceId, bookingId, updateData)
    } catch (error) {
      console.error("Error confirming booking:", error)
      return { success: false, error: "Failed to confirm booking" }
    }
  }

  /**
   * Cancel booking
   */
  static async cancelBooking(
    experienceId: string,
    bookingId: string,
    reason?: string
  ): Promise<ServiceResponse<void>> {
    try {
      const updateData: ExperienceBookingUpdateData = {
        status: "cancelled",
        cancelledAt: serverTimestamp() as Timestamp,
        cancellationReason: reason,
      }

      return await this.updateBooking(experienceId, bookingId, updateData)
    } catch (error) {
      console.error("Error cancelling booking:", error)
      return { success: false, error: "Failed to cancel booking" }
    }
  }

  /**
   * Complete booking (after experience is finished)
   */
  static async completeBooking(
    experienceId: string,
    bookingId: string
  ): Promise<ServiceResponse<void>> {
    try {
      const updateData: ExperienceBookingUpdateData = {
        status: "completed",
        completedAt: serverTimestamp() as Timestamp,
      }

      return await this.updateBooking(experienceId, bookingId, updateData)
    } catch (error) {
      console.error("Error completing booking:", error)
      return { success: false, error: "Failed to complete booking" }
    }
  }

  /**
   * Get user's booking history
   */
  static async getUserBookings(
    _userId: string,
    _filters?: BookingHistoryFilters,
    _pageSize: number = 10,
    _lastDoc?: QueryDocumentSnapshot
  ): Promise<ServiceResponse<BookingSearchResult>> {
    try {
      // Note: This is a complex query that would require a collection group query
      // For now, we'll implement a simpler version that searches across all experiences
      // In production, you might want to maintain a separate user-bookings collection

      // This is a simplified implementation - in reality, you'd need to use collection group queries
      // or maintain a separate index for user bookings
      const bookings: ExperienceBooking[] = []

      // TODO: Implement proper collection group query for user bookings
      // For now, return empty result
      return {
        success: true,
        data: {
          bookings,
          total: 0,
          hasMore: false,
        },
      }
    } catch (error) {
      console.error("Error getting user bookings:", error)
      return { success: false, error: "Failed to get user bookings" }
    }
  }

  /**
   * Get bookings for an experience
   */
  static async getExperienceBookings(
    experienceId: string,
    status?: BookingStatus[],
    pageSize: number = 10
  ): Promise<ServiceResponse<ExperienceBooking[]>> {
    try {
      // First check if the experience exists
      const experienceRef = doc(db, this.EXPERIENCES_COLLECTION, experienceId)
      const experienceDoc = await getDoc(experienceRef)

      if (!experienceDoc.exists()) {
        return { success: false, error: "Experience not found" }
      }

      let bookingsQuery = query(
        collection(db, this.EXPERIENCES_COLLECTION, experienceId, this.BOOKINGS_SUBCOLLECTION),
        orderBy("bookedAt", "desc")
      )

      if (status && status.length > 0) {
        bookingsQuery = query(bookingsQuery, where("status", "in", status))
      }

      bookingsQuery = query(bookingsQuery, limit(pageSize))

      const querySnapshot = await getDocs(bookingsQuery)
      const bookings: ExperienceBooking[] = querySnapshot.docs.map(
        (doc) =>
          ({
            id: doc.id,
            ...doc.data(),
          }) as ExperienceBooking
      )

      return { success: true, data: bookings }
    } catch (error) {
      console.error("Error getting experience bookings:", error)
      // Return empty array instead of error for missing subcollections
      // This is expected when no bookings exist yet
      return { success: true, data: [] }
    }
  }

  /**
   * Delete booking (admin only)
   */
  static async deleteBooking(
    experienceId: string,
    bookingId: string
  ): Promise<ServiceResponse<void>> {
    try {
      const bookingRef = doc(
        db,
        this.EXPERIENCES_COLLECTION,
        experienceId,
        this.BOOKINGS_SUBCOLLECTION,
        bookingId
      )
      await deleteDoc(bookingRef)
      return { success: true }
    } catch (error) {
      console.error("Error deleting booking:", error)
      return { success: false, error: "Failed to delete booking" }
    }
  }
}
