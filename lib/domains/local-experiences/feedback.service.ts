import {
  collection,
  doc,
  setDoc,
  getDoc,
  getDocs,
  query,
  where,
  orderBy,
  serverTimestamp,
  Timestamp,
} from "firebase/firestore"
import { db } from "@/lib/firebase"
import { ServiceResponse } from "../base/base.types"
import {
  UserFeedback,
  HostFeedback,
  ExperienceFeedback,
  UserFeedbackCreateData,
  HostFeedbackCreateData,
} from "./feedback.types"

/**
 * Service for managing experience feedback
 * Stores feedback in: /localExperiences/{experienceId}/feedback/{bookingId}
 */
export class FeedbackService {
  private static readonly COLLECTION = "localExperiences"
  private static readonly FEEDBACK_SUBCOLLECTION = "feedback"

  /**
   * Submit user feedback for an experience
   */
  static async submitUserFeedback(
    feedbackData: UserFeedbackCreateData
  ): Promise<ServiceResponse<string>> {
    try {
      const { experienceId, bookingId } = feedbackData

      // Check if feedback already exists
      const feedbackRef = doc(
        db,
        this.COLLECTION,
        experienceId,
        this.FEEDBACK_SUBCOLLECTION,
        bookingId
      )

      const existingFeedback = await getDoc(feedbackRef)

      if (existingFeedback.exists()) {
        const data = existingFeedback.data()
        if (data.userFeedback) {
          return {
            success: false,
            error: "User feedback has already been submitted for this experience",
          }
        }
      }

      const userFeedback: UserFeedback = {
        id: bookingId,
        ...feedbackData,
        createdAt: serverTimestamp() as Timestamp,
        submittedAt: serverTimestamp() as Timestamp,
      }

      // Update or create the feedback document with user feedback
      const updateData = existingFeedback.exists()
        ? { userFeedback, updatedAt: serverTimestamp() }
        : {
            bookingId,
            experienceId,
            experienceTitle: feedbackData.experienceTitle,
            experienceDate: feedbackData.experienceDate,
            experienceTime: feedbackData.experienceTime,
            userFeedback,
            createdAt: serverTimestamp(),
            updatedAt: serverTimestamp(),
          }

      await setDoc(feedbackRef, updateData, { merge: true })

      return {
        success: true,
        data: bookingId,
      }
    } catch (error) {
      console.error("Error submitting user feedback:", error)
      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to submit feedback",
      }
    }
  }

  /**
   * Submit host feedback for an experience
   */
  static async submitHostFeedback(
    feedbackData: HostFeedbackCreateData
  ): Promise<ServiceResponse<string>> {
    try {
      const { experienceId, bookingId } = feedbackData

      // Check if feedback already exists
      const feedbackRef = doc(
        db,
        this.COLLECTION,
        experienceId,
        this.FEEDBACK_SUBCOLLECTION,
        bookingId
      )

      const existingFeedback = await getDoc(feedbackRef)

      if (existingFeedback.exists()) {
        const data = existingFeedback.data()
        if (data.hostFeedback) {
          return {
            success: false,
            error: "Host feedback has already been submitted for this experience",
          }
        }
      }

      const hostFeedback: HostFeedback = {
        id: bookingId,
        ...feedbackData,
        createdAt: serverTimestamp() as Timestamp,
        submittedAt: serverTimestamp() as Timestamp,
      }

      // Update or create the feedback document with host feedback
      const updateData = existingFeedback.exists()
        ? { hostFeedback, updatedAt: serverTimestamp() }
        : {
            bookingId,
            experienceId,
            experienceTitle: feedbackData.experienceTitle,
            experienceDate: feedbackData.experienceDate,
            experienceTime: feedbackData.experienceTime,
            hostFeedback,
            createdAt: serverTimestamp(),
            updatedAt: serverTimestamp(),
          }

      await setDoc(feedbackRef, updateData, { merge: true })

      return {
        success: true,
        data: bookingId,
      }
    } catch (error) {
      console.error("Error submitting host feedback:", error)
      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to submit feedback",
      }
    }
  }

  /**
   * Get feedback for a specific booking
   */
  static async getFeedback(
    experienceId: string,
    bookingId: string
  ): Promise<ServiceResponse<ExperienceFeedback | null>> {
    try {
      const feedbackRef = doc(
        db,
        this.COLLECTION,
        experienceId,
        this.FEEDBACK_SUBCOLLECTION,
        bookingId
      )

      const feedbackDoc = await getDoc(feedbackRef)

      if (!feedbackDoc.exists()) {
        return {
          success: true,
          data: null,
        }
      }

      const data = feedbackDoc.data() as ExperienceFeedback

      return {
        success: true,
        data,
      }
    } catch (error) {
      console.error("Error getting feedback:", error)
      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to get feedback",
      }
    }
  }

  /**
   * Get all feedback for an experience
   */
  static async getExperienceFeedback(
    experienceId: string
  ): Promise<ServiceResponse<ExperienceFeedback[]>> {
    try {
      const feedbackQuery = query(
        collection(db, this.COLLECTION, experienceId, this.FEEDBACK_SUBCOLLECTION),
        orderBy("createdAt", "desc")
      )

      const querySnapshot = await getDocs(feedbackQuery)

      const feedback: ExperienceFeedback[] = querySnapshot.docs.map((doc) => ({
        ...doc.data(),
      })) as ExperienceFeedback[]

      return {
        success: true,
        data: feedback,
      }
    } catch (error) {
      console.error("Error getting experience feedback:", error)
      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to get experience feedback",
      }
    }
  }

  /**
   * Check if user has already submitted feedback for a booking
   */
  static async hasUserSubmittedFeedback(
    experienceId: string,
    bookingId: string
  ): Promise<ServiceResponse<boolean>> {
    try {
      const feedbackResponse = await this.getFeedback(experienceId, bookingId)

      const hasSubmitted = feedbackResponse.data?.userFeedback !== undefined

      return {
        success: true,
        data: hasSubmitted,
      }
    } catch (error) {
      console.error("Error checking user feedback status:", error)
      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to check feedback status",
      }
    }
  }

  /**
   * Check if host has already submitted feedback for a booking
   */
  static async hasHostSubmittedFeedback(
    experienceId: string,
    bookingId: string
  ): Promise<ServiceResponse<boolean>> {
    try {
      const feedbackResponse = await this.getFeedback(experienceId, bookingId)

      const hasSubmitted = feedbackResponse.data?.hostFeedback !== undefined

      return {
        success: true,
        data: hasSubmitted,
      }
    } catch (error) {
      console.error("Error checking host feedback status:", error)
      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to check feedback status",
      }
    }
  }
}
