import { create } from "zustand"
import { devtools } from "zustand/middleware"
import {
  LocalExperience,
  ExperienceSearchFilters,
  ExperienceSearchResult,
  ExperienceAvailability,
} from "./local-experiences.types"

/**
 * Local Experiences store state
 */
interface LocalExperiencesState {
  // Experience discovery
  experiences: LocalExperience[]
  searchFilters: ExperienceSearchFilters
  searchResult: ExperienceSearchResult | null
  isSearching: boolean
  searchError: string | null

  // Selected experience
  selectedExperience: LocalExperience | null
  selectedExperienceAvailability: ExperienceAvailability | null
  isLoadingExperience: boolean
  experienceError: string | null

  // Availability
  isLoadingAvailability: boolean
  availabilityError: string | null

  // UI state
  isExperienceModalOpen: boolean
  isBookingModalOpen: boolean
}

/**
 * Local Experiences store actions
 */
interface LocalExperiencesActions {
  // Experience discovery
  setExperiences: (experiences: LocalExperience[]) => void
  setSearchFilters: (filters: ExperienceSearchFilters) => void
  setSearchResult: (result: ExperienceSearchResult | null) => void
  setIsSearching: (isSearching: boolean) => void
  setSearchError: (error: string | null) => void
  clearSearchResults: () => void

  // Selected experience
  setSelectedExperience: (experience: LocalExperience | null) => void
  setSelectedExperienceAvailability: (availability: ExperienceAvailability | null) => void
  setIsLoadingExperience: (isLoading: boolean) => void
  setExperienceError: (error: string | null) => void

  // Availability
  setIsLoadingAvailability: (isLoading: boolean) => void
  setAvailabilityError: (error: string | null) => void

  // UI state
  setIsExperienceModalOpen: (isOpen: boolean) => void
  setIsBookingModalOpen: (isOpen: boolean) => void
  openExperienceModal: (experience: LocalExperience) => void
  closeExperienceModal: () => void
  openBookingModal: () => void
  closeBookingModal: () => void

  // Reset functions
  resetExperienceState: () => void
  resetSearchState: () => void
  resetAllState: () => void
}

/**
 * Combined store type
 */
type LocalExperiencesStore = LocalExperiencesState & LocalExperiencesActions

/**
 * Initial state
 */
const initialState: LocalExperiencesState = {
  // Experience discovery
  experiences: [],
  searchFilters: {},
  searchResult: null,
  isSearching: false,
  searchError: null,

  // Selected experience
  selectedExperience: null,
  selectedExperienceAvailability: null,
  isLoadingExperience: false,
  experienceError: null,

  // Availability
  isLoadingAvailability: false,
  availabilityError: null,

  // UI state
  isExperienceModalOpen: false,
  isBookingModalOpen: false,
}

/**
 * Local Experiences Zustand store
 */
export const useLocalExperiencesStore = create<LocalExperiencesStore>()(
  devtools(
    (set) => ({
      ...initialState,

      // Experience discovery actions
      setExperiences: (experiences) => set({ experiences }, false, "setExperiences"),

      setSearchFilters: (filters) => set({ searchFilters: filters }, false, "setSearchFilters"),

      setSearchResult: (result) => set({ searchResult: result }, false, "setSearchResult"),

      setIsSearching: (isSearching) => set({ isSearching }, false, "setIsSearching"),

      setSearchError: (error) => set({ searchError: error }, false, "setSearchError"),

      clearSearchResults: () =>
        set(
          {
            searchResult: null,
            experiences: [],
            searchError: null,
          },
          false,
          "clearSearchResults"
        ),

      // Selected experience actions
      setSelectedExperience: (experience) =>
        set({ selectedExperience: experience }, false, "setSelectedExperience"),

      setSelectedExperienceAvailability: (availability) =>
        set(
          { selectedExperienceAvailability: availability },
          false,
          "setSelectedExperienceAvailability"
        ),

      setIsLoadingExperience: (isLoading) =>
        set({ isLoadingExperience: isLoading }, false, "setIsLoadingExperience"),

      setExperienceError: (error) => set({ experienceError: error }, false, "setExperienceError"),

      // Availability actions
      setIsLoadingAvailability: (isLoading) =>
        set({ isLoadingAvailability: isLoading }, false, "setIsLoadingAvailability"),

      setAvailabilityError: (error) =>
        set({ availabilityError: error }, false, "setAvailabilityError"),

      // UI state actions
      setIsExperienceModalOpen: (isOpen) =>
        set({ isExperienceModalOpen: isOpen }, false, "setIsExperienceModalOpen"),

      setIsBookingModalOpen: (isOpen) =>
        set({ isBookingModalOpen: isOpen }, false, "setIsBookingModalOpen"),

      openExperienceModal: (experience) =>
        set(
          {
            selectedExperience: experience,
            isExperienceModalOpen: true,
            experienceError: null,
          },
          false,
          "openExperienceModal"
        ),

      closeExperienceModal: () =>
        set(
          {
            isExperienceModalOpen: false,
            selectedExperience: null,
            selectedExperienceAvailability: null,
            experienceError: null,
          },
          false,
          "closeExperienceModal"
        ),

      openBookingModal: () => set({ isBookingModalOpen: true }, false, "openBookingModal"),

      closeBookingModal: () => set({ isBookingModalOpen: false }, false, "closeBookingModal"),

      // Reset functions
      resetExperienceState: () =>
        set(
          {
            selectedExperience: null,
            selectedExperienceAvailability: null,
            isLoadingExperience: false,
            experienceError: null,
            isExperienceModalOpen: false,
            isBookingModalOpen: false,
          },
          false,
          "resetExperienceState"
        ),

      resetSearchState: () =>
        set(
          {
            experiences: [],
            searchFilters: {},
            searchResult: null,
            isSearching: false,
            searchError: null,
          },
          false,
          "resetSearchState"
        ),

      resetAllState: () => set({ ...initialState }, false, "resetAllState"),
    }),
    {
      name: "local-experiences-store",
    }
  )
)
