import { useCallback, useEffect } from "react"
import { toast } from "sonner"
import { LocalExperiencesBookingService } from "./local-experiences-booking.service"
import { useLocalExperiencesBookingStore } from "./local-experiences-booking.store"
import {
  createExperienceCheckoutSession,
  confirmExperienceBooking,
  redirectToExperienceCheckout,
} from "@/lib/server/actions/experience-booking"
import { useUser } from "@/lib/domains/auth/auth.hooks"
import { AuthService } from "@/lib/domains/auth/auth.service"
import {
  ExperienceBookingCreateData,
  BookingHistoryFilters,
  BookingStatus,
} from "./local-experiences-booking.types"

/**
 * Hook for managing experience bookings
 */
export const useLocalExperiencesBooking = () => {
  const {
    currentBooking,
    bookingForm,
    isCreatingBooking,
    bookingError,
    isBookingFormValid,
    setCurrentBooking,
    setBookingForm,
    resetBookingForm,
    setIsCreatingBooking,
    setBookingError,
    validateBookingForm,
    addGuest,
    removeGuest,
    updateGuest,
    resetBookingState,
  } = useLocalExperiencesBookingStore()

  /**
   * Create a new booking
   */
  const createBooking = useCallback(
    async (experienceId: string, bookingData: ExperienceBookingCreateData) => {
      try {
        setIsCreatingBooking(true)
        setBookingError(null)

        const response = await LocalExperiencesBookingService.createBooking(
          experienceId,
          bookingData
        )

        if (response.success) {
          toast.success("Booking created successfully!")
          return response.data
        } else {
          const errorMessage =
            response.error instanceof Error ? response.error.message : "Failed to create booking"
          setBookingError(errorMessage)
          toast.error(errorMessage)
          return null
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : "Failed to create booking"
        setBookingError(errorMessage)
        toast.error(errorMessage)
        return null
      } finally {
        setIsCreatingBooking(false)
      }
    },
    [setIsCreatingBooking, setBookingError]
  )

  /**
   * Get booking by ID
   */
  const getBooking = useCallback(
    async (experienceId: string, bookingId: string) => {
      try {
        const response = await LocalExperiencesBookingService.getBooking(experienceId, bookingId)

        if (response.success) {
          setCurrentBooking(response.data || null)
          return response.data
        } else {
          const errorMessage =
            response.error instanceof Error ? response.error.message : "Failed to get booking"
          toast.error(errorMessage)
          return null
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : "Failed to get booking"
        toast.error(errorMessage)
        return null
      }
    },
    [setCurrentBooking]
  )

  /**
   * Cancel booking
   */
  const cancelBooking = useCallback(
    async (experienceId: string, bookingId: string, reason?: string) => {
      try {
        const response = await LocalExperiencesBookingService.cancelBooking(
          experienceId,
          bookingId,
          reason
        )

        if (response.success) {
          toast.success("Booking cancelled successfully")
          // Refresh current booking if it's the one being cancelled
          if (currentBooking?.id === bookingId) {
            await getBooking(experienceId, bookingId)
          }
          return true
        } else {
          const errorMessage =
            response.error instanceof Error ? response.error.message : "Failed to cancel booking"
          toast.error(errorMessage)
          return false
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : "Failed to cancel booking"
        toast.error(errorMessage)
        return false
      }
    },
    [currentBooking, getBooking]
  )

  return {
    // State
    currentBooking,
    bookingForm,
    isCreatingBooking,
    bookingError,
    isBookingFormValid,

    // Actions
    createBooking,
    getBooking,
    cancelBooking,
    setCurrentBooking,
    setBookingForm,
    resetBookingForm,
    validateBookingForm,
    addGuest,
    removeGuest,
    updateGuest,
    resetBookingState,
  }
}

/**
 * Hook for managing booking payments
 */
export const useBookingPayment = () => {
  const {
    bookingConfirmation,
    isProcessingPayment,
    paymentError,
    setBookingConfirmation,
    setIsProcessingPayment,
    setPaymentError,
    resetPaymentState,
  } = useLocalExperiencesBookingStore()

  /**
   * Create checkout session and redirect to payment
   */
  const processPayment = useCallback(
    async (experienceId: string, bookingId: string) => {
      try {
        setIsProcessingPayment(true)
        setPaymentError(null)

        // First get the booking to get required data
        const bookingResponse = await LocalExperiencesBookingService.getBooking(
          experienceId,
          bookingId
        )
        if (!bookingResponse.success || !bookingResponse.data) {
          throw new Error("Failed to get booking details")
        }

        const booking = bookingResponse.data

        // Get auth token
        const userToken = await AuthService.getAuthToken()
        if (!userToken) {
          throw new Error("Authentication required")
        }

        const response = await createExperienceCheckoutSession(
          experienceId,
          bookingId,
          booking.experienceTitle,
          booking.pricing.total,
          booking.pricing.currency,
          userToken
        )

        if (response.success && response.sessionId) {
          // Redirect to Stripe checkout
          await redirectToExperienceCheckout(
            experienceId,
            bookingId,
            booking.experienceTitle,
            booking.pricing.total,
            booking.pricing.currency,
            userToken
          )
          return true
        } else {
          const errorMessage = response.error || "Failed to create payment session"
          setPaymentError(errorMessage)
          toast.error(errorMessage)
          return false
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : "Failed to process payment"
        setPaymentError(errorMessage)
        toast.error(errorMessage)
        return false
      } finally {
        setIsProcessingPayment(false)
      }
    },
    [setIsProcessingPayment, setPaymentError]
  )

  /**
   * Confirm booking after successful payment
   */
  const confirmBooking = useCallback(
    async (experienceId: string, bookingId: string, sessionId: string) => {
      try {
        setIsProcessingPayment(true)
        setPaymentError(null)

        // Get auth token
        const userToken = await AuthService.getAuthToken()
        if (!userToken) {
          throw new Error("Authentication required")
        }

        const response = await confirmExperienceBooking(
          sessionId,
          bookingId,
          experienceId,
          userToken
        )

        if (response.success) {
          toast.success("Booking confirmed successfully!")
          // Get the updated booking data
          const bookingResponse = await LocalExperiencesBookingService.getBooking(
            experienceId,
            bookingId
          )
          if (bookingResponse.success && bookingResponse.data) {
            setBookingConfirmation({
              booking: bookingResponse.data,
            })
            return bookingResponse.data
          }
          return null
        } else {
          const errorMessage = response.error || "Failed to confirm booking"
          setPaymentError(errorMessage)
          toast.error(errorMessage)
          return null
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : "Failed to confirm booking"
        setPaymentError(errorMessage)
        toast.error(errorMessage)
        return null
      } finally {
        setIsProcessingPayment(false)
      }
    },
    [setIsProcessingPayment, setPaymentError, setBookingConfirmation]
  )

  return {
    // State
    bookingConfirmation,
    isProcessingPayment,
    paymentError,

    // Actions
    processPayment,
    confirmBooking,
    setBookingConfirmation,
    resetPaymentState,
  }
}

/**
 * Hook for managing booking confirmation
 */
export const useBookingConfirmation = () => {
  const {
    bookingConfirmation,
    isProcessingPayment,
    paymentError,
    setBookingConfirmation,
    setIsProcessingPayment,
    setPaymentError,
    resetPaymentState,
  } = useLocalExperiencesBookingStore()

  /**
   * Confirm booking after successful payment
   */
  const confirmBooking = useCallback(
    async (experienceId: string, bookingId: string, sessionId: string) => {
      try {
        setIsProcessingPayment(true)
        setPaymentError(null)

        // Get auth token
        const userToken = await AuthService.getAuthToken()
        if (!userToken) {
          throw new Error("Authentication required")
        }

        const response = await confirmExperienceBooking(
          sessionId,
          bookingId,
          experienceId,
          userToken
        )

        if (response.success) {
          toast.success("Booking confirmed successfully!")
          // Get the updated booking data
          const bookingResponse = await LocalExperiencesBookingService.getBooking(
            experienceId,
            bookingId
          )
          if (bookingResponse.success && bookingResponse.data) {
            setBookingConfirmation({
              booking: bookingResponse.data,
            })
            return bookingResponse.data
          }
          return null
        } else {
          const errorMessage = response.error || "Failed to confirm booking"
          setPaymentError(errorMessage)
          toast.error(errorMessage)
          return null
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : "Failed to confirm booking"
        setPaymentError(errorMessage)
        toast.error(errorMessage)
        return null
      } finally {
        setIsProcessingPayment(false)
      }
    },
    [setIsProcessingPayment, setPaymentError, setBookingConfirmation]
  )

  return {
    // State
    bookingConfirmation,
    isProcessingPayment,
    paymentError,

    // Actions
    confirmBooking,
    setBookingConfirmation,
    resetPaymentState,
  }
}

/**
 * Hook for managing user booking history
 */
export const useBookingHistory = () => {
  const user = useUser()
  const {
    userBookings,
    bookingHistory,
    isLoadingHistory,
    historyError,
    historyFilters,
    setUserBookings,
    setBookingHistory,
    setIsLoadingHistory,
    setHistoryError,
    setHistoryFilters,
    resetHistoryState,
  } = useLocalExperiencesBookingStore()

  /**
   * Load user booking history
   */
  const loadBookingHistory = useCallback(
    async (filters?: BookingHistoryFilters) => {
      try {
        if (!user) {
          setHistoryError("User not authenticated")
          return
        }

        setIsLoadingHistory(true)
        setHistoryError(null)

        const response = await LocalExperiencesBookingService.getUserBookings(user.uid, filters)

        if (response.success) {
          setBookingHistory(response.data || null)
          setUserBookings(response.data?.bookings || [])
        } else {
          const errorMessage =
            response.error instanceof Error
              ? response.error.message
              : "Failed to load booking history"
          setHistoryError(errorMessage)
          toast.error(errorMessage)
        }
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : "Failed to load booking history"
        setHistoryError(errorMessage)
        toast.error(errorMessage)
      } finally {
        setIsLoadingHistory(false)
      }
    },
    [user, setIsLoadingHistory, setHistoryError, setBookingHistory, setUserBookings]
  )

  /**
   * Update filters and reload
   */
  const updateFilters = useCallback(
    (filters: BookingHistoryFilters) => {
      setHistoryFilters(filters)
      loadBookingHistory(filters)
    },
    [setHistoryFilters, loadBookingHistory]
  )

  // Load initial data
  useEffect(() => {
    if (user) {
      loadBookingHistory(historyFilters)
    }
  }, [user, loadBookingHistory, historyFilters])

  return {
    // State
    userBookings,
    bookingHistory,
    isLoadingHistory,
    historyError,
    historyFilters,

    // Actions
    loadBookingHistory,
    updateFilters,
    resetHistoryState,
  }
}

/**
 * Hook for managing experience bookings (for hosts/admin)
 */
export const useExperienceBookings = (experienceId: string) => {
  const {
    experienceBookings,
    isLoadingExperienceBookings,
    experienceBookingsError,
    setExperienceBookings,
    setIsLoadingExperienceBookings,
    setExperienceBookingsError,
  } = useLocalExperiencesBookingStore()

  /**
   * Load bookings for an experience
   */
  const loadExperienceBookings = useCallback(
    async (status?: BookingStatus[]) => {
      try {
        setIsLoadingExperienceBookings(true)
        setExperienceBookingsError(null)

        const response = await LocalExperiencesBookingService.getExperienceBookings(
          experienceId,
          status
        )

        if (response.success) {
          setExperienceBookings(response.data || [])
        } else {
          const errorMessage =
            response.error instanceof Error
              ? response.error.message
              : "Failed to load experience bookings"
          setExperienceBookingsError(errorMessage)
          toast.error(errorMessage)
        }
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : "Failed to load experience bookings"
        setExperienceBookingsError(errorMessage)
        toast.error(errorMessage)
      } finally {
        setIsLoadingExperienceBookings(false)
      }
    },
    [
      experienceId,
      setIsLoadingExperienceBookings,
      setExperienceBookingsError,
      setExperienceBookings,
    ]
  )

  // Load initial data
  useEffect(() => {
    if (experienceId) {
      loadExperienceBookings()
    }
  }, [experienceId, loadExperienceBookings])

  return {
    // State
    experienceBookings,
    isLoadingExperienceBookings,
    experienceBookingsError,

    // Actions
    loadExperienceBookings,
  }
}
