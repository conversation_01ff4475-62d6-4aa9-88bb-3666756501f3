import { Timestamp } from "firebase/firestore"
import { BaseEntity } from "../base/base.types"
import {
  NotificationType as SharedNotificationType,
  BaseNotificationData,
  NotificationTypes,
} from "../../shared/notification.types"

/**
 * Re-export shared notification type
 */
export type NotificationType = SharedNotificationType

/**
 * Re-export notification types constants
 */
export { NotificationTypes }

/**
 * Notification entity with Firebase client SDK timestamps
 */
export interface Notification extends BaseEntity, BaseNotificationData {
  // BaseEntity provides: id, createdAt, updatedAt with client SDK Timestamp
  // BaseNotificationData provides all the notification fields
}

/**
 * Notification creation data
 */
export type NotificationCreateData = Omit<Notification, "id" | "createdAt" | "updatedAt">

/**
 * Notification update data
 */
export type NotificationUpdateData = Partial<Pick<Notification, "read">>
