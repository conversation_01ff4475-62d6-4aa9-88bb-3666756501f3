import { db } from "@/lib/firebase"
import { collection, query, where, orderBy, limit, onSnapshot } from "firebase/firestore"
import { BaseRealtimeService } from "../base/base.realtime.service"
import { Notification } from "./notification.types"

/**
 * Notification real-time service for Firebase operations
 */
export class NotificationRealtimeService extends BaseRealtimeService {
  private static readonly COLLECTION = "users"
  private static readonly SUBCOLLECTION = "notifications"

  /**
   * Subscribe to notifications for a user with real-time updates
   * @param userId User ID
   * @param limitCount Number of notifications to fetch
   * @param callback Callback function to handle notification updates
   * @returns Unsubscribe function
   */
  static subscribeToNotifications(
    userId: string,
    limitCount: number = 50,
    callback: (notifications: Notification[], error?: Error) => void
  ): () => void {
    try {
      const q = query(
        collection(db, this.COLLECTION, userId, this.SUBCOLLECTION),
        orderBy("createdAt", "desc"),
        limit(limitCount)
      )

      return onSnapshot(
        q,
        (snapshot) => {
          try {
            const notifications: Notification[] = []

            snapshot.forEach((doc) => {
              const data = doc.data()
              notifications.push({
                id: doc.id,
                ...data,
              } as Notification)
            })

            callback(notifications)
          } catch (error) {
            console.error("Error processing notification snapshot:", error)
            callback([], error as Error)
          }
        },
        (error) => {
          console.error("Error in notification subscription:", error)
          callback([], error)
        }
      )
    } catch (error) {
      console.error("Error setting up notification subscription:", error)
      callback([], error as Error)
      return () => {}
    }
  }

  /**
   * Subscribe to unread notification count for a user
   * @param userId User ID
   * @param callback Callback function to handle unread count updates
   * @returns Unsubscribe function
   */
  static subscribeToUnreadCount(
    userId: string,
    callback: (count: number, error?: Error) => void
  ): () => void {
    try {
      const q = query(
        collection(db, this.COLLECTION, userId, this.SUBCOLLECTION),
        where("read", "==", false)
      )

      return onSnapshot(
        q,
        (snapshot) => {
          try {
            callback(snapshot.size)
          } catch (error) {
            console.error("Error processing unread count snapshot:", error)
            callback(0, error as Error)
          }
        },
        (error) => {
          console.error("Error in unread count subscription:", error)
          callback(0, error)
        }
      )
    } catch (error) {
      console.error("Error setting up unread count subscription:", error)
      callback(0, error as Error)
      return () => {}
    }
  }
}
