"use client"

import { useCallback } from "react"
import {
  useNotificationStore,
  useNotificationsData,
  useUnreadCount as useUnreadCountSelector,
  useNotificationLoading,
  useNotificationError,
  useNotificationMarkingAsRead,
} from "./notification.store"
import { NotificationCreateData } from "./notification.types"

// Export real-time hooks
export * from "./notification.realtime.hooks"

/**
 * Hook for managing notifications
 * @returns Notification state and actions
 */
export function useNotifications() {
  const notifications = useNotificationsData()
  const unreadCount = useUnreadCountSelector()
  const loading = useNotificationLoading()
  const error = useNotificationError()

  const markAsRead = useNotificationStore((state) => state.markAsRead)
  const markAllAsRead = useNotificationStore((state) => state.markAllAsRead)
  const deleteNotification = useNotificationStore((state) => state.deleteNotification)

  const handleMarkAsRead = useCallback(
    async (notificationId: string) => {
      await markAsRead(notificationId)
    },
    [markAsRead]
  )

  const handleMarkAllAsRead = useCallback(async () => {
    await markAllAsRead()
  }, [markAllAsRead])

  const handleDeleteNotification = useCallback(
    async (notificationId: string) => {
      await deleteNotification(notificationId)
    },
    [deleteNotification]
  )

  return {
    notifications,
    unreadCount,
    loading,
    error,
    markAsRead: handleMarkAsRead,
    markAllAsRead: handleMarkAllAsRead,
    deleteNotification: handleDeleteNotification,
  }
}

/**
 * Hook for creating message mention notifications
 * @returns Function to create message mention notifications
 */
export function useCreateMessageMentionNotifications() {
  const createMessageMentionNotifications = useNotificationStore(
    (state) => state.createMessageMentionNotifications
  )

  const createNotifications = useCallback(
    async (
      mentionedUserIds: string[],
      tripId: string,
      tripName: string,
      senderName: string,
      senderUserId: string,
      senderPhotoURL?: string
    ) => {
      const notificationData: Omit<NotificationCreateData, "userId"> = {
        type: "message_mention",
        title: "New Message",
        message: `${senderName} mentioned you in ${tripName}`,
        read: false,
        actionUrl: `/trips/${tripId}?tab=chat`,
        relatedEntityId: tripId,
        relatedEntityType: "trip",
        senderUserId,
        senderUserName: senderName,
        senderUserPhotoURL: senderPhotoURL,
      }

      await createMessageMentionNotifications(mentionedUserIds, notificationData)
    },
    [createMessageMentionNotifications]
  )

  return { createNotifications }
}

/**
 * Hook for getting notification marking state
 * @param notificationId Notification ID
 * @returns Whether the notification is being marked as read
 */
export function useNotificationMarkingState(notificationId: string) {
  const markingAsRead = useNotificationMarkingAsRead(notificationId)

  return { markingAsRead }
}
