import { Timestamp } from "firebase/firestore"
import { BaseEntity } from "../base/base.types"
import { ActivityPreferences } from "../activity-preferences/activity-preferences.types"

/**
 * User preferences entity
 */
export interface UserPreferences extends BaseEntity {
  userId: string
  theme: "light" | "dark" | "system"
  travelPreferences: string[]
  budgetRange: [number, number] | string
  availabilityPreferences: string[]
  preferredTravelSeasons: string[]
  travelGroupPreferences: string[]
  // AI preferences
  aiEnabled: boolean
  proactiveSuggestions: boolean
  // Notification preferences
  notificationsEnabled: boolean
  emailNotifications: boolean
  pushNotifications: boolean
  tripUpdatesNotifications: boolean
  squadMessagesNotifications: boolean
  invitationNotifications: boolean
  aiSuggestionsNotifications: boolean
  location: string
  locationPlaceId: string
  // Activity preferences (Pro-only feature)
  activityPreferences?: ActivityPreferences | null
}

/**
 * User preferences creation data
 */
export type UserPreferencesCreateData = Omit<UserPreferences, "id" | "createdAt" | "updatedAt">

/**
 * User preferences update data
 */
export type UserPreferencesUpdateData = Partial<UserPreferences>

/**
 * Utility function to check if user can access activity preferences
 * @param userId User ID
 * @returns Promise<boolean> indicating if user has Pro subscription
 */
export const canAccessActivityPreferences = async (userId: string): Promise<boolean> => {
  try {
    // Import here to avoid circular dependencies
    const { FlatSubscriptionService } = await import(
      "../user-subscription/flat-subscription.service"
    )
    return await FlatSubscriptionService.hasFeatureAccess(userId, "unlimited_ai")
  } catch (error) {
    console.error("Error checking activity preferences access:", error)
    return false
  }
}
