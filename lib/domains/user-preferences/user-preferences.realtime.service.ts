import { BaseRealtimeService } from "../base/base.realtime.service"
import { UserPreferences } from "./user-preferences.types"

/**
 * User preferences real-time service for Firebase real-time operations
 */
export class UserPreferencesRealtimeService {
  private static readonly COLLECTION = "userPreferences"

  /**
   * Subscribe to user preferences by user ID
   * @param userId User ID
   * @param callback Callback function to handle preferences changes
   * @returns Unsubscribe function
   */
  static subscribeToUserPreferences(
    userId: string,
    callback: (preferences: UserPreferences | null, error?: Error) => void
  ): () => void {
    return BaseRealtimeService.subscribeToDocument<UserPreferences>(
      this.COLLECTION,
      userId,
      callback
    )
  }
}
