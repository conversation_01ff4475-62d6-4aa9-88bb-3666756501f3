"use client"

import { useEffect } from "react"
import { useUserPreferencesStore } from "./user-preferences.store"
import { useUser } from "../auth/auth.hooks"
import { UserPreferencesUpdateData } from "./user-preferences.types"

// Export real-time hooks
export * from "./user-preferences.realtime.hooks"

/**
 * Hook to get user preferences
 */
export const useUserPreferences = (userId?: string, useRealtime: boolean = false) => {
  const authUser = useUser()
  const uid = userId || authUser?.uid || ""

  const {
    preferences,
    loading,
    error,
    fetchUserPreferences,
    updateUserPreferences,
    updateUserTheme,
    subscribeToUserPreferences,
    unsubscribeFromUserPreferences,
  } = useUserPreferencesStore()

  useEffect(() => {
    if (uid) {
      if (useRealtime) {
        // Use real-time subscription
        subscribeToUserPreferences(uid)

        // Cleanup on unmount
        return () => {
          unsubscribeFromUserPreferences()
        }
      } else {
        // Use regular fetch
        fetchUserPreferences(uid)
      }
    }

    // Return empty cleanup function for TypeScript
    return () => {}
  }, [
    uid,
    fetchUserPreferences,
    subscribeToUserPreferences,
    unsubscribeFromUserPreferences,
    useRealtime,
  ])

  // Create convenience update functions
  const updatePreferences = async (preferencesData: UserPreferencesUpdateData) => {
    if (!uid) return false
    return updateUserPreferences(uid, preferencesData)
  }

  const updateTheme = async (theme: "light" | "dark" | "system") => {
    if (!uid) return false
    return updateUserTheme(uid, theme)
  }

  return {
    preferences,
    loading,
    error,
    updatePreferences,
    updateTheme,
  }
}
