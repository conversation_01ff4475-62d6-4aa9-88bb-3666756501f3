"use client"

import { useEffect, useState } from "react"
import { UserPreferences } from "./user-preferences.types"
import { UserPreferencesRealtimeService } from "./user-preferences.realtime.service"
import { useUser } from "../auth/auth.hooks"

/**
 * Hook to get real-time updates for user preferences
 */
export const useRealtimeUserPreferences = (userId?: string) => {
  const authUser = useUser()
  const uid = userId || authUser?.uid || ""

  const [preferences, setPreferences] = useState<UserPreferences | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<Error | null>(null)

  useEffect(() => {
    if (!uid) {
      setPreferences(null)
      setLoading(false)
      return () => {}
    }

    setLoading(true)

    const unsubscribe = UserPreferencesRealtimeService.subscribeToUserPreferences(
      uid,
      (data, err) => {
        if (err) {
          console.error("Error getting real-time user preferences:", err)
          setError(err)
          setLoading(false)
          return
        }

        setPreferences(data)
        setLoading(false)
      }
    )

    return () => unsubscribe()
  }, [uid])

  return { preferences, loading, error }
}
