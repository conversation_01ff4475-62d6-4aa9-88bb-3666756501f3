"use client"

import { create } from "zustand"
import { UserPreferences, UserPreferencesUpdateData } from "./user-preferences.types"
import { UserPreferencesService } from "./user-preferences.service"
import { UserPreferencesRealtimeService } from "./user-preferences.realtime.service"
import { toast } from "@/components/ui/use-toast"

/**
 * User preferences store state interface
 */
interface UserPreferencesState {
  // State
  preferences: UserPreferences | null
  loading: boolean
  error: Error | null

  // Real-time subscriptions
  preferencesSubscription: (() => void) | null

  // Actions
  setLoading: (loading: boolean) => void
  setUserPreferences: (preferences: UserPreferences | null) => void
  fetchUserPreferences: (userId: string) => Promise<void>
  updateUserPreferences: (
    userId: string,
    preferencesData: UserPreferencesUpdateData
  ) => Promise<boolean>
  updateUserTheme: (userId: string, theme: "light" | "dark" | "system") => Promise<boolean>
  subscribeToUserPreferences: (userId: string) => void
  unsubscribeFromUserPreferences: () => void
}

/**
 * User preferences store with Zustand
 */
export const useUserPreferencesStore = create<UserPreferencesState>((set, get) => ({
  // Initial state
  preferences: null,
  loading: false,
  error: null,

  // Real-time subscriptions
  preferencesSubscription: null,

  // Actions
  setLoading: (loading) => {
    set({ loading })
  },
  setUserPreferences: (preferences) => {
    set({ preferences })
  },

  fetchUserPreferences: async (userId: string) => {
    try {
      set({ loading: true, error: null })
      const preferences = await UserPreferencesService.getUserPreferences(userId)
      set({ preferences, loading: false })
    } catch (error) {
      console.error("Error fetching user preferences:", error)
      set({ error: error as Error, loading: false })
    }
  },

  updateUserPreferences: async (userId, preferencesData) => {
    try {
      set({ loading: true, error: null })
      const result = await UserPreferencesService.updateUserPreferences(userId, preferencesData)

      // If successful, refresh preferences
      if (result.success) {
        await get().fetchUserPreferences(userId)
      }

      set({ loading: false })
      toast({
        title: "Preferences updated",
        description: "Your preferences have been updated successfully.",
      })
      return result.success
    } catch (error) {
      console.error("Error updating user preferences:", error)
      set({ error: error as Error, loading: false })
      return false
    }
  },

  updateUserTheme: async (userId, theme) => {
    try {
      set({ loading: true, error: null })
      const result = await UserPreferencesService.updateUserTheme(userId, theme)

      // If successful, refresh preferences
      if (result.success) {
        await get().fetchUserPreferences(userId)
      }

      set({ loading: false })
      toast({
        title: "Theme updated",
        description: "Your theme preference has been updated successfully.",
      })
      return result.success
    } catch (error) {
      console.error("Error updating user theme:", error)
      set({ error: error as Error, loading: false })
      return false
    }
  },

  subscribeToUserPreferences: (userId: string) => {
    // Unsubscribe from any existing subscription
    const { unsubscribeFromUserPreferences } = get()
    unsubscribeFromUserPreferences()

    // Subscribe to the user preferences
    const unsubscribe = UserPreferencesRealtimeService.subscribeToUserPreferences(
      userId,
      (preferences, error) => {
        if (error) {
          set({ error, loading: false })
          return
        }

        set({ preferences, loading: false })
      }
    )

    set({ preferencesSubscription: unsubscribe })
  },

  unsubscribeFromUserPreferences: () => {
    const { preferencesSubscription } = get()
    if (preferencesSubscription) {
      preferencesSubscription()
      set({ preferencesSubscription: null })
    }
  },
}))
