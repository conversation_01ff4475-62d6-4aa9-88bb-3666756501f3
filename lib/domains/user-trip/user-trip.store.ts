"use client"

import { create } from "zustand"
import { UserTrip, UserTripStatus } from "./user-trip.types"
import { UserTripService } from "./user-trip.service"

/**
 * User-Trip store state interface
 */
interface UserTripState {
  // State
  userTrips: UserTrip[]
  currentUserTrip: UserTrip | null
  loading: boolean
  error: Error | null

  // Actions
  checkUserTripStatus: (userId: string, tripId: string) => Promise<UserTrip | null>
  updateUserTripStatus: (userId: string, tripId: string, status: UserTripStatus) => Promise<boolean>
  getTripAttendees: (tripId: string) => Promise<string[]>
  getUserAttendingTrips: (userId: string) => Promise<string[]>
  clearUserTrips: () => void
}

/**
 * User-Trip store with Zustand
 */
export const useUserTripStore = create<UserTripState>((set, get) => ({
  // Initial state
  userTrips: [],
  currentUserTrip: null,
  loading: false,
  error: null,

  // Actions
  checkUserTripStatus: async (userId: string, tripId: string) => {
    try {
      set({ loading: true, error: null })
      const userTrip = await UserTripService.checkUserTripStatus(userId, tripId)
      set({ currentUserTrip: userTrip, loading: false })
      return userTrip
    } catch (error) {
      console.error("Error checking user trip status:", error)
      set({ error: error as Error, loading: false })
      return null
    }
  },

  updateUserTripStatus: async (userId: string, tripId: string, status: UserTripStatus) => {
    try {
      set({ loading: true, error: null })
      const result = await UserTripService.updateUserTripStatus(userId, tripId, status)

      if (result.success) {
        // Refresh the current user trip
        const userTrip = await UserTripService.checkUserTripStatus(userId, tripId)
        set({ currentUserTrip: userTrip })
      }

      set({ loading: false })
      return result.success
    } catch (error) {
      console.error("Error updating user trip status:", error)
      set({ error: error as Error, loading: false })
      return false
    }
  },

  getTripAttendees: async (tripId: string) => {
    try {
      set({ loading: true, error: null })
      const attendees = await UserTripService.getTripAttendees(tripId)
      set({ loading: false })
      return attendees
    } catch (error) {
      console.error("Error getting trip attendees:", error)
      set({ error: error as Error, loading: false })
      return []
    }
  },

  getUserAttendingTrips: async (userId: string) => {
    try {
      set({ loading: true, error: null })
      const tripIds = await UserTripService.getUserAttendingTrips(userId)
      set({ loading: false })
      return tripIds
    } catch (error) {
      console.error("Error getting user attending trips:", error)
      set({ error: error as Error, loading: false })
      return []
    }
  },

  clearUserTrips: () => {
    set({ userTrips: [], currentUserTrip: null })
  },
}))
