"use client"

import { useEffect, useState, useMemo } from "react"
import { Trip } from "./trip.types"
import { TripRealtimeService } from "./trip.realtime.service"
import { useUser } from "@/lib/domains/auth/auth.hooks"
import { useRealtimeUserSquads } from "../squad/squad.realtime.hooks"
import { UserTripService } from "../user-trip/user-trip.service"

import { useAuthStore } from "@/lib/domains/auth/auth.store"
import { useSquadStore } from "@/lib/domains/squad/squad.store"
import { useTripStore } from "./trip.store"

/**
 * Hook to get real-time updates for a specific trip
 */
export const useRealtimeTrip = (tripId: string) => {
  const [trip, setTrip] = useState<Trip | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<Error | null>(null)

  useEffect(() => {
    if (!tripId) {
      setTrip(null)
      setLoading(false)
      return () => {}
    }

    setLoading(true)

    const unsubscribe = TripRealtimeService.subscribeToTrip(tripId, async (data, err) => {
      if (err) {
        console.error("Error getting real-time trip:", err)
        setError(err)
        setLoading(false)
        return
      }

      // REMOVED: syncTripAttendees call that was causing infinite write loop
      // The TripRealtimeService.subscribeToTrip already handles getting attendees
      // No need to sync here as it triggers infinite writes

      setTrip(data)
      setLoading(false)
    })

    return () => unsubscribe()
  }, [tripId])

  return { trip, loading, error }
}

/**
 * Hook to get real-time updates for user trips
 */
export const useRealtimeUserTrips = () => {
  const user = useUser()
  const [trips, setTrips] = useState<Trip[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<Error | null>(null)

  useEffect(() => {
    if (!user?.uid) {
      setTrips([])
      setLoading(false)
      setError(null)
      return () => {}
    }

    setLoading(true)

    const unsubscribe = TripRealtimeService.subscribeToUserTrips(user.uid, (data, err) => {
      if (err) {
        console.error("Error getting real-time user trips:", err)
        setError(err)
        setLoading(false)
        return
      }

      setTrips(data)
      setLoading(false)
    })

    return () => unsubscribe()
  }, [user])

  return { trips, loading, error }
}

/**
 * Hook to get real-time updates for squad trips
 */
export const useRealtimeSquadTrips = (squadId: string) => {
  const trips = useTripStore((state) => state.trips)
  const setTrips = useTripStore((state) => state.setTrips)
  // const [trips, setTrips] = useState<Trip[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<Error | null>(null)

  useEffect(() => {
    if (!squadId) {
      setTrips([])
      setLoading(false)
      return () => {}
    }

    setLoading(true)

    const unsubscribe = TripRealtimeService.subscribeToSquadTrips(squadId, (data, err) => {
      if (err) {
        console.error("Error getting real-time squad trips:", err)
        setError(err)
        setLoading(false)
        return
      }

      setTrips(data)
      setLoading(false)
    })

    return () => unsubscribe()
  }, [squadId])

  return { trips, loading, error }
}

/**
 * Hook to get real-time updates for all trips across all user squads
 * This combines useRealtimeUserSquads and useRealtimeSquadTrips
 */
// export const useRealtimeUserAllTrips = () => {
//   const user = useAuthStore((state) => state.user)

//   // Get all user squads with real-time updates
//   const squads = useSquadStore((state) => state.squads)
//   const squadsLoading = useSquadStore((state) => state.loading)
//   const squadsError = useSquadStore((state) => state.error)

//   // State for all trips
//   const [tripsLoading, setTripsLoading] = useState(true)
//   const [tripsError, setTripsError] = useState<Error | null>(null)

//   // Track subscriptions for cleanup
//   const unsubscribes: (() => void)[] = [];
//   const [subscriptions, setSubscriptions] = useState<(() => void)[]>([])

//   // Track trips by squad ID to avoid duplicates
//   const [tripsBySquad, setTripsBySquad] = useState<Record<string, Trip[]>>({})

//   // Effect to subscribe to trips for each squad
//   useEffect(() => {
//     if (!user || squadsLoading) {
//       setTripsBySquad({})
//       setTripsLoading(true)
//       return
//     }

//     if (squadsError) {
//       setTripsError(squadsError)
//       setTripsLoading(false)
//       return
//     }

//     // If no squads, set empty trips and return
//     if (squads.length === 0) {
//       setTripsBySquad({})
//       setTripsLoading(false)
//       return
//     }

//     // Clear previous subscriptions
//     subscriptions.forEach((unsubscribe) => unsubscribe())
//     setSubscriptions([])

//     // Start loading
//     setTripsLoading(true)

//     // Create a new subscriptions array
//     const newSubscriptions: (() => void)[] = []

//     // Initialize a counter to track when all subscriptions have loaded
//     let loadedCount = 0
//     const totalCount = squads.length

//     // Create a new trips by squad object
//     const newTripsBySquad: Record<string, Trip[]> = {}

//     // Subscribe to trips for each squad
//     squads.forEach((squad) => {
//       const unsubscribe = TripRealtimeService.subscribeToSquadTrips(squad.id, (data, err) => {
//         if (err) {
//           console.error(`Error getting real-time trips for squad ${squad.id}:`, err)
//           // Don't set error here, just log it
//           return
//         }

//         // Update trips for this squad
//         setTripsBySquad((prev) => ({
//           ...prev,
//           [squad.id]: data || [],
//         }))

//         // Increment loaded count
//         // loadedCount++

//         // // If all squads have loaded, set loading to false
//         // if (loadedCount === totalCount) {
//         //   setTripsLoading(false)
//         // }
//       })

//       // Add to new subscriptions
//       unsubscribes.push(unsubscribe)

//       // Initialize trips for this squad
//       // newTripsBySquad[squad.id] = []
//     })

//     // // Update subscriptions
//     // setSubscriptions(newSubscriptions)

//     // // Initialize tripsBySquad
//     // setTripsBySquad(newTripsBySquad)

//     // Cleanup function
//     return () => {
//       newSubscriptions.forEach((unsubscribe) => unsubscribe())
//     }
//   }, [user, squads, squadsLoading, squadsError])

//   // Update allTrips whenever tripsBySquad changes
//   useEffect(() => {
//     // Combine all trips from all squads
//     const combined = Object.values(tripsBySquad).flat()

//     // Remove duplicates (in case a trip belongs to multiple squads)
//     const uniqueTrips = Array.from(new Map(combined.map((trip) => [trip.id, trip])).values())

//     setAllTrips(uniqueTrips)
//   }, [tripsBySquad])

//   // Compute upcoming and past trips
//   const { upcomingTrips, pastTrips } = useMemo(() => {
//     const now = new Date()

//     const upcoming = allTrips.filter((trip) => {
//       if (!trip.endDate) return false
//       const endDate = trip.endDate instanceof Date ? trip.endDate : trip.endDate.toDate()
//       return trip.status !== "completed" && endDate >= now
//     })

//     const past = allTrips.filter((trip) => {
//       if (!trip.endDate) return false
//       const endDate = trip.endDate instanceof Date ? trip.endDate : trip.endDate.toDate()
//       return trip.status === "completed" || endDate < now
//     })

//     return { upcomingTrips: upcoming, pastTrips: past }
//   }, [allTrips])

//   const allTrips = useMemo(() => {
//     const combined = Object.values(tripsBySquad).flat();
//     return Array.from(new Map(combined.map((trip) => [trip.id, trip])).values());
//   }, [tripsBySquad]);

//   return {
//     trips: allTrips,
//     upcomingTrips,
//     pastTrips,
//     loading: squadsLoading || tripsLoading,
//     error: squadsError || tripsError,
//   }
// }

export const useRealtimeUserAllTrips = () => {
  // --- Primary State ---
  const user = useAuthStore((state) => state.user)
  const { squads, loading: squadsLoading, error: squadsError } = useRealtimeUserSquads()

  // --- Local State for this Hook ---
  const [allTrips, setAllTrips] = useState<Trip[]>([])
  const [tripsLoading, setTripsLoading] = useState(true)
  const [tripsError, setTripsError] = useState<Error | null>(null)

  // --- Main Effect for Subscriptions ---
  useEffect(() => {
    // Primary guard: No user or still waiting for squads, so reset and wait.
    if (!user || squadsLoading) {
      setAllTrips([])
      setTripsLoading(true)
      return
    }

    // Handle case where the user has no squads after loading is complete.
    if (squads.length === 0) {
      setAllTrips([])
      setTripsLoading(false)
      return
    }

    // This object will temporarily hold trips from all subscriptions.
    const tripsFromAllSquads: Record<string, Trip[]> = {}

    const unsubscribes = squads.map((squad) => {
      return TripRealtimeService.subscribeToSquadTrips(squad.id, (squadTrips, err) => {
        if (err) {
          console.error(`Error for squad ${squad.id}:`, err)
          // Optionally set a partial error state
          return
        }

        // Store the latest trips for this specific squad.
        tripsFromAllSquads[squad.id] = squadTrips || []

        // Combine all trips from all squads into a single flat array.
        const combined = Object.values(tripsFromAllSquads).flat()

        // Remove duplicates and update the final state.
        const uniqueTrips = Array.from(new Map(combined.map((trip) => [trip.id, trip])).values())
        setAllTrips(uniqueTrips)
      })
    })

    // Subscriptions are set up, so we are no longer loading.
    setTripsLoading(false)

    // The single cleanup function that runs when the hook unmounts or dependencies change.
    return () => {
      unsubscribes.forEach((unsub) => unsub())
    }
  }, [user, squads, squadsLoading])

  // --- Derived State (Upcoming and Past Trips) ---
  const { upcomingTrips, pastTrips } = useMemo(() => {
    if (tripsLoading) {
      return { upcomingTrips: [], pastTrips: [] }
    }

    const now = new Date()
    const upcoming: Trip[] = []
    const past: Trip[] = []

    allTrips.forEach((trip) => {
      if (!trip.endDate) return
      const endDate = trip.endDate instanceof Date ? trip.endDate : trip.endDate.toDate()

      if (trip.status === "completed" || endDate < now) {
        past.push(trip)
      } else {
        upcoming.push(trip)
      }
    })

    return { upcomingTrips: upcoming, pastTrips: past }
  }, [allTrips, tripsLoading])

  // --- Return Final State ---
  return {
    trips: allTrips,
    upcomingTrips,
    pastTrips,
    loading: squadsLoading || tripsLoading,
    error: squadsError || tripsError,
  }
}
