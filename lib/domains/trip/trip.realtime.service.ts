import { BaseRealtimeService } from "../base/base.realtime.service"
import { Trip } from "./trip.types"
import { UserTripService } from "../user-trip/user-trip.service"
import { UserTrip } from "../user-trip/user-trip.types"

// We'll use the UserTripService directly instead of destructuring the method

/**
 * Trip real-time service for Firebase real-time operations
 */
export class TripRealtimeService {
  private static readonly COLLECTION = "trips"

  /**
   * Subscribe to a trip by ID
   * @param tripId Trip ID
   * @param callback Callback function to handle trip changes
   * @returns Unsubscribe function
   */
  static subscribeToTrip(
    tripId: string,
    callback: (trip: Trip | null, error?: Error) => void
  ): () => void {
    return BaseRealtimeService.subscribeToDocument<Trip>(
      this.COLLECTION,
      tripId,
      async (data, error) => {
        if (error) {
          callback(null, error)
          return
        }

        if (data) {
          try {
            // Get the actual attendees from userTrips collection
            const attendees = await UserTripService.getTripAttendees(tripId)
            callback({ ...data, attendees })
          } catch (err) {
            console.error("Error getting trip attendees:", err)
            callback(data) // Still return the trip data without updated attendees
          }
        } else {
          callback(null)
        }
      }
    )
  }

  /**
   * Subscribe to trips for a user
   * @param userId User ID
   * @param callback Callback function to handle trips changes
   * @returns Unsubscribe function
   */
  static subscribeToUserTrips(
    userId: string,
    callback: (trips: Trip[], error?: Error) => void
  ): () => void {
    // We need to query the userTrips collection to get the trips the user is attending
    // This is a more complex subscription that requires joining data from multiple collections
    // For now, we'll use a simpler approach by querying trips where the user is the creator
    return BaseRealtimeService.subscribeToQuery<Trip>(
      this.COLLECTION,
      [["attendees", "array-contains", userId]],
      async (trips, error) => {
        if (error) {
          callback([], error)
          return
        }

        try {
          // Process each trip to ensure the attendees field is up-to-date
          const processedTrips = await Promise.all(
            trips.map(async (trip) => {
              // Get the actual attendees from userTrips collection
              const attendees = await UserTripService.getTripAttendees(trip.id)

              // Ensure tasksCompleted and totalTasks have default values
              const tasksCompleted =
                typeof trip.tasksCompleted === "number" ? trip.tasksCompleted : 0
              const totalTasks = typeof trip.totalTasks === "number" ? trip.totalTasks : 0

              return {
                ...trip,
                attendees,
                tasksCompleted,
                totalTasks,
              } as Trip
            })
          )

          callback(processedTrips)
        } catch (err) {
          console.error("Error processing user trips:", err)
          callback(trips) // Still return the trips without updated attendees
        }
      }
    )
  }

  /**
   * Subscribe to trips for a squad
   * @param squadId Squad ID
   * @param callback Callback function to handle trips changes
   * @returns Unsubscribe function
   */
  static subscribeToSquadTrips(
    squadId: string,
    callback: (trips: Trip[], error?: Error) => void
  ): () => void {
    return BaseRealtimeService.subscribeToQuery<Trip>(
      this.COLLECTION,
      [["squadId", "==", squadId]],
      async (trips, error) => {
        if (error) {
          callback([], error)
          return
        }

        try {
          // Process each trip to ensure the attendees field is up-to-date
          const processedTrips = await Promise.all(
            trips.map(async (trip) => {
              // Get the actual attendees from userTrips collection
              const attendees = await UserTripService.getTripAttendees(trip.id)

              // Ensure tasksCompleted and totalTasks have default values
              const tasksCompleted =
                typeof trip.tasksCompleted === "number" ? trip.tasksCompleted : 0
              const totalTasks = typeof trip.totalTasks === "number" ? trip.totalTasks : 0

              return {
                ...trip,
                attendees,
                tasksCompleted,
                totalTasks,
              } as Trip
            })
          )

          callback(processedTrips)
        } catch (err) {
          console.error("Error processing squad trips:", err)
          callback(trips) // Still return the trips without updated attendees
        }
      }
    )
  }
}
