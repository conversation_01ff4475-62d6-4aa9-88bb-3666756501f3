"use client"

import { useEffect, useState } from "react"
import { useTripStore } from "./trip.store"
import { Trip, TripCreateData, TripUpdateData } from "./trip.types"
import { TripService } from "./trip.service"
import { useUser } from "@/lib/domains/auth/auth.hooks"
import { UserTripService } from "@/lib/domains/user-trip/user-trip.service"

// Export real-time hooks
export * from "./trip.realtime.hooks"

/**
 * Hook to get all trips for the current user
 */
export const useUserTrips = (useRealtime: boolean = false) => {
  const user = useUser()
  const { trips, loading, error, fetchUserTrips, subscribeToUserTrips, unsubscribeFromUserTrips } =
    useTripStore()

  useEffect(() => {
    if (user?.uid) {
      if (useRealtime) {
        // Use real-time subscription
        subscribeToUserTrips(user.uid)

        // Cleanup on unmount
        return () => {
          unsubscribeFromUserTrips()
        }
      } else {
        // Use regular fetch
        fetchUserTrips(user.uid)
      }
    }

    // Return empty cleanup function for TypeScript
    return () => {}
  }, [user, fetchUserTrips, subscribeToUserTrips, unsubscribeFromUserTrips, useRealtime])

  return { trips, loading, error }
}

/**
 * Hook to get all trips for a squad
 */
export const useSquadTrips = (squadId: string, useRealtime: boolean = false) => {
  const {
    trips,
    loading,
    error,
    fetchSquadTrips,
    subscribeToSquadTrips,
    unsubscribeFromSquadTrips,
  } = useTripStore()

  useEffect(() => {
    if (squadId) {
      if (useRealtime) {
        // Use real-time subscription
        subscribeToSquadTrips(squadId)

        // Cleanup on unmount
        return () => {
          unsubscribeFromSquadTrips()
        }
      } else {
        // Use regular fetch
        fetchSquadTrips(squadId)
      }
    }

    // Return empty cleanup function for TypeScript
    return () => {}
  }, [squadId, fetchSquadTrips, subscribeToSquadTrips, unsubscribeFromSquadTrips, useRealtime])

  return { trips, loading, error }
}

/**
 * Hook to get a specific trip
 */
export const useTrip = (tripId: string, useRealtime: boolean = false) => {
  const { currentTrip, loading, error, fetchTrip, subscribeToTrip, unsubscribeFromTrip } =
    useTripStore()

  useEffect(() => {
    if (tripId) {
      if (useRealtime) {
        // Use real-time subscription
        subscribeToTrip(tripId)

        // Cleanup on unmount
        return () => {
          unsubscribeFromTrip()
        }
      } else {
        // Use regular fetch
        fetchTrip(tripId)
      }
    }

    // Return empty cleanup function for TypeScript
    return () => {}
  }, [tripId, fetchTrip, subscribeToTrip, unsubscribeFromTrip, useRealtime])

  return { trip: currentTrip, loading, error }
}

/**
 * Hook to create a trip with toast notifications
 */
export const useCreateTrip = () => {
  const [creating, setCreating] = useState(false)
  const [error, setError] = useState<Error | null>(null)
  const { createTrip } = useTripStore()

  const create = async (tripData: TripCreateData, options?: { showToast?: boolean }) => {
    try {
      setCreating(true)
      setError(null)
      const tripId = await createTrip(tripData)
      setCreating(false)

      // Show success toast if enabled (default: true)
      if (options?.showToast !== false) {
        const { toast } = await import("@/components/ui/use-toast")
        toast({
          title: "Trip created!",
          description: "Your trip has been created successfully.",
        })
      }

      return tripId
    } catch (err) {
      const error = err as Error
      setError(error)
      setCreating(false)

      // Show error toast if enabled (default: true)
      if (options?.showToast !== false) {
        const { toast } = await import("@/components/ui/use-toast")
        toast({
          title: "Error",
          description: "Failed to create trip. Please try again.",
          variant: "destructive",
        })
      }

      return null
    }
  }

  return { create, creating, error }
}

/**
 * Hook to update a trip
 */
export const useUpdateTrip = (tripId?: string) => {
  const [updating, setUpdating] = useState(false)
  const [error, setError] = useState<Error | null>(null)
  const { updateTrip } = useTripStore()

  const update = async (data: TripUpdateData, id?: string) => {
    const targetId = id || tripId
    if (!targetId) {
      setError(new Error("No trip ID provided"))
      return false
    }

    try {
      setUpdating(true)
      setError(null)
      const success = await updateTrip(targetId, data)
      setUpdating(false)
      return success
    } catch (err) {
      setError(err as Error)
      setUpdating(false)
      return false
    }
  }

  return { update, updating, error }
}

/**
 * Hook to check if a user is the trip leader
 */
export const useIsTripLeader = (tripId: string) => {
  const user = useUser()
  const [isLeader, setIsLeader] = useState(false)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const checkLeaderStatus = async () => {
      if (user?.uid && tripId) {
        setLoading(true)
        try {
          const result = await TripService.isUserTripLeader(user.uid, tripId)
          setIsLeader(result)
        } catch (error) {
          console.error("Error checking trip leader status:", error)
          setIsLeader(false)
        } finally {
          setLoading(false)
        }
      } else {
        setIsLeader(false)
        setLoading(false)
      }
    }

    checkLeaderStatus()
  }, [user, tripId])

  return { isLeader, loading }
}

/**
 * Hook to delete a trip
 */
export const useDeleteTrip = () => {
  const user = useUser()
  const { deleteTrip } = useTripStore()

  const handleDeleteTrip = async (tripId: string): Promise<boolean> => {
    if (!user?.uid) {
      console.error("User not authenticated")
      return false
    }

    try {
      const result = await TripService.deleteTrip(tripId, user.uid)
      if (result.success) {
        // Update the store to remove the trip
        await deleteTrip(tripId)
        return true
      } else {
        console.error("Error deleting trip:", result.error)
        return false
      }
    } catch (error) {
      console.error("Error deleting trip:", error)
      return false
    }
  }

  return { deleteTrip: handleDeleteTrip }
}

/**
 * Hook to get trips created by the current user
 */
export const useUserCreatedTrips = () => {
  const user = useUser()
  const [trips, setTrips] = useState<Trip[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<Error | null>(null)

  useEffect(() => {
    const fetchTrips = async () => {
      if (user?.uid) {
        setLoading(true)
        setError(null)
        try {
          const userTrips = await TripService.getUserCreatedTrips(user.uid)
          setTrips(userTrips)
        } catch (err) {
          setError(err as Error)
        } finally {
          setLoading(false)
        }
      }
    }

    fetchTrips()

    // Return empty cleanup function for TypeScript
    return () => {}
  }, [user])

  return { trips, loading, error }
}

/**
 * Hook to get trips the user is attending (status: "going")
 */
export const useUserAttendingTrips = () => {
  const user = useUser()
  const [trips, setTrips] = useState<Trip[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<Error | null>(null)

  useEffect(() => {
    const fetchTrips = async () => {
      if (user?.uid) {
        setLoading(true)
        setError(null)
        try {
          // Get trip IDs where user has "going" status
          const tripIds = await UserTripService.getUserAttendingTrips(user.uid)

          // Fetch full trip data for each ID
          const tripPromises = tripIds.map((tripId: string) => TripService.getTrip(tripId))
          const tripResults = await Promise.allSettled(tripPromises)

          // Filter out failed requests and null results
          const validTrips = tripResults
            .filter(
              (result: PromiseSettledResult<Trip | null>): result is PromiseFulfilledResult<Trip> =>
                result.status === "fulfilled" && result.value !== null
            )
            .map((result: PromiseFulfilledResult<Trip>) => result.value)

          setTrips(validTrips)
        } catch (err) {
          setError(err as Error)
        } finally {
          setLoading(false)
        }
      }
    }

    fetchTrips()

    // Return empty cleanup function for TypeScript
    return () => {}
  }, [user])

  return { trips, loading, error }
}

/**
 * Hook to get trips the user is interested in (status: "interested")
 */
export const useUserInterestedTrips = () => {
  const user = useUser()
  const [trips, setTrips] = useState<Trip[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<Error | null>(null)

  useEffect(() => {
    const fetchTrips = async () => {
      if (user?.uid) {
        setLoading(true)
        setError(null)
        try {
          // Get trip IDs where user has "interested" status
          const tripIds = await UserTripService.getUserInterestedTrips(user.uid)

          // Fetch full trip data for each ID
          const tripPromises = tripIds.map((tripId: string) => TripService.getTrip(tripId))
          const tripResults = await Promise.allSettled(tripPromises)

          // Filter out failed requests and null results
          const validTrips = tripResults
            .filter(
              (result: PromiseSettledResult<Trip | null>): result is PromiseFulfilledResult<Trip> =>
                result.status === "fulfilled" && result.value !== null
            )
            .map((result: PromiseFulfilledResult<Trip>) => result.value)

          setTrips(validTrips)
        } catch (err) {
          setError(err as Error)
        } finally {
          setLoading(false)
        }
      }
    }

    fetchTrips()

    // Return empty cleanup function for TypeScript
    return () => {}
  }, [user])

  return { trips, loading, error }
}
