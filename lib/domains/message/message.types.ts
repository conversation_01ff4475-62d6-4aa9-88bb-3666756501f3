import { Timestamp } from "firebase/firestore"
import { BaseEntity } from "../base/base.types"

/**
 * Message entity
 */
export interface Message extends BaseEntity {
  tripId: string
  senderId: string
  senderName: string
  senderPhotoURL?: string
  content: string
  mentionedUserIds: string[]
  imageUrl?: string // For future image support
  isAIResponse?: boolean // Indicates if this is an AI-generated response
  aiPrompt?: string // Original user prompt that triggered this AI response (for analytics)
  messageType?: "user" | "ai_response" // Explicit message type for easier filtering
}

/**
 * Message creation data
 */
export type MessageCreateData = Omit<Message, "id" | "createdAt" | "updatedAt">

/**
 * Message update data
 */
export type MessageUpdateData = Partial<Pick<Message, "content">>

/**
 * AI message creation data for trip chat
 */
export interface AIMessageCreateData {
  tripId: string
  userPrompt: string
  aiResponse: string
  userId: string
  userDisplayName: string
  userPhotoURL?: string
}
