"use client"

import { create } from "zustand"
import { DocumentSnapshot } from "firebase/firestore"
import { Message, MessageCreateData } from "./message.types"
import { MessageService } from "./message.service"
import { toast } from "@/components/ui/use-toast"

/**
 * Message store state interface
 */
interface MessageState {
  // State
  messages: Record<string, Message[]> // Keyed by tripId
  loading: Record<string, boolean> // Keyed by tripId
  error: Record<string, Error | null> // Keyed by tripId
  sending: Record<string, boolean> // Keyed by tripId
  hasMore: Record<string, boolean> // Keyed by tripId
  lastDoc: Record<string, DocumentSnapshot | null> // Keyed by tripId
  newMessageCount: Record<string, number> // Keyed by tripId

  // Actions
  setMessages: (tripId: string, messages: Message[]) => void
  addMessage: (tripId: string, message: Message) => void
  addMessages: (tripId: string, messages: Message[]) => void
  setLoading: (tripId: string, loading: boolean) => void
  setError: (tripId: string, error: Error | null) => void
  setSending: (tripId: string, sending: boolean) => void
  setHasMore: (tripId: string, hasMore: boolean) => void
  setLastDoc: (tripId: string, lastDoc: DocumentSnapshot | null) => void
  incrementNewMessageCount: (tripId: string) => void
  resetNewMessageCount: (tripId: string) => void
  sendMessage: (tripId: string, messageData: MessageCreateData) => Promise<void>
  loadMoreMessages: (tripId: string) => Promise<void>
  clearTripMessages: (tripId: string) => void
}

/**
 * Message store
 */
export const useMessageStore = create<MessageState>((set, get) => ({
  // Initial state
  messages: {},
  loading: {},
  error: {},
  sending: {},
  hasMore: {},
  lastDoc: {},
  newMessageCount: {},

  // Actions
  setMessages: (tripId, messages) =>
    set((state) => {
      // Deduplicate messages by ID to prevent duplicates from optimistic updates
      const existingMessages = state.messages[tripId] ?? []
      const existingIds = new Set(existingMessages.map((m) => m.id))

      // Only add messages that don't already exist
      const newMessages = messages.filter((m) => !existingIds.has(m.id))

      // Merge existing and new messages, sort by createdAt
      const allMessages = [...existingMessages, ...newMessages].sort((a, b) => {
        if (!a.createdAt && !b.createdAt) return 0
        if (!a.createdAt) return 1
        if (!b.createdAt) return -1

        try {
          const aTime = a.createdAt.toDate
            ? a.createdAt.toDate().getTime()
            : typeof a.createdAt === "string"
              ? new Date(a.createdAt).getTime()
              : (a.createdAt as any).getTime()
          const bTime = b.createdAt.toDate
            ? b.createdAt.toDate().getTime()
            : typeof b.createdAt === "string"
              ? new Date(b.createdAt).getTime()
              : (b.createdAt as any).getTime()
          return aTime - bTime
        } catch (error) {
          return 0
        }
      })

      return {
        messages: { ...state.messages, [tripId]: allMessages },
      }
    }),

  addMessage: (tripId, message) =>
    set((state) => {
      const existingMessages = state.messages[tripId] ?? []

      // Check if message already exists
      const messageExists = existingMessages.some((m) => m.id === message.id)
      if (messageExists) {
        return state // Don't add duplicate
      }

      return {
        messages: {
          ...state.messages,
          [tripId]: [message, ...existingMessages],
        },
      }
    }),

  addMessages: (tripId, messages) =>
    set((state) => ({
      messages: {
        ...state.messages,
        [tripId]: [...(state.messages[tripId] ?? []), ...messages],
      },
    })),

  setLoading: (tripId, loading) =>
    set((state) => ({
      loading: { ...state.loading, [tripId]: loading },
    })),

  setError: (tripId, error) =>
    set((state) => ({
      error: { ...state.error, [tripId]: error },
    })),

  setSending: (tripId, sending) =>
    set((state) => ({
      sending: { ...state.sending, [tripId]: sending },
    })),

  setHasMore: (tripId, hasMore) =>
    set((state) => ({
      hasMore: { ...state.hasMore, [tripId]: hasMore },
    })),

  setLastDoc: (tripId, lastDoc) =>
    set((state) => ({
      lastDoc: { ...state.lastDoc, [tripId]: lastDoc },
    })),

  incrementNewMessageCount: (tripId) =>
    set((state) => ({
      newMessageCount: {
        ...state.newMessageCount,
        [tripId]: (state.newMessageCount[tripId] ?? 0) + 1,
      },
    })),

  resetNewMessageCount: (tripId) =>
    set((state) => ({
      newMessageCount: { ...state.newMessageCount, [tripId]: 0 },
    })),

  sendMessage: async (tripId, messageData) => {
    const { setSending, setError, addMessage } = get()

    try {
      setSending(tripId, true)
      setError(tripId, null)

      const messageId = await MessageService.createMessage(tripId, messageData)

      // The message will be added via real-time subscription
      // but we can optimistically add it here for immediate feedback
      const optimisticMessage: Message = {
        ...messageData,
        id: messageId,
        createdAt: null, // Will be updated by real-time subscription
      }

      addMessage(tripId, optimisticMessage)
    } catch (error) {
      console.error("Error sending message:", error)
      setError(tripId, error as Error)
      toast({
        title: "Error",
        description: "Failed to send message. Please try again.",
        variant: "destructive",
      })
    } finally {
      setSending(tripId, false)
    }
  },

  loadMoreMessages: async (tripId) => {
    const { setLoading, setError, addMessages, setHasMore, setLastDoc, lastDoc } = get()

    try {
      setLoading(tripId, true)
      setError(tripId, null)

      const currentLastDoc = lastDoc[tripId] || undefined
      const { messages, lastDoc: newLastDoc } = await MessageService.getMessages(
        tripId,
        20,
        currentLastDoc
      )

      addMessages(tripId, messages)
      setLastDoc(tripId, newLastDoc)
      setHasMore(tripId, messages.length === 20)
    } catch (error) {
      console.error("Error loading more messages:", error)
      setError(tripId, error as Error)
      toast({
        title: "Error",
        description: "Failed to load more messages. Please try again.",
        variant: "destructive",
      })
    } finally {
      setLoading(tripId, false)
    }
  },

  clearTripMessages: (tripId) =>
    set((state) => {
      const newState = { ...state }
      delete newState.messages[tripId]
      delete newState.loading[tripId]
      delete newState.error[tripId]
      delete newState.sending[tripId]
      delete newState.hasMore[tripId]
      delete newState.lastDoc[tripId]
      delete newState.newMessageCount[tripId]
      return newState
    }),
}))

/**
 * Selectors for message store
 */

// Create stable empty array to prevent infinite loops
const EMPTY_ARRAY: Message[] = []

/**
 * Individual selector hooks for better performance
 */
export const useMessagesSelector = (tripId: string) => {
  return useMessageStore((state) => {
    const messages = state.messages[tripId]
    return messages ?? EMPTY_ARRAY
  })
}

export const useMessageLoading = (tripId: string) =>
  useMessageStore((state) => state.loading[tripId] ?? false)

export const useMessageError = (tripId: string) =>
  useMessageStore((state) => state.error[tripId] ?? null)

export const useMessageSending = (tripId: string) =>
  useMessageStore((state) => state.sending[tripId] ?? false)

export const useMessageHasMore = (tripId: string) =>
  useMessageStore((state) => state.hasMore[tripId] ?? false)

export const useNewMessageCount = (tripId: string) =>
  useMessageStore((state) => state.newMessageCount[tripId] ?? 0)
