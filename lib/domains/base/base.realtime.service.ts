import { db } from "@/lib/firebase"
import {
  collection,
  doc,
  query,
  where,
  onSnapshot,
  WhereFilterOp,
  DocumentData,
  QuerySnapshot,
  DocumentSnapshot,
  CollectionReference,
  Query,
} from "firebase/firestore"

/**
 * Base real-time service class with common Firebase real-time operations
 */
export class BaseRealtimeService {
  /**
   * Subscribe to a document by ID
   * @param collectionPath The collection path
   * @param id The document ID
   * @param callback The callback function to handle document changes
   * @returns Unsubscribe function
   */
  static subscribeToDocument<T>(
    collectionPath: string,
    id: string,
    callback: (data: T | null, error?: Error) => void
  ): () => void {
    try {
      const docRef = doc(db, collectionPath, id)

      return onSnapshot(
        docRef,
        (docSnap: DocumentSnapshot) => {
          if (docSnap.exists()) {
            callback({ ...docSnap.data(), id: docSnap.id } as T)
          } else {
            callback(null)
          }
        },
        (error: Error) => {
          console.error(`Error subscribing to document in ${collectionPath}:`, error)
          callback(null, error)
        }
      )
    } catch (error) {
      console.error(`Error setting up document subscription in ${collectionPath}:`, error)
      callback(null, error as Error)
      // Return a no-op unsubscribe function
      return () => {}
    }
  }

  /**
   * Subscribe to a collection query
   * @param collectionPath The collection path
   * @param conditions Query conditions
   * @param callback The callback function to handle query results
   * @returns Unsubscribe function
   */
  static subscribeToQuery<T>(
    collectionPath: string,
    conditions: Array<[string, WhereFilterOp, any]>,
    callback: (data: T[], error?: Error) => void
  ): () => void {
    try {
      let q: CollectionReference | Query = collection(db, collectionPath)

      if (conditions.length > 0) {
        q = query(q, ...conditions.map(([field, op, value]) => where(field, op, value)))
      }

      return onSnapshot(
        q,
        (querySnapshot: QuerySnapshot<DocumentData>) => {
          const items = querySnapshot.docs.map((doc) => ({ ...doc.data(), id: doc.id }) as T)
          callback(items)
        },
        (error: Error) => {
          console.error(`Error subscribing to query in ${collectionPath}:`, error)
          callback([], error)
        }
      )
    } catch (error) {
      console.error(`Error setting up query subscription in ${collectionPath}:`, error)
      callback([], error as Error)
      // Return a no-op unsubscribe function
      return () => {}
    }
  }

  /**
   * Subscribe to a collection or query
   * @param queryOrCollection The query or collection reference
   * @param callback The callback function to handle query results
   * @returns Unsubscribe function
   */
  static subscribeToCollection<T>(
    queryOrCollection: Query | CollectionReference,
    callback: (data: T[], error?: Error) => void
  ): () => void {
    try {
      return onSnapshot(
        queryOrCollection,
        (querySnapshot: QuerySnapshot<DocumentData>) => {
          const items = querySnapshot.docs.map((doc) => ({ ...doc.data(), id: doc.id }) as T)
          callback(items)
        },
        (error: Error) => {
          console.error("Error subscribing to collection:", error)
          callback([], error)
        }
      )
    } catch (error) {
      console.error("Error setting up collection subscription:", error)
      callback([], error as Error)
      // Return a no-op unsubscribe function
      return () => {}
    }
  }
}
