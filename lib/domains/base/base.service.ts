import { db } from "@/lib/firebase"
import {
  collection,
  doc,
  getDoc,
  getDocs,
  updateDoc,
  query,
  where,
  serverTimestamp,
  setDoc,
  deleteDoc,
  DocumentReference,
  CollectionReference,
  Query,
  WhereFilterOp,
  QuerySnapshot,
  DocumentSnapshot,
} from "firebase/firestore"
import { ServiceResponse } from "./base.types"

/**
 * Base service class with common Firebase operations
 */
export class BaseService {
  /**
   * Get a document by ID
   * @param collectionPath The collection path
   * @param id The document ID
   * @returns The document data or null if not found
   */
  static async getById<T>(collectionPath: string, id: string): Promise<T | null> {
    try {
      const docRef = doc(db, collectionPath, id)
      const docSnap = await getDoc(docRef)

      if (docSnap.exists()) {
        return { ...docSnap.data(), id: docSnap.id } as T
      }

      return null
    } catch (error) {
      console.error(`Error getting document from ${collectionPath}:`, error)
      throw error
    }
  }

  /**
   * Create a new document
   * @param collectionPath The collection path
   * @param data The document data
   * @returns The new document ID
   */
  static async create<T extends { id?: string }>(collectionPath: string, data: T): Promise<string> {
    try {
      const docRef = doc(collection(db, collectionPath))
      const docId = docRef.id

      await setDoc(docRef, {
        ...data,
        id: docId,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
      })

      return docId
    } catch (error) {
      console.error(`Error creating document in ${collectionPath}:`, error)
      throw error
    }
  }

  /**
   * Update a document
   * @param collectionPath The collection path
   * @param id The document ID
   * @param data The data to update
   * @returns A service response
   */
  static async update<T>(
    collectionPath: string,
    id: string,
    data: Partial<T>
  ): Promise<ServiceResponse> {
    try {
      const docRef = doc(db, collectionPath, id)

      await updateDoc(docRef, {
        ...data,
        updatedAt: serverTimestamp(),
      })

      return { success: true, id }
    } catch (error) {
      console.error(`Error updating document in ${collectionPath}:`, error)
      return { success: false, error }
    }
  }

  /**
   * Delete a document
   * @param collectionPath The collection path
   * @param id The document ID
   * @returns A service response
   */
  static async delete(collectionPath: string, id: string): Promise<ServiceResponse> {
    try {
      const docRef = doc(db, collectionPath, id)
      await deleteDoc(docRef)
      return { success: true }
    } catch (error) {
      console.error(`Error deleting document from ${collectionPath}:`, error)
      return { success: false, error }
    }
  }

  /**
   * Query documents
   * @param collectionPath The collection path
   * @param conditions Query conditions
   * @returns Array of documents matching the query
   */
  static async query<T>(
    collectionPath: string,
    conditions: Array<[string, WhereFilterOp, any]>
  ): Promise<T[]> {
    try {
      let q = collection(db, collectionPath)

      if (conditions.length > 0) {
        q = query(q, ...conditions.map(([field, op, value]) => where(field, op, value))) as any
      }

      const querySnapshot = await getDocs(q as any)
      return querySnapshot.docs.map((doc) => ({ ...(doc.data() as object), id: doc.id }) as T)
    } catch (error) {
      console.error(`Error querying documents from ${collectionPath}:`, error)
      throw error
    }
  }
}
