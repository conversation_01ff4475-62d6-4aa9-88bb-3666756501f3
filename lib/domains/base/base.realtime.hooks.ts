"use client"

import { useEffect, useState } from "react"

/**
 * Base hook for real-time document subscription
 * @param subscribeFunction Function to subscribe to a document that accepts callbacks for data and errors
 * @param dependencies Dependencies array for the useEffect hook
 * @returns The document data, loading state, and error
 */
export function useRealtimeDocument<T>(
  subscribeFunction: (
    onData: (data: T | null) => void,
    onError: (error: Error) => void
  ) => (() => void) | undefined,
  dependencies: React.DependencyList = []
) {
  const [data, setData] = useState<T | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<Error | null>(null)

  useEffect(() => {
    setLoading(true)
    setError(null)

    // Subscribe to the document with callbacks to update state
    const unsubscribe = subscribeFunction(
      (newData) => {
        setData(newData)
        setLoading(false)
      },
      (err) => {
        setError(err)
        setLoading(false)
      }
    )

    // Cleanup function
    return () => {
      if (unsubscribe) {
        unsubscribe()
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, dependencies)

  return { data, loading, error }
}

/**
 * Base hook for real-time collection subscription
 * @param subscribeFunction Function to subscribe to a collection that accepts callbacks for data and errors
 * @param dependencies Dependencies array for the useEffect hook
 * @returns The collection data, loading state, and error
 */
export function useRealtimeCollection<T>(
  subscribeFunction: (
    onData: (data: T[]) => void,
    onError: (error: Error) => void
  ) => (() => void) | undefined,
  dependencies: React.DependencyList = []
) {
  const [data, setData] = useState<T[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<Error | null>(null)

  useEffect(() => {
    setLoading(true)
    setError(null)

    // Subscribe to the collection with callbacks to update state
    const unsubscribe = subscribeFunction(
      (newData) => {
        setData(newData)
        setLoading(false)
      },
      (err) => {
        setError(err)
        setLoading(false)
      }
    )

    // Cleanup function
    return () => {
      if (unsubscribe) {
        unsubscribe()
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, dependencies)

  return { data, loading, error }
}

/**
 * Base hook creator for real-time document subscription
 * @param setData Function to set the document data
 * @param setLoading Function to set the loading state
 * @param setError Function to set the error
 * @returns A function that accepts a subscribe function with callbacks and returns an unsubscribe function
 */
export function createRealtimeDocumentHook<T>(
  setData: (data: T | null) => void,
  setLoading: (loading: boolean) => void,
  setError: (error: Error | null) => void
) {
  return (
    subscribeFunction: (
      onData: (data: T | null) => void,
      onError: (error: Error) => void
    ) => (() => void) | undefined
  ) => {
    setLoading(true)
    setError(null)

    try {
      // Subscribe to the document with callbacks to update state
      const unsubscribe = subscribeFunction(
        (newData) => {
          setData(newData)
          setLoading(false)
        },
        (err) => {
          setError(err)
          setLoading(false)
        }
      )

      // Return the unsubscribe function
      return unsubscribe
    } catch (err) {
      setError(err as Error)
      setLoading(false)
      return undefined
    }
  }
}

/**
 * Base hook creator for real-time collection subscription
 * @param setData Function to set the collection data
 * @param setLoading Function to set the loading state
 * @param setError Function to set the error
 * @returns A function that accepts a subscribe function with callbacks and returns an unsubscribe function
 */
export function createRealtimeCollectionHook<T>(
  setData: (data: T[]) => void,
  setLoading: (loading: boolean) => void,
  setError: (error: Error | null) => void
) {
  return (
    subscribeFunction: (
      onData: (data: T[]) => void,
      onError: (error: Error) => void
    ) => (() => void) | undefined
  ) => {
    setLoading(true)
    setError(null)

    try {
      // Subscribe to the collection with callbacks to update state
      const unsubscribe = subscribeFunction(
        (newData) => {
          setData(newData)
          setLoading(false)
        },
        (err) => {
          setError(err)
          setLoading(false)
        }
      )

      // Return the unsubscribe function
      return unsubscribe
    } catch (err) {
      setError(err as Error)
      setLoading(false)
      return undefined
    }
  }
}
