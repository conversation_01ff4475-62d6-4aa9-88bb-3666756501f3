import { db } from "@/lib/firebase"
import {
  doc,
  setDoc,
  getDoc,
  updateDoc,
  deleteDoc,
  collection,
  query,
  getDocs,
  serverTimestamp,
  orderBy,
} from "firebase/firestore"
import { ServiceResponse } from "../base/base.types"
import {
  MemberTravelDetails,
  TravelDetailsCreateData,
  TravelDetailsUpdateData,
  ImageUploadData,
  ImageUploadResponse,
} from "./travel-details.types"
import { uploadTravelDetailsAction } from "@/app/(authenticated)/trips/[id]/actions/upload-travel-details"

/**
 * Travel details service for Firebase operations
 */
export class TravelDetailsService {
  private static readonly COLLECTION = "trips"
  private static readonly SUBCOLLECTION = "travel-details"

  /**
   * Get travel details collection reference for a trip
   */
  private static getCollectionRef(tripId: string) {
    return collection(db, this.COLLECTION, tripId, this.SUBCOLLECTION)
  }

  /**
   * Get travel details document reference
   */
  private static getDocRef(tripId: string, memberId: string) {
    return doc(db, this.COLLECTION, tripId, this.SUBCOLLECTION, memberId)
  }

  /**
   * Upload image to Vercel Blob using server action
   */
  static async uploadImage(
    tripId: string,
    userId: string,
    imageData: ImageUploadData
  ): Promise<ImageUploadResponse> {
    try {
      // Validate inputs
      if (!tripId || !userId || !imageData.file) {
        const error = "Missing required parameters for image upload"
        console.error(error, { tripId: !!tripId, userId: !!userId, file: !!imageData.file })
        return {
          success: false,
          error,
        }
      }

      const formData = new FormData()
      formData.append("file", imageData.file)

      const result = await uploadTravelDetailsAction(tripId, userId, imageData.type, formData)

      if (!result) {
        console.error("Server action returned null/undefined result")
        return {
          success: false,
          error: "Server action returned no result",
        }
      }

      return result
    } catch (error) {
      console.error("Error in TravelDetailsService.uploadImage:", {
        error,
        message: error instanceof Error ? error.message : "Unknown error",
        stack: error instanceof Error ? error.stack : undefined,
        tripId,
        userId,
        imageType: imageData.type,
      })

      // Provide more specific error messages
      let errorMessage = "Upload failed"
      if (error instanceof Error) {
        if (error.message.includes("fetch")) {
          errorMessage = "Network error during upload"
        } else if (error.message.includes("server")) {
          errorMessage = "Server error during upload"
        } else {
          errorMessage = error.message
        }
      }

      return {
        success: false,
        error: errorMessage,
      }
    }
  }

  /**
   * Create or update travel details for a member
   */
  static async upsertTravelDetails(
    tripId: string,
    memberId: string,
    memberName: string,
    data: Partial<Pick<MemberTravelDetails, "flightImage" | "accommodationImage">>
  ): Promise<ServiceResponse> {
    try {
      const docRef = this.getDocRef(tripId, memberId)
      const existingDoc = await getDoc(docRef)

      if (existingDoc.exists()) {
        // Update existing document
        await updateDoc(docRef, {
          ...data,
          lastUpdated: serverTimestamp(),
        })
      } else {
        // Create new document
        await setDoc(docRef, {
          id: memberId,
          memberName,
          ...data,
          createdAt: serverTimestamp(),
          lastUpdated: serverTimestamp(),
        })
      }

      return { success: true }
    } catch (error) {
      console.error("Error upserting travel details:", error)
      return { success: false, error }
    }
  }

  /**
   * Get travel details for a specific member
   */
  static async getMemberTravelDetails(
    tripId: string,
    memberId: string
  ): Promise<MemberTravelDetails | null> {
    try {
      const docRef = this.getDocRef(tripId, memberId)
      const docSnap = await getDoc(docRef)

      if (docSnap.exists()) {
        return { ...docSnap.data(), id: docSnap.id } as MemberTravelDetails
      }

      return null
    } catch (error) {
      console.error("Error getting member travel details:", error)
      throw error
    }
  }

  /**
   * Get all travel details for a trip
   */
  static async getTripTravelDetails(tripId: string): Promise<MemberTravelDetails[]> {
    try {
      const collectionRef = this.getCollectionRef(tripId)
      const q = query(collectionRef, orderBy("lastUpdated", "asc"))
      const querySnapshot = await getDocs(q)

      return querySnapshot.docs.map((doc) => ({
        ...doc.data(),
        id: doc.id,
      })) as MemberTravelDetails[]
    } catch (error) {
      console.error("Error getting trip travel details:", error)
      throw error
    }
  }

  /**
   * Delete travel details for a member
   */
  static async deleteTravelDetails(tripId: string, memberId: string): Promise<ServiceResponse> {
    try {
      const docRef = this.getDocRef(tripId, memberId)
      await deleteDoc(docRef)

      return { success: true }
    } catch (error) {
      console.error("Error deleting travel details:", error)
      return { success: false, error }
    }
  }

  /**
   * Update specific image for a member
   */
  static async updateMemberImage(
    tripId: string,
    memberId: string,
    memberName: string,
    imageType: "flight" | "accommodation",
    imageUrl: string
  ): Promise<ServiceResponse> {
    try {
      const updateData = {
        [imageType === "flight" ? "flightImage" : "accommodationImage"]: imageUrl,
      }

      return await this.upsertTravelDetails(tripId, memberId, memberName, updateData)
    } catch (error) {
      console.error("Error updating member image:", error)
      return { success: false, error }
    }
  }
}
