"use client"

import { useEffect, useState, useMemo } from "react"
import { MemberTravelDetails } from "./travel-details.types"
import { TravelDetailsRealtimeService } from "./travel-details.realtime.service"
import { useTravelDetailsStore } from "./travel-details.store"

/**
 * Hook to get real-time updates for trip travel details
 */
export const useRealtimeTripTravelDetails = (tripId: string) => {
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<Error | null>(null)
  const { setCurrentTripDetails } = useTravelDetailsStore()

  // Memoize tripId to prevent unnecessary re-subscriptions
  const memoizedTripId = useMemo(() => tripId, [tripId])

  useEffect(() => {
    if (!memoizedTripId) {
      setLoading(false)
      return
    }

    setLoading(true)
    setError(null)

    const unsubscribe = TravelDetailsRealtimeService.subscribeToTripTravelDetails(
      memoizedTripId,
      (details, error) => {
        if (error) {
          console.error("Error in travel details subscription:", error)
          setError(error)
          setLoading(false)
          return
        }

        setCurrentTripDetails(details)
        setLoading(false)
      }
    )

    return () => {
      unsubscribe()
    }
  }, [memoizedTripId, setCurrentTripDetails])

  const currentTripDetails = useTravelDetailsStore((state) => state.currentTripDetails)

  return {
    travelDetails: currentTripDetails,
    loading,
    error,
  }
}

/**
 * Hook to get real-time updates for a specific member's travel details
 */
export const useRealtimeMemberTravelDetails = (tripId: string, memberId: string) => {
  const [memberDetails, setMemberDetails] = useState<MemberTravelDetails | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<Error | null>(null)

  // Memoize parameters to prevent unnecessary re-subscriptions
  const memoizedParams = useMemo(() => ({ tripId, memberId }), [tripId, memberId])

  useEffect(() => {
    if (!memoizedParams.tripId || !memoizedParams.memberId) {
      setLoading(false)
      return
    }

    setLoading(true)
    setError(null)

    const unsubscribe = TravelDetailsRealtimeService.subscribeToMemberTravelDetails(
      memoizedParams.tripId,
      memoizedParams.memberId,
      (details, error) => {
        if (error) {
          console.error("Error in member travel details subscription:", error)
          setError(error)
          setLoading(false)
          return
        }

        setMemberDetails(details)
        setLoading(false)
      }
    )

    return () => {
      unsubscribe()
    }
  }, [memoizedParams])

  return {
    memberDetails,
    loading,
    error,
  }
}
