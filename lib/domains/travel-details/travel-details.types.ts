import { Timestamp } from "firebase/firestore"
import { BaseEntity } from "../base/base.types"

/**
 * Member travel details entity
 */
export interface MemberTravelDetails extends BaseEntity {
  id: string // member id
  memberName: string
  flightImage?: string
  accommodationImage?: string
  lastUpdated: Timestamp
}

/**
 * Travel details creation data
 */
export type TravelDetailsCreateData = Omit<MemberTravelDetails, "id" | "createdAt" | "lastUpdated">

/**
 * Travel details update data
 */
export type TravelDetailsUpdateData = Partial<Omit<MemberTravelDetails, "id" | "createdAt">>

/**
 * Image upload data
 */
export interface ImageUploadData {
  file: File
  type: "flight" | "accommodation"
}

/**
 * Image upload response
 */
export interface ImageUploadResponse {
  success: boolean
  url?: string
  error?: string
}
