import { BaseRealtimeService } from "../base/base.realtime.service"
import { MemberTravelDetails } from "./travel-details.types"
import { collection, query, orderBy } from "firebase/firestore"
import { db } from "@/lib/firebase"

/**
 * Travel details real-time service for Firebase operations
 */
export class TravelDetailsRealtimeService extends BaseRealtimeService {
  private static readonly COLLECTION = "trips"
  private static readonly SUBCOLLECTION = "travel-details"

  /**
   * Subscribe to travel details for a trip
   * @param tripId Trip ID
   * @param callback Callback function to handle travel details changes
   * @returns Unsubscribe function
   */
  static subscribeToTripTravelDetails(
    tripId: string,
    callback: (details: MemberTravelDetails[], error?: Error) => void
  ): () => void {
    try {
      const collectionRef = collection(db, this.COLLECTION, tripId, this.SUBCOLLECTION)
      const q = query(collectionRef, orderBy("lastUpdated", "asc"))

      return BaseRealtimeService.subscribeToCollection<MemberTravelDetails>(q, (data, error) => {
        if (error) {
          callback([], error)
          return
        }
        callback(data)
      })
    } catch (error) {
      console.error("Error setting up travel details subscription:", error)
      callback([], error as Error)
      return () => {} // Return empty unsubscribe function
    }
  }

  /**
   * Subscribe to travel details for a specific member
   * @param tripId Trip ID
   * @param memberId Member ID
   * @param callback Callback function to handle member travel details changes
   * @returns Unsubscribe function
   */
  static subscribeToMemberTravelDetails(
    tripId: string,
    memberId: string,
    callback: (details: MemberTravelDetails | null, error?: Error) => void
  ): () => void {
    const documentPath = `${this.COLLECTION}/${tripId}/${this.SUBCOLLECTION}`

    return BaseRealtimeService.subscribeToDocument<MemberTravelDetails>(
      documentPath,
      memberId,
      callback
    )
  }
}
