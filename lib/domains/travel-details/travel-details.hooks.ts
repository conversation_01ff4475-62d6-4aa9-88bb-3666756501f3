"use client"

import { useCallback } from "react"
import { useTravelDetailsStore } from "./travel-details.store"
import { ImageUploadData } from "./travel-details.types"
import { toast } from "@/components/ui/use-toast"

// Export real-time hooks
export * from "./travel-details.realtime.hooks"

/**
 * Hook for travel details operations
 */
export const useTravelDetails = () => {
  const {
    currentTripDetails,
    loading,
    uploadingImages,
    error,
    fetchTripTravelDetails,
    uploadAndUpdateImage,
    clearError,
  } = useTravelDetailsStore()

  /**
   * Upload image with toast notifications
   */
  const uploadImage = useCallback(
    async (
      tripId: string,
      memberId: string,
      memberName: string,
      imageData: ImageUploadData
    ): Promise<boolean> => {
      try {
        const success = await uploadAndUpdateImage(tripId, memberId, memberName, imageData)

        if (success) {
          toast({
            title: "Upload successful",
            description: `${imageData.type === "flight" ? "Flight" : "Accommodation"} image uploaded successfully.`,
          })
        } else {
          // Get the error from the store for more specific messaging
          const errorMessage = error?.message || "Failed to upload image. Please try again."

          toast({
            title: "Upload failed",
            description: errorMessage,
            variant: "destructive",
          })
        }

        return success
      } catch (error) {
        console.error("Error in uploadImage hook:", {
          error,
          message: error instanceof Error ? error.message : "Unknown error",
          tripId,
          memberId,
          imageType: imageData.type,
        })

        // Provide more specific error messages based on the error
        let errorMessage = "An unexpected error occurred. Please try again."
        if (error instanceof Error) {
          if (error.message.includes("network") || error.message.includes("fetch")) {
            errorMessage = "Network error. Please check your connection and try again."
          } else if (error.message.includes("size")) {
            errorMessage = "File size too large. Please choose a smaller image."
          } else if (error.message.includes("type")) {
            errorMessage = "Invalid file type. Please choose a JPEG, PNG, or WebP image."
          } else if (error.message.includes("storage") || error.message.includes("blob")) {
            errorMessage = "Storage error. Please try again later."
          }
        }

        toast({
          title: "Upload failed",
          description: errorMessage,
          variant: "destructive",
        })
        return false
      }
    },
    [uploadAndUpdateImage, toast, error]
  )

  /**
   * Fetch travel details for a trip
   */
  const fetchDetails = useCallback(
    async (tripId: string) => {
      try {
        await fetchTripTravelDetails(tripId)
      } catch (error) {
        console.error("Error fetching travel details:", error)
        toast({
          title: "Error",
          description: "Failed to load travel details. Please try again.",
          variant: "destructive",
        })
      }
    },
    [fetchTripTravelDetails]
  )

  /**
   * Check if a member is currently uploading an image
   */
  const isUploading = useCallback(
    (memberId: string): boolean => {
      return uploadingImages[memberId] || false
    },
    [uploadingImages]
  )

  /**
   * Get travel details for a specific member
   */
  const getMemberDetails = useCallback(
    (memberId: string) => {
      return currentTripDetails.find((detail) => detail.id === memberId) || null
    },
    [currentTripDetails]
  )

  /**
   * Check if any member has uploaded travel details
   */
  const hasTravelDetails = useCallback(() => {
    return currentTripDetails.length > 0
  }, [currentTripDetails])

  /**
   * Get members with flight images
   */
  const getMembersWithFlightImages = useCallback(() => {
    return currentTripDetails.filter((detail) => detail.flightImage)
  }, [currentTripDetails])

  /**
   * Get members with accommodation images
   */
  const getMembersWithAccommodationImages = useCallback(() => {
    return currentTripDetails.filter((detail) => detail.accommodationImage)
  }, [currentTripDetails])

  return {
    // Data
    travelDetails: currentTripDetails,

    // Loading states
    loading,
    isUploading,

    // Error state
    error,
    clearError,

    // Actions
    uploadImage,
    fetchDetails,

    // Utilities
    getMemberDetails,
    hasTravelDetails,
    getMembersWithFlightImages,
    getMembersWithAccommodationImages,
  }
}
