import admin from 'firebase-admin';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: '.env.local' });

// Initialize Firebase Admin
if (!admin.apps.length) {
  const serviceAccountKey = JSON.parse(process.env.FIREBASE_SERVICE_ACCOUNT_KEY);
  admin.initializeApp({
    credential: admin.credential.cert(serviceAccountKey),
    projectId: 'brotrip-mvp'
  });
}

const db = admin.firestore();

async function checkCompletedBookings() {
  try {
    console.log('🔍 Checking for completed bookings eligible for feedback...\n');
    
    // Get all local experiences
    const experiencesSnapshot = await db.collection('localExperiences').get();
    console.log(`Found ${experiencesSnapshot.size} local experiences\n`);
    
    let totalBookings = 0;
    let completedBookings = 0;
    let eligibleForFeedback = [];
    
    for (const experienceDoc of experiencesSnapshot.docs) {
      const experienceId = experienceDoc.id;
      const experienceData = experienceDoc.data();
      
      console.log(`📍 Experience: ${experienceData.title} (${experienceId})`);
      
      // Get bookings for this experience
      const bookingsSnapshot = await db
        .collection('localExperiences')
        .doc(experienceId)
        .collection('bookings')
        .get();
      
      console.log(`   - Total bookings: ${bookingsSnapshot.size}`);
      totalBookings += bookingsSnapshot.size;
      
      const experienceCompletedBookings = [];
      
      for (const bookingDoc of bookingsSnapshot.docs) {
        const bookingData = bookingDoc.data();
        const bookingId = bookingDoc.id;
        
        console.log(`   - Booking ${bookingId}: status=${bookingData.status}, payment=${bookingData.paymentStatus}`);
        
        if (bookingData.status === 'completed') {
          completedBookings++;
          experienceCompletedBookings.push({
            bookingId,
            experienceId,
            experienceTitle: experienceData.title,
            userName: bookingData.userName,
            userEmail: bookingData.userEmail,
            userId: bookingData.userId,
            date: bookingData.date,
            time: bookingData.time,
            completedAt: bookingData.completedAt
          });
        }
      }
      
      if (experienceCompletedBookings.length > 0) {
        console.log(`   ✅ Found ${experienceCompletedBookings.length} completed bookings`);
        eligibleForFeedback.push(...experienceCompletedBookings);
      }
      
      console.log('');
    }
    
    console.log('📊 SUMMARY:');
    console.log(`Total experiences: ${experiencesSnapshot.size}`);
    console.log(`Total bookings: ${totalBookings}`);
    console.log(`Completed bookings: ${completedBookings}`);
    console.log(`Eligible for feedback: ${eligibleForFeedback.length}\n`);
    
    if (eligibleForFeedback.length > 0) {
      console.log('🎯 COMPLETED BOOKINGS ELIGIBLE FOR FEEDBACK:');
      eligibleForFeedback.forEach((booking, index) => {
        console.log(`${index + 1}. ${booking.experienceTitle}`);
        console.log(`   - Booking ID: ${booking.bookingId}`);
        console.log(`   - User: ${booking.userName} (${booking.userEmail})`);
        console.log(`   - Date: ${booking.date} at ${booking.time}`);
        console.log(`   - Feedback URL: /experiences/feedback/${booking.bookingId}`);
        console.log('');
      });
    } else {
      console.log('❌ No completed bookings found that are eligible for feedback');
    }
    
  } catch (error) {
    console.error('Error checking bookings:', error);
  }
}

checkCompletedBookings().then(() => {
  console.log('✅ Check completed');
  process.exit(0);
}).catch(error => {
  console.error('❌ Error:', error);
  process.exit(1);
});
