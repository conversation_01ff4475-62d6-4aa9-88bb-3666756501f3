# dependencies
/node_modules
/.pnp
.pnp.js
.pnp.*
.yarn/*

# pnpm
.pnpm-store/
.pnpm-debug.log*

# testing
/coverage

# next.js
/.next/
/out/
.next/
next-env.d.ts

# production
/build
/dist

# misc
.DS_Store
*.pem
Thumbs.db

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# local env files
.env*.local
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# vercel
.vercel

# typescript
*.tsbuildinfo

# cache
.eslintcache
.stylelintcache
