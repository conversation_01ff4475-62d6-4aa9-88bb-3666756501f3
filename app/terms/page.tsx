import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { ArrowLeft, ExternalLink } from "lucide-react"
import { Logo } from "@/components/ui/logo"

export const metadata = {
  title: "Terms of Service | Togeda.ai",
  description: "Terms of Service for Togeda.ai",
}

export default function TermsPage() {
  return (
    <div className="flex flex-col min-h-screen">
      {/* Header */}
      <header className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="container mx-auto px-4 py-4 flex justify-between items-center">
          <div className="flex items-center gap-2">
            <Link href="/" className="flex items-center gap-2">
              <Logo width={120} height={42} />
            </Link>
          </div>
          <div className="flex gap-4 items-center">
            <Link href="/">
              <Button variant="ghost">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Home
              </Button>
            </Link>
          </div>
        </div>
      </header>

      {/* Content */}
      <main className="flex-1 py-8 bg-gradient-to-b from-background to-muted/20">
        <div className="container mx-auto px-4 max-w-4xl">
          <div className="bg-background/80 backdrop-blur-sm rounded-lg border shadow-sm p-8">
            <div className="prose prose-neutral dark:prose-invert max-w-none">
              <div className="text-center mb-8">
                <h1 className="text-4xl md:text-5xl font-bold mb-4 bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
                  Terms of Service for Togeda.ai
                </h1>
                <div className="inline-block px-4 py-2 bg-primary/10 rounded-full">
                  <p className="text-sm text-muted-foreground m-0">Effective Date: May 14, 2025</p>
                </div>
              </div>

              <div className="bg-muted/30 rounded-lg p-6 mb-8 border-l-4 border-primary">
                <p className="text-base leading-relaxed m-0">
                  These Terms of Service ("Terms") govern your access to and use of the Togeda.ai
                  platform (the "Service"), operated by Valencia Drive Commerce LLC ("we," "our," or
                  "us"). By using the Service, you agree to these Terms. If you do not agree, please
                  do not use our platform.
                </p>
              </div>

              <div className="bg-background rounded-lg p-6 border shadow-sm">
                <h2 className="text-2xl md:text-3xl font-semibold mt-0 mb-4 text-primary">
                  1. Eligibility & Account Management
                </h2>
                <ul className="pl-6 border-l-4 border-primary/30 bg-muted/20 rounded-r-lg py-4 space-y-3">
                  <li className="pl-4 flex items-start gap-2">
                    <span className="w-2 h-2 bg-primary rounded-full mt-2 flex-shrink-0"></span>
                    <span>You must be at least 13 years old to use Togeda.ai.</span>
                  </li>
                  <li className="pl-4 flex items-start gap-2">
                    <span className="w-2 h-2 bg-primary rounded-full mt-2 flex-shrink-0"></span>
                    <span>
                      Each user must create and maintain their own account; account sharing is
                      prohibited.
                    </span>
                  </li>
                  <li className="pl-4 flex items-start gap-2">
                    <span className="w-2 h-2 bg-primary rounded-full mt-2 flex-shrink-0"></span>
                    <span>
                      You are responsible for maintaining the confidentiality of your account
                      credentials.
                    </span>
                  </li>
                </ul>
              </div>

              <div className="bg-background rounded-lg p-6 border shadow-sm mt-6">
                <h2 className="text-2xl md:text-3xl font-semibold mt-0 mb-4 text-primary">
                  2. Permitted Uses
                </h2>
                <ul className="pl-6 border-l-4 border-primary/30 bg-muted/20 rounded-r-lg py-4 space-y-3">
                  <li className="pl-4 flex items-start gap-2">
                    <span className="w-2 h-2 bg-primary rounded-full mt-2 flex-shrink-0"></span>
                    <span>
                      You may use the Service for personal and commercial purposes (e.g., organizing
                      group trips, retreats, and events).
                    </span>
                  </li>
                  <li className="pl-4 flex items-start gap-2">
                    <span className="w-2 h-2 bg-primary rounded-full mt-2 flex-shrink-0"></span>
                    <span>All use must comply with applicable laws and these Terms.</span>
                  </li>
                </ul>
              </div>

              <div className="bg-background rounded-lg p-6 border shadow-sm mt-6">
                <h2 className="text-2xl md:text-3xl font-semibold mt-0 mb-4 text-primary">
                  3. User-Generated Content
                </h2>
                <ul className="pl-6 border-l-4 border-primary/30 bg-muted/20 rounded-r-lg py-4 space-y-3">
                  <li className="pl-4 flex items-start gap-2">
                    <span className="w-2 h-2 bg-primary rounded-full mt-2 flex-shrink-0"></span>
                    <span>
                      You retain ownership of content you create, including trip names, itineraries,
                      and messages.
                    </span>
                  </li>
                  <li className="pl-4 flex items-start gap-2">
                    <span className="w-2 h-2 bg-primary rounded-full mt-2 flex-shrink-0"></span>
                    <span>
                      We retain rights to use anonymized and aggregated data for analytics and
                      service improvement.
                    </span>
                  </li>
                  <li className="pl-4 flex items-start gap-2">
                    <span className="w-2 h-2 bg-primary rounded-full mt-2 flex-shrink-0"></span>
                    <span>
                      Content is not displayed publicly unless you choose to share it through the
                      platform.
                    </span>
                  </li>
                </ul>
              </div>

              <div className="bg-background rounded-lg p-6 border shadow-sm mt-6">
                <h2 className="text-2xl md:text-3xl font-semibold mt-0 mb-4 text-primary">
                  4. Intellectual Property
                </h2>
                <ul className="pl-6 border-l-4 border-primary/30 bg-muted/20 rounded-r-lg py-4 space-y-3">
                  <li className="pl-4 flex items-start gap-2">
                    <span className="w-2 h-2 bg-primary rounded-full mt-2 flex-shrink-0"></span>
                    <span>
                      All content generated by the Service, including AI-generated itineraries and
                      recommendations, is the intellectual property of Valencia Drive Commerce LLC.
                    </span>
                  </li>
                  <li className="pl-4 flex items-start gap-2">
                    <span className="w-2 h-2 bg-primary rounded-full mt-2 flex-shrink-0"></span>
                    <span>
                      You are permitted to share and export content for personal and commercial use
                      but may not resell or distribute it as a commercial product.
                    </span>
                  </li>
                </ul>
              </div>

              <div className="bg-background rounded-lg p-6 border shadow-sm mt-6">
                <h2 className="text-2xl md:text-3xl font-semibold mt-0 mb-4 text-primary">
                  5. Payments & Refund Policy
                </h2>
                <ul className="pl-6 border-l-4 border-primary/30 bg-muted/20 rounded-r-lg py-4 space-y-3">
                  <li className="pl-4 flex items-start gap-2">
                    <span className="w-2 h-2 bg-primary rounded-full mt-2 flex-shrink-0"></span>
                    <span>Payments are securely processed through Stripe.</span>
                  </li>
                  <li className="pl-4 flex items-start gap-2">
                    <span className="w-2 h-2 bg-primary rounded-full mt-2 flex-shrink-0"></span>
                    <span>All purchases are non-refundable unless otherwise required by law.</span>
                  </li>
                  <li className="pl-4 flex items-start gap-2">
                    <span className="w-2 h-2 bg-primary rounded-full mt-2 flex-shrink-0"></span>
                    <span>For payment disputes, please contact <NAME_EMAIL>.</span>
                  </li>
                </ul>
              </div>

              <div className="bg-background rounded-lg p-6 border shadow-sm mt-6">
                <h2 className="text-2xl md:text-3xl font-semibold mt-0 mb-4 text-primary">
                  6. Termination & Suspension of Accounts
                </h2>
                <ul className="pl-6 border-l-4 border-primary/30 bg-muted/20 rounded-r-lg py-4 space-y-3">
                  <li className="pl-4 flex items-start gap-2">
                    <span className="w-2 h-2 bg-primary rounded-full mt-2 flex-shrink-0"></span>
                    <span>
                      We reserve the right to issue warnings, temporarily suspend, or permanently
                      terminate accounts that violate these Terms, based on the severity and
                      frequency of violations.
                    </span>
                  </li>
                  <li className="pl-4 flex items-start gap-2">
                    <span className="w-2 h-2 bg-primary rounded-full mt-2 flex-shrink-0"></span>
                    <span>
                      Examples of violations include misuse of payment systems, abusive behavior,
                      fraudulent activity, or attempts to bypass app functionality.
                    </span>
                  </li>
                </ul>
              </div>

              <div className="bg-background rounded-lg p-6 border shadow-sm mt-6">
                <h2 className="text-2xl md:text-3xl font-semibold mt-0 mb-4 text-primary">
                  7. Disclaimers & Limitation of Liability
                </h2>
                <ul className="pl-6 border-l-4 border-primary/30 bg-muted/20 rounded-r-lg py-4 space-y-3">
                  <li className="pl-4 flex items-start gap-2">
                    <span className="w-2 h-2 bg-primary rounded-full mt-2 flex-shrink-0"></span>
                    <span>
                      Togeda.ai is a planning and coordination tool; we are not responsible for trip
                      cancellations, third-party services, or the safety of travel activities.
                    </span>
                  </li>
                  <li className="pl-4 flex items-start gap-2">
                    <span className="w-2 h-2 bg-primary rounded-full mt-2 flex-shrink-0"></span>
                    <span>
                      Our Service is provided "as-is" and "as-available" without warranties of any
                      kind.
                    </span>
                  </li>
                  <li className="pl-4 flex items-start gap-2">
                    <span className="w-2 h-2 bg-primary rounded-full mt-2 flex-shrink-0"></span>
                    <span>
                      To the fullest extent permitted by law, we disclaim all liability for damages
                      related to your use of the Service.
                    </span>
                  </li>
                </ul>
              </div>

              <div className="bg-background rounded-lg p-6 border shadow-sm mt-6">
                <h2 className="text-2xl md:text-3xl font-semibold mt-0 mb-4 text-primary">
                  8. Privacy
                </h2>
                <ul className="pl-6 border-l-4 border-primary/30 bg-muted/20 rounded-r-lg py-4 space-y-3">
                  <li className="pl-4 flex items-start gap-2">
                    <span className="w-2 h-2 bg-primary rounded-full mt-2 flex-shrink-0"></span>
                    <span>
                      Your use of the Service is also governed by our{" "}
                      <Link
                        href="/privacy"
                        className="underline text-primary hover:text-primary/80"
                      >
                        Privacy Policy
                      </Link>
                      , which outlines how we collect, use, and protect your personal information.
                    </span>
                  </li>
                </ul>
              </div>

              <div className="bg-background rounded-lg p-6 border shadow-sm mt-6">
                <h2 className="text-2xl md:text-3xl font-semibold mt-0 mb-4 text-primary">
                  9. Governing Law & Dispute Resolution
                </h2>
                <ul className="pl-6 border-l-4 border-primary/30 bg-muted/20 rounded-r-lg py-4 space-y-3">
                  <li className="pl-4 flex items-start gap-2">
                    <span className="w-2 h-2 bg-primary rounded-full mt-2 flex-shrink-0"></span>
                    <span>
                      These Terms are governed by the laws of the State of Texas, without regard to
                      conflict of law principles.
                    </span>
                  </li>
                  <li className="pl-4 flex items-start gap-2">
                    <span className="w-2 h-2 bg-primary rounded-full mt-2 flex-shrink-0"></span>
                    <span>
                      Any disputes will be resolved in the courts located in McLennan County, Texas.
                    </span>
                  </li>
                </ul>
              </div>

              <div className="bg-background rounded-lg p-6 border shadow-sm mt-6">
                <h2 className="text-2xl md:text-3xl font-semibold mt-0 mb-4 text-primary">
                  10. Modifications to Terms
                </h2>
                <ul className="pl-6 border-l-4 border-primary/30 bg-muted/20 rounded-r-lg py-4 space-y-3">
                  <li className="pl-4 flex items-start gap-2">
                    <span className="w-2 h-2 bg-primary rounded-full mt-2 flex-shrink-0"></span>
                    <span>
                      We may update these Terms from time to time. Continued use of the Service
                      after changes are posted constitutes your acceptance of the updated Terms.
                    </span>
                  </li>
                </ul>
              </div>

              <div className="bg-background rounded-lg p-6 border shadow-sm mt-6">
                <h2 className="text-2xl md:text-3xl font-semibold mt-0 mb-4 text-primary">
                  11. Contact Us
                </h2>
                <div className="pl-6 border-l-4 border-primary/30 bg-muted/20 rounded-r-lg py-4">
                  <p className="pl-4 m-0">
                    For questions about these Terms, contact us at:
                    <br />
                    <strong>Valencia Drive Commerce LLC</strong>
                    <br />
                    Email:{" "}
                    <a
                      href="mailto:<EMAIL>"
                      className="text-primary hover:text-primary/80 underline"
                    >
                      <EMAIL>
                    </a>
                  </p>
                </div>
              </div>

              <div className="mt-8 flex flex-col sm:flex-row gap-4">
                <Link
                  href="https://sga.formaloo.me/togeda-ai-contact-us "
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  <Button className="flex items-center gap-2 w-full sm:w-auto">
                    Contact Us <ExternalLink className="h-4 w-4" />
                  </Button>
                </Link>
                <Link href="/privacy">
                  <Button variant="outline" className="flex items-center gap-2 w-full sm:w-auto">
                    View Privacy Policy
                  </Button>
                </Link>
              </div>
            </div>
          </div>
        </div>
      </main>

      {/* Footer */}
      <footer className="border-t py-6">
        <div className="container mx-auto px-4 text-center text-sm text-muted-foreground">
          <p>© {new Date().getFullYear()} Valencia Drive Commerce LLC. All rights reserved.</p>
        </div>
      </footer>
    </div>
  )
}
