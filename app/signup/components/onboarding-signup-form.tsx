"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON>, use<PERSON>earch<PERSON>ara<PERSON> } from "next/navigation"
import Link from "next/link"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  Card<PERSON><PERSON><PERSON>,
  CardFooter,
} from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Logo } from "@/components/ui/logo"
import { ArrowLeft, ArrowRight, X, Loader2 } from "lucide-react"
import { MonthSelector } from "@/components/month-selector"
import { LocationInputWithGeolocation } from "@/components/location-input-with-geolocation"
import { useProviderSignIn } from "@/lib/domains/auth/auth.hooks"
import { toast } from "@/components/ui/use-toast"
import { completeUserProfileWithOnboardingAction } from "../../auth/oauth/actions/complete-user-profile-with-onboarding"
import { ImageUploadWithCompression } from "@/components/ui/image-upload-with-compression"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>back, AvatarImage } from "@/components/ui/avatar"
import {
  ALLOWED_TRAVEL_TYPES,
  ALLOWED_AVAILABILITY_PREFERENCES,
  ALLOWED_TRAVEL_GROUP_PREFERENCES,
} from "@/lib/constants/travel-types"

// Google SVG Icon Component
const GoogleIcon = () => (
  <svg className="w-5 h-5" viewBox="0 0 24 24">
    <path
      fill="#4285F4"
      d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
    />
    <path
      fill="#34A853"
      d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
    />
    <path
      fill="#FBBC05"
      d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
    />
    <path
      fill="#EA4335"
      d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
    />
  </svg>
)

interface OnboardingData {
  name: string
  bio: string
  location: string
  locationPlaceId?: string
  selectedTravelPreferences: string[]
  budget: string
  selectedAvailability: string[]
  selectedMonths: string[]
  selectedTravelGroups: string[]
  referralCode: string
  profilePicture: File | null
}

export function OnboardingSignupForm() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const { signInWithProvider } = useProviderSignIn()
  const [step, setStep] = useState(1)
  const [invitedEmail, setInvitedEmail] = useState<string | null>(null)
  const [isSigningUp, setIsSigningUp] = useState(false)
  const totalSteps = 4

  // Onboarding data state
  const [onboardingData, setOnboardingData] = useState<OnboardingData>({
    name: "",
    bio: "",
    location: "",
    locationPlaceId: undefined,
    selectedTravelPreferences: [],
    budget: "mid-range",
    selectedAvailability: [],
    selectedMonths: [],
    selectedTravelGroups: [],
    referralCode: "",
    profilePicture: null,
  })

  // Extract invited email and referral code from search params
  useEffect(() => {
    const email = searchParams.get("invited_email")
    if (email) {
      setInvitedEmail(email)
    }

    const referral = searchParams.get("referral_code")
    if (referral) {
      setOnboardingData((prev) => ({ ...prev, referralCode: referral }))
    }
  }, [searchParams])

  const handleInputChange = (field: keyof OnboardingData, value: any) => {
    setOnboardingData((prev) => ({ ...prev, [field]: value }))
  }

  const togglePreference = (
    preference: string,
    field: "selectedTravelPreferences" | "selectedAvailability" | "selectedTravelGroups"
  ) => {
    setOnboardingData((prev) => {
      const current = prev[field] as string[]
      if (current.includes(preference)) {
        return { ...prev, [field]: current.filter((p) => p !== preference) }
      } else {
        return { ...prev, [field]: [...current, preference] }
      }
    })
  }

  const handleProfilePictureSelect = (file: File) => {
    setOnboardingData((prev) => ({ ...prev, profilePicture: file }))
  }

  const removeProfilePicture = () => {
    setOnboardingData((prev) => ({ ...prev, profilePicture: null }))
  }

  const nextStep = () => {
    if (step < totalSteps) {
      setStep(step + 1)
    }
  }

  const prevStep = () => {
    if (step > 1) {
      setStep(step - 1)
    }
  }

  const handleProviderSignup = async (providerId: string) => {
    if (isSigningUp) return // Prevent multiple clicks

    setIsSigningUp(true)

    try {
      // Sign in with the OAuth provider directly
      const result = await signInWithProvider(providerId)

      if (result.success && result.data) {
        // Validate email match if this is an invitation signup
        if (invitedEmail && result.data.user?.email) {
          const oauthEmail = result.data.user.email.toLowerCase()
          const expectedEmail = invitedEmail.toLowerCase()

          if (oauthEmail !== expectedEmail) {
            throw new Error(
              `Email mismatch: You were invited as ${invitedEmail}, but signed in with ${result.data.user.email}. Please sign in with the invited email address.`
            )
          }
        }

        // Process onboarding data
        await processOnboardingData(result.data)
      } else {
        toast({
          title: "Sign-in failed",
          description:
            typeof result.error === "string"
              ? result.error
              : "Please try again or contact support if the problem persists.",
          variant: "destructive",
        })
      }
    } catch (error: any) {
      console.error("OAuth sign-in error:", error)
      toast({
        title: "Sign-in failed",
        description:
          error.message || "Please try again or contact support if the problem persists.",
        variant: "destructive",
      })
    } finally {
      setIsSigningUp(false)
    }
  }

  const processOnboardingData = async (authResult: any) => {
    try {
      // Prepare profile picture form data if exists
      let profilePictureFormData: FormData | undefined
      if (onboardingData.profilePicture) {
        profilePictureFormData = new FormData()
        profilePictureFormData.append("file", onboardingData.profilePicture)
      }

      // Complete user profile with onboarding data
      const result = await completeUserProfileWithOnboardingAction(
        {
          userId: authResult.user?.uid || "",
          email: authResult.user?.email || "",
          photoURL: authResult.user?.photoURL || undefined,
          name: onboardingData.name,
          bio: onboardingData.bio,
          location: onboardingData.location || undefined,
          locationPlaceId: onboardingData.locationPlaceId || undefined,
          selectedTravelPreferences: onboardingData.selectedTravelPreferences,
          budget: onboardingData.budget,
          selectedAvailability: onboardingData.selectedAvailability,
          selectedMonths: onboardingData.selectedMonths,
          selectedTravelGroups: onboardingData.selectedTravelGroups,
          referralCode: onboardingData.referralCode.trim() || undefined,
          invitedEmail: invitedEmail || undefined,
        },
        profilePictureFormData
      )

      if (result.success) {
        toast({
          title: "Welcome to Togeda.ai!",
          description: "Your account has been created successfully.",
        })

        // Redirect to callback or dashboard
        const callbackUrl = searchParams.get("callback")
        router.push(callbackUrl ? decodeURIComponent(callbackUrl) : "/dashboard")
      } else {
        throw new Error(result.error || "Failed to create profile")
      }
    } catch (error: any) {
      if (error?.message === "User profile already exists") {
        toast({
          title: "Profile already exists",
          description: "We found your existing account and signed you in.",
          variant: "warning",
        })
        router.push("/dashboard")
      } else {
        console.error("Profile creation error:", error)
        toast({
          title: "Profile creation failed",
          description: "Please try again or contact support if the problem persists.",
          variant: "destructive",
        })
      }
    }
  }

  const getStepDescription = (step: number) => {
    switch (step) {
      case 1:
        return "Basic Information"
      case 2:
        return "Travel Preferences"
      case 3:
        return "Availability & Groups"
      case 4:
        return "Complete Your Profile"
      default:
        return ""
    }
  }

  return (
    <div className="min-h-screen flex flex-col">
      <header className="border-b">
        <div className="container mx-auto px-4 py-4">
          <Link href="/">
            <Logo />
          </Link>
        </div>
      </header>
      <main className="flex-1 flex items-center justify-center p-4">
        <Card className="w-full max-w-md">
          <CardHeader>
            <CardTitle>Join Togeda.ai</CardTitle>
            <CardDescription>
              Step {step} of {totalSteps}: {getStepDescription(step)}
              {invitedEmail && (
                <span className="block mt-2 text-sm text-primary">Invited as: {invitedEmail}</span>
              )}
            </CardDescription>
          </CardHeader>
          <CardContent>
            {step === 1 && (
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="name">Full Name</Label>
                  <Input
                    id="name"
                    placeholder="John Doe"
                    value={onboardingData.name}
                    onChange={(e) => handleInputChange("name", e.target.value)}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="referralCode">Referral Code (Optional)</Label>
                  <Input
                    id="referralCode"
                    placeholder="Enter referral code"
                    value={onboardingData.referralCode}
                    onChange={(e) =>
                      handleInputChange("referralCode", e.target.value.toUpperCase())
                    }
                    maxLength={8}
                  />
                  {onboardingData.referralCode && (
                    <p className="text-xs text-muted-foreground mt-1">
                      Using referral code: {onboardingData.referralCode} (Length:{" "}
                      {onboardingData.referralCode.length})
                    </p>
                  )}
                </div>
              </div>
            )}

            {step === 2 && (
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label>Travel Preferences</Label>
                  <div className="grid grid-cols-2 gap-2">
                    {ALLOWED_TRAVEL_TYPES.map((pref) => (
                      <Button
                        key={pref}
                        variant={
                          onboardingData.selectedTravelPreferences.includes(pref)
                            ? "default"
                            : "outline"
                        }
                        className="justify-start"
                        onClick={() => togglePreference(pref, "selectedTravelPreferences")}
                      >
                        {pref}
                      </Button>
                    ))}
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="budget">Typical Budget</Label>
                  <select
                    id="budget"
                    className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                    value={onboardingData.budget}
                    onChange={(e) => handleInputChange("budget", e.target.value)}
                  >
                    <option value="budget-friendly">Budget-friendly</option>
                    <option value="mid-range">Mid-range</option>
                    <option value="luxury">Luxury</option>
                  </select>
                </div>
              </div>
            )}

            {step === 3 && (
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label>Availability</Label>
                  <div className="grid grid-cols-2 gap-2">
                    {ALLOWED_AVAILABILITY_PREFERENCES.map((avail) => (
                      <Button
                        key={avail}
                        variant={
                          onboardingData.selectedAvailability.includes(avail)
                            ? "default"
                            : "outline"
                        }
                        className="justify-start"
                        onClick={() => togglePreference(avail, "selectedAvailability")}
                      >
                        {avail}
                      </Button>
                    ))}
                  </div>
                </div>
                <div className="space-y-2">
                  <Label>Preferred Travel Seasons</Label>
                  <MonthSelector
                    selectedMonths={onboardingData.selectedMonths}
                    onChange={(months) => handleInputChange("selectedMonths", months)}
                    onConfirm={() => {}}
                  />
                </div>
                <div className="space-y-2">
                  <Label>Travel Group</Label>
                  <div className="grid grid-cols-2 gap-2">
                    {ALLOWED_TRAVEL_GROUP_PREFERENCES.map((group) => (
                      <Button
                        key={group}
                        variant={
                          onboardingData.selectedTravelGroups.includes(group)
                            ? "default"
                            : "outline"
                        }
                        className="justify-start"
                        onClick={() => togglePreference(group, "selectedTravelGroups")}
                      >
                        {group}
                      </Button>
                    ))}
                  </div>
                </div>
              </div>
            )}

            {step === 4 && (
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label>Profile Picture (Optional)</Label>
                  {onboardingData.profilePicture ? (
                    <div className="flex items-center gap-4">
                      <div className="relative">
                        <Avatar className="w-20 h-20">
                          <AvatarImage
                            src={URL.createObjectURL(onboardingData.profilePicture)}
                            alt="Profile preview"
                          />
                          <AvatarFallback>
                            {onboardingData.name
                              .split(" ")
                              .map((n) => n[0])
                              .join("")
                              .toUpperCase()}
                          </AvatarFallback>
                        </Avatar>
                        <Button
                          type="button"
                          variant="destructive"
                          size="sm"
                          className="absolute -top-2 -right-2 h-6 w-6 rounded-full p-0"
                          onClick={removeProfilePicture}
                        >
                          <X className="h-3 w-3" />
                        </Button>
                      </div>
                      <div className="flex-1">
                        <p className="text-sm font-medium">{onboardingData.profilePicture.name}</p>
                        <p className="text-xs text-muted-foreground">
                          {(onboardingData.profilePicture.size / 1024).toFixed(1)} KB
                        </p>
                      </div>
                    </div>
                  ) : (
                    <ImageUploadWithCompression
                      preset="profilePicture"
                      onFileSelect={handleProfilePictureSelect}
                      onError={(error) => console.error("Upload error:", error)}
                      showPreview={false}
                      showCompressionStats={false}
                      className="w-full"
                    />
                  )}
                </div>
                <LocationInputWithGeolocation
                  value={onboardingData.location}
                  onChange={(value, placeId) => {
                    handleInputChange("location", value)
                    handleInputChange("locationPlaceId", placeId)
                  }}
                  placeholder="Where are you based?"
                  required={false}
                  allowUnauthenticated={true}
                  label="Location (Optional)"
                  showGeolocationButton={false}
                />
                <div className="space-y-2">
                  <Label htmlFor="bio">Bio (Optional)</Label>
                  <textarea
                    id="bio"
                    rows={3}
                    placeholder="Tell your friends a bit about yourself..."
                    className="flex w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                    value={onboardingData.bio}
                    onChange={(e) => handleInputChange("bio", e.target.value)}
                  />
                </div>

                {/* OAuth Provider Selection */}
                <div className="space-y-2 pt-4 border-t">
                  <Label>Complete signup with:</Label>
                  <Button
                    variant="outline"
                    className="w-full h-12 text-left justify-start gap-3"
                    onClick={() => handleProviderSignup("google")}
                    disabled={isSigningUp}
                  >
                    {isSigningUp ? <Loader2 className="h-5 w-5 animate-spin" /> : <GoogleIcon />}
                    <span className="flex-1">
                      {isSigningUp ? "Signing up..." : "Continue with Google"}
                    </span>
                  </Button>
                  <p className="text-xs text-muted-foreground">
                    {onboardingData.profilePicture
                      ? "Your uploaded photo will be used as your profile picture."
                      : "We'll use your Google profile photo, or a default avatar if none is available."}
                  </p>
                </div>
              </div>
            )}
          </CardContent>

          {step < 4 && (
            <CardFooter className="flex justify-between">
              <Button variant="outline" onClick={prevStep} disabled={step === 1}>
                <ArrowLeft className="mr-2 h-4 w-4" /> Back
              </Button>
              <Button onClick={nextStep}>
                Next <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </CardFooter>
          )}

          {step === 4 && (
            <CardFooter className="flex justify-start">
              <Button variant="outline" onClick={prevStep}>
                <ArrowLeft className="mr-2 h-4 w-4" /> Back
              </Button>
            </CardFooter>
          )}

          {step < 4 && (
            <div className="text-center text-sm pb-4">
              Already have an account?{" "}
              <a href="/login" className="text-primary hover:underline">
                Sign in
              </a>
            </div>
          )}
        </Card>
      </main>
    </div>
  )
}
