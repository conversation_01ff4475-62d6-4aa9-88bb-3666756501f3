"use client"

import { Suspense } from "react"
import { OnboardingSignupForm } from "./components/onboarding-signup-form"
import { PageLoading } from "@/components/page-loading"
import { useAuthRedirect } from "@/lib/domains/auth/auth.hooks"

function SignupPageContent() {
  const { loading, isRedirecting } = useAuthRedirect()

  // Show loading while checking auth state or redirecting
  if (loading || isRedirecting) {
    return <PageLoading message="Loading..." />
  }

  return <OnboardingSignupForm />
}

export default function SignupPage() {
  return (
    <Suspense fallback={<PageLoading message="Loading..." />}>
      <SignupPageContent />
    </Suspense>
  )
}
