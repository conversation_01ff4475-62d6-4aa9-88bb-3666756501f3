import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { ArrowLeft, ExternalLink } from "lucide-react"
import { Logo } from "@/components/ui/logo"

export const metadata = {
  title: "Privacy Policy | Togeda.ai",
  description: "Privacy Policy for Togeda.ai",
}

export default function PrivacyPage() {
  return (
    <div className="flex flex-col min-h-screen">
      {/* Header */}
      <header className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="container mx-auto px-4 py-4 flex justify-between items-center">
          <div className="flex items-center gap-2">
            <Link href="/" className="flex items-center gap-2">
              <Logo width={120} height={42} />
            </Link>
          </div>
          <div className="flex gap-4 items-center">
            <Link href="/">
              <Button variant="ghost">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Home
              </Button>
            </Link>
          </div>
        </div>
      </header>

      {/* Content */}
      <main className="flex-1 py-8 bg-gradient-to-b from-background to-muted/20">
        <div className="container mx-auto px-4 max-w-4xl">
          <div className="bg-background/80 backdrop-blur-sm rounded-lg border shadow-sm p-8">
            <div className="prose prose-neutral dark:prose-invert max-w-none">
              <div className="text-center mb-8">
                <h1 className="text-4xl md:text-5xl font-bold mb-4 bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
                  Privacy Policy for Togeda.ai
                </h1>
                <div className="inline-block px-4 py-2 bg-primary/10 rounded-full">
                  <p className="text-sm text-muted-foreground m-0">Effective Date: May 14, 2025</p>
                </div>
              </div>

              <div className="bg-muted/30 rounded-lg p-6 mb-8 border-l-4 border-primary">
                <p className="text-base leading-relaxed m-0">
                  Valencia Drive Commerce LLC ("we," "our," or "us"), the parent company of
                  Togeda.ai, is committed to protecting your privacy. This Privacy Policy explains
                  how we collect, use, share, and protect your personal information when you use
                  Togeda.ai.
                </p>
              </div>

              <div className="bg-background rounded-lg p-6 border shadow-sm">
                <h2 className="text-2xl md:text-3xl font-semibold mt-0 mb-4 text-primary">
                  1. Information We Collect
                </h2>
                <p className="mb-4 text-muted-foreground">
                  We collect the following information to provide and improve our services:
                </p>
                <ul className="pl-6 border-l-4 border-primary/30 bg-muted/20 rounded-r-lg py-4 space-y-3">
                  <li className="pl-4 flex items-start gap-2">
                    <span className="w-2 h-2 bg-primary rounded-full mt-2 flex-shrink-0"></span>
                    <span>
                      <strong>Personal Identifiers:</strong> Name, email address, and contact
                      information.
                    </span>
                  </li>
                  <li className="pl-4 flex items-start gap-2">
                    <span className="w-2 h-2 bg-primary rounded-full mt-2 flex-shrink-0"></span>
                    <span>
                      <strong>Payment Information:</strong> Processed securely through Stripe. We do
                      not store your payment details directly.
                    </span>
                  </li>
                  <li className="pl-4 flex items-start gap-2">
                    <span className="w-2 h-2 bg-primary rounded-full mt-2 flex-shrink-0"></span>
                    <span>
                      <strong>Location Data:</strong> To provide relevant trip suggestions and
                      planning options.
                    </span>
                  </li>
                  <li className="pl-4 flex items-start gap-2">
                    <span className="w-2 h-2 bg-primary rounded-full mt-2 flex-shrink-0"></span>
                    <span>
                      <strong>Travel Preferences:</strong> Information you share about your travel
                      interests and group activities.
                    </span>
                  </li>
                  <li className="pl-4 flex items-start gap-2">
                    <span className="w-2 h-2 bg-primary rounded-full mt-2 flex-shrink-0"></span>
                    <span>
                      <strong>Device and Usage Data:</strong> Collected via Google Analytics to
                      analyze traffic and improve user experience.
                    </span>
                  </li>
                </ul>
              </div>

              <div className="bg-background rounded-lg p-6 border shadow-sm mt-6">
                <h2 className="text-2xl md:text-3xl font-semibold mt-0 mb-4 text-primary">
                  2. How We Use Your Information
                </h2>
                <ul className="pl-6 border-l-4 border-primary/30 bg-muted/20 rounded-r-lg py-4 space-y-3">
                  <li className="pl-4 flex items-start gap-2">
                    <span className="w-2 h-2 bg-primary rounded-full mt-2 flex-shrink-0"></span>
                    <span>
                      To personalize your travel planning experience and suggest relevant trips.
                    </span>
                  </li>
                  <li className="pl-4 flex items-start gap-2">
                    <span className="w-2 h-2 bg-primary rounded-full mt-2 flex-shrink-0"></span>
                    <span>To process payments securely via Stripe.</span>
                  </li>
                  <li className="pl-4 flex items-start gap-2">
                    <span className="w-2 h-2 bg-primary rounded-full mt-2 flex-shrink-0"></span>
                    <span>To improve our website and services through analytics insights.</span>
                  </li>
                  <li className="pl-4 flex items-start gap-2">
                    <span className="w-2 h-2 bg-primary rounded-full mt-2 flex-shrink-0"></span>
                    <span>
                      To communicate with you about updates, new features, and account-related
                      matters.
                    </span>
                  </li>
                  <li className="pl-4 flex items-start gap-2">
                    <span className="w-2 h-2 bg-primary rounded-full mt-2 flex-shrink-0"></span>
                    <span>
                      To comply with legal obligations and protect against fraudulent activities.
                    </span>
                  </li>
                </ul>
              </div>

              <div className="bg-background rounded-lg p-6 border shadow-sm mt-6">
                <h2 className="text-2xl md:text-3xl font-semibold mt-0 mb-4 text-primary">
                  3. Data Sharing and Third-Party Services
                </h2>
                <p className="mb-4 text-muted-foreground">
                  We do not sell or share your personal information with third-party marketers.
                  However, we share data with trusted service providers solely to operate our
                  platform:
                </p>
                <ul className="pl-6 border-l-4 border-primary/30 bg-muted/20 rounded-r-lg py-4 space-y-3">
                  <li className="pl-4 flex items-start gap-2">
                    <span className="w-2 h-2 bg-primary rounded-full mt-2 flex-shrink-0"></span>
                    <span>Stripe for payment processing.</span>
                  </li>
                  <li className="pl-4 flex items-start gap-2">
                    <span className="w-2 h-2 bg-primary rounded-full mt-2 flex-shrink-0"></span>
                    <span>Google Analytics for website traffic analysis.</span>
                  </li>
                </ul>
              </div>

              <div className="bg-background rounded-lg p-6 border shadow-sm mt-6">
                <h2 className="text-2xl md:text-3xl font-semibold mt-0 mb-4 text-primary">
                  4. International Users
                </h2>
                <div className="pl-6 border-l-4 border-primary/30 bg-muted/20 rounded-r-lg py-4">
                  <p className="pl-4 m-0">
                    Togeda.ai is accessible worldwide. If you access our services from outside the
                    United States, your data will be processed and stored in the U.S. By using our
                    services, you consent to this transfer.
                  </p>
                </div>
              </div>

              <div className="bg-background rounded-lg p-6 border shadow-sm mt-6">
                <h2 className="text-2xl md:text-3xl font-semibold mt-0 mb-4 text-primary">
                  5. Children's Privacy
                </h2>
                <div className="pl-6 border-l-4 border-primary/30 bg-muted/20 rounded-r-lg py-4">
                  <p className="pl-4 m-0">
                    We do not knowingly collect data from children under the age of 13. If we learn
                    that personal information has been collected from a child under 13, we will take
                    appropriate steps to delete it immediately.
                  </p>
                </div>
              </div>

              <div className="bg-background rounded-lg p-6 border shadow-sm mt-6">
                <h2 className="text-2xl md:text-3xl font-semibold mt-0 mb-4 text-primary">
                  6. Data Retention
                </h2>
                <p className="mb-4 text-muted-foreground">
                  We retain user data for as long as your account is active or as needed to provide
                  you services.
                </p>
                <ul className="pl-6 border-l-4 border-primary/30 bg-muted/20 rounded-r-lg py-4 space-y-3">
                  <li className="pl-4 flex items-start gap-2">
                    <span className="w-2 h-2 bg-primary rounded-full mt-2 flex-shrink-0"></span>
                    <span>
                      <strong>Active Accounts:</strong> Retain data indefinitely for user
                      convenience.
                    </span>
                  </li>
                  <li className="pl-4 flex items-start gap-2">
                    <span className="w-2 h-2 bg-primary rounded-full mt-2 flex-shrink-0"></span>
                    <span>
                      <strong>Inactive Accounts:</strong> Automatically delete after 24 months of
                      inactivity, with prior notice via email.
                    </span>
                  </li>
                  <li className="pl-4 flex items-start gap-2">
                    <span className="w-2 h-2 bg-primary rounded-full mt-2 flex-shrink-0"></span>
                    <span>
                      Users can request data deletion at any time via{" "}
                      <a
                        href="mailto:<EMAIL>"
                        className="text-primary hover:text-primary/80 underline"
                      >
                        <EMAIL>
                      </a>
                      .
                    </span>
                  </li>
                </ul>
              </div>

              <div className="bg-background rounded-lg p-6 border shadow-sm mt-6">
                <h2 className="text-2xl md:text-3xl font-semibold mt-0 mb-4 text-primary">
                  7. Your Rights
                </h2>
                <p className="mb-4 text-muted-foreground">You have the right to:</p>
                <ul className="pl-6 border-l-4 border-primary/30 bg-muted/20 rounded-r-lg py-4 space-y-3">
                  <li className="pl-4 flex items-start gap-2">
                    <span className="w-2 h-2 bg-primary rounded-full mt-2 flex-shrink-0"></span>
                    <span>
                      Access, update, or delete your personal data through your account settings.
                    </span>
                  </li>
                  <li className="pl-4 flex items-start gap-2">
                    <span className="w-2 h-2 bg-primary rounded-full mt-2 flex-shrink-0"></span>
                    <span>
                      Request a complete data export or deletion by contacting us at{" "}
                      <a
                        href="mailto:<EMAIL>"
                        className="text-primary hover:text-primary/80 underline"
                      >
                        <EMAIL>
                      </a>
                      .
                    </span>
                  </li>
                  <li className="pl-4 flex items-start gap-2">
                    <span className="w-2 h-2 bg-primary rounded-full mt-2 flex-shrink-0"></span>
                    <span>Opt out of non-essential communications at any time.</span>
                  </li>
                </ul>
              </div>

              <div className="bg-background rounded-lg p-6 border shadow-sm mt-6">
                <h2 className="text-2xl md:text-3xl font-semibold mt-0 mb-4 text-primary">
                  8. Managing Your Data
                </h2>
                <p className="mb-4 text-muted-foreground">You can:</p>
                <ul className="pl-6 border-l-4 border-primary/30 bg-muted/20 rounded-r-lg py-4 space-y-3">
                  <li className="pl-4 flex items-start gap-2">
                    <span className="w-2 h-2 bg-primary rounded-full mt-2 flex-shrink-0"></span>
                    <span>Edit or delete your profile data in the Settings menu</span>
                  </li>
                  <li className="pl-4 flex items-start gap-2">
                    <span className="w-2 h-2 bg-primary rounded-full mt-2 flex-shrink-0"></span>
                    <span>Contact us to request account deletion or data export</span>
                  </li>
                </ul>
              </div>

              <div className="bg-background rounded-lg p-6 border shadow-sm mt-6">
                <h2 className="text-2xl md:text-3xl font-semibold mt-0 mb-4 text-primary">
                  9. Security
                </h2>
                <div className="pl-6 border-l-4 border-primary/30 bg-muted/20 rounded-r-lg py-4">
                  <p className="pl-4 m-0">
                    We use industry-standard encryption and secure storage through Firebase to
                    protect your information. Despite this, no online service can guarantee absolute
                    security.
                  </p>
                </div>
              </div>

              <div className="bg-background rounded-lg p-6 border shadow-sm mt-6">
                <h2 className="text-2xl md:text-3xl font-semibold mt-0 mb-4 text-primary">
                  10. Changes to This Policy
                </h2>
                <div className="pl-6 border-l-4 border-primary/30 bg-muted/20 rounded-r-lg py-4">
                  <p className="pl-4 m-0">
                    We may update this Privacy Policy from time to time. We'll notify you of any
                    significant changes by posting the updated policy with a new effective date.
                  </p>
                </div>
              </div>

              <div className="bg-background rounded-lg p-6 border shadow-sm mt-6">
                <h2 className="text-2xl md:text-3xl font-semibold mt-0 mb-4 text-primary">
                  11. Contact Us
                </h2>
                <div className="pl-6 border-l-4 border-primary/30 bg-muted/20 rounded-r-lg py-4">
                  <p className="pl-4 m-0">
                    For any questions, concerns, or data requests, contact:
                    <br />
                    <strong>Valencia Drive Commerce LLC</strong>
                    <br />
                    Email:{" "}
                    <a
                      href="mailto:<EMAIL>"
                      className="text-primary hover:text-primary/80 underline"
                    >
                      <EMAIL>
                    </a>
                  </p>
                </div>
              </div>

              <div className="mt-8 flex flex-col sm:flex-row gap-4">
                <Link
                  href="https://sga.formaloo.me/togeda-ai-contact-us "
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  <Button className="flex items-center gap-2 w-full sm:w-auto">
                    Contact Us <ExternalLink className="h-4 w-4" />
                  </Button>
                </Link>
                <Link href="/terms">
                  <Button variant="outline" className="flex items-center gap-2 w-full sm:w-auto">
                    View Terms of Service
                  </Button>
                </Link>
              </div>
            </div>
          </div>
        </div>
      </main>

      {/* Footer */}
      <footer className="border-t py-6">
        <div className="container mx-auto px-4 text-center text-sm text-muted-foreground">
          <p>© {new Date().getFullYear()} Valencia Drive Commerce LLC. All rights reserved.</p>
        </div>
      </footer>
    </div>
  )
}
