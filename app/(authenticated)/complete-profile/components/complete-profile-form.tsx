"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON>, use<PERSON>earch<PERSON>ara<PERSON> } from "next/navigation"
import Link from "next/link"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  Card<PERSON><PERSON><PERSON>,
  CardFooter,
} from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Logo } from "@/components/ui/logo"
import { ArrowLeft, ArrowRight, X } from "lucide-react"
import { MonthSelector } from "@/components/month-selector"
import { LocationInputWithGeolocation } from "@/components/location-input-with-geolocation"
import { ImageUploadWithCompression } from "@/components/ui/image-upload-with-compression"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { toast } from "@/components/ui/use-toast"
import { useUser } from "@/lib/domains/auth/auth.hooks"
import { useAuthStore } from "@/lib/domains/auth/auth.store"
import { completeUserProfileAction } from "../actions/complete-user-profile"
import {
  ALLOWED_TRAVEL_TYPES,
  ALLOWED_AVAILABILITY_PREFERENCES,
  ALLOWED_TRAVEL_GROUP_PREFERENCES,
} from "@/lib/constants/travel-types"

interface ProfileData {
  name: string
  bio: string
  location: string
  locationPlaceId?: string
  selectedTravelPreferences: string[]
  budget: string
  selectedAvailability: string[]
  selectedMonths: string[]
  selectedTravelGroups: string[]
  referralCode: string
  profilePicture: File | null
}

export function CompleteProfileForm() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const user = useUser()
  const { fetchUserData } = useAuthStore()
  const [step, setStep] = useState(1)
  const [loading, setLoading] = useState(false)
  const [callbackUrl, setCallbackUrl] = useState<string | null>(null)
  const [invitedEmail, setInvitedEmail] = useState<string | null>(null)
  const totalSteps = 3

  // Profile data state
  const [profileData, setProfileData] = useState<ProfileData>({
    name: "",
    bio: "",
    location: "",
    locationPlaceId: undefined,
    selectedTravelPreferences: [],
    budget: "mid-range",
    selectedAvailability: [],
    selectedMonths: [],
    selectedTravelGroups: [],
    referralCode: "",
    profilePicture: null,
  })

  // Extract callback URL and invited email from search params
  useEffect(() => {
    const callback = searchParams.get("callback")
    if (callback) {
      setCallbackUrl(callback)
    }

    const email = searchParams.get("invited_email")
    if (email) {
      setInvitedEmail(email)
    }

    const referral = searchParams.get("referral_code")
    if (referral) {
      setProfileData((prev) => ({ ...prev, referralCode: referral }))
    }
  }, [searchParams])

  const handleInputChange = (field: keyof ProfileData, value: any) => {
    setProfileData((prev) => ({ ...prev, [field]: value }))
  }

  const togglePreference = (
    preference: string,
    field: "selectedTravelPreferences" | "selectedAvailability" | "selectedTravelGroups"
  ) => {
    setProfileData((prev) => {
      const current = prev[field] as string[]
      if (current.includes(preference)) {
        return { ...prev, [field]: current.filter((p) => p !== preference) }
      } else {
        return { ...prev, [field]: [...current, preference] }
      }
    })
  }

  const handleProfilePictureSelect = (file: File) => {
    setProfileData((prev) => ({ ...prev, profilePicture: file }))
  }

  const removeProfilePicture = () => {
    setProfileData((prev) => ({ ...prev, profilePicture: null }))
  }

  const nextStep = () => {
    if (step < totalSteps) {
      setStep(step + 1)
    }
  }

  const prevStep = () => {
    if (step > 1) {
      setStep(step - 1)
    }
  }

  const getStepDescription = (step: number) => {
    switch (step) {
      case 1:
        return "Basic Information"
      case 2:
        return "Travel Preferences"
      case 3:
        return "Complete Your Profile"
      default:
        return ""
    }
  }

  const handleSubmit = async () => {
    if (!user) {
      toast({
        title: "Authentication required",
        description: "Please sign in to complete your profile.",
        variant: "destructive",
      })
      return
    }

    setLoading(true)

    try {
      // Prepare profile picture form data
      let profilePictureFormData: FormData | undefined
      if (profileData.profilePicture) {
        profilePictureFormData = new FormData()
        profilePictureFormData.append("file", profileData.profilePicture)
      }

      // Complete user profile using server action
      const result = await completeUserProfileAction(
        {
          userId: user.uid,
          email: user.email || "",
          name: profileData.name,
          bio: profileData.bio,
          location: profileData.location || undefined,
          locationPlaceId: profileData.locationPlaceId || undefined,
          selectedTravelPreferences: profileData.selectedTravelPreferences,
          budget: profileData.budget,
          selectedAvailability: profileData.selectedAvailability,
          selectedMonths: profileData.selectedMonths,
          selectedTravelGroups: profileData.selectedTravelGroups,
          referralCode: profileData.referralCode.trim() || undefined,
          // Include OAuth provider info
          photoURL: user.photoURL || undefined,
          authProvider: "google", // This could be dynamic based on how user signed in
        },
        profilePictureFormData
      )

      if (result.success) {
        // Small delay to ensure database write has completed
        await new Promise((resolve) => setTimeout(resolve, 500))

        // Refresh auth store to update isNewUser status
        await fetchUserData()

        toast({
          title: "Profile completed successfully!",
          description: "Welcome to Togeda.ai! Let's start planning your next adventure.",
        })

        // Small delay to ensure auth store has been updated
        await new Promise((resolve) => setTimeout(resolve, 100))

        // Redirect to callback URL or dashboard
        // The authenticated layout will handle redirecting to /welcome if newUser=true
        if (callbackUrl) {
          router.push(decodeURIComponent(callbackUrl))
        } else {
          router.push("/dashboard")
        }
      } else {
        toast({
          title: "Error completing profile",
          description: result.error || "Something went wrong. Please try again.",
          variant: "destructive",
        })
      }
    } catch (error: any) {
      console.error("Profile completion error:", error)
      toast({
        title: "Error completing profile",
        description: "Something went wrong. Please try again.",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  if (!user) {
    return null // This will be handled by the authenticated layout
  }

  return (
    <div className="min-h-screen flex flex-col">
      <header className="border-b">
        <div className="container mx-auto px-4 py-4">
          <Link href="/">
            <Logo />
          </Link>
        </div>
      </header>
      <main className="flex-1 flex items-center justify-center p-4">
        <Card className="w-full max-w-md">
          <CardHeader>
            <CardTitle>Complete Your Profile</CardTitle>
            <CardDescription>
              Step {step} of {totalSteps}: {getStepDescription(step)}
              {invitedEmail && (
                <span className="block mt-2 text-sm text-primary">Invited as: {invitedEmail}</span>
              )}
            </CardDescription>
          </CardHeader>
          <CardContent>
            {step === 1 && (
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="name">Full Name</Label>
                  <Input
                    id="name"
                    placeholder="John Doe"
                    value={profileData.name}
                    onChange={(e) => handleInputChange("name", e.target.value)}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="referralCode">Referral Code (Optional)</Label>
                  <Input
                    id="referralCode"
                    placeholder="Enter referral code"
                    value={profileData.referralCode}
                    onChange={(e) =>
                      handleInputChange("referralCode", e.target.value.toUpperCase())
                    }
                  />
                </div>
              </div>
            )}

            {step === 2 && (
              <div className="space-y-6">
                <div className="space-y-3">
                  <Label>Travel Preferences</Label>
                  <div className="grid grid-cols-2 gap-3">
                    {ALLOWED_TRAVEL_TYPES.map((type) => (
                      <Button
                        key={type}
                        variant={
                          profileData.selectedTravelPreferences.includes(type)
                            ? "default"
                            : "outline"
                        }
                        className="justify-start"
                        onClick={() => togglePreference(type, "selectedTravelPreferences")}
                      >
                        {type}
                      </Button>
                    ))}
                  </div>
                </div>

                <div className="space-y-2">
                  <Label>Budget Range</Label>
                  <div className="grid grid-cols-1 gap-2">
                    {["budget", "mid-range", "luxury"].map((budgetOption) => (
                      <Button
                        key={budgetOption}
                        variant={profileData.budget === budgetOption ? "default" : "outline"}
                        className="justify-start h-auto p-3"
                        onClick={() => handleInputChange("budget", budgetOption)}
                      >
                        <div className="text-left">
                          <div className="font-medium capitalize">{budgetOption}</div>
                          <div className="text-xs text-muted-foreground">
                            {budgetOption === "budget" && "Cost-conscious travel"}
                            {budgetOption === "mid-range" && "Balanced comfort and value"}
                            {budgetOption === "luxury" && "Premium experiences"}
                          </div>
                        </div>
                      </Button>
                    ))}
                  </div>
                </div>
              </div>
            )}

            {step === 3 && (
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label>Availability</Label>
                  <div className="grid grid-cols-2 gap-2">
                    {ALLOWED_AVAILABILITY_PREFERENCES.map((avail) => (
                      <Button
                        key={avail}
                        variant={
                          profileData.selectedAvailability.includes(avail) ? "default" : "outline"
                        }
                        className="justify-start"
                        onClick={() => togglePreference(avail, "selectedAvailability")}
                      >
                        {avail}
                      </Button>
                    ))}
                  </div>
                </div>
                <div className="space-y-2">
                  <Label>Preferred Travel Seasons</Label>
                  <MonthSelector
                    selectedMonths={profileData.selectedMonths}
                    onChange={(months) => handleInputChange("selectedMonths", months)}
                    onConfirm={() => {}}
                  />
                </div>
                <div className="space-y-2">
                  <Label>Travel Group</Label>
                  <div className="grid grid-cols-2 gap-2">
                    {ALLOWED_TRAVEL_GROUP_PREFERENCES.map((group) => (
                      <Button
                        key={group}
                        variant={
                          profileData.selectedTravelGroups.includes(group) ? "default" : "outline"
                        }
                        className="justify-start"
                        onClick={() => togglePreference(group, "selectedTravelGroups")}
                      >
                        {group}
                      </Button>
                    ))}
                  </div>
                </div>
                <div className="space-y-2">
                  <Label>Profile Picture (Optional)</Label>
                  {profileData.profilePicture ? (
                    <div className="flex items-center gap-4">
                      <div className="relative">
                        <Avatar className="w-20 h-20">
                          <AvatarImage
                            src={URL.createObjectURL(profileData.profilePicture)}
                            alt="Profile preview"
                          />
                          <AvatarFallback>
                            {profileData.name
                              .split(" ")
                              .map((n) => n[0])
                              .join("")
                              .toUpperCase()}
                          </AvatarFallback>
                        </Avatar>
                        <Button
                          type="button"
                          variant="destructive"
                          size="sm"
                          className="absolute -top-2 -right-2 h-6 w-6 rounded-full p-0"
                          onClick={removeProfilePicture}
                        >
                          <X className="h-3 w-3" />
                        </Button>
                      </div>
                      <div className="flex-1">
                        <p className="text-sm font-medium">{profileData.profilePicture.name}</p>
                        <p className="text-xs text-muted-foreground">
                          {(profileData.profilePicture.size / 1024).toFixed(1)} KB
                        </p>
                      </div>
                    </div>
                  ) : (
                    <ImageUploadWithCompression
                      preset="profilePicture"
                      onFileSelect={handleProfilePictureSelect}
                      onError={(error) => console.error("Upload error:", error)}
                      showPreview={false}
                      showCompressionStats={false}
                      className="w-full"
                    />
                  )}
                </div>
                <LocationInputWithGeolocation
                  value={profileData.location}
                  onChange={(value, placeId) => {
                    handleInputChange("location", value)
                    handleInputChange("locationPlaceId", placeId)
                  }}
                  placeholder="Where are you based?"
                  required={false}
                  allowUnauthenticated={true}
                  label="Location (Optional)"
                  showGeolocationButton={false}
                />
                <div className="space-y-2">
                  <Label htmlFor="bio">Bio (Optional)</Label>
                  <textarea
                    id="bio"
                    rows={3}
                    placeholder="Tell your friends a bit about yourself..."
                    className="flex w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                    value={profileData.bio}
                    onChange={(e) => handleInputChange("bio", e.target.value)}
                  />
                </div>
              </div>
            )}
          </CardContent>

          {step < totalSteps && (
            <CardFooter className="flex justify-between">
              <Button variant="outline" onClick={prevStep} disabled={step === 1}>
                <ArrowLeft className="mr-2 h-4 w-4" /> Back
              </Button>
              <Button onClick={nextStep} disabled={step === 1 && !profileData.name.trim()}>
                Next <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </CardFooter>
          )}

          {step === totalSteps && (
            <CardFooter className="flex justify-between">
              <Button variant="outline" onClick={prevStep}>
                <ArrowLeft className="mr-2 h-4 w-4" /> Back
              </Button>
              <Button onClick={handleSubmit} disabled={loading || !profileData.name.trim()}>
                {loading ? "Creating Profile..." : "Complete Profile"}
              </Button>
            </CardFooter>
          )}
        </Card>
      </main>
    </div>
  )
}
