"use client"

import { Suspense } from "react"
import { CompleteProfileForm } from "./components/complete-profile-form"
import { PageLoading } from "@/components/page-loading"

function CompleteProfilePageContent() {
  return <CompleteProfileForm />
}

export default function CompleteProfilePage() {
  return (
    <Suspense fallback={<PageLoading message="Loading..." />}>
      <CompleteProfilePageContent />
    </Suspense>
  )
}
