"use client"

/**
 * Generic invitation page for invitation-links (no specific invitee)
 * This page only shows "Accept" and "Go to Dashboard" options - no decline button
 * URL format: /squad-invitation/{invitationId}
 */

import Link from "next/link"
import { usePara<PERSON>, useRouter } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { Check, X, Users, AlertTriangle } from "lucide-react"
import { useAuthStatus } from "@/lib/domains/auth/auth.hooks"
import { PageLoading } from "@/components/page-loading"
import { useSquadInvitation } from "@/lib/domains/invitation/squad-invitation.hooks"

export default function GenericInvitationPage() {
  const params = useParams()
  const router = useRouter()
  const { loading: authLoading } = useAuthStatus()

  // Get invitation ID from URL (this route is for generic invitations only)
  const invitationId = params.id as string
  const invitationSendId = undefined // Generic invitations don't have sendId

  // Use the squad invitation hook
  const { data, state, actions } = useSquadInvitation(invitationId, invitationSendId)

  // Handle join squad action
  const handleJoinSquad = async () => {
    const success = await actions.joinSquad()
    if (success) {
      router.push(`/squads/${data.invitationLink?.squadId}`)
    }
  }

  // Show loading state
  if (authLoading || state.loading) {
    return <PageLoading message="Loading invitation..." />
  }

  // Show error state
  if (state.error) {
    return (
      <div className="min-h-screen flex items-center justify-center p-4">
        <Card className="w-full max-w-md">
          <CardHeader>
            <CardTitle>Invitation Error</CardTitle>
            <CardDescription>There was a problem with this invitation</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-3 mb-4">
              <div className="bg-red-100 p-2 rounded-full">
                <X className="h-5 w-5 text-red-600" />
              </div>
              <div>
                <p className="text-red-700">{state.error}</p>
              </div>
            </div>
          </CardContent>
          <CardFooter>
            <Link href="/dashboard" className="w-full">
              <Button variant="outline" className="w-full">
                Go to Dashboard
              </Button>
            </Link>
          </CardFooter>
        </Card>
      </div>
    )
  }

  // Extract data from hook
  const { invitationLink, specificInvitee, isAlreadyMember, invitationStatus } = data

  // Check if user is already a member of the squad
  if (isAlreadyMember) {
    return (
      <div className="min-h-screen flex items-center justify-center p-4">
        <Card className="w-full max-w-md">
          <CardHeader>
            <CardTitle>Already a Member</CardTitle>
            <CardDescription>You're already part of {invitationLink?.squadName}</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-3 mb-4">
              <div className="bg-green-100 p-2 rounded-full">
                <Check className="h-5 w-5 text-green-600" />
              </div>
              <div>
                <p>You're already a member of this squad!</p>
              </div>
            </div>
            <p className="text-muted-foreground mb-4">
              You can access the squad from your dashboard.
            </p>
          </CardContent>
          <CardFooter>
            <Link href={`/squads/${invitationLink?.squadId}`} className="w-full">
              <Button className="w-full">Go to Squad</Button>
            </Link>
          </CardFooter>
        </Card>
      </div>
    )
  }

  // Check invitation status for specific invitees
  if (invitationStatus === "accepted") {
    return (
      <div className="min-h-screen flex items-center justify-center p-4">
        <Card className="w-full max-w-md">
          <CardHeader>
            <CardTitle>Invitation Already Accepted</CardTitle>
            <CardDescription>You've already accepted this invitation</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-3 mb-4">
              <div className="bg-green-100 p-2 rounded-full">
                <Check className="h-5 w-5 text-green-600" />
              </div>
              <div>
                <p>You previously accepted this invitation to join {invitationLink?.squadName}.</p>
              </div>
            </div>
            <p className="text-muted-foreground mb-4">
              If you're not seeing the squad in your dashboard, there might have been an issue with
              the join process. You can try using the general invitation link to join the squad.
            </p>
          </CardContent>
          <CardFooter className="flex flex-col space-y-2">
            <Link href={`/squads/${invitationLink?.squadId}`} className="w-full">
              <Button className="w-full">Go to Squad</Button>
            </Link>
            <Link href="/dashboard" className="w-full">
              <Button variant="outline" className="w-full">
                Go to Dashboard
              </Button>
            </Link>
          </CardFooter>
        </Card>
      </div>
    )
  }

  if (invitationStatus === "rejected") {
    return (
      <div className="min-h-screen flex items-center justify-center p-4">
        <Card className="w-full max-w-md">
          <CardHeader>
            <CardTitle>Invitation Previously Declined</CardTitle>
            <CardDescription>You've already declined this invitation</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-3 mb-4">
              <div className="bg-red-100 p-2 rounded-full">
                <X className="h-5 w-5 text-red-600" />
              </div>
              <div>
                <p>You previously declined the invitation to join {invitationLink?.squadName}.</p>
              </div>
            </div>
            <p className="text-muted-foreground mb-4">
              Declined invitations cannot be reprocessed. If you've changed your mind, please
              contact <span className="font-medium">{invitationLink?.inviterName}</span> to send you
              a new invitation.
            </p>
          </CardContent>
          <CardFooter>
            <Link href="/dashboard" className="w-full">
              <Button variant="outline" className="w-full">
                Go to Dashboard
              </Button>
            </Link>
          </CardFooter>
        </Card>
      </div>
    )
  }

  // Show invitation details for pending invitations
  return (
    <div className="min-h-screen flex items-center justify-center p-4">
      <Card className="w-full max-w-md">
        <CardHeader>
          <CardTitle>Squad Invitation</CardTitle>
          <CardDescription>You've been invited to join {invitationLink?.squadName}</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="text-center">
              <div className="bg-blue-100 p-3 rounded-full w-16 h-16 mx-auto mb-4 flex items-center justify-center">
                <Users className="h-8 w-8 text-blue-600" />
              </div>
              <h3 className="font-semibold text-lg mb-2">{invitationLink?.squadName}</h3>
              <p className="text-muted-foreground">
                Invited by <span className="font-medium">{invitationLink?.inviterName}</span>
              </p>
            </div>

            {/* Show specific email for invitation links with specific invitees */}
            {specificInvitee && (
              <div className="bg-blue-50 dark:bg-blue-950 p-3 rounded-lg">
                <p className="text-sm text-blue-700 dark:text-blue-300">
                  This invitation is specifically for:{" "}
                  <span className="font-medium">{specificInvitee.email}</span>
                </p>
              </div>
            )}

            {/* Show invitation status for pending invitations */}
            {invitationStatus === "sent" && (
              <div className="bg-yellow-50 dark:bg-yellow-950 p-3 rounded-lg">
                <div className="flex items-center gap-2">
                  <AlertTriangle className="h-4 w-4 text-yellow-600 dark:text-yellow-400" />
                  <p className="text-sm text-yellow-700 dark:text-yellow-300">
                    <span className="font-medium">Pending Invitation</span> - This invitation is
                    waiting for your response.
                  </p>
                </div>
              </div>
            )}

            {/* Show expiration date */}
            {invitationLink?.expiresAt && (
              <div className="text-center">
                <p className="text-sm text-muted-foreground">
                  Expires on {formatExpirationDate(invitationLink.expiresAt)}
                </p>
              </div>
            )}
          </div>
        </CardContent>
        <CardFooter className="flex flex-col space-y-2">
          <Button onClick={handleJoinSquad} disabled={state.processing} className="w-full">
            {state.processing ? "Joining..." : "Accept Invitation"}
          </Button>
          {/* Generic invitations only show "Go to Dashboard" option, no decline */}
          <Link href="/dashboard" className="w-full">
            <Button variant="outline" className="w-full">
              Go to Dashboard
            </Button>
          </Link>
        </CardFooter>
      </Card>
    </div>
  )

  // Format expiration date helper function
  function formatExpirationDate(expiresAt: any) {
    if (!expiresAt) return ""

    try {
      let date: Date

      if (typeof expiresAt.toDate === "function") {
        // Firestore timestamp object
        date = expiresAt.toDate()
      } else if (expiresAt.seconds) {
        // Firestore timestamp as plain object with seconds
        date = new Date(expiresAt.seconds * 1000)
      } else if (expiresAt._seconds) {
        // Alternative Firestore timestamp format
        date = new Date(expiresAt._seconds * 1000)
      } else {
        // Try to parse as regular date
        date = new Date(expiresAt)
      }

      // Validate the date
      if (isNaN(date.getTime())) {
        console.warn("Invalid date format:", expiresAt)
        return "Invalid date"
      }

      return date.toLocaleDateString()
    } catch (error) {
      console.error("Error formatting expiration date:", error, expiresAt)
      return "Invalid date"
    }
  }
}
