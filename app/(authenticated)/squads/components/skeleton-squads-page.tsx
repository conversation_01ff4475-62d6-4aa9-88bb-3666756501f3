import { Skeleton } from "@/components/ui/skeleton"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>er, CardHeader } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Popover, PopoverTrigger } from "@/components/ui/popover"
import { HelpCircle } from "lucide-react"

interface SkeletonSquadsPageProps {
  className?: string
  cardCount?: number
}

export function SkeletonSquadsPage({ className = "", cardCount = 6 }: SkeletonSquadsPageProps) {
  return (
    <div className={`p-6 ${className}`}>
      {/* Header section */}
      <div className="flex justify-between items-center mb-6">
        <div className="flex items-center gap-2">
          <h1 className="text-3xl font-bold">My Squads</h1>
          <Popover>
            <PopoverTrigger asChild>
              <Button
                variant="ghost"
                size="icon"
                className="h-6 w-6"
                aria-label="Squad information"
                disabled
              >
                <HelpCircle className="h-4 w-4" />
              </Button>
            </PopoverTrigger>
          </Popover>
        </div>
        <Skeleton className="h-10 w-28" /> {/* "Create Squad" button */}
      </div>

      {/* Grid of squad cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {Array.from({ length: cardCount }).map((_, index) => (
          <Card key={index} className="hover:shadow-md transition-shadow">
            <CardHeader>
              <Skeleton className="h-6 w-3/4" /> {/* Squad name */}
              <Skeleton className="h-4 w-20" /> {/* Member count */}
            </CardHeader>
            <CardContent>
              {/* Overlapping avatars */}
              <div className="flex -space-x-2 overflow-hidden mb-4">
                {Array.from({ length: Math.floor(Math.random() * 4) + 2 }).map((_, i) => (
                  <Skeleton key={i} className="h-10 w-10 rounded-full border-2 border-background" />
                ))}
              </div>
              {/* Description */}
              <div className="space-y-2">
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-5/6" />
                <Skeleton className="h-4 w-3/4" />
              </div>
            </CardContent>
            <CardFooter>
              <Skeleton className="h-10 w-full" /> {/* "View Squad" button */}
            </CardFooter>
          </Card>
        ))}
      </div>
    </div>
  )
}
