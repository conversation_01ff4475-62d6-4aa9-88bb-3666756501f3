"use client"

import { useState, useEffect, Suspense, lazy } from "react"
import { use<PERSON><PERSON><PERSON>, use<PERSON><PERSON>er, useSearchParams } from "next/navigation"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { useAuthStore } from "@/lib/domains/auth/auth.store"
import { useAuthStatus } from "@/lib/domains/auth/auth.hooks"
import { useRealtimeSquad } from "@/lib/domains/squad/squad.realtime.hooks"
import { useIsSquadLeader, useIsSquadMember } from "@/lib/domains/squad/squad.hooks"

// Import shared components
import { SquadLoading } from "./components/loading"
import { SquadNotFound } from "./components/not-found"
import { SquadHeader } from "./components/shared/squad-header"
import { InviteDialog } from "./components/invitations/invite-dialog"

// Lazy load tab components to reduce initial bundle size
const OverviewTab = lazy(() =>
  import("./components/overview/overview-tab").then((module) => ({ default: module.OverviewTab }))
)
const TripsTab = lazy(() =>
  import("./components/trips/trips-tab").then((module) => ({ default: module.TripsTab }))
)
const MembersTab = lazy(() =>
  import("./components/members/members-tab").then((module) => ({ default: module.MembersTab }))
)
const SettingsTab = lazy(() =>
  import("./components/settings/settings-tab").then((module) => ({ default: module.SettingsTab }))
)
const InvitationsTab = lazy(() =>
  import("./components/invitations/invitations-tab").then((module) => ({
    default: module.InvitationsTab,
  }))
)

// Tab loading skeleton component
const SquadTabSkeleton = () => (
  <div className="space-y-6">
    {/* Header Section Skeleton */}
    <div className="space-y-4">
      <div className="h-8 w-48 bg-muted animate-pulse rounded" />
      <div className="h-4 w-full bg-muted animate-pulse rounded" />
    </div>

    {/* Stats/Cards Section Skeleton */}
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      {[...Array(3)].map((_, i) => (
        <div key={i} className="p-4 border rounded-lg space-y-3">
          <div className="h-6 w-24 bg-muted animate-pulse rounded" />
          <div className="h-8 w-16 bg-muted animate-pulse rounded" />
          <div className="h-3 w-full bg-muted animate-pulse rounded" />
        </div>
      ))}
    </div>

    {/* Content List Skeleton */}
    <div className="space-y-3">
      {[...Array(4)].map((_, i) => (
        <div key={i} className="p-4 border rounded-lg space-y-2">
          <div className="flex justify-between items-center">
            <div className="h-5 w-1/3 bg-muted animate-pulse rounded" />
            <div className="h-4 w-20 bg-muted animate-pulse rounded" />
          </div>
          <div className="h-4 w-2/3 bg-muted animate-pulse rounded" />
        </div>
      ))}
    </div>
  </div>
)

function SquadPageContent() {
  const params = useParams()
  const router = useRouter()
  const searchParams = useSearchParams()

  const user = useAuthStore((state) => state.user)
  const authLoading = useAuthStore((state) => state.loading)
  const squadId = params.id as string

  // Track the active tab for any future tab-specific logic and analytics
  const [activeTab, setActiveTab] = useState("overview")
  const [inviteDialogOpen, setInviteDialogOpen] = useState(false)

  // Handle tab parameter from URL
  useEffect(() => {
    const tab = searchParams.get("tab")
    if (tab) {
      setActiveTab(tab)
    }
  }, [searchParams])

  // Get squad data with real-time updates
  const { squad, loading: squadLoading, error: squadError } = useRealtimeSquad(squadId)

  // Check if user is squad leader or member
  const { isLeader, loading: leaderCheckLoading } = useIsSquadLeader(squadId)
  const { isMember, loading: memberCheckLoading } = useIsSquadMember(squadId)

  if (authLoading || squadLoading || leaderCheckLoading || memberCheckLoading) {
    return <SquadLoading />
  }

  if (!user) {
    router.push("/login")
    return null
  }

  if (!squad || (!isMember && !isLeader)) {
    return <SquadNotFound />
  }

  return (
    <div className="p-6 max-w-[100vw] overflow-x-hidden">
      <SquadHeader
        squad={squad}
        onInviteClick={() => setInviteDialogOpen(true)}
        isSquadLead={isLeader}
        onRedirectToInvitations={() => {
          setActiveTab("invitations")
          router.push(`/squads/${squad.id}?tab=invitations`)
        }}
      />

      <Tabs value={activeTab} className="space-y-4" onValueChange={setActiveTab}>
        <div className="overflow-x-auto pb-2 max-w-[100vw] no-scrollbar">
          <TabsList className="w-full flex flex-nowrap overflow-x-auto">
            <TabsTrigger value="overview" className="flex-shrink-0">
              Overview
            </TabsTrigger>
            <TabsTrigger value="trips" className="flex-shrink-0">
              Trips
            </TabsTrigger>
            <TabsTrigger value="members" className="flex-shrink-0">
              Members
            </TabsTrigger>
            {isLeader && (
              <TabsTrigger value="invitations" className="flex-shrink-0">
                Invitations
              </TabsTrigger>
            )}
            <TabsTrigger value="settings" className="flex-shrink-0">
              Settings
            </TabsTrigger>
          </TabsList>
        </div>

        <TabsContent value="overview">
          <Suspense fallback={<SquadTabSkeleton />}>
            <OverviewTab squad={squad} />
          </Suspense>
        </TabsContent>

        <TabsContent value="trips">
          <Suspense fallback={<SquadTabSkeleton />}>
            <TripsTab squad={squad} />
          </Suspense>
        </TabsContent>

        <TabsContent value="members">
          <Suspense fallback={<SquadTabSkeleton />}>
            <MembersTab squad={squad} onInviteClick={() => setInviteDialogOpen(true)} />
          </Suspense>
        </TabsContent>

        <TabsContent value="invitations">
          <Suspense fallback={<SquadTabSkeleton />}>
            <InvitationsTab squad={squad} onInviteClick={() => setInviteDialogOpen(true)} />
          </Suspense>
        </TabsContent>

        <TabsContent value="settings">
          <Suspense fallback={<SquadTabSkeleton />}>
            <SettingsTab squad={squad} />
          </Suspense>
        </TabsContent>
      </Tabs>

      {/* Invite Dialog */}
      {squad && (
        <InviteDialog
          open={inviteDialogOpen}
          onOpenChange={setInviteDialogOpen}
          squadId={squad.id}
          squadName={squad.name}
          onInviteSent={() => {
            // Refresh any data if needed
          }}
          onRedirectToInvitations={() => {
            setActiveTab("invitations")
            router.push(`/squads/${squad.id}?tab=invitations`)
          }}
        />
      )}
    </div>
  )
}

export default function SquadPage() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <SquadPageContent />
    </Suspense>
  )
}
