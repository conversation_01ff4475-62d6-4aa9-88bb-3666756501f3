"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Separator } from "@/components/ui/separator"
import { UserPlus, Link2, <PERSON><PERSON>, Check, Clock, Users, Mail } from "lucide-react"
import { useRealtimeSquadInvitationSends } from "@/lib/domains/invitation/invitation.realtime.hooks"
import { useUser } from "@/lib/domains/auth/auth.hooks"
import { Squad } from "@/lib/domains/squad/squad.types"
// Legacy InvitationList removed - all invitations now use invitation-sends
import { InvitationSendsList } from "./invitation-sends-list"
import { SectionLoading } from "@/components/section-loading"
import { toast } from "@/components/ui/use-toast"
import { ErrorBoundary } from "@/components/error-boundary"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import {
  useInvitationLink,
  useGenerateInvitationLink,
} from "@/lib/domains/invitation/invitation.hooks"
import { generateInvitationLink } from "@/lib/email-service"

interface InvitationsTabProps {
  squad: Squad
  onInviteClick?: () => void
}

export function InvitationsTab({ squad, onInviteClick }: InvitationsTabProps) {
  const squadId = squad.id
  const user = useUser()
  const [copiedLink, setCopiedLink] = useState(false)
  const [isSquadLead, setIsSquadLead] = useState(false)

  // Check if current user is squad leader
  useEffect(() => {
    if (user) {
      setIsSquadLead(user.uid === squad.leaderId)
    } else {
      setIsSquadLead(false)
    }
  }, [user, squad.leaderId])

  // Only initialize invitation hooks if user is squad leader
  const {
    invitationSends,
    loading: invitationSendsLoading,
    error: invitationSendsError,
  } = useRealtimeSquadInvitationSends(isSquadLead ? squadId : "")

  // Get invitation link only for squad leaders
  const {
    invitationLink,
    loading: linkLoading,
    refetch,
  } = useInvitationLink(isSquadLead ? squadId : "")
  const { generate: generateLink, generating } = useGenerateInvitationLink(() => {
    // Refetch invitation link when a new one is created
    refetch()
  })

  // Show error toast if there's an error
  useEffect(() => {
    if (invitationSendsError) {
      toast({
        title: "Error",
        description: "Unable to load invitations. Please try again later.",
        variant: "destructive",
      })
    }
  }, [invitationSendsError])

  // Generate or get invitation link
  const handleGenerateLink = async () => {
    if (invitationLink) return invitationLink

    const newLink = await generateLink(squad.id, squad.name)
    if (newLink) {
      toast({
        title: "Invitation link generated",
        description: "Your shareable invitation link is ready!",
      })
    }
    return newLink
  }

  // Copy invitation link to clipboard
  const handleCopyLink = async () => {
    const link = invitationLink || (await handleGenerateLink())
    if (!link) return

    const fullLink = generateInvitationLink(link.id)

    try {
      await navigator.clipboard.writeText(fullLink)
      setCopiedLink(true)
      toast({
        title: "Link copied",
        description: "Invitation link copied to clipboard",
      })

      setTimeout(() => setCopiedLink(false), 2000)
    } catch (error) {
      toast({
        title: "Failed to copy",
        description: "Please copy the link manually",
        variant: "destructive",
      })
    }
  }

  // Format expiration date
  const formatExpirationDate = (expiresAt: any) => {
    if (!expiresAt) return ""
    const date = expiresAt.toDate ? expiresAt.toDate() : new Date(expiresAt)
    return date.toLocaleDateString()
  }

  if (invitationSendsLoading || linkLoading) {
    return <SectionLoading message="Loading invitations..." />
  }

  if (!isSquadLead) {
    return (
      <Card>
        <CardContent className="p-6 text-center">
          <h3 className="font-medium mb-2">Access Restricted</h3>
          <p className="text-muted-foreground">
            Only the squad leader can view and manage invitations.
          </p>
        </CardContent>
      </Card>
    )
  }

  return (
    <ErrorBoundary>
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <h2 className="text-xl font-semibold">Squad Invitations</h2>
          {onInviteClick && (
            <Button onClick={onInviteClick}>
              <UserPlus className="mr-2 h-4 w-4" /> Invite People
            </Button>
          )}
        </div>

        {/* Invitation Link Section */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Link2 className="h-5 w-5" />
              Shareable Invitation Link
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <Clock className="h-4 w-4" />
              <span>
                {invitationLink
                  ? `Expires on ${formatExpirationDate(invitationLink.expiresAt)}`
                  : "Links expire after 10 days"}
              </span>
            </div>

            {invitationLink ? (
              <div className="space-y-3">
                <div className="flex items-center gap-2 p-3 bg-muted rounded-md">
                  <Input
                    value={generateInvitationLink(invitationLink.id)}
                    readOnly
                    className="flex-1 bg-background"
                  />
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleCopyLink}
                    disabled={linkLoading}
                  >
                    {copiedLink ? <Check className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
                  </Button>
                </div>

                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <Users className="h-4 w-4" />
                  <span>Anyone with this link can join your squad</span>
                </div>
              </div>
            ) : (
              <div className="text-center py-6">
                <Button
                  onClick={handleGenerateLink}
                  disabled={generating || linkLoading}
                  className="flex items-center gap-2"
                >
                  <Link2 className="h-4 w-4" />
                  {generating ? "Generating..." : "Generate Invitation Link"}
                </Button>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Recent Email Invitations */}
        <Separator />
        <div>
          <h3 className="text-lg font-medium mb-4 flex items-center gap-2">
            <Mail className="h-5 w-5" />
            Recent Email Invitations ({invitationSends.length})
          </h3>

          {invitationSendsLoading ? (
            <SectionLoading />
          ) : invitationSendsError ? (
            <Card>
              <CardContent className="p-6 text-center">
                <p className="text-red-600">
                  Error loading invitation sends: {invitationSendsError.message}
                </p>
              </CardContent>
            </Card>
          ) : (
            <InvitationSendsList invitationSends={invitationSends} squadId={squad.id} />
          )}
        </div>

        {/* Legacy invitations removed - all invitations now use invitation-sends system */}
      </div>
    </ErrorBoundary>
  )
}
