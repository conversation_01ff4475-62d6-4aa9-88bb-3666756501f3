"use client"

import { useState, useEffect, useCallback } from "react"
import { use<PERSON>out<PERSON> } from "next/navigation"
import { useParams } from "next/navigation"
import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  CardFooter,
} from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { toast } from "@/components/ui/use-toast"
import { useAuthStatus } from "@/lib/domains/auth/auth.hooks"
import {
  useIsUserSubscribed,
  useCanCreateMoreTripsInSquad,
  useUserSubscriptionWithInit,
} from "@/lib/domains/user-subscription/user-subscription.hooks"
import { SubscriptionErrorType } from "@/lib/domains/user-subscription/user-subscription.types"
import { Loader2, ArrowLeft, ImageIcon } from "lucide-react"
import { DateRangePicker } from "@/components/ui/date-range-picker"
import { getLocationImageByPlaceId, fetchGooglePlaceImageByPlaceId } from "@/lib/google-places"
import { OptimizedImage } from "@/components/optimized-image"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { PageLoading } from "@/components/page-loading"

// Import domain-specific hooks and types
import { TripCreateData, TripFormData } from "@/lib/domains/trip/trip.types"
import { useCreateTrip } from "@/lib/domains/trip/trip.hooks"
import { useRealtimeSquad } from "@/lib/domains/squad/squad.realtime.hooks"

// Import the destination autocomplete component
import { DestinationAutocomplete } from "@/app/(authenticated)/trips/create/components/destination-autocomplete"

export default function CreateTrip() {
  const params = useParams()
  const squadId = params.id as string
  const { user, loading: authLoading } = useAuthStatus()
  const router = useRouter()
  const isSubscribed = useIsUserSubscribed()
  const canCreateMoreTripsInSquadFunc = useCanCreateMoreTripsInSquad()
  const { handleSubscriptionError } = useUserSubscriptionWithInit()

  // Use the squad hook to get squad data
  const { squad, loading: loadingSquad, error: squadError } = useRealtimeSquad(squadId)

  // Use the trip creation hook
  const { create, creating, error: createError } = useCreateTrip()

  // Form state
  const [formData, setFormData] = useState<TripFormData>({
    name: "",
    destination: "",
    placeId: undefined,
    squadId: squadId, // Pre-populate with the current squad ID
    startDate: null,
    endDate: null,
    budget: 1000,
    description: "",
  })

  // UI state
  const [canCreateTrip, setCanCreateTrip] = useState(true)
  const [checkingLimits, setCheckingLimits] = useState(false)
  const [locationImage, setLocationImage] = useState<string | null>(null)
  const [googlePlaceImage, setGooglePlaceImage] = useState<{
    photoReference: string
    placeId: string
  } | null>(null)
  const [imageAttribution, setImageAttribution] = useState<{
    name: string
    photoReference?: string
    username?: string
    link?: string
  } | null>(null)
  const [loadingImage, setLoadingImage] = useState(false)
  const [lastFetchedLocation, setLastFetchedLocation] = useState<{
    placeId: string
    name: string
  } | null>(null)

  // Check if the user can create a trip in this squad
  useEffect(() => {
    if (authLoading || !user || !squadId || isSubscribed) return
    const checkTripLimit = async () => {
      setCheckingLimits(true)
      try {
        const canCreate = await canCreateMoreTripsInSquadFunc(user.uid, squadId)
        setCanCreateTrip(canCreate)
      } catch (error) {
        console.error("Error checking trip limits:", error)
        setCanCreateTrip(true) // Default to allowing creation
      } finally {
        setCheckingLimits(false)
      }
    }

    checkTripLimit()
  }, [isSubscribed, squadId, canCreateMoreTripsInSquadFunc, user?.uid, authLoading])

  // Fetch location image when place ID changes
  const fetchLocationImageByPlaceId = useCallback(
    async (placeId: string, locationName: string) => {
      // Skip fetching if we've already fetched this location
      if (
        lastFetchedLocation &&
        lastFetchedLocation.placeId === placeId &&
        lastFetchedLocation.name === locationName
      ) {
        return
      }

      try {
        console.log("Starting fetchLocationImageByPlaceId with:", { placeId, locationName })
        setLoadingImage(true)

        // Use the standard caching flow which handles everything properly
        const imageResult = await getLocationImageByPlaceId(placeId, locationName)
        console.log("Image result from getLocationImageByPlaceId:", imageResult)

        setLocationImage(imageResult.url)
        if (imageResult.attribution) {
          setImageAttribution(imageResult.attribution)
        }

        // Get GooglePlaceImage data for the trip creation
        const googlePlaceImageResult = await fetchGooglePlaceImageByPlaceId(placeId, locationName)
        if (googlePlaceImageResult) {
          setGooglePlaceImage(googlePlaceImageResult)
        }

        // Save this location as the last one we fetched
        setLastFetchedLocation({
          placeId,
          name: locationName,
        })
      } catch (error) {
        console.error("Error fetching location image by place ID:", error)
        setLocationImage(null)
        setGooglePlaceImage(null)
        setImageAttribution(null)
      } finally {
        setLoadingImage(false)
      }
    },
    [lastFetchedLocation]
  )

  // Handle form field changes
  const handleChange = useCallback(
    (field: keyof TripFormData, value: any, placeId?: string) => {
      // If this is a destination change with a place ID, update both fields
      if (field === "destination") {
        setFormData((prev) => ({
          ...prev,
          destination: value,
          placeId: placeId,
        }))

        // Only fetch image if a place ID is provided (meaning user clicked an autocomplete suggestion)
        // This prevents loading images for every keystroke
        if (placeId) {
          fetchLocationImageByPlaceId(placeId, value)
        } else if (value !== formData.destination) {
          // If user is typing manually (no placeId) and the value has changed,
          // clear the current image since it no longer matches the location
          if (locationImage) {
            setLocationImage(null)
            setGooglePlaceImage(null)
            setImageAttribution(null)
            setLastFetchedLocation(null)
          }
        }
      } else {
        // For other fields, just update the value
        setFormData((prev) => ({ ...prev, [field]: value }))
      }
    },
    [formData.destination, locationImage, fetchLocationImageByPlaceId]
  )

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!user || !squad) return

    // Validate required fields
    if (!formData.name || !formData.destination || !formData.startDate || !formData.endDate) {
      toast({
        title: "Missing information",
        description: "Please fill in all required fields.",
        variant: "destructive",
      })
      return
    }

    // Validate that startDate is in the future (not today or earlier)
    const today = new Date()
    today.setHours(0, 0, 0, 0) // Reset time to start of day for accurate comparison

    if (formData.startDate && formData.startDate <= today) {
      toast({
        title: "Invalid start date",
        description: "Start date must be in the future (not today or earlier).",
        variant: "destructive",
      })
      return
    }

    // Double-check subscription limits before creating
    if (!isSubscribed) {
      setCheckingLimits(true)
      const canCreate = await canCreateMoreTripsInSquadFunc(user.uid, squadId)
      setCheckingLimits(false)

      if (!canCreate) {
        // Use centralized error handling
        handleSubscriptionError(SubscriptionErrorType.MAX_TRIPS_PER_SQUAD_REACHED)
        return
      }
    }

    try {
      // Prepare trip data
      const tripData: TripCreateData = {
        name: formData.name,
        destination: formData.destination,
        squadId: formData.squadId || squadId, // Use formData.squadId or fallback to squadId
        startDate: formData.startDate as any,
        endDate: formData.endDate as any,
        budget: formData.budget,
        description: formData.description,
        // Include place ID, googlePlaceImage, locationThumbnail (fallback), and imageAttribution if available
        ...(formData.placeId ? { placeId: formData.placeId } : {}),
        ...(googlePlaceImage ? { googlePlaceImage } : {}),
        ...(locationImage ? { locationThumbnail: locationImage } : {}),
        ...(imageAttribution ? { imageAttribution } : {}),
        status: "planning",
        attendees: [], // Will be populated from squad members after creation
        leaderId: user.uid,
        createdBy: user.uid,
      }

      // Create the trip using the hook
      const tripId = await create(tripData)

      if (!tripId) {
        throw new Error("Failed to create trip")
      }

      toast({
        title: "Trip created!",
        description: "Your trip has been created successfully.",
      })

      // Redirect to the trip page
      router.push(`/trips/${tripId}`)
    } catch (error) {
      console.error("Error creating trip:", error)
      toast({
        title: "Error",
        description: createError?.message || "Failed to create trip. Please try again.",
        variant: "destructive",
      })
    }
  }

  // Show loading state
  if (loadingSquad) {
    return <PageLoading />
  }

  // Show error if squad not found
  if (squadError || !squad) {
    return (
      <div className="container py-10">
        <Alert variant="destructive">
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>
            {squadError?.message || "Squad not found. Please try again."}
          </AlertDescription>
        </Alert>
        <div className="mt-4">
          <Link href="/squads">
            <Button variant="outline">
              <ArrowLeft className="mr-2 h-4 w-4" /> Back to Squads
            </Button>
          </Link>
        </div>
      </div>
    )
  }

  return (
    <div className="container py-6">
      <div className="max-w-3xl mx-auto">
        <div className="flex items-center mb-6">
          <Link href={`/squads/${squadId}`}>
            <Button variant="outline" size="sm">
              <ArrowLeft className="mr-2 h-4 w-4" /> Back to Squad
            </Button>
          </Link>
        </div>
        <div className="flex items-center mb-6">
          <h1 className="text-3xl font-bold ml-4">Create a Trip for {squad.name}</h1>
        </div>

        {!canCreateTrip && !isSubscribed && (
          <Alert className="mb-6">
            <AlertTitle>Subscription Limit Reached</AlertTitle>
            <AlertDescription>
              You've reached the maximum number of trips allowed for this squad on the free plan.
              Upgrade to create more trips.
            </AlertDescription>
          </Alert>
        )}

        <Card>
          <CardHeader>
            <CardTitle>Trip Details</CardTitle>
            <CardDescription>Fill in the details for your new trip</CardDescription>
          </CardHeader>
          <form onSubmit={handleSubmit}>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <div>
                  <Label htmlFor="name">Trip Name</Label>
                  <Input
                    id="name"
                    value={formData.name}
                    onChange={(e) => handleChange("name", e.target.value)}
                    placeholder="Summer Vacation 2023"
                    required
                  />
                </div>

                <div className="space-y-2">
                  <DestinationAutocomplete
                    value={formData.destination}
                    onChange={(value, placeId) => handleChange("destination", value, placeId)}
                    placeholder="Miami, Florida"
                    required
                  />

                  {/* Location Image Preview - only show when we have a valid destination with placeId */}
                  {formData.destination && formData.placeId && (
                    <div className="mt-2 space-y-1">
                      <div className="rounded-lg overflow-hidden">
                        {loadingImage ? (
                          <div className="aspect-video bg-muted flex items-center justify-center">
                            <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
                          </div>
                        ) : locationImage ? (
                          <OptimizedImage
                            src={locationImage}
                            alt={formData.destination}
                            aspectRatio="video"
                            className="rounded-lg"
                            priority
                            quality={90}
                          />
                        ) : (
                          <div className="aspect-video bg-muted flex items-center justify-center">
                            <ImageIcon className="h-8 w-8 text-muted-foreground" />
                            <span className="ml-2 text-muted-foreground">No image available</span>
                          </div>
                        )}
                      </div>
                      {imageAttribution && (
                        <div className="text-xs text-muted-foreground text-right">
                          {imageAttribution.link ? (
                            <>
                              Photo by{" "}
                              <a
                                href={imageAttribution.link}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="hover:underline"
                              >
                                {imageAttribution.name}
                              </a>{" "}
                              on{" "}
                              <a
                                href="https://unsplash.com"
                                target="_blank"
                                rel="noopener noreferrer"
                                className="hover:underline"
                              >
                                Unsplash
                              </a>
                            </>
                          ) : (
                            <>Photo of {imageAttribution.name} via Google Places</>
                          )}
                        </div>
                      )}
                    </div>
                  )}
                </div>

                <div className="space-y-2">
                  <Label>Trip Dates</Label>
                  <DateRangePicker
                    startDate={formData.startDate}
                    endDate={formData.endDate}
                    onDateRangeChange={(startDate, endDate) => {
                      setFormData((prev) => ({
                        ...prev,
                        startDate,
                        endDate,
                      }))
                    }}
                    placeholder="Select trip dates"
                    disabled={(date) => {
                      // Disable today and past dates
                      const today = new Date()
                      today.setHours(0, 0, 0, 0)
                      return date <= today
                    }}
                    required
                  />
                </div>

                <div>
                  <Label htmlFor="budget">Budget (USD)</Label>
                  <div className="relative">
                    <span className="absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground">
                      $
                    </span>
                    <Input
                      id="budget"
                      value={formData.budget}
                      onChange={(e) => {
                        // Only allow whole numbers (no decimals)
                        const value = e.target.value && parseInt(e.target.value)
                        handleChange("budget", value || 0)
                      }}
                      className="pl-6"
                      placeholder="1000"
                      type="text"
                      pattern="[0-9]*"
                      inputMode="numeric"
                      maxLength={10}
                    />
                  </div>
                </div>

                <div>
                  <Label htmlFor="description">Description (optional)</Label>
                  <Textarea
                    id="description"
                    value={formData.description}
                    onChange={(e) => handleChange("description", e.target.value)}
                    placeholder="Add some details about this trip..."
                    rows={4}
                  />
                </div>
              </div>
            </CardContent>
            <CardFooter className="flex justify-end">
              <Button
                type="submit"
                disabled={creating || (squadId && !canCreateTrip) || checkingLimits}
              >
                {(creating || checkingLimits) && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                Create Trip
              </Button>
            </CardFooter>
          </form>
        </Card>
      </div>
    </div>
  )
}
