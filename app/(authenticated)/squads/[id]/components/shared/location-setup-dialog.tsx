"use client"

import React from "react"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import { MapPin } from "lucide-react"

interface LocationSetupDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  preferenceType: "local" | "national"
}

export function LocationSetupDialog({
  open,
  onOpenChange,
  preferenceType,
}: LocationSetupDialogProps) {
  const handleGoToSettings = () => {
    window.open("/settings", "_blank")
    onOpenChange(false)
  }

  return (
    <AlertDialog open={open} onOpenChange={onOpenChange}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle className="flex items-center gap-2">
            <MapPin className="h-5 w-5" />
            Location Required
          </AlertDialogTitle>
          <AlertDialogDescription>
            You need to configure your location to use {preferenceType} trip suggestions.
            {preferenceType === "local"
              ? " This will show destinations within 100 miles of your location."
              : " This will show destinations within your country."}
            <br />
            <br />
            Would you like to go to your profile settings to configure your location?
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel>Cancel</AlertDialogCancel>
          <AlertDialogAction onClick={handleGoToSettings}>Go to Settings</AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  )
}
