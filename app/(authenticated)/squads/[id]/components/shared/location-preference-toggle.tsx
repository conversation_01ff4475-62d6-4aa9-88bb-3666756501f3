"use client"

import React from "react"
import { But<PERSON> } from "@/components/ui/button"
import { MapPin, Globe, Flag } from "lucide-react"

export type LocationPreference = "local" | "national" | "global"

interface LocationPreferenceToggleProps {
  value: LocationPreference
  onChange: (preference: LocationPreference) => void
  disabled?: boolean
  hasUserLocation?: boolean
  onLocationSetupPrompt?: () => void
}

export function LocationPreferenceToggle({
  value,
  onChange,
  disabled = false,
  hasUserLocation = false,
  onLocationSetupPrompt,
}: LocationPreferenceToggleProps) {
  const options = [
    {
      value: "local" as const,
      label: "Local",
      description: "100 miles or less",
      icon: MapPin,
      disabled: !hasUserLocation,
    },
    {
      value: "national" as const,
      label: "National",
      description: "Within my country",
      icon: Flag,
      disabled: !hasUserLocation,
    },
    {
      value: "global" as const,
      label: "Global",
      description: "Anywhere in the world",
      icon: Globe,
      disabled: false,
    },
  ]

  const handleOptionClick = (option: (typeof options)[0]) => {
    const isDisabled = disabled || option.disabled

    if (isDisabled && option.disabled && !hasUserLocation && onLocationSetupPrompt) {
      // Show prompt for location setup
      onLocationSetupPrompt()
    } else if (!isDisabled) {
      onChange(option.value)
    }
  }

  return (
    <div className="flex flex-row gap-1 w-full">
      {options.map((option) => {
        const Icon = option.icon
        const isSelected = value === option.value
        const isDisabled = disabled || option.disabled

        return (
          <Button
            key={option.value}
            variant={isSelected ? "default" : "outline"}
            size="sm"
            onClick={() => handleOptionClick(option)}
            disabled={disabled} // Only disable if globally disabled, not for location issues
            className={`
              flex flex-col items-center gap-1 h-auto py-2 px-2 flex-1
              ${isSelected ? "bg-primary text-primary-foreground" : ""}
              ${isDisabled && !option.disabled ? "opacity-50 cursor-not-allowed" : "cursor-pointer"}
              ${option.disabled && !hasUserLocation ? "opacity-60" : ""}
            `}
          >
            <Icon className="h-3 w-3 sm:h-4 sm:w-4" />
            <div className="text-center">
              <div className="text-xs font-medium">{option.label}</div>
              <div className="text-xs opacity-75 hidden sm:block">{option.description}</div>
            </div>
          </Button>
        )
      })}
    </div>
  )
}
