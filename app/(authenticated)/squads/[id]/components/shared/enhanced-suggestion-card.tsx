"use client"

import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { Eye } from "lucide-react"
import { OptimizedImage } from "@/components/optimized-image"
import { CachedTripSuggestion } from "@/lib/domains/ai-suggestions/ai-suggestions-cache.service"
import { useEnhancedSuggestionImage } from "@/lib/hooks/use-enhanced-suggestion-image"

interface EnhancedSuggestionCardProps {
  suggestion: CachedTripSuggestion
  index: number
  onViewDetails: (suggestion: CachedTripSuggestion) => void
}

export function EnhancedSuggestionCard({
  suggestion,
  index,
  onViewDetails,
}: EnhancedSuggestionCardProps) {
  const { imageUrl, isLoading, handleImageError } = useEnhancedSuggestionImage(
    suggestion,
    "300x150"
  )

  return (
    <Card key={index} className="overflow-hidden">
      <div className="aspect-video relative">
        <OptimizedImage
          src={imageUrl}
          alt={suggestion.destination}
          aspectRatio="video"
          className={`object-cover transition-opacity duration-300 ${
            isLoading ? "opacity-75" : "opacity-100"
          }`}
          onError={handleImageError}
          fallbackSrc="/placeholder.svg?height=150&width=300"
        />
        <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent" />
        <div className="absolute bottom-0 left-0 right-0 p-3">
          <h3 className="font-semibold text-white">{suggestion.destination}</h3>
          <p className="text-xs text-white/80">{suggestion.budget}</p>
        </div>
      </div>
      <CardContent className="pt-4">
        <div className="flex flex-wrap gap-2 mb-3">
          {suggestion.tags.slice(0, 3).map((tag, i) => (
            <Badge key={i} variant="secondary" className="text-xs">
              {tag}
            </Badge>
          ))}
        </div>
        <p className="text-sm text-muted-foreground line-clamp-3">{suggestion.description}</p>
        <div className="mt-3 flex justify-between items-center">
          <Button
            variant="outline"
            size="sm"
            className="text-xs"
            onClick={() => onViewDetails(suggestion)}
          >
            <Eye className="h-3 w-3 mr-1" /> View Details
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}
