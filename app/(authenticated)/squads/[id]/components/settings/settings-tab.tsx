"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"
import { toast } from "@/components/ui/use-toast"
import { useUser } from "@/lib/domains/auth/auth.hooks"
import { Squad } from "@/lib/domains/squad/squad.types"
import { useUpdateSquad, useDeleteSquad, useLeaveSquad } from "@/lib/domains/squad/squad.hooks"

import { ErrorBoundary } from "@/components/error-boundary"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import * as z from "zod"

// Form validation schema
const squadFormSchema = z.object({
  name: z
    .string()
    .min(2, "Squad name must be at least 2 characters")
    .max(50, "Squad name cannot exceed 50 characters"),
  description: z.string().max(500, "Description cannot exceed 500 characters").optional(),
})

type SquadFormValues = z.infer<typeof squadFormSchema>

interface SettingsTabProps {
  squad: Squad
}

export function SettingsTab({ squad }: SettingsTabProps) {
  const router = useRouter()
  const squadId = squad.id
  const user = useUser()

  // Use our custom hooks
  const { update, updating: saving } = useUpdateSquad(squadId)
  const { deleteSquad, deleting } = useDeleteSquad()
  const { leaveSquad, leaving } = useLeaveSquad()

  const [isSquadLead, setIsSquadLead] = useState(false)
  const [showDeleteDialog, setShowDeleteDialog] = useState(false)
  const [showLeaveDialog, setShowLeaveDialog] = useState(false)

  // Initialize form with react-hook-form
  const form = useForm<SquadFormValues>({
    resolver: zodResolver(squadFormSchema),
    defaultValues: {
      name: "",
      description: "",
    },
  })

  // Update form when squad data changes
  useEffect(() => {
    form.reset({
      name: squad.name,
      description: squad.description || "",
    })

    // Check if current user is squad leader
    if (user) {
      setIsSquadLead(user.uid === squad.leaderId)
    }
  }, [squad.name, squad.description, squad.leaderId, user, form])

  const handleSaveChanges = async (data: SquadFormValues) => {
    if (!isSquadLead) return

    try {
      const success = await update({
        name: data.name,
        description: data.description,
      })

      if (success) {
        toast({
          title: "Squad settings updated",
          description: "Your squad settings have been updated successfully.",
        })
      } else {
        toast({
          title: "Error",
          description: "Failed to update squad settings. Please try again.",
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error("Error updating squad settings:", error)
      toast({
        title: "Error",
        description: "Failed to update squad settings. Please try again.",
        variant: "destructive",
      })
    }
  }

  const handleDeleteSquad = async () => {
    if (!isSquadLead) return

    try {
      const result = await deleteSquad(squadId)

      if (result.success) {
        toast({
          title: "Squad deleted",
          description: "The squad has been deleted successfully.",
        })
        router.replace("/dashboard", { scroll: false })
      } else {
        toast({
          title: "Error",
          description: result.error
            ? (result.error as Error).message
            : "Failed to delete squad. Please try again.",
          variant: "destructive",
        })
        setShowDeleteDialog(false)
      }
    } catch (error) {
      console.error("Error deleting squad:", error)
      toast({
        title: "Error",
        description: "Failed to delete squad. Please try again.",
        variant: "destructive",
      })
      setShowDeleteDialog(false)
    }
  }

  const handleLeaveSquad = async () => {
    if (!user) return

    try {
      const result = await leaveSquad(squadId, user.uid)

      if (result.success) {
        toast({
          title: "Left squad",
          description: "You have left the squad successfully.",
        })

        // Redirect to dashboard
        router.push("/dashboard")
      } else {
        toast({
          title: "Error",
          description: result.error
            ? (result.error as Error).message
            : "Failed to leave squad. Please try again.",
          variant: "destructive",
        })
        setShowLeaveDialog(false)
      }
    } catch (error) {
      console.error("Error leaving squad:", error)
      toast({
        title: "Error",
        description: "Failed to leave squad. Please try again.",
        variant: "destructive",
      })
      setShowLeaveDialog(false)
    }
  }

  return (
    <ErrorBoundary>
      <div className="space-y-4">
        <h2 className="text-xl font-semibold">Squad Settings</h2>

        {isSquadLead && (
          <Card>
            <CardHeader>
              <CardTitle>General Settings</CardTitle>
              <CardDescription>Manage your squad details</CardDescription>
            </CardHeader>
            <form onSubmit={form.handleSubmit(handleSaveChanges)}>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="name">Squad Name</Label>
                  <Input
                    id="name"
                    {...form.register("name")}
                    disabled={!isSquadLead || saving}
                    aria-invalid={form.formState.errors.name ? "true" : "false"}
                  />
                  {form.formState.errors.name && (
                    <p className="text-sm text-destructive mt-1">
                      {form.formState.errors.name.message}
                    </p>
                  )}
                </div>
                <div className="space-y-2">
                  <Label htmlFor="description">Description</Label>
                  <Textarea
                    id="description"
                    rows={3}
                    {...form.register("description")}
                    disabled={!isSquadLead || saving}
                    aria-invalid={form.formState.errors.description ? "true" : "false"}
                  />
                  {form.formState.errors.description && (
                    <p className="text-sm text-destructive mt-1">
                      {form.formState.errors.description.message}
                    </p>
                  )}
                </div>
              </CardContent>
              <CardFooter>
                <Button type="submit" disabled={!isSquadLead || saving || !form.formState.isDirty}>
                  {saving ? "Saving..." : "Save Changes"}
                </Button>
              </CardFooter>
            </form>
          </Card>
        )}

        <Card>
          <CardHeader>
            <CardTitle>Danger Zone</CardTitle>
            <CardDescription>Irreversible actions</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Show leave squad option for all members, including leaders when they're not the only member */}
            {(!isSquadLead || (isSquadLead && (squad?.memberCount || 0) > 1)) && (
              <div className="flex justify-between items-center p-4 border rounded-md border-destructive/20 bg-destructive/5">
                <div>
                  <h3 className="font-medium">Leave Squad</h3>
                  <p className="text-sm text-muted-foreground">
                    {isSquadLead && (squad?.memberCount || 0) > 1
                      ? "Leadership will be transferred to another member"
                      : "Remove yourself from this squad"}
                  </p>
                </div>
                <AlertDialog open={showLeaveDialog} onOpenChange={setShowLeaveDialog}>
                  <AlertDialogTrigger asChild>
                    <Button
                      variant="outline"
                      className="text-destructive border-destructive/50"
                      disabled={leaving}
                    >
                      {leaving ? "Leaving..." : "Leave Squad"}
                    </Button>
                  </AlertDialogTrigger>
                  <AlertDialogContent>
                    <AlertDialogHeader>
                      <AlertDialogTitle>Are you sure?</AlertDialogTitle>
                      <AlertDialogDescription>
                        {isSquadLead && (squad?.memberCount || 0) > 1
                          ? "This will remove you from the squad and transfer leadership to another member. You will need to be invited again to rejoin."
                          : "This will remove you from the squad. You will need to be invited again to rejoin."}
                      </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                      <AlertDialogCancel>Cancel</AlertDialogCancel>
                      <AlertDialogAction onClick={handleLeaveSquad} disabled={leaving}>
                        {leaving ? "Leaving..." : "Leave Squad"}
                      </AlertDialogAction>
                    </AlertDialogFooter>
                  </AlertDialogContent>
                </AlertDialog>
              </div>
            )}

            {/* Show delete squad option for squad leaders */}
            {isSquadLead && (
              <div className="flex justify-between items-center p-4 border rounded-md border-destructive/20 bg-destructive/5">
                <div>
                  <h3 className="font-medium">Delete Squad</h3>
                  <p className="text-sm text-muted-foreground">This action cannot be undone</p>
                </div>
                <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
                  <AlertDialogTrigger asChild>
                    <Button variant="destructive" disabled={deleting}>
                      {deleting ? "Deleting..." : "Delete Squad"}
                    </Button>
                  </AlertDialogTrigger>
                  <AlertDialogContent>
                    <AlertDialogHeader>
                      <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
                      <AlertDialogDescription>
                        This action cannot be undone. This will permanently delete the squad and all
                        associated data, including trips and tasks.
                      </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                      <AlertDialogCancel>Cancel</AlertDialogCancel>
                      <AlertDialogAction onClick={handleDeleteSquad} disabled={deleting}>
                        {deleting ? "Deleting..." : "Delete Squad"}
                      </AlertDialogAction>
                    </AlertDialogFooter>
                  </AlertDialogContent>
                </AlertDialog>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </ErrorBoundary>
  )
}
