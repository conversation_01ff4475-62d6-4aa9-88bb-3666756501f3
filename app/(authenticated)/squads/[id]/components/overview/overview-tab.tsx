"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import {
  <PERSON>,
  CardContent,
  CardDescription,
  <PERSON><PERSON>ooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"

import { Calendar, Compass, MapPin, Plus, Star, Users } from "lucide-react"
import { useRealtimeSquadTrips } from "@/lib/domains/trip/trip.realtime.hooks"
import { Trip } from "@/lib/domains/trip/trip.types"
import { Squad } from "@/lib/domains/squad/squad.types"
import { TripIdeasSection } from "./trip-ideas-section"
import { UpcomingTrips } from "./upcoming-trips"
import { SquadStats } from "./squad-stats"

import { SectionLoading } from "@/components/section-loading"
import { toast } from "@/components/ui/use-toast"
import { ErrorBoundary } from "@/components/error-boundary"

interface OverviewTabProps {
  squad: Squad
}

export function OverviewTab({ squad }: OverviewTabProps) {
  const squadId = squad.id
  const { trips, loading, error } = useRealtimeSquadTrips(squadId)

  const [upcomingTrips, setUpcomingTrips] = useState<Trip[]>([])
  const [pastTrips, setPastTrips] = useState<Trip[]>([])
  const [isDataCached, setIsDataCached] = useState(false)

  // Show error toast if there's an error
  useEffect(() => {
    if (error) {
      toast({
        title: "Error",
        description: "Unable to load squad data. Please try again later.",
        variant: "destructive",
      })
    }
  }, [error])

  // Process trips into upcoming and past categories
  useEffect(() => {
    if (!trips) return

    const now = new Date()
    const upcoming: Trip[] = []
    const past: Trip[] = []

    trips.forEach((trip) => {
      // Convert Firebase timestamp to Date - safely handle different timestamp formats
      const endDate =
        trip.endDate && typeof trip.endDate.toDate === "function"
          ? trip.endDate.toDate()
          : new Date()

      if (endDate < now) {
        past.push(trip)
      } else {
        upcoming.push(trip)
      }
    })

    // Sort upcoming trips by start date (ascending)
    upcoming.sort((a, b) => {
      const dateA =
        a.startDate && typeof a.startDate.toDate === "function" ? a.startDate.toDate() : new Date()
      const dateB =
        b.startDate && typeof b.startDate.toDate === "function" ? b.startDate.toDate() : new Date()
      return dateA.getTime() - dateB.getTime()
    })

    // Sort past trips by end date (descending)
    past.sort((a, b) => {
      const dateA =
        a.endDate && typeof a.endDate.toDate === "function" ? a.endDate.toDate() : new Date()
      const dateB =
        b.endDate && typeof b.endDate.toDate === "function" ? b.endDate.toDate() : new Date()
      return dateB.getTime() - dateA.getTime()
    })

    setUpcomingTrips(upcoming)
    setPastTrips(past)
    setIsDataCached(true)

    // Cache the data in sessionStorage for quick access
    try {
      sessionStorage.setItem(`squad_${squadId}_upcoming_trips`, JSON.stringify(upcoming))
      sessionStorage.setItem(`squad_${squadId}_past_trips`, JSON.stringify(past))
    } catch (e) {
      console.error("Error caching trip data:", e)
    }
  }, [trips, squadId])

  // Try to load cached data while waiting for real-time data
  useEffect(() => {
    if (!loading || isDataCached) return

    try {
      const cachedUpcoming = sessionStorage.getItem(`squad_${squadId}_upcoming_trips`)
      const cachedPast = sessionStorage.getItem(`squad_${squadId}_past_trips`)

      if (cachedUpcoming) {
        setUpcomingTrips(JSON.parse(cachedUpcoming))
      }

      if (cachedPast) {
        setPastTrips(JSON.parse(cachedPast))
      }
    } catch (e) {
      console.error("Error loading cached trip data:", e)
    }
  }, [loading, isDataCached, squadId])

  if (loading && !isDataCached) {
    return <SectionLoading message="Loading squad overview..." />
  }

  return (
    <ErrorBoundary>
      <div className="space-y-6">
        <ErrorBoundary>
          <UpcomingTrips upcomingTrips={upcomingTrips} squadId={squadId} />
        </ErrorBoundary>
        <ErrorBoundary>
          <TripIdeasSection squadId={squadId} />
        </ErrorBoundary>
        <ErrorBoundary>
          <SquadStats squad={squad} upcomingTrips={upcomingTrips} pastTrips={pastTrips} />
        </ErrorBoundary>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6"></div>
      </div>
    </ErrorBoundary>
  )
}
