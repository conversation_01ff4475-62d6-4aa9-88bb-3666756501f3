"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"

import { MapPin, Star } from "lucide-react"
import { Trip } from "@/lib/domains/trip/trip.types"
import { Squad } from "@/lib/domains/squad/squad.types"
import { SkeletonStats } from "@/components/skeleton-loader"
import { useSquadAverageRating } from "@/lib/domains/trip-review/trip-review.hooks"

interface SquadStatsProps {
  squad: Squad
  upcomingTrips: Trip[]
  pastTrips: Trip[]
}

export function SquadStats({ squad, upcomingTrips, pastTrips }: SquadStatsProps) {
  const [loading, setLoading] = useState(true)
  const [mostPopularDestination, setMostPopularDestination] = useState<string>("")
  const [destinationCount, setDestinationCount] = useState<number>(0)

  // Get squad average rating from recent 10 completed trips
  const { averageRating, tripCount, loading: ratingLoading } = useSquadAverageRating(squad.id, 10)

  // Calculate most popular destination dynamically
  useEffect(() => {
    if (pastTrips.length === 0) {
      setMostPopularDestination("")
      setDestinationCount(0)
      setLoading(false)
      return
    }

    try {
      // Count occurrences of each destination
      const destinationCounts = pastTrips.reduce(
        (acc, trip) => {
          const destination = trip.destination.trim()
          acc[destination] = (acc[destination] || 0) + 1
          return acc
        },
        {} as Record<string, number>
      )

      // Find the destination with the highest count
      const mostPopular = Object.entries(destinationCounts).reduce(
        (max, [destination, count]) => {
          return count > max.count ? { destination, count } : max
        },
        { destination: "", count: 0 }
      )

      setMostPopularDestination(mostPopular.destination)
      setDestinationCount(mostPopular.count)
      setLoading(false)
    } catch (error) {
      console.error("Error calculating most popular destination:", error)
      setMostPopularDestination("")
      setDestinationCount(0)
      setLoading(false)
    }
  }, [pastTrips])

  // Overall loading state includes both destination calculation and rating loading
  const isLoading = loading || ratingLoading

  return (
    <Card>
      <CardHeader>
        <CardTitle>Squad Stats</CardTitle>
        <CardDescription>Trip history and activity</CardDescription>
      </CardHeader>
      {isLoading ? (
        <CardContent>
          <SkeletonStats />
        </CardContent>
      ) : (
        <CardContent className="space-y-6">
          <div className="space-y-2">
            <div className="flex justify-between items-center">
              <h4 className="text-sm font-medium">Total Trips</h4>
              <span className="font-bold">{upcomingTrips.length + pastTrips.length}</span>
            </div>
            <div className="flex justify-between items-center">
              <h4 className="text-sm font-medium">Upcoming Trips</h4>
              <span className="font-bold">{upcomingTrips.length}</span>
            </div>
            <div className="flex justify-between items-center">
              <h4 className="text-sm font-medium">Past Trips</h4>
              <span className="font-bold">{pastTrips.length}</span>
            </div>
          </div>

          <div className="space-y-2">
            <h4 className="text-sm font-medium">Average Trip Rating</h4>
            {averageRating !== null ? (
              <div className="flex items-center gap-2">
                <div className="flex">
                  {[...Array(5)].map((_, i) => (
                    <Star
                      key={i}
                      className={`h-5 w-5 ${
                        i < Math.round(averageRating)
                          ? "text-yellow-400 fill-yellow-400"
                          : "text-muted-foreground"
                      }`}
                    />
                  ))}
                </div>
                <span className="text-sm text-muted-foreground">
                  {averageRating.toFixed(1)} ({tripCount} trip{tripCount !== 1 ? "s" : ""})
                </span>
              </div>
            ) : (
              <div className="flex items-center gap-2">
                <div className="flex">
                  {[...Array(5)].map((_, i) => (
                    <Star key={i} className="h-5 w-5 text-muted-foreground" />
                  ))}
                </div>
                <span className="text-sm text-muted-foreground">No rated trips yet</span>
              </div>
            )}
          </div>

          {pastTrips.length > 0 && mostPopularDestination && (
            <div className="space-y-2">
              <h4 className="text-sm font-medium">Most Popular Destination</h4>
              <div className="flex items-center gap-2">
                <MapPin className="h-4 w-4 text-primary" />
                <div className="flex flex-col">
                  <span className="font-medium">{mostPopularDestination}</span>
                  <span className="text-xs text-muted-foreground">
                    {destinationCount} trip{destinationCount !== 1 ? "s" : ""}
                  </span>
                </div>
              </div>
            </div>
          )}
        </CardContent>
      )}
    </Card>
  )
}
