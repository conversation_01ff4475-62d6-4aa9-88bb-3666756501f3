"use client"

import { useState, useEffect } from "react"
import { useParams } from "next/navigation"
import { AiTripSuggestions } from "../shared/ai-trip-suggestions"
import { toast } from "@/components/ui/use-toast"
import { ErrorBoundary } from "@/components/error-boundary"
import { Button } from "@/components/ui/button"
import { RefreshCw } from "lucide-react"

interface TripIdeasSectionProps {
  squadId?: string
}

export function TripIdeasSection({ squadId }: TripIdeasSectionProps) {
  const params = useParams()
  const id = squadId || (params.id as string)
  const [retryCount, setRetryCount] = useState(0)
  const [isLoading, setIsLoading] = useState(false)
  const [hasError, setHasError] = useState(false)

  // Reset error state when retrying
  useEffect(() => {
    if (retryCount > 0) {
      setHasError(false)
      setIsLoading(true)

      // Simulate API call completion
      const timer = setTimeout(() => {
        setIsLoading(false)
      }, 2000)

      return () => clearTimeout(timer)
    }
  }, [retryCount])

  // Handle API failure
  const handleApiFailure = () => {
    setHasError(true)
    setIsLoading(false)

    toast({
      title: "Error",
      description: "Failed to load trip suggestions. Please try again.",
      variant: "destructive",
    })
  }

  // Handle retry
  const handleRetry = () => {
    setRetryCount((prev) => prev + 1)
  }

  if (hasError) {
    return (
      <div className="p-6 border rounded-lg bg-muted/20 text-center">
        <h3 className="font-medium mb-2">Unable to load trip suggestions</h3>
        <p className="text-sm text-muted-foreground mb-4">
          We couldn't generate trip ideas at this time.
        </p>
        <Button onClick={handleRetry} variant="outline" size="sm">
          <RefreshCw className="h-4 w-4 mr-2" /> Try Again
        </Button>
      </div>
    )
  }

  return (
    <ErrorBoundary
      fallback={
        <div className="p-6 border rounded-lg bg-muted/20 text-center">
          <h3 className="font-medium mb-2">Something went wrong</h3>
          <p className="text-sm text-muted-foreground mb-4">
            We couldn't load trip suggestions at this time.
          </p>
          <Button onClick={handleRetry} variant="outline" size="sm">
            <RefreshCw className="h-4 w-4 mr-2" /> Try Again
          </Button>
        </div>
      }
    >
      <AiTripSuggestions
        squadId={id}
        key={`suggestions-${retryCount}`}
        onError={handleApiFailure}
      />
    </ErrorBoundary>
  )
}
