"use client"

import { useState } from "react"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"

import { Calendar, Compass, Plus, Users } from "lucide-react"
import { Trip } from "@/lib/domains/trip/trip.types"
import { OptimizedImage } from "@/components/optimized-image"
import { Skeleton } from "@/components/ui/skeleton"
import { getTripImageUrl } from "@/lib/utils/trip-image-utils"
import { toast } from "@/components/ui/use-toast"

interface UpcomingTripsProps {
  upcomingTrips: Trip[]
  squadId: string
}

export function UpcomingTrips({ upcomingTrips, squadId }: UpcomingTripsProps) {
  const [imageLoading, setImageLoading] = useState(true)

  // Format date safely
  const formatDate = (timestamp: any) => {
    if (!timestamp) return ""

    try {
      return typeof timestamp.toDate === "function"
        ? timestamp.toDate().toLocaleDateString()
        : new Date(timestamp).toLocaleDateString()
    } catch (error) {
      console.error("Error formatting date:", error)
      return "Invalid date"
    }
  }

  // Handle image error
  const handleImageError = () => {
    setImageLoading(false)
    toast({
      title: "Image Error",
      description: "Failed to load trip image",
      variant: "destructive",
    })
  }

  return (
    <Card className="md:col-span-2">
      <CardHeader>
        <CardTitle>Upcoming Trip</CardTitle>
        <CardDescription>Your next adventure with this squad</CardDescription>
      </CardHeader>
      {upcomingTrips.length > 0 ? (
        <>
          <CardContent className="p-0">
            <div className="aspect-video relative overflow-hidden">
              {imageLoading && <Skeleton className="absolute inset-0 z-10 rounded-t-lg" />}
              <OptimizedImage
                src={getTripImageUrl(upcomingTrips[0], "600x200")}
                alt={upcomingTrips[0].destination}
                aspectRatio="video"
                className="rounded-t-lg w-full h-full object-cover"
                priority
                onLoad={() => setImageLoading(false)}
                onError={handleImageError}
                fallbackSrc="/placeholder.svg?height=200&width=600"
              />
              <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/60 to-transparent p-4 z-10">
                <h3 className="text-white text-xl font-bold">{upcomingTrips[0].destination}</h3>
                <p className="text-white/80 text-sm">
                  {formatDate(upcomingTrips[0].startDate)} - {formatDate(upcomingTrips[0].endDate)}
                </p>
              </div>
            </div>
            <div className="p-4 space-y-4">
              <div className="flex justify-between items-center">
                <div className="flex items-center gap-2">
                  <Calendar className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm">
                    {formatDate(upcomingTrips[0].startDate)} -{" "}
                    {formatDate(upcomingTrips[0].endDate)}
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  <Users className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm">
                    {upcomingTrips[0].attendees?.length || 0} attending
                  </span>
                </div>
              </div>
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <h4 className="font-medium">Trip Preparation</h4>
                  <span className="text-sm text-muted-foreground">
                    {upcomingTrips[0].tasksCompleted || 0} of {upcomingTrips[0].totalTasks || 0}{" "}
                    tasks completed
                  </span>
                </div>
                <div className="w-full h-2 bg-muted rounded-full overflow-hidden">
                  <div
                    className="h-full bg-primary"
                    style={{
                      width: `${((upcomingTrips[0].tasksCompleted || 0) / Math.max(upcomingTrips[0].totalTasks || 0, 1)) * 100}%`,
                    }}
                  ></div>
                </div>
              </div>
            </div>
          </CardContent>
          <CardFooter>
            <Link href={`/trips/${upcomingTrips[0].id}`} className="w-full">
              <Button className="w-full">View Trip Details</Button>
            </Link>
          </CardFooter>
        </>
      ) : (
        <CardContent className="flex flex-col items-center justify-center p-6">
          <div className="w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center mb-4">
            <Compass className="h-6 w-6 text-primary" />
          </div>
          <p className="font-medium text-center">No Upcoming Trips</p>
          <p className="text-sm text-muted-foreground text-center mt-1 mb-4">
            Plan your next adventure with this squad
          </p>
          <Link href={`/squads/${squadId}/create-trip`}>
            <Button>
              <Plus className="h-4 w-4 mr-2" /> Plan a Trip
            </Button>
          </Link>
        </CardContent>
      )}
    </Card>
  )
}
