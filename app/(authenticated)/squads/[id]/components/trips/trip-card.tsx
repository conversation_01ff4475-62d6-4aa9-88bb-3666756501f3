"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { Card, CardContent } from "@/components/ui/card"
import { Clock, Star, Users } from "lucide-react"
import { type Trip } from "@/lib/domains/trip/trip.types"
import { OptimizedImage } from "@/components/optimized-image"
import { Skeleton } from "@/components/ui/skeleton"
import { getTripImageUrl } from "@/lib/utils/trip-image-utils"
import { TripReviewService } from "@/lib/domains/trip-review/trip-review.service"
import { TripReviewAggregate } from "@/lib/domains/trip-review/trip-review.types"

interface TripCardProps {
  trip: Trip
  isPast: boolean
}

export function TripCard({ trip, isPast }: TripCardProps) {
  const [imageLoading, setImageLoading] = useState(true)
  const [reviewAggregate, setReviewAggregate] = useState<TripReviewAggregate | null>(null)
  const [ratingLoading, setRatingLoading] = useState(false)

  // Only load ratings for completed trips
  const isCompleted = trip.status === "completed"

  // Load rating data for completed trips
  useEffect(() => {
    if (!isPast || !isCompleted) return

    const loadRating = async () => {
      setRatingLoading(true)
      try {
        const aggregate = await TripReviewService.getTripReviewAggregate(trip.id)
        setReviewAggregate(aggregate)
      } catch (error) {
        console.error(`Error loading rating for trip ${trip.id}:`, error)
        setReviewAggregate(null)
      } finally {
        setRatingLoading(false)
      }
    }

    loadRating()
  }, [trip.id, isPast, isCompleted])

  // Format date safely
  const formatDate = (timestamp: any) => {
    if (!timestamp) return ""

    try {
      return typeof timestamp.toDate === "function"
        ? timestamp.toDate().toLocaleDateString()
        : new Date(timestamp).toLocaleDateString()
    } catch (error) {
      console.error("Error formatting date:", error)
      return "Invalid date"
    }
  }

  // Render star rating
  const renderStarRating = (rating: number, totalReviews: number) => {
    return (
      <div className="flex items-center gap-1">
        <div className="flex">
          {[...Array(5)].map((_, i) => (
            <Star
              key={i}
              className={`h-4 w-4 ${
                i < Math.round(rating) ? "text-yellow-400 fill-yellow-400" : "text-muted-foreground"
              }`}
            />
          ))}
        </div>
        {totalReviews > 0 && (
          <span className="text-xs text-muted-foreground">({totalReviews})</span>
        )}
      </div>
    )
  }

  return (
    <Link href={`/trips/${trip.id}`}>
      <Card className="h-full hover:shadow-md transition-shadow">
        <div className="aspect-video relative overflow-hidden rounded-t-lg">
          {imageLoading && <Skeleton className="absolute inset-0 z-10" />}
          <OptimizedImage
            src={getTripImageUrl(trip, "400x200")}
            alt={trip.destination}
            aspectRatio="video"
            className="rounded-t-lg"
            priority
            onLoad={() => setImageLoading(false)}
            fallbackSrc="/placeholder.svg?height=200&width=400"
          />
          <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/60 to-transparent p-4 z-10">
            <h3 className="text-white font-bold">{trip.destination}</h3>
            <p className="text-white/80 text-sm">
              {formatDate(trip.startDate)} - {formatDate(trip.endDate)}
            </p>
          </div>
        </div>
        <CardContent className="pt-4">
          <div className="flex justify-between items-center mb-2">
            <div className="text-sm text-muted-foreground flex items-center">
              <Users className="h-3 w-3 mr-1" /> {trip.attendees.length}
            </div>
            {isPast && isCompleted && (
              <div>
                {ratingLoading ? (
                  <div className="flex">
                    {[...Array(5)].map((_, i) => (
                      <Star key={i} className="h-4 w-4 text-muted-foreground animate-pulse" />
                    ))}
                  </div>
                ) : reviewAggregate && reviewAggregate.totalReviews > 0 ? (
                  renderStarRating(reviewAggregate.averageRating, reviewAggregate.totalReviews)
                ) : (
                  <span className="text-xs text-muted-foreground">No reviews</span>
                )}
              </div>
            )}
          </div>
          {isPast ? (
            <p className="text-sm text-muted-foreground">
              {trip.description || "No description available"}
            </p>
          ) : (
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <Clock className="h-4 w-4 text-muted-foreground" />
                <div className="text-sm">
                  <span className="font-medium">{trip.tasksCompleted}</span>
                  <span className="text-muted-foreground">
                    {" "}
                    / {trip.totalTasks} tasks completed
                  </span>
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </Link>
  )
}
