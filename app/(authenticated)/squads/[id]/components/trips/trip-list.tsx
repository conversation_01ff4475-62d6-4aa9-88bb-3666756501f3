"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Plus } from "lucide-react"
import { useRealtimeSquadTrips } from "@/lib/domains/trip/trip.realtime.hooks"
import { Trip } from "@/lib/domains/trip/trip.types"
import { Squad } from "@/lib/domains/squad/squad.types"
import { TripCard } from "./trip-card"
import { SectionLoading } from "@/components/section-loading"
import { toast } from "@/components/ui/use-toast"
import { ErrorBoundary } from "@/components/error-boundary"
import { AiTripSuggestions } from "../shared/ai-trip-suggestions"

interface TripListProps {
  squad: Squad
}

export function TripList({ squad }: TripListProps) {
  const squadId = squad.id
  const { trips, loading, error } = useRealtimeSquadTrips(squadId)

  const [upcomingTrips, setUpcomingTrips] = useState<Trip[]>([])
  const [pastTrips, setPastTrips] = useState<Trip[]>([])

  // Process trips into upcoming and past categories
  useEffect(() => {
    if (!trips) return

    const now = new Date()
    const upcoming: Trip[] = []
    const past: Trip[] = []

    trips.forEach((trip) => {
      // Convert Firebase timestamp to Date - safely handle different timestamp formats
      const endDate =
        trip.endDate && typeof trip.endDate.toDate === "function"
          ? trip.endDate.toDate()
          : new Date()

      if (endDate < now) {
        past.push(trip)
      } else {
        upcoming.push(trip)
      }
    })

    // Sort upcoming trips by start date (ascending)
    upcoming.sort((a, b) => {
      const dateA =
        a.startDate && typeof a.startDate.toDate === "function" ? a.startDate.toDate() : new Date()
      const dateB =
        b.startDate && typeof b.startDate.toDate === "function" ? b.startDate.toDate() : new Date()
      return dateA.getTime() - dateB.getTime()
    })

    // Sort past trips by end date (descending)
    past.sort((a, b) => {
      const dateA =
        a.endDate && typeof a.endDate.toDate === "function" ? a.endDate.toDate() : new Date()
      const dateB =
        b.endDate && typeof b.endDate.toDate === "function" ? b.endDate.toDate() : new Date()
      return dateB.getTime() - dateA.getTime()
    })

    setUpcomingTrips(upcoming)
    setPastTrips(past)
  }, [trips])

  // Show error toast if there's an error
  useEffect(() => {
    if (error) {
      toast({
        title: "Error",
        description: "Unable to load trips. Please try again later.",
        variant: "destructive",
      })
    }
  }, [error])

  if (loading) {
    return <SectionLoading message="Loading trips..." />
  }

  return (
    <ErrorBoundary>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h2 className="text-2xl font-bold tracking-tight">Squad Trips</h2>
          <Button asChild>
            <Link href={`/squads/${squadId}/create-trip`}>
              <Plus className="mr-2 h-4 w-4" /> Create Trip
            </Link>
          </Button>
        </div>

        <Tabs defaultValue="upcoming" className="space-y-4">
          <TabsList>
            <TabsTrigger value="upcoming">Upcoming ({upcomingTrips.length})</TabsTrigger>
            <TabsTrigger value="past">Past ({pastTrips.length})</TabsTrigger>
            <TabsTrigger value="ideas">Trip Ideas</TabsTrigger>
          </TabsList>

          <TabsContent value="upcoming" className="space-y-4">
            {upcomingTrips.length === 0 ? (
              <div className="text-center py-8">
                <p className="text-muted-foreground mb-4">No upcoming trips</p>
                <Button asChild>
                  <Link href={`/squads/${squadId}/create-trip`}>
                    <Plus className="mr-2 h-4 w-4" /> Create Trip
                  </Link>
                </Button>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {upcomingTrips.map((trip) => (
                  <ErrorBoundary key={trip.id}>
                    <TripCard trip={trip} isPast={false} />
                  </ErrorBoundary>
                ))}
              </div>
            )}
          </TabsContent>

          <TabsContent value="past" className="space-y-4">
            {pastTrips.length === 0 ? (
              <div className="text-center py-8">
                <p className="text-muted-foreground">No past trips</p>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {pastTrips.map((trip) => (
                  <ErrorBoundary key={trip.id}>
                    <TripCard trip={trip} isPast={true} />
                  </ErrorBoundary>
                ))}
              </div>
            )}
          </TabsContent>

          <TabsContent value="ideas" className="space-y-4">
            <ErrorBoundary>
              <AiTripSuggestions squadId={squadId} />
            </ErrorBoundary>
          </TabsContent>
        </Tabs>
      </div>
    </ErrorBoundary>
  )
}
