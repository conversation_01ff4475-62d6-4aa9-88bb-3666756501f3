"use client"

import { useState, useEffect } from "react"
import { X, Filter } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { Checkbox } from "@/components/ui/checkbox"
import { Slider } from "@/components/ui/slider"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { <PERSON>alog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { useLocalExperiences } from "@/lib/domains/local-experiences"
import { ExperienceCategory, ExperienceSearchFilters } from "@/lib/domains/local-experiences"

interface ExperienceFiltersModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
}

export function ExperienceFiltersModal({ open, onOpenChange }: ExperienceFiltersModalProps) {
  const { searchFilters, searchExperiences, clearSearch, loadAllExperiences } =
    useLocalExperiences()

  // Local state for filters
  const [localFilters, setLocalFilters] = useState<ExperienceSearchFilters>({})

  // Initialize local filters when modal opens
  useEffect(() => {
    if (open) {
      setLocalFilters(searchFilters)
    }
  }, [open, searchFilters])

  const categories: ExperienceCategory[] = [
    "adventure",
    "food",
    "culture",
    "entertainment",
    "outdoor",
    "indoor",
    "default",
  ]

  const handleCategoryToggle = (category: ExperienceCategory) => {
    const currentCategories = localFilters.categories || []
    const newCategories = currentCategories.includes(category)
      ? currentCategories.filter((c) => c !== category)
      : [...currentCategories, category]

    setLocalFilters((prev) => ({
      ...prev,
      categories: newCategories.length > 0 ? newCategories : undefined,
    }))
  }

  const handlePriceRangeChange = (values: number[]) => {
    setLocalFilters((prev) => ({
      ...prev,
      priceRange: {
        min: values[0],
        max: values[1],
      },
    }))
  }

  const handleDurationRangeChange = (values: number[]) => {
    setLocalFilters((prev) => ({
      ...prev,
      durationRange: {
        min: values[0],
        max: values[1],
      },
    }))
  }

  const handleGuestCountChange = (value: string) => {
    const numValue = parseInt(value)
    setLocalFilters((prev) => ({
      ...prev,
      maxGuests: isNaN(numValue) ? undefined : numValue,
    }))
  }

  const handleLocationChange = (value: string) => {
    setLocalFilters((prev) => ({
      ...prev,
      location: value.trim() || undefined,
    }))
  }

  const handleRatingChange = (value: string) => {
    const numValue = parseFloat(value)
    setLocalFilters((prev) => ({
      ...prev,
      minRating: isNaN(numValue) ? undefined : numValue,
    }))
  }

  const handleApplyFilters = () => {
    // Apply filters and search
    if (Object.keys(localFilters).length > 0) {
      searchExperiences(localFilters)
    } else {
      clearSearch()
      loadAllExperiences()
    }
    onOpenChange(false)
  }

  const handleClearFilters = () => {
    setLocalFilters({})
    clearSearch()
    loadAllExperiences()
    onOpenChange(false)
  }

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
    }).format(price)
  }

  const formatDuration = (minutes: number) => {
    if (minutes < 60) return `${minutes}m`
    const hours = Math.floor(minutes / 60)
    const remainingMinutes = minutes % 60
    return remainingMinutes > 0 ? `${hours}h ${remainingMinutes}m` : `${hours}h`
  }

  const activeFiltersCount = Object.keys(localFilters).filter((key) => {
    const value = localFilters[key as keyof ExperienceSearchFilters]
    if (Array.isArray(value)) return value.length > 0
    if (typeof value === "object" && value !== null) return true
    return value !== undefined && value !== null && value !== ""
  }).length

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-md max-h-[90vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            Filters
            {activeFiltersCount > 0 && (
              <Badge variant="secondary" className="ml-2">
                {activeFiltersCount}
              </Badge>
            )}
          </DialogTitle>
        </DialogHeader>

        <div className="flex-1 overflow-y-auto space-y-6 pr-2">
          {/* Categories */}
          <div>
            <Label className="text-base font-medium">Categories</Label>
            <div className="mt-3 flex flex-wrap gap-2">
              {categories.map((category) => (
                <button
                  key={category}
                  onClick={() => handleCategoryToggle(category)}
                  className={`px-3 py-1 rounded-full text-sm border transition-colors ${
                    localFilters.categories?.includes(category)
                      ? "bg-primary text-primary-foreground border-primary"
                      : "bg-background border-border hover:bg-muted"
                  }`}
                >
                  {category.charAt(0).toUpperCase() + category.slice(1)}
                </button>
              ))}
            </div>
          </div>

          <Separator />

          {/* Price Range */}
          <div>
            <Label className="text-base font-medium">Price Range</Label>
            <div className="mt-3 space-y-3">
              <Slider
                value={[localFilters.priceRange?.min || 0, localFilters.priceRange?.max || 500]}
                onValueChange={handlePriceRangeChange}
                max={500}
                min={0}
                step={10}
                className="w-full"
              />
              <div className="flex justify-between text-sm text-muted-foreground">
                <span>{formatPrice(localFilters.priceRange?.min || 0)}</span>
                <span>{formatPrice(localFilters.priceRange?.max || 500)}</span>
              </div>
            </div>
          </div>

          <Separator />

          {/* Duration Range */}
          <div>
            <Label className="text-base font-medium">Duration</Label>
            <div className="mt-3 space-y-3">
              <Slider
                value={[
                  localFilters.durationRange?.min || 30,
                  localFilters.durationRange?.max || 480,
                ]}
                onValueChange={handleDurationRangeChange}
                max={480}
                min={30}
                step={30}
                className="w-full"
              />
              <div className="flex justify-between text-sm text-muted-foreground">
                <span>{formatDuration(localFilters.durationRange?.min || 30)}</span>
                <span>{formatDuration(localFilters.durationRange?.max || 480)}</span>
              </div>
            </div>
          </div>

          <Separator />

          {/* Location */}
          <div>
            <Label htmlFor="location" className="text-base font-medium">
              Location
            </Label>
            <Input
              id="location"
              placeholder="Enter city or area"
              value={localFilters.location || ""}
              onChange={(e) => handleLocationChange(e.target.value)}
              className="mt-2"
            />
          </div>

          <Separator />

          {/* Guest Count */}
          <div>
            <Label htmlFor="guests" className="text-base font-medium">
              Maximum Guests
            </Label>
            <Input
              id="guests"
              type="number"
              placeholder="Any"
              min="1"
              max="50"
              value={localFilters.maxGuests || ""}
              onChange={(e) => handleGuestCountChange(e.target.value)}
              className="mt-2"
            />
          </div>

          <Separator />

          {/* Minimum Rating */}
          <div>
            <Label htmlFor="rating" className="text-base font-medium">
              Minimum Rating
            </Label>
            <Input
              id="rating"
              type="number"
              placeholder="Any"
              min="0"
              max="5"
              step="0.1"
              value={localFilters.minRating || ""}
              onChange={(e) => handleRatingChange(e.target.value)}
              className="mt-2"
            />
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex gap-3 pt-4 border-t">
          <Button variant="outline" onClick={handleClearFilters} className="flex-1">
            Clear All
          </Button>
          <Button onClick={handleApplyFilters} className="flex-1">
            Apply Filters
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  )
}

export { ExperienceFiltersModal }
