"use client"

import { useState, useEffect, useCallback } from "react"
import { Calendar } from "@/components/ui/calendar"
import { But<PERSON> } from "@/components/ui/button"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { CalendarIcon } from "lucide-react"
import { format } from "date-fns"
import { cn } from "@/lib/utils"
import { LocalExperiencesService } from "@/lib/domains/local-experiences/local-experiences.service"

interface ExperienceCalendarProps {
  experienceId: string
  selectedDate?: string
  onDateChange: (date: string) => void
  placeholder?: string
  className?: string
  disabled?: boolean
}

export function ExperienceCalendar({
  experienceId,
  selectedDate,
  onDateChange,
  placeholder = "Select date",
  className,
  disabled = false,
}: ExperienceCalendarProps) {
  const [open, setOpen] = useState(false)
  const [availableDaysOfWeek, setAvailableDaysOfWeek] = useState<string[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [selectedDateObj, setSelectedDateObj] = useState<Date | undefined>(
    selectedDate ? new Date(selectedDate) : undefined
  )

  // Load available days of the week for this experience
  useEffect(() => {
    const loadAvailableDays = async () => {
      setIsLoading(true)
      try {
        const response = await LocalExperiencesService.getAvailableDaysOfWeek(experienceId)
        if (response.success && response.data) {
          setAvailableDaysOfWeek(response.data)
        }
      } catch (error) {
        console.error("Error loading available days:", error)
      } finally {
        setIsLoading(false)
      }
    }

    if (experienceId) {
      loadAvailableDays()
    }
  }, [experienceId])

  // Update selected date when prop changes
  useEffect(() => {
    setSelectedDateObj(selectedDate ? new Date(selectedDate) : undefined)
  }, [selectedDate])

  // Helper function to get day of week from date
  const getDayOfWeek = useCallback((date: Date): string => {
    const days = ["sunday", "monday", "tuesday", "wednesday", "thursday", "friday", "saturday"]
    return days[date.getDay()]
  }, [])

  // Check if a date should be disabled
  const isDateDisabled = useCallback(
    (date: Date): boolean => {
      const today = new Date()
      today.setHours(0, 0, 0, 0)

      // Disable past dates (but allow today)
      if (date < today) {
        return true
      }

      // If still loading, disable all dates
      if (isLoading) {
        return true
      }

      // Check if this day of the week is available
      const dayOfWeek = getDayOfWeek(date)
      return !availableDaysOfWeek.includes(dayOfWeek)
    },
    [availableDaysOfWeek, isLoading, getDayOfWeek]
  )

  // Handle date selection
  const handleDateSelect = (date: Date | undefined) => {
    if (date && !isDateDisabled(date)) {
      setSelectedDateObj(date)
      const dateString = format(date, "yyyy-MM-dd")
      onDateChange(dateString)
      setOpen(false)
    }
  }

  // Format display text
  const getDisplayText = () => {
    if (selectedDateObj) {
      return format(selectedDateObj, "MMM dd, yyyy")
    }
    return placeholder
  }

  // Get available days text for helper
  const getAvailableDaysText = () => {
    if (isLoading) return "Loading..."
    if (availableDaysOfWeek.length === 0) return "No available days"

    const dayNames = {
      monday: "Mon",
      tuesday: "Tue",
      wednesday: "Wed",
      thursday: "Thu",
      friday: "Fri",
      saturday: "Sat",
      sunday: "Sun",
    }

    return availableDaysOfWeek.map((day) => dayNames[day as keyof typeof dayNames]).join(", ")
  }

  return (
    <div className="space-y-2">
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            className={cn(
              "w-full justify-start text-left font-normal",
              !selectedDateObj && "text-muted-foreground",
              className
            )}
            disabled={disabled || isLoading}
          >
            <CalendarIcon className="mr-2 h-4 w-4" />
            {getDisplayText()}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-auto p-0" align="start">
          <div className="p-3">
            <Calendar
              mode="single"
              selected={selectedDateObj}
              onSelect={handleDateSelect}
              disabled={isDateDisabled}
              initialFocus
              className="rounded-md border-0"
            />
            <div className="mt-3 pt-3 border-t text-xs text-muted-foreground">
              <div className="font-medium mb-1">Available days:</div>
              <div>{getAvailableDaysText()}</div>
            </div>
          </div>
        </PopoverContent>
      </Popover>
    </div>
  )
}
