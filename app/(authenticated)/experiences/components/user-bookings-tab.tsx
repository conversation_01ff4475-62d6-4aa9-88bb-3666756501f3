"use client"

import { useState, useEffect } from "react"
import { Calendar, Clock, MapPin, Users, Star, Mail, AlertCircle } from "lucide-react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { Skeleton } from "@/components/ui/skeleton"
import { Alert, AlertDescription } from "@/components/ui/alert"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { useUserLocalExperienceBookings } from "@/lib/domains/user/user-local-experience-bookings.hooks"
import {
  ExperienceBooking,
  BookingStatus,
} from "@/lib/domains/user/user-local-experience-bookings.types"

export function UserBookingsTab() {
  const [statusFilter, setStatusFilter] = useState<BookingStatus | "all">("all")

  const {
    userBookings,
    isLoadingHistory,
    historyError,
    hasMoreBookings,
    updateFilters,
    resetFilters,
    loadMoreBookings,
    refreshBookingHistory,
  } = useUserLocalExperienceBookings()

  // Reset filters when component mounts to ensure clean state
  useEffect(() => {
    resetFilters()
    setStatusFilter("all")
  }, [resetFilters])

  const handleStatusFilterChange = (value: string) => {
    const newStatus = value as BookingStatus | "all"
    setStatusFilter(newStatus)

    const filters = newStatus === "all" ? {} : { status: [newStatus as BookingStatus] }
    updateFilters(filters)
  }

  const formatPrice = (price: number, currency: string) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: currency,
    }).format(price)
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      weekday: "short",
      year: "numeric",
      month: "short",
      day: "numeric",
    })
  }

  const formatTime = (timeString: string) => {
    return new Date(`2000-01-01T${timeString}`).toLocaleTimeString("en-US", {
      hour: "numeric",
      minute: "2-digit",
      hour12: true,
    })
  }

  const getStatusColor = (status: BookingStatus) => {
    switch (status) {
      case "confirmed":
        return "bg-green-100 text-green-800 border-green-200"
      case "pending":
        return "bg-yellow-100 text-yellow-800 border-yellow-200"
      case "cancelled":
        return "bg-red-100 text-red-800 border-red-200"
      case "completed":
        return "bg-blue-100 text-blue-800 border-blue-200"
      default:
        return "bg-gray-100 text-gray-800 border-gray-200"
    }
  }

  if (historyError) {
    return (
      <div className="space-y-6">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{historyError}</AlertDescription>
        </Alert>
        <div className="text-center">
          <Button onClick={refreshBookingHistory} variant="outline">
            Try Again
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header with Filter */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h2 className="text-2xl font-bold">My Bookings</h2>
          <p className="text-muted-foreground">View and manage your experience bookings</p>
        </div>

        <div className="flex items-center gap-4">
          <Select value={statusFilter} onValueChange={handleStatusFilterChange}>
            <SelectTrigger className="w-40">
              <SelectValue placeholder="Filter by status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Bookings</SelectItem>
              <SelectItem value="confirmed">Confirmed</SelectItem>
              <SelectItem value="pending">Pending</SelectItem>
              <SelectItem value="completed">Completed</SelectItem>
              <SelectItem value="cancelled">Cancelled</SelectItem>
            </SelectContent>
          </Select>

          <Button onClick={refreshBookingHistory} variant="outline" size="sm">
            Refresh
          </Button>
        </div>
      </div>

      {/* Cancellation Notice */}
      <Alert>
        <Mail className="h-4 w-4" />
        <AlertDescription>
          Need to cancel or request a refund? Please contact us at{" "}
          <a href="mailto:<EMAIL>" className="font-medium underline">
            <EMAIL>
          </a>
        </AlertDescription>
      </Alert>

      {/* Bookings List */}
      {isLoadingHistory ? (
        <BookingsListSkeleton />
      ) : userBookings.length > 0 ? (
        <div className="space-y-4">
          {userBookings.map((booking) => (
            <BookingCard
              key={booking.id}
              booking={booking}
              formatPrice={formatPrice}
              formatDate={formatDate}
              formatTime={formatTime}
              getStatusColor={getStatusColor}
            />
          ))}

          {/* Load More Button */}
          {hasMoreBookings && (
            <div className="text-center pt-4">
              <Button onClick={loadMoreBookings} variant="outline">
                Load More Bookings
              </Button>
            </div>
          )}
        </div>
      ) : (
        <div className="text-center py-12">
          <div className="max-w-md mx-auto">
            <div className="mb-4">
              <Calendar className="h-12 w-12 text-muted-foreground mx-auto" />
            </div>
            <h3 className="text-lg font-semibold mb-2">No bookings yet</h3>
            <p className="text-muted-foreground">
              {statusFilter === "all"
                ? "Start exploring experiences to make your first booking!"
                : `No ${statusFilter} bookings found.`}
            </p>
          </div>
        </div>
      )}
    </div>
  )
}

// Booking Card Component
interface BookingCardProps {
  booking: ExperienceBooking
  formatPrice: (price: number, currency: string) => string
  formatDate: (dateString: string) => string
  formatTime: (timeString: string) => string
  getStatusColor: (status: BookingStatus) => string
}

function BookingCard({
  booking,
  formatPrice,
  formatDate,
  formatTime,
  getStatusColor,
}: BookingCardProps) {
  return (
    <Card className="overflow-hidden">
      <CardHeader className="pb-3">
        <div className="flex justify-between items-start">
          <div className="space-y-1">
            <CardTitle className="text-lg">{booking.experienceTitle}</CardTitle>
            <p className="text-sm text-muted-foreground">Hosted by {booking.experienceHost}</p>
          </div>
          <Badge className={getStatusColor(booking.status)} variant="outline">
            {booking.status.charAt(0).toUpperCase() + booking.status.slice(1)}
          </Badge>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Booking Details */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 text-sm">
          <div className="flex items-center gap-2">
            <Calendar className="h-4 w-4 text-muted-foreground" />
            <span>{formatDate(booking.date)}</span>
          </div>
          <div className="flex items-center gap-2">
            <Clock className="h-4 w-4 text-muted-foreground" />
            <span>{formatTime(booking.time)}</span>
          </div>
          <div className="flex items-center gap-2">
            <Users className="h-4 w-4 text-muted-foreground" />
            <span>
              {booking.guests} guest{booking.guests > 1 ? "s" : ""}
            </span>
          </div>
          <div className="flex items-center gap-2">
            <MapPin className="h-4 w-4 text-muted-foreground" />
            <span>{booking.experienceLocation}</span>
          </div>
        </div>

        {/* Special Requests */}
        {booking.specialRequests && (
          <div className="text-sm">
            <span className="font-medium">Special Requests: </span>
            <span className="text-muted-foreground">{booking.specialRequests}</span>
          </div>
        )}

        {/* Pricing */}
        <div className="flex justify-between items-center pt-2 border-t">
          <div className="text-sm text-muted-foreground">Booking ID: {booking.id.slice(-8)}</div>
          <div className="text-right">
            <div className="text-lg font-semibold">
              {formatPrice(booking.pricing.total, booking.pricing.currency)}
            </div>
            <div className="text-xs text-muted-foreground">
              {booking.pricing.guests} ×{" "}
              {formatPrice(booking.pricing.basePrice, booking.pricing.currency)}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

// Loading Skeleton
function BookingsListSkeleton() {
  return (
    <div className="space-y-4">
      {Array.from({ length: 3 }).map((_, i) => (
        <Card key={i} className="overflow-hidden">
          <CardHeader className="pb-3">
            <div className="flex justify-between items-start">
              <div className="space-y-2">
                <Skeleton className="h-5 w-48" />
                <Skeleton className="h-4 w-32" />
              </div>
              <Skeleton className="h-6 w-20" />
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
              <Skeleton className="h-4 w-24" />
              <Skeleton className="h-4 w-20" />
              <Skeleton className="h-4 w-16" />
              <Skeleton className="h-4 w-28" />
            </div>
            <div className="flex justify-between items-center pt-2 border-t">
              <Skeleton className="h-4 w-32" />
              <div className="text-right space-y-1">
                <Skeleton className="h-5 w-16" />
                <Skeleton className="h-3 w-20" />
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  )
}
