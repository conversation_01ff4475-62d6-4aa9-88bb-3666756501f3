"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Card,
  CardContent,
  CardDescription,
  Card<PERSON>ooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { toast } from "@/components/ui/use-toast"
import { Loader2 } from "lucide-react"
import { useUserPreferences } from "@/lib/domains/user-preferences/user-preferences.hooks"
import { useUser } from "@/lib/domains/auth/auth.hooks"

export function AIPreferences() {
  const { preferences, loading: preferencesLoading, updatePreferences } = useUserPreferences()
  const user = useUser()

  // Local state
  const [localAIEnabled, setLocalAIEnabled] = useState(true)
  const [localProactiveSuggestions, setLocalProactiveSuggestions] = useState(true)
  const [saving, setSaving] = useState(false)
  const [initialLoading, setInitialLoading] = useState(true)

  // Update local state when preferences change
  useEffect(() => {
    if (!preferencesLoading && preferences) {
      setLocalAIEnabled(preferences.aiEnabled ?? true)
      setLocalProactiveSuggestions(preferences.proactiveSuggestions ?? true)
      setInitialLoading(false)
    }
  }, [preferences, preferencesLoading])

  const savePreferences = async () => {
    setSaving(true)
    try {
      await updatePreferences({
        aiEnabled: localAIEnabled,
        proactiveSuggestions: localProactiveSuggestions,
      })
    } catch (error) {
      console.error("Error updating AI preferences:", error)
      toast({
        title: "Error",
        description: "Failed to update AI preferences. Please try again.",
        variant: "destructive",
      })
    } finally {
      setSaving(false)
    }
  }

  const clearAIHistory = () => {
    // Placeholder for clearing AI history
    toast({
      title: "AI History cleared",
      description: "Your AI conversation history has been cleared.",
    })
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>AI Assistant Preferences</CardTitle>
        <CardDescription>Customize how the AI assistant works for you</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {initialLoading ? (
          <div className="flex flex-col items-center justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin text-primary mb-4" />
            <p className="text-sm text-muted-foreground">Loading AI preferences...</p>
          </div>
        ) : !user ? (
          <div className="flex flex-col items-center justify-center py-8">
            <p className="text-sm text-muted-foreground mb-4">Unable to load AI preferences.</p>
            <p className="text-sm text-muted-foreground">
              Please sign in to view your AI preferences.
            </p>
          </div>
        ) : (
          <>
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="ai-enabled">AI Assistant</Label>
                <p className="text-sm text-muted-foreground">
                  Enable or disable the AI trip planning assistant
                </p>
              </div>
              <Switch
                id="ai-enabled"
                checked={localAIEnabled}
                onCheckedChange={setLocalAIEnabled}
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="ai-suggestions">Proactive Suggestions</Label>
                <p className="text-sm text-muted-foreground">
                  Allow AI to suggest activities and destinations
                </p>
              </div>
              <Switch
                id="ai-suggestions"
                checked={localProactiveSuggestions}
                onCheckedChange={setLocalProactiveSuggestions}
                disabled={!localAIEnabled}
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="ai-history">AI History</Label>
                <p className="text-sm text-muted-foreground">Clear your AI conversation history</p>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={clearAIHistory}
                disabled={!localAIEnabled}
              >
                Clear History
              </Button>
            </div>
          </>
        )}
      </CardContent>
      <CardFooter>
        <Button onClick={savePreferences} disabled={saving || initialLoading || !user}>
          {saving ? (
            <>
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              Saving...
            </>
          ) : (
            "Save Preferences"
          )}
        </Button>
      </CardFooter>
    </Card>
  )
}
