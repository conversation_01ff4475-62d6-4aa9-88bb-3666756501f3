"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { toast } from "@/components/ui/use-toast"
import { useTheme } from "next-themes"
import { useSetTheme } from "@/lib/domains/theme/theme.hooks"

export function ThemeSettings() {
  const { setTheme: setNextTheme, resolvedTheme } = useTheme()
  const setZustandTheme = useSetTheme()
  const [isChangingTheme, setIsChangingTheme] = useState(false)
  const [isDarkMode, setIsDarkMode] = useState(resolvedTheme === "dark")

  const handleThemeChange = async (checked: boolean) => {
    // Get the new theme
    const newTheme = checked ? "dark" : "light"

    // Show brief loading state but don't disable the button
    setIsChangingTheme(true)

    try {
      // Update next-themes for immediate UI change
      setNextTheme(newTheme)
      setIsDarkMode(newTheme === "dark")
      // Update zustand store for Firestore and local state persistence
      await setZustandTheme(newTheme)

      toast({
        title: "Theme updated",
        description: `Theme changed to ${newTheme} mode.`,
      })
    } catch (error) {
      console.error("Error updating theme preference:", error)
      toast({
        title: "Error",
        description: "There was a problem saving your theme preference.",
        variant: "destructive",
      })
    } finally {
      setIsChangingTheme(false)
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Theme Settings</CardTitle>
        <CardDescription>Customize the appearance of the app</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="flex items-center justify-between">
          <div className="space-y-0.5">
            <Label htmlFor="dark-mode">Dark Mode</Label>
            <p className="text-sm text-muted-foreground">Switch between light and dark themes</p>
          </div>
          <div className="flex items-center gap-2">
            {isChangingTheme && (
              <div className="text-xs text-muted-foreground animate-pulse">Updating...</div>
            )}
            <Switch id="dark-mode" checked={isDarkMode} onCheckedChange={handleThemeChange} />
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
