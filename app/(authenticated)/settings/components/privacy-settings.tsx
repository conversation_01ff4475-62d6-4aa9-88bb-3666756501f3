"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { Switch } from "@/components/ui/switch"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Trash2 } from "lucide-react"
import { useUser } from "@/lib/domains/auth/auth.hooks"
import { toast } from "@/components/ui/use-toast"

export function PrivacySettings() {
  const user = useUser()
  const [saving, setSaving] = useState(false)

  const savePrivacySettings = async () => {
    setSaving(true)
    try {
      // Save privacy settings to Firebase
      // This is a placeholder for future implementation
      // When implementing, make sure to use a store function that handles toast notifications
      // For now, we'll keep the toast here since there's no store function yet

      toast({
        title: "Privacy settings updated",
        description: "Your privacy settings have been updated successfully.",
      })
    } catch (error) {
      console.error("Error updating privacy settings:", error)
      toast({
        title: "Error",
        description: "Failed to update privacy settings. Please try again.",
        variant: "destructive",
      })
    } finally {
      setSaving(false)
    }
  }

  const updatePassword = async () => {
    // Placeholder for password update functionality
    toast({
      title: "Password update",
      description: "Password update functionality will be implemented in the future.",
    })
  }

  const deleteAccount = () => {
    // Placeholder for account deletion
    toast({
      title: "Account deletion",
      description: "Account deletion functionality will be implemented in the future.",
      variant: "destructive",
    })
  }

  return (
    <>
      <Card>
        <CardHeader>
          <CardTitle>Privacy Settings</CardTitle>
          <CardDescription>Manage your privacy and security preferences</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="profile-visibility">Profile Visibility</Label>
              <p className="text-sm text-muted-foreground">Control who can see your profile</p>
            </div>
            <Select defaultValue="friends">
              <SelectTrigger id="profile-visibility" className="w-[180px]">
                <SelectValue placeholder="Select visibility" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="public">Public</SelectItem>
                <SelectItem value="friends">Friends Only</SelectItem>
                <SelectItem value="private">Private</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="location-sharing">Location Sharing</Label>
              <p className="text-sm text-muted-foreground">
                Share your location with squad members during trips
              </p>
            </div>
            <Switch id="location-sharing" defaultChecked />
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="data-collection">Data Collection</Label>
              <p className="text-sm text-muted-foreground">
                Allow anonymous usage data collection to improve the app
              </p>
            </div>
            <Switch id="data-collection" defaultChecked />
          </div>
        </CardContent>
        <CardFooter>
          <Button onClick={savePrivacySettings} disabled={saving || !user}>
            {saving ? "Saving..." : "Save Privacy Settings"}
          </Button>
        </CardFooter>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Security</CardTitle>
          <CardDescription>Manage your account security</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="current-password">Current Password</Label>
            <Input id="current-password" type="password" />
          </div>

          <div className="space-y-2">
            <Label htmlFor="new-password">New Password</Label>
            <Input id="new-password" type="password" />
          </div>

          <div className="space-y-2">
            <Label htmlFor="confirm-password">Confirm New Password</Label>
            <Input id="confirm-password" type="password" />
          </div>

          <div className="flex items-center justify-between pt-4">
            <div className="space-y-0.5">
              <Label htmlFor="two-factor">Two-Factor Authentication</Label>
              <p className="text-sm text-muted-foreground">
                Add an extra layer of security to your account
              </p>
            </div>
            <Button variant="outline">Set Up 2FA</Button>
          </div>
        </CardContent>
        <CardFooter className="flex justify-between">
          <Button variant="outline" className="text-destructive" onClick={deleteAccount}>
            <Trash2 className="h-4 w-4 mr-2" /> Delete Account
          </Button>
          <Button onClick={updatePassword}>Update Password</Button>
        </CardFooter>
      </Card>
    </>
  )
}
