"use client"

import { useState, useRef, useEffect, useCallback, Suspense, lazy } from "react"
import { useSearchParams, useRouter, usePathname } from "next/navigation"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { useIsUserSubscribed } from "@/lib/domains/user-subscription"

// Lazy load settings components to reduce initial bundle size
const ProfileSettings = lazy(() =>
  import("./profile-settings").then((module) => ({ default: module.ProfileSettings }))
)
const TravelPreferences = lazy(() =>
  import("./travel-preferences").then((module) => ({ default: module.TravelPreferences }))
)
const ThemeSettings = lazy(() =>
  import("./theme-settings").then((module) => ({ default: module.ThemeSettings }))
)
const AppPreferences = lazy(() =>
  import("./app-preferences").then((module) => ({ default: module.AppPreferences }))
)
const AIPreferences = lazy(() =>
  import("./ai-preferences").then((module) => ({ default: module.AIPreferences }))
)
const NotificationSettings = lazy(() =>
  import("./notification-settings").then((module) => ({ default: module.NotificationSettings }))
)
const PrivacySettings = lazy(() =>
  import("./privacy-settings").then((module) => ({ default: module.PrivacySettings }))
)
const BillingSettingsWrapper = lazy(() =>
  import("./billing-settings-wrapper").then((module) => ({
    default: module.BillingSettingsWrapper,
  }))
)
const ActivityPreferencesSettings = lazy(() =>
  import("./activity-preferences-settings").then((module) => ({
    default: module.ActivityPreferencesSettings,
  }))
)
const ReferralsSettings = lazy(() =>
  import("./referrals-settings").then((module) => ({ default: module.ReferralsSettings }))
)

// Settings tab loading skeleton component
const SettingsTabSkeleton = () => (
  <div className="space-y-6">
    {/* Settings Section Skeleton */}
    <div className="space-y-4">
      {[...Array(3)].map((_, i) => (
        <div key={i} className="p-6 border rounded-lg space-y-4">
          <div className="space-y-2">
            <div className="h-6 w-48 bg-muted animate-pulse rounded" />
            <div className="h-4 w-full bg-muted animate-pulse rounded" />
          </div>

          <div className="space-y-3">
            {[...Array(2)].map((_, j) => (
              <div key={j} className="flex items-center justify-between">
                <div className="space-y-1">
                  <div className="h-4 w-32 bg-muted animate-pulse rounded" />
                  <div className="h-3 w-48 bg-muted animate-pulse rounded" />
                </div>
                <div className="h-6 w-12 bg-muted animate-pulse rounded" />
              </div>
            ))}
          </div>
        </div>
      ))}
    </div>
  </div>
)

export function SettingsContent() {
  const router = useRouter()
  const pathname = usePathname()
  const searchParams = useSearchParams()
  const [activeTab, setActiveTab] = useState("profile")
  const tabsListRef = useRef<HTMLDivElement>(null)
  const isSubscribed = useIsUserSubscribed()

  // Function to update the URL when tab changes
  const handleTabChange = useCallback(
    (value: string) => {
      setActiveTab(value)

      // Create a new URLSearchParams object
      const params = new URLSearchParams(searchParams.toString())
      params.set("tab", value)

      // Update the URL without refreshing the page
      router.replace(`${pathname}?${params.toString()}`, { scroll: false })
    },
    [pathname, router, searchParams]
  )

  // Handle URL parameters for tab selection
  useEffect(() => {
    const tabParam = searchParams.get("tab")
    if (
      tabParam &&
      [
        "profile",
        "preferences",
        "notifications",
        "privacy",
        "billing",
        "activity-preferences",
        "referrals",
      ].includes(tabParam)
    ) {
      setActiveTab(tabParam)

      // Scroll the tab into view on mobile
      setTimeout(() => {
        if (tabsListRef.current) {
          const tabElement = tabsListRef.current.querySelector(`[data-value="${tabParam}"]`)
          if (tabElement) {
            // Scroll the tab into view with some padding
            const container = tabsListRef.current
            const scrollLeft =
              tabElement.getBoundingClientRect().left -
              container.getBoundingClientRect().left +
              container.scrollLeft -
              16 // Add some padding

            container.scrollTo({
              left: scrollLeft,
              behavior: "smooth",
            })
          }
        }
      }, 100) // Small delay to ensure the DOM is updated
    }
  }, [searchParams])

  return (
    <Tabs
      defaultValue="profile"
      className="space-y-4 w-full"
      onValueChange={handleTabChange}
      value={activeTab}
    >
      <div className="overflow-x-auto pb-2 max-w-[100vw] no-scrollbar">
        <TabsList className="w-full flex flex-nowrap overflow-x-auto" ref={tabsListRef}>
          <TabsTrigger value="profile" className="flex-shrink-0">
            Profile
          </TabsTrigger>
          <TabsTrigger value="preferences" className="flex-shrink-0">
            Preferences
          </TabsTrigger>
          {isSubscribed && (
            <TabsTrigger value="activity-preferences" className="flex-shrink-0">
              Activity Preferences
            </TabsTrigger>
          )}
          <TabsTrigger value="notifications" className="flex-shrink-0">
            Notifications
          </TabsTrigger>
          <TabsTrigger value="privacy" className="flex-shrink-0">
            Privacy & Security
          </TabsTrigger>
          <TabsTrigger value="billing" className="flex-shrink-0">
            Billing
          </TabsTrigger>
          <TabsTrigger value="referrals" className="flex-shrink-0">
            Referrals
          </TabsTrigger>
        </TabsList>
      </div>

      <TabsContent value="profile" className="space-y-4 w-full max-w-full overflow-x-hidden">
        <Suspense fallback={<SettingsTabSkeleton />}>
          <ProfileSettings />
        </Suspense>
      </TabsContent>

      <TabsContent value="preferences" className="space-y-4 w-full max-w-full overflow-x-hidden">
        <Suspense fallback={<SettingsTabSkeleton />}>
          <ThemeSettings />
          <AppPreferences />
          <TravelPreferences />
          <AIPreferences />
        </Suspense>
      </TabsContent>

      {isSubscribed && (
        <TabsContent
          value="activity-preferences"
          className="space-y-4 w-full max-w-full overflow-x-hidden"
        >
          <Suspense fallback={<SettingsTabSkeleton />}>
            <ActivityPreferencesSettings />
          </Suspense>
        </TabsContent>
      )}

      <TabsContent value="notifications" className="space-y-4 w-full max-w-full overflow-x-hidden">
        <Suspense fallback={<SettingsTabSkeleton />}>
          <NotificationSettings />
        </Suspense>
      </TabsContent>

      <TabsContent value="privacy" className="space-y-4 w-full max-w-full overflow-x-hidden">
        <Suspense fallback={<SettingsTabSkeleton />}>
          <PrivacySettings />
        </Suspense>
      </TabsContent>

      <TabsContent value="billing" className="space-y-4 w-full max-w-full overflow-x-hidden">
        <Suspense fallback={<SettingsTabSkeleton />}>
          <BillingSettingsWrapper />
        </Suspense>
      </TabsContent>

      <TabsContent value="referrals" className="space-y-4 w-full max-w-full overflow-x-hidden">
        <Suspense fallback={<SettingsTabSkeleton />}>
          <ReferralsSettings />
        </Suspense>
      </TabsContent>
    </Tabs>
  )
}
