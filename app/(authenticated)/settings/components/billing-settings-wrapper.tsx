"use client"

import { useState, useEffect, useCallback } from "react"
import { BillingSettings as OriginalBillingSettings } from "./billing-settings"
import { ActivityPreferencesWelcomeModal } from "./activity-preferences-welcome-modal"
import { useIsUserSubscribed, useSubscriptionStatusHandler } from "@/lib/domains/user-subscription"
import { useActivityPreferences } from "@/lib/domains/activity-preferences"
import { toast } from "@/components/ui/use-toast"

export function BillingSettingsWrapper() {
  const [toastShown, setToastShown] = useState(false)
  const [showWelcomeModal, setShowWelcomeModal] = useState(false)
  const isSubscribed = useIsUserSubscribed()
  const { preferences } = useActivityPreferences()
  const { handleSubscriptionStatusChange } = useSubscriptionStatusHandler()

  // Check for successful checkout session and handle welcome flow
  useEffect(() => {
    // Only show toast if we haven't shown it yet for this session
    if (!toastShown) {
      // Try to handle status change with the hook first
      const statusHandled = handleSubscriptionStatusChange()

      if (statusHandled && isSubscribed) {
        // Check if this is a new Pro subscriber (no activity preferences set)
        const isNewProSubscriber =
          !preferences ||
          (!preferences.eateries?.cuisineTypes?.length &&
            !preferences.shopping?.style?.length &&
            !preferences.entertainment?.venues?.length)

        if (isNewProSubscriber) {
          // Check if user has already seen the welcome flow
          const hasSeenWelcome =
            typeof window !== "undefined" &&
            localStorage.getItem("activity-preferences-welcome-shown") === "true"

          if (!hasSeenWelcome) {
            // Show welcome modal for new Pro subscribers instead of the regular toast
            setShowWelcomeModal(true)
          }
        }
        setToastShown(true)
      } else if (statusHandled) {
        // For cancellation or other status changes, just mark as shown
        setToastShown(true)
      }
    }
  }, [handleSubscriptionStatusChange, toastShown, isSubscribed, preferences])

  // Handle welcome modal close
  const handleWelcomeModalClose = useCallback(() => {
    setShowWelcomeModal(false)
    // Mark that user has seen the welcome flow
    if (typeof window !== "undefined") {
      localStorage.setItem("activity-preferences-welcome-shown", "true")
    }
  }, [])

  return (
    <>
      <OriginalBillingSettings />
      <ActivityPreferencesWelcomeModal
        isOpen={showWelcomeModal}
        onClose={handleWelcomeModalClose}
      />
    </>
  )
}
