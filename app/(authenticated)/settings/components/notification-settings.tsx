"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  <PERSON>,
  CardContent,
  CardDescription,
  Card<PERSON>ooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { toast } from "@/components/ui/use-toast"
import { Loader2 } from "lucide-react"
import { useUserPreferences } from "@/lib/domains/user-preferences/user-preferences.hooks"
import { useUser } from "@/lib/domains/auth/auth.hooks"

export function NotificationSettings() {
  const { preferences, loading: preferencesLoading, updatePreferences } = useUserPreferences()
  const user = useUser()

  // Local state
  const [localNotificationsEnabled, setLocalNotificationsEnabled] = useState(true)
  const [localEmailNotifications, setLocalEmailNotifications] = useState(true)
  const [localPushNotifications, setLocalPushNotifications] = useState(true)
  const [localTripUpdates, setLocalTripUpdates] = useState(true)
  const [localSquadMessages, setLocalSquadMessages] = useState(true)
  const [localInvitations, setLocalInvitations] = useState(true)
  const [localAISuggestions, setLocalAISuggestions] = useState(true)
  const [saving, setSaving] = useState(false)
  const [initialLoading, setInitialLoading] = useState(true)

  // Update local state when preferences change
  useEffect(() => {
    if (!preferencesLoading && preferences) {
      setLocalNotificationsEnabled(preferences.notificationsEnabled ?? true)
      setLocalEmailNotifications(preferences.emailNotifications ?? true)
      setLocalPushNotifications(preferences.pushNotifications ?? true)
      setLocalTripUpdates(preferences.tripUpdatesNotifications ?? true)
      setLocalSquadMessages(preferences.squadMessagesNotifications ?? true)
      setLocalInvitations(preferences.invitationNotifications ?? true)
      setLocalAISuggestions(preferences.aiSuggestionsNotifications ?? true)
      setInitialLoading(false)
    }
  }, [preferences, preferencesLoading])

  const saveNotificationSettings = async () => {
    setSaving(true)
    try {
      await updatePreferences({
        notificationsEnabled: localNotificationsEnabled,
        emailNotifications: localEmailNotifications,
        pushNotifications: localPushNotifications,
        tripUpdatesNotifications: localTripUpdates,
        squadMessagesNotifications: localSquadMessages,
        invitationNotifications: localInvitations,
        aiSuggestionsNotifications: localAISuggestions,
      })
    } catch (error) {
      console.error("Error updating notification settings:", error)
      toast({
        title: "Error",
        description: "Failed to update notification settings. Please try again.",
        variant: "destructive",
      })
    } finally {
      setSaving(false)
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Notification Settings</CardTitle>
        <CardDescription>Manage how you receive notifications</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {initialLoading ? (
          <div className="flex flex-col items-center justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin text-primary mb-4" />
            <p className="text-sm text-muted-foreground">Loading notification settings...</p>
          </div>
        ) : !user ? (
          <div className="flex flex-col items-center justify-center py-8">
            <p className="text-sm text-muted-foreground mb-4">
              Unable to load notification settings.
            </p>
            <p className="text-sm text-muted-foreground">
              Please sign in to view your notification settings.
            </p>
          </div>
        ) : (
          <>
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="notifications-enabled">All Notifications</Label>
                <p className="text-sm text-muted-foreground">Master toggle for all notifications</p>
              </div>
              <Switch
                id="notifications-enabled"
                checked={localNotificationsEnabled}
                onCheckedChange={setLocalNotificationsEnabled}
              />
            </div>

            <div className="border-t pt-4 mt-4">
              <h3 className="text-sm font-medium mb-3">Notification Channels</h3>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="email-notifications">Email Notifications</Label>
                    <p className="text-sm text-muted-foreground">Receive notifications via email</p>
                  </div>
                  <Switch
                    id="email-notifications"
                    checked={localEmailNotifications}
                    onCheckedChange={setLocalEmailNotifications}
                    disabled={!localNotificationsEnabled}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="push-notifications">Push Notifications</Label>
                    <p className="text-sm text-muted-foreground">
                      Receive notifications on your device
                    </p>
                  </div>
                  <Switch
                    id="push-notifications"
                    checked={localPushNotifications}
                    onCheckedChange={setLocalPushNotifications}
                    disabled={!localNotificationsEnabled}
                  />
                </div>
              </div>
            </div>

            <div className="border-t pt-4 mt-4">
              <h3 className="text-sm font-medium mb-3">Notification Types</h3>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="trip-updates">Trip Updates</Label>
                    <p className="text-sm text-muted-foreground">
                      Changes to trips, new activities, etc.
                    </p>
                  </div>
                  <Switch
                    id="trip-updates"
                    checked={localTripUpdates}
                    onCheckedChange={setLocalTripUpdates}
                    disabled={!localNotificationsEnabled}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="squad-messages">Squad Messages</Label>
                    <p className="text-sm text-muted-foreground">New messages in squad chats</p>
                  </div>
                  <Switch
                    id="squad-messages"
                    checked={localSquadMessages}
                    onCheckedChange={setLocalSquadMessages}
                    disabled={!localNotificationsEnabled}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="task-reminders">Task Reminders</Label>
                    <p className="text-sm text-muted-foreground">Reminders for assigned tasks</p>
                  </div>
                  <Switch
                    id="task-reminders"
                    checked={localInvitations}
                    onCheckedChange={setLocalInvitations}
                    disabled={!localNotificationsEnabled}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="ai-suggestions">AI Suggestions</Label>
                    <p className="text-sm text-muted-foreground">
                      Trip suggestions from the AI assistant
                    </p>
                  </div>
                  <Switch
                    id="ai-suggestions"
                    checked={localAISuggestions}
                    onCheckedChange={setLocalAISuggestions}
                    disabled={!localNotificationsEnabled}
                  />
                </div>
              </div>
            </div>
          </>
        )}
      </CardContent>
      <CardFooter>
        <Button onClick={saveNotificationSettings} disabled={saving || initialLoading || !user}>
          {saving ? (
            <>
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              Saving...
            </>
          ) : (
            "Save Notification Settings"
          )}
        </Button>
      </CardFooter>
    </Card>
  )
}
