"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Trash2, Loader2, X } from "lucide-react"
import {
  useUserData,
  useUserDataLoading,
  useUpdateUserData,
  useUser,
} from "@/lib/domains/auth/auth.hooks"
import { useProfilePictureManager } from "@/lib/domains/user/user.hooks"
import { ImageUploadWithCompression } from "@/components/ui/image-upload-with-compression"
import { toast } from "@/components/ui/use-toast"
import { getBestAvatar, getInitials } from "@/lib/utils/avatar-utils"

export function ProfileSettings() {
  const userData = useUserData()
  const userDataLoading = useUserDataLoading()
  const updateUser = useUpdateUserData()
  const user = useUser()
  const { uploadProfilePicture, removeProfilePicture, uploading, removing } =
    useProfilePictureManager()

  const [selectedFile, setSelectedFile] = useState<File | null>(null)

  const handleFileSelect = (file: File) => {
    setSelectedFile(file)
  }

  const handleUploadError = (error: string) => {
    toast({
      title: "Upload Error",
      description: error,
      variant: "destructive",
    })
  }

  const handleUpload = async () => {
    if (!selectedFile || !user?.uid) return

    try {
      const result = await uploadProfilePicture(selectedFile)

      if (!result.success) {
        toast({
          title: "Upload failed",
          description: result.error || "Failed to upload profile picture.",
          variant: "destructive",
        })
      } else {
        // Clear selected file on success
        setSelectedFile(null)
      }
      // Success toast is handled by the hook
    } catch (error) {
      console.error("Error uploading profile picture:", error)
      toast({
        title: "Upload failed",
        description: "Something went wrong. Please try again.",
        variant: "destructive",
      })
    }
  }

  const handleProfilePictureRemove = async () => {
    if (!user?.uid) return

    try {
      const result = await removeProfilePicture(userData?.photoURL || undefined)

      if (!result.success) {
        toast({
          title: "Removal failed",
          description: result.error || "Failed to remove profile picture.",
          variant: "destructive",
        })
      }
      // Success toast is handled by the hook
    } catch (error) {
      console.error("Error removing profile picture:", error)
      toast({
        title: "Removal failed",
        description: "Something went wrong. Please try again.",
        variant: "destructive",
      })
    }
  }

  const saveProfileChanges = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!userData || !user) return

    try {
      const formElement = e.target as HTMLFormElement
      const formData = new FormData(formElement)

      const updatedData = {
        displayName: formData.get("fullName") as string,
        bio: formData.get("bio") as string,
        // Email is managed by Google OAuth and cannot be changed
      }

      // Use the auth store's updateUserData function with showToast=true
      updateUser(updatedData)
    } catch (error) {
      console.error("Error updating profile:", error)
      // Toast is already shown by the updateUserData function
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Profile Information</CardTitle>
        <CardDescription>Update your personal information</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {userDataLoading ? (
          <div className="flex flex-col items-center justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin text-primary mb-4" />
            <p className="text-sm text-muted-foreground">Loading profile information...</p>
          </div>
        ) : (
          userData && (
            <form id="profileForm" onSubmit={saveProfileChanges}>
              <div className="flex flex-col md:flex-row gap-6">
                <div className="flex flex-col items-center gap-4">
                  <div className="relative">
                    <Avatar className="h-24 w-24">
                      <AvatarImage
                        src={getBestAvatar(userData?.photoURL, userData?.displayName, 96)}
                        alt={userData?.displayName || "User"}
                      />
                      <AvatarFallback>{getInitials(userData?.displayName)}</AvatarFallback>
                    </Avatar>
                    {(uploading || removing) && (
                      <div className="absolute inset-0 bg-black/50 rounded-full flex items-center justify-center">
                        <Loader2 className="h-6 w-6 animate-spin text-white" />
                      </div>
                    )}
                  </div>
                  <div className="space-y-3">
                    {!selectedFile ? (
                      <ImageUploadWithCompression
                        preset="profilePicture"
                        onFileSelect={handleFileSelect}
                        onError={handleUploadError}
                        showPreview={false}
                        showCompressionStats={false}
                        className="w-full"
                      />
                    ) : (
                      <div className="space-y-2">
                        <div className="flex items-center gap-2 p-2 bg-muted rounded-md">
                          <img
                            src={URL.createObjectURL(selectedFile)}
                            alt="Selected"
                            className="w-8 h-8 rounded object-cover"
                          />
                          <span className="text-sm font-medium">{selectedFile.name}</span>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => setSelectedFile(null)}
                            className="ml-auto"
                          >
                            <X className="h-4 w-4" />
                          </Button>
                        </div>
                        <Button onClick={handleUpload} disabled={uploading} className="w-full">
                          {uploading ? (
                            <>
                              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                              Uploading...
                            </>
                          ) : (
                            "Upload Profile Picture"
                          )}
                        </Button>
                      </div>
                    )}

                    {userData?.photoURL && (
                      <Button
                        variant="outline"
                        size="sm"
                        className="text-destructive w-full"
                        type="button"
                        disabled={uploading || removing}
                        onClick={handleProfilePictureRemove}
                      >
                        {removing ? (
                          <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                        ) : (
                          <Trash2 className="h-4 w-4 mr-2" />
                        )}
                        Remove Picture
                      </Button>
                    )}
                  </div>
                </div>

                <div className="flex-1 space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="fullName">Full Name</Label>
                      <Input
                        id="fullName"
                        name="fullName"
                        defaultValue={userData?.displayName || ""}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="email">Email</Label>
                      <Input
                        id="email"
                        name="email"
                        type="email"
                        defaultValue={userData?.email || ""}
                        disabled
                        className="bg-muted cursor-not-allowed"
                      />
                      <p className="text-xs text-muted-foreground">
                        Email is managed by your Google account and cannot be changed here.
                      </p>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="phone">Phone Number</Label>
                      <Input id="phone" name="phone" type="tel" defaultValue="" />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="bio">Bio</Label>
                    <textarea
                      id="bio"
                      name="bio"
                      rows={3}
                      defaultValue={userData?.bio || ""}
                      className="flex w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                    ></textarea>
                  </div>
                </div>
              </div>
            </form>
          )
        )}
      </CardContent>
      <CardFooter>
        <Button type="submit" form="profileForm" disabled={userDataLoading || !userData}>
          {userDataLoading ? "Saving..." : "Save Changes"}
        </Button>
      </CardFooter>
    </Card>
  )
}
