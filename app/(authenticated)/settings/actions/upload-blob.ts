"use server"

import { put, del } from "@vercel/blob"
import {
  optimizeImage,
  OPTIMIZATION_PRESETS,
  fileToBuffer,
  validateImageBuffer,
} from "@/lib/utils/server-image-optimization"
import { MAX_FILE_SIZE, ALLOWED_TYPES } from "@/lib/utils/image-compression"

export interface BlobUploadResult {
  success: boolean
  url?: string
  error?: string
}

/**
 * Upload a file to Vercel Blob storage
 * This is a simple blob upload without authentication - the calling code handles user updates
 */
export async function uploadBlobAction(
  formData: FormData,
  userId: string
): Promise<BlobUploadResult> {
  try {
    const file = formData.get("file") as File

    // Validate required fields
    if (!file) {
      return {
        success: false,
        error: "No file provided",
      }
    }

    if (!userId) {
      return {
        success: false,
        error: "User ID is required",
      }
    }

    // Validate file type
    if (!ALLOWED_TYPES.includes(file.type)) {
      return {
        success: false,
        error: "Invalid file type. Only JPEG, PNG, and WebP images are allowed.",
      }
    }

    // Validate file size (now 50MB limit)
    if (file.size > MAX_FILE_SIZE) {
      const maxSizeMB = Math.round(MAX_FILE_SIZE / (1024 * 1024))
      return {
        success: false,
        error: `File size too large. Maximum size is ${maxSizeMB}MB.`,
      }
    }

    // Check if Vercel Blob token is available
    if (!process.env.BLOB_READ_WRITE_TOKEN) {
      console.error("BLOB_READ_WRITE_TOKEN is not configured")
      return {
        success: false,
        error: "Storage service is not configured",
      }
    }

    // Generate a unique filename
    const timestamp = Date.now()
    const fileExtension = file.name.split(".").pop() || "jpg"
    const filename = `profile-pictures/${userId}/${timestamp}.${fileExtension}`

    try {
      // Convert file to buffer for processing
      const inputBuffer = await fileToBuffer(file)

      // Validate the image buffer
      const validation = await validateImageBuffer(inputBuffer)
      if (!validation.valid) {
        return {
          success: false,
          error: validation.error || "Invalid image file",
        }
      }

      // Optimize the image for profile picture use
      const optimizationResult = await optimizeImage(
        inputBuffer,
        OPTIMIZATION_PRESETS.profilePicture
      )

      if (!optimizationResult.success) {
        return {
          success: false,
          error: optimizationResult.error || "Failed to optimize image",
        }
      }

      // Create optimized file for upload
      const optimizedFile = new File(
        [optimizationResult.buffer!],
        filename.replace(/\.[^/.]+$/, ".webp"), // Change extension to webp
        { type: "image/webp" }
      )

      // Upload optimized image to Vercel Blob
      const blob = await put(filename.replace(/\.[^/.]+$/, ".webp"), optimizedFile, {
        access: "public",
        token: process.env.BLOB_READ_WRITE_TOKEN,
      })

      return {
        success: true,
        url: blob.url,
      }
    } catch (uploadError) {
      console.error("Vercel Blob upload error:", uploadError)

      let errorMessage = "Failed to upload image to storage"
      if (uploadError instanceof Error) {
        if (uploadError.message.includes("token")) {
          errorMessage = "Storage authentication failed"
        } else if (uploadError.message.includes("size")) {
          errorMessage = "File size exceeds storage limits"
        } else if (
          uploadError.message.includes("network") ||
          uploadError.message.includes("fetch")
        ) {
          errorMessage = "Network error during upload"
        }
      }

      return {
        success: false,
        error: errorMessage,
      }
    }
  } catch (error) {
    console.error("Blob upload error:", error)
    return {
      success: false,
      error: "Internal server error",
    }
  }
}

/**
 * Delete a file from Vercel Blob storage
 */
export async function deleteBlobAction(url: string): Promise<BlobUploadResult> {
  try {
    if (!url || !url.includes("blob.vercel-storage.com")) {
      return {
        success: false,
        error: "Invalid blob URL",
      }
    }

    if (!process.env.BLOB_READ_WRITE_TOKEN) {
      console.error("BLOB_READ_WRITE_TOKEN is not configured")
      return {
        success: false,
        error: "Storage service is not configured",
      }
    }

    try {
      await del(url, {
        token: process.env.BLOB_READ_WRITE_TOKEN,
      })

      return {
        success: true,
      }
    } catch (deleteError) {
      console.error("Vercel Blob delete error:", deleteError)
      return {
        success: false,
        error: "Failed to delete file from storage",
      }
    }
  } catch (error) {
    console.error("Blob delete error:", error)
    return {
      success: false,
      error: "Internal server error",
    }
  }
}
