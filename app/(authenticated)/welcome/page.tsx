"use client"

import { useEffect, useState, useCallback } from "react"
import { useRouter } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Skeleton } from "@/components/ui/skeleton"
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Refresh<PERSON><PERSON> } from "lucide-react"
import { useNewUserExperience } from "@/lib/domains/user/user.hooks"
import { useNewUserTripSuggestions } from "@/lib/domains/ai-suggestions/ai-suggestions-new-user-trips.hooks"
import { PageLoading } from "@/components/page-loading"
import { toast } from "@/components/ui/use-toast"
import { EnhancedWelcomeSuggestionCard } from "./components/enhanced-welcome-suggestion-card"
import { useAuthStore } from "@/lib/domains/auth/auth.store"
import { useUserStore } from "@/lib/domains/user/user.store"

export default function WelcomePage() {
  const router = useRouter()
  const authUser = useAuthStore((state) => state.user)
  const authLoading = useAuthStore((state) => state.loading)
  const appUser = useUserStore((state) => state.user)
  const appUserLoading = useUserStore((state) => state.loading)

  const { endNewUserExperience } = useNewUserExperience()
  const newUserSuggestionsHook = useNewUserTripSuggestions()
  const {
    suggestions,
    loading: suggestionsLoading,
    error: suggestionsError,
    loadSuggestions,
  } = newUserSuggestionsHook
  const showAiSuggestions = (newUserSuggestionsHook as any).showAiSuggestions

  const [selectedSuggestion, setSelectedSuggestion] = useState<number | null>(null)
  const [redirecting, setRedirecting] = useState(false)
  const [isNavigatingAway, setIsNavigatingAway] = useState(false)

  // Check if user should be on this page
  // useEffect(() => {
  //   if (appUserLoading || !appUser) return

  //   if (appUser.newUser === false && !isNavigatingAway) {
  //     router.push("/dashboard")
  //   }
  // }, [appUser, appUserLoading, router, isNavigatingAway])

  // Load suggestions when component mounts
  useEffect(() => {
    if (appUserLoading || !appUser) return

    if (appUser.newUser === false && !isNavigatingAway) {
      router.push("/dashboard")
    }

    if (authLoading || !authUser) return

    if (appUser?.newUser === true && !suggestionsLoading && !showAiSuggestions) {
      loadSuggestions()
    }
  }, [
    appUser,
    appUserLoading,
    router,
    isNavigatingAway,
    authUser,
    authLoading,
    suggestionsLoading,
    showAiSuggestions,
    loadSuggestions,
  ])

  const handleCreateTrip = useCallback(
    async (suggestionIndex: number) => {
      if (!authUser) return

      setRedirecting(true)
      setIsNavigatingAway(true)

      try {
        // Store selected suggestion index in sessionStorage for trip creation
        sessionStorage.setItem("selectedNewUserSuggestion", suggestionIndex.toString())

        // End new user experience
        await endNewUserExperience()

        // Redirect to squad creation with new user flag
        router.push("/squads/create?newUser=true")
      } catch (error) {
        console.error("Error handling create trip:", error)
        toast({
          title: "Error",
          description: "Something went wrong. Please try again.",
          variant: "destructive",
        })
        setRedirecting(false)
        setIsNavigatingAway(false)
      }
    },
    [authUser, endNewUserExperience, router]
  )

  const handleSkip = useCallback(async () => {
    if (!authUser) return

    setRedirecting(true)
    setIsNavigatingAway(true)

    try {
      // End new user experience
      await endNewUserExperience()

      toast({
        title: "Welcome to Togeda.ai!",
        description: "You can explore the app at your own pace.",
      })

      // Redirect to dashboard
      router.push("/dashboard")
    } catch (error) {
      console.error("Error handling skip:", error)
      toast({
        title: "Error",
        description: "Something went wrong. Please try again.",
        variant: "destructive",
      })
      setRedirecting(false)
      setIsNavigatingAway(false)
    }
  }, [authUser, endNewUserExperience, router])

  // Show loading while checking user status or redirecting
  if (appUserLoading || !appUser || appUser.newUser === false || redirecting) {
    return <PageLoading message="Loading..." />
  }

  // Show error if user is not authenticated
  if (!authUser) {
    return <PageLoading message="Authenticating..." />
  }

  return (
    <div className="min-h-screen bg-background flex items-center justify-center p-4">
      <div className="w-full max-w-6xl mx-auto">
        <div className="text-center mb-8">
          <div className="flex items-center justify-center mb-4">
            <Sparkles className="h-8 w-8 text-primary mr-2" />
            <h1 className="text-4xl font-bold text-foreground">Welcome to Togeda.ai!</h1>
          </div>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
            As an introduction to the app, we'll let you experience first hand what Togeda.ai is. As
            your first trip, here's what we suggest for your next adventure based on your
            preferences:
          </p>
        </div>

        {/* Trip Suggestions */}
        <div className="mb-8">
          {suggestionsLoading && (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {[1, 2, 3].map((i) => (
                <Card key={i} className="overflow-hidden">
                  <div className="aspect-video relative">
                    <Skeleton className="absolute inset-0" />
                  </div>
                  <CardContent className="pt-4">
                    <Skeleton className="h-6 w-3/4 mb-2" />
                    <Skeleton className="h-4 w-1/2 mb-4" />
                    <div className="flex gap-2 mb-4">
                      <Skeleton className="h-5 w-16" />
                      <Skeleton className="h-5 w-16" />
                    </div>
                    <Skeleton className="h-4 w-full mb-1" />
                    <Skeleton className="h-4 w-full mb-1" />
                    <Skeleton className="h-4 w-2/3 mb-4" />
                    <Skeleton className="h-10 w-full" />
                  </CardContent>
                </Card>
              ))}
            </div>
          )}

          {suggestionsError && (
            <div className="text-center py-8">
              <p className="text-destructive mb-4">{suggestionsError}</p>
              <Button onClick={() => loadSuggestions(true)} variant="outline">
                <RefreshCw className="h-4 w-4 mr-2" />
                Try Again
              </Button>
            </div>
          )}

          {showAiSuggestions && (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {suggestions.slice(0, 3).map((suggestion, index) => (
                <EnhancedWelcomeSuggestionCard
                  key={index}
                  suggestion={suggestion}
                  index={index}
                  isSelected={selectedSuggestion === index}
                  onClick={() => setSelectedSuggestion(index)}
                  onCreateTrip={handleCreateTrip}
                  redirecting={redirecting}
                />
              ))}
            </div>
          )}
        </div>

        {/* Action Buttons */}
        <div className="text-center">
          <Button
            variant="outline"
            size="lg"
            onClick={handleSkip}
            disabled={redirecting}
            className="border-muted-foreground/20 text-muted-foreground hover:text-foreground hover:bg-muted/50 transition-all duration-300"
          >
            <SkipForward className="h-5 w-5 mr-2" />I would like to explore the app on my own
          </Button>
        </div>
      </div>
    </div>
  )
}
