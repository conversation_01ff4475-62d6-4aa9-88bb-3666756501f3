"use client"

import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { ArrowRight } from "lucide-react"
import { OptimizedImage } from "@/components/optimized-image"
import { CachedTripSuggestion } from "@/lib/domains/ai-suggestions/ai-suggestions-cache.service"
import { useEnhancedSuggestionImage } from "@/lib/hooks/use-enhanced-suggestion-image"

interface EnhancedWelcomeSuggestionCardProps {
  suggestion: CachedTripSuggestion
  index: number
  isSelected: boolean
  onClick: () => void
  onCreateTrip: (index: number) => void
  redirecting: boolean
}

export function EnhancedWelcomeSuggestionCard({
  suggestion,
  index,
  isSelected,
  onClick,
  onCreateTrip,
  redirecting,
}: EnhancedWelcomeSuggestionCardProps) {
  const { imageUrl, isLoading, handleImageError } = useEnhancedSuggestionImage(
    suggestion,
    "300x150"
  )

  return (
    <Card
      key={index}
      className={`cursor-pointer transition-all duration-300 group ${
        isSelected ? "ring-2 ring-primary shadow-lg" : "hover:shadow-md"
      }`}
      onClick={onClick}
    >
      {/* Image Section */}
      <div className="aspect-video relative overflow-hidden">
        {imageUrl && !imageUrl.includes("placeholder.svg") ? (
          <OptimizedImage
            src={imageUrl}
            alt={suggestion.destination}
            aspectRatio="video"
            className={`object-cover transition-all duration-300 group-hover:scale-105 ${
              isLoading ? "opacity-75" : "opacity-100"
            }`}
            onError={handleImageError}
            fallbackSrc="/placeholder.svg?height=150&width=300"
          />
        ) : (
          <div className="w-full h-full bg-gradient-to-br from-primary/10 to-primary/20 flex items-center justify-center">
            <span className="text-primary text-lg font-medium">{suggestion.destination}</span>
          </div>
        )}
        {/* Overlay gradient for better text readability */}
        <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent" />
        {/* Attribution */}
        {suggestion.attribution && (
          <div className="absolute bottom-1 right-1 text-[10px] text-white/70 bg-black/30 px-1 py-0.5 rounded">
            {suggestion.attribution.name}
          </div>
        )}
      </div>

      {/* Content Section */}
      <CardContent className="p-4">
        <div className="space-y-3">
          {/* Destination and Budget */}
          <div>
            <h3 className="font-semibold text-lg text-foreground">{suggestion.destination}</h3>
            <p className="text-sm text-muted-foreground">{suggestion.budget}</p>
          </div>

          {/* Tags */}
          <div className="flex flex-wrap gap-1">
            {suggestion.tags.slice(0, 3).map((tag, tagIndex) => (
              <Badge key={tagIndex} variant="secondary" className="text-xs">
                {tag}
              </Badge>
            ))}
            {suggestion.tags.length > 3 && (
              <Badge variant="outline" className="text-xs">
                +{suggestion.tags.length - 3} more
              </Badge>
            )}
          </div>

          {/* Description */}
          <p className="text-sm text-muted-foreground line-clamp-2 mb-4">
            {suggestion.description}
          </p>

          {/* Budget Section - Separate on Desktop */}
          <div className="mb-4">
            <div className="hidden md:block">
              <div className="bg-muted/50 rounded-lg p-3 mb-4">
                <span className="text-sm font-medium text-foreground">
                  Budget: {suggestion.budget}
                </span>
              </div>
            </div>
            <div className="md:hidden">
              <span className="text-sm font-medium text-primary">Budget: {suggestion.budget}</span>
            </div>
          </div>

          {/* Action Button */}
          <Button
            size="sm"
            onClick={(e) => {
              e.stopPropagation()
              onCreateTrip(index)
            }}
            className="w-full bg-gradient-to-br from-[#FFD54F] to-[#FFB300] hover:from-[#FFCC02] hover:to-[#FF8F00] text-[#FFF8DC] [text-shadow:1px_1px_2px_rgba(0,0,0,0.5)] font-medium transition-all duration-300"
            disabled={redirecting}
          >
            Create This Trip
            <ArrowRight className="h-4 w-4 ml-2" />
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}
