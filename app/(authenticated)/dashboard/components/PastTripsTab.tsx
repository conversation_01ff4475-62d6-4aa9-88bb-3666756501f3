"use client"

import { useRef, useEffect } from "react"
import Link from "next/link"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Calendar, Star, MessageSquare, Loader2 } from "lucide-react"
import { OptimizedImage } from "@/components/optimized-image"
import { formatDateRange } from "./utils"
import { getTripImageUrl } from "@/lib/utils/trip-image-utils"
import { Squad } from "@/lib/domains/squad/squad.types"
import { Trip } from "@/lib/domains/trip/trip.types"
import { useUser } from "@/lib/domains/auth/auth.hooks"
import { usePaginatedPastTripsWithReviews } from "@/lib/domains/trip-review/trip-review.hooks"

interface PastTripsTabProps {
  squads: Squad[]
  pastTrips: Trip[]
  loading: boolean
}

export function PastTripsTab({ squads, pastTrips, loading }: PastTripsTabProps) {
  const user = useUser()
  const loadMoreRef = useRef<HTMLDivElement>(null)

  // Use the new paginated hook with 5 trips per page
  const {
    tripsWithReviews,
    loading: reviewsLoading,
    hasMore,
    error,
    loadMore,
  } = usePaginatedPastTripsWithReviews(pastTrips, user, 5)

  // Intersection Observer for infinite scroll
  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting && hasMore && !reviewsLoading) {
          loadMore()
        }
      },
      { threshold: 0.1 }
    )

    if (loadMoreRef.current) {
      observer.observe(loadMoreRef.current)
    }

    return () => {
      if (loadMoreRef.current) {
        observer.unobserve(loadMoreRef.current)
      }
    }
  }, [hasMore, reviewsLoading, loadMore])
  const renderStarRating = (rating: number, totalReviews: number) => {
    return (
      <div className="flex items-center gap-1">
        <div className="flex">
          {[...Array(5)].map((_, i) => (
            <Star
              key={i}
              className={`h-4 w-4 ${
                i < Math.round(rating) ? "text-yellow-400 fill-yellow-400" : "text-muted-foreground"
              }`}
            />
          ))}
        </div>
        {totalReviews > 0 && (
          <span className="text-xs text-muted-foreground">
            {rating.toFixed(1)} ({totalReviews})
          </span>
        )}
      </div>
    )
  }

  return (
    <div className="space-y-4">
      <h2 className="text-xl font-semibold">Past Trips</h2>

      {/* Error State */}
      {error && (
        <Card>
          <CardContent className="flex flex-col items-center justify-center p-6">
            <div className="w-12 h-12 rounded-full bg-destructive/10 flex items-center justify-center mb-4">
              <Calendar className="h-6 w-6 text-destructive" />
            </div>
            <p className="font-medium text-center">Error Loading Trips</p>
            <p className="text-sm text-muted-foreground text-center mt-1">{error.message}</p>
            <Button
              variant="outline"
              size="sm"
              className="mt-3"
              onClick={() => window.location.reload()}
            >
              Try Again
            </Button>
          </CardContent>
        </Card>
      )}

      {/* Trips Grid */}
      {tripsWithReviews.length > 0 ? (
        <>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {tripsWithReviews.map((trip, index) => {
              const isCompleted = trip.status === "completed"
              const hasReviewData = trip.reviewAggregate && trip.reviewAggregate.totalReviews > 0
              const needsReview = isCompleted && !trip.userHasReviewed

              return (
                <Card
                  key={`${trip.id}-${index}`}
                  className="h-full hover:shadow-md transition-shadow"
                >
                  <Link href={`/trips/${trip.id}`}>
                    <div className="aspect-video relative overflow-hidden rounded-t-lg">
                      <OptimizedImage
                        src={getTripImageUrl(trip, "400x200")}
                        alt={trip.destination}
                        aspectRatio="video"
                        className="rounded-t-lg"
                        priority
                      />
                      <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/60 to-transparent p-4">
                        <h3 className="text-white font-bold">{trip.destination}</h3>
                        <p className="text-white/80 text-sm">
                          {formatDateRange(trip.startDate, trip.endDate)}
                        </p>
                      </div>
                      {needsReview && (
                        <div className="absolute top-2 right-2">
                          <Badge className="bg-yellow-500 text-yellow-900 hover:bg-yellow-600">
                            Review Needed
                          </Badge>
                        </div>
                      )}
                    </div>
                  </Link>
                  <CardContent className="pt-4 space-y-3">
                    <div className="flex justify-between items-center">
                      <Badge>
                        {squads.find((s) => s.id === trip.squadId)?.name || "Unknown Squad"}
                      </Badge>
                      {hasReviewData ? (
                        renderStarRating(
                          trip.reviewAggregate!.averageRating,
                          trip.reviewAggregate!.totalReviews
                        )
                      ) : isCompleted ? (
                        <span className="text-xs text-muted-foreground">No reviews yet</span>
                      ) : (
                        <div className="flex">
                          {[...Array(5)].map((_, i) => (
                            <Star key={i} className="h-4 w-4 text-muted-foreground" />
                          ))}
                        </div>
                      )}
                    </div>
                    <p className="text-sm text-muted-foreground">
                      {trip.description || "No description available"}
                    </p>

                    {/* Review Actions for Completed Trips */}
                    {isCompleted && (
                      <div className="flex gap-2 pt-2">
                        {needsReview ? (
                          <Link href={`/trips/${trip.id}/review`} className="flex-1">
                            <Button size="sm" className="w-full">
                              <Star className="h-3 w-3 mr-1" />
                              Review Trip
                            </Button>
                          </Link>
                        ) : (
                          <Link href={`/trips/${trip.id}/review`} className="flex-1">
                            <Button variant="outline" size="sm" className="w-full">
                              <Star className="h-3 w-3 mr-1" />
                              View Review
                            </Button>
                          </Link>
                        )}
                        <Link href={`/trips/${trip.id}?tab=chat`}>
                          <Button variant="outline" size="sm">
                            <MessageSquare className="h-3 w-3" />
                          </Button>
                        </Link>
                      </div>
                    )}
                  </CardContent>
                </Card>
              )
            })}
          </div>

          {/* Infinite Scroll Trigger */}
          <div ref={loadMoreRef} className="flex justify-center py-4">
            {reviewsLoading && (
              <div className="flex items-center gap-2">
                <Loader2 className="h-4 w-4 animate-spin" />
                <span className="text-sm text-muted-foreground">Loading more trips...</span>
              </div>
            )}
            {!hasMore && tripsWithReviews.length > 0 && (
              <span className="text-sm text-muted-foreground">No more trips to load</span>
            )}
          </div>
        </>
      ) : loading || (reviewsLoading && tripsWithReviews.length === 0) ? (
        <Card>
          <CardContent className="flex flex-col items-center justify-center p-6">
            <div className="w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center mb-4">
              <Calendar className="h-6 w-6 text-primary animate-pulse" />
            </div>
            <p className="font-medium text-center">Loading Past Trips...</p>
            <p className="text-sm text-muted-foreground text-center mt-1">
              Please wait while we load your trip history
            </p>
          </CardContent>
        </Card>
      ) : (
        <Card>
          <CardContent className="flex flex-col items-center justify-center p-6">
            <div className="w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center mb-4">
              <Calendar className="h-6 w-6 text-primary" />
            </div>
            <p className="font-medium text-center">No Past Trips</p>
            <p className="text-sm text-muted-foreground text-center mt-1">
              Your completed trips will appear here
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
