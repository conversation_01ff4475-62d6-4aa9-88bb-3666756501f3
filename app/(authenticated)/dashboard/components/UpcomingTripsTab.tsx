"use client"

import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Users, Compass, Clock, Plus } from "lucide-react"
import { OptimizedImage } from "@/components/optimized-image"
import { formatDateRange } from "./utils"
import { Squad } from "@/lib/domains/squad/squad.types"
import { Trip } from "@/lib/domains/trip/trip.types"
import { useValidatedTripImageUrl } from "@/lib/hooks/use-validated-image-url"

// Trip Card Component with validation for dashboard
function DashboardTripCard({
  trip,
  squads,
  tripsAttendeesDetails,
  formatDateRange,
  index = 0,
}: {
  trip: Trip
  squads: Squad[]
  tripsAttendeesDetails: any
  formatDateRange: (start: any, end: any) => string
  index?: number
}) {
  // Disable validation for dashboard to improve LCP performance
  const { imageUrl, isValidating } = useValidatedTripImageUrl(trip, "400x200", false)

  return (
    <Link href={`/trips/${trip.id}`} key={trip.id}>
      <Card className="h-full hover:shadow-md transition-shadow">
        <div className="aspect-video relative overflow-hidden rounded-t-lg">
          <OptimizedImage
            src={imageUrl}
            alt={trip.destination}
            aspectRatio="video"
            className={`rounded-t-lg transition-opacity duration-300 ${
              isValidating ? "opacity-75" : "opacity-100"
            }`}
            priority={index === 0} // Prioritize first image for LCP optimization
            quality={60} // Reduced quality for faster loading
            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 33vw, 25vw" // More aggressive sizing
          />
          <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/60 to-transparent p-4">
            <h3 className="text-white font-bold">{trip.destination}</h3>
            <p className="text-white/80 text-sm">{formatDateRange(trip.startDate, trip.endDate)}</p>
          </div>
        </div>
        <CardContent className="pt-4">
          <div className="flex justify-between items-center mb-2">
            <Badge>{squads.find((s) => s.id === trip.squadId)?.name || "Unknown Squad"}</Badge>
            <div className="text-sm text-muted-foreground flex items-center">
              <Users className="h-3 w-3 mr-1" />{" "}
              {Array.isArray(trip.attendees) ? trip.attendees.length : 0}
            </div>
          </div>
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <Clock className="h-4 w-4 text-muted-foreground" />
              <div className="text-sm">
                <span className="font-medium">{trip.tasksCompleted || 0}</span>
                <span className="text-muted-foreground">
                  {" "}
                  / {trip.totalTasks || 0} tasks completed
                </span>
              </div>
            </div>
            {/* Attendee Avatars */}
            {tripsAttendeesDetails[trip.id] && tripsAttendeesDetails[trip.id].length > 0 && (
              <div className="flex items-center gap-2">
                <div className="flex -space-x-2">
                  {tripsAttendeesDetails[trip.id]
                    .filter((attendee: any) => attendee.status === "going")
                    .slice(0, 4)
                    .map((attendee: any) => (
                      <Avatar key={attendee.userId} className="h-6 w-6 border-2 border-background">
                        <AvatarImage
                          src={getBestAvatar(attendee.user.photoURL, attendee.user.displayName, 24)}
                          alt={attendee.user.displayName || "User"}
                        />
                        <AvatarFallback className="text-xs">
                          {getInitials(attendee.user.displayName)}
                        </AvatarFallback>
                      </Avatar>
                    ))}
                  {tripsAttendeesDetails[trip.id].filter(
                    (attendee: any) => attendee.status === "going"
                  ).length > 4 && (
                    <div className="h-6 w-6 rounded-full bg-muted border-2 border-background flex items-center justify-center">
                      <span className="text-xs text-muted-foreground">
                        +
                        {tripsAttendeesDetails[trip.id].filter(
                          (attendee: any) => attendee.status === "going"
                        ).length - 4}
                      </span>
                    </div>
                  )}
                </div>
                <span className="text-xs text-muted-foreground">
                  {tripsAttendeesDetails[trip.id].filter(
                    (attendee: any) => attendee.status === "going"
                  ).length > 0
                    ? "going"
                    : ""}
                </span>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </Link>
  )
}
import { User, UserTrip } from "@/lib/domains"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { getBestAvatar, getInitials } from "@/lib/utils/avatar-utils"
import { useRealtimeTripsAttendeesDetails } from "@/lib/domains/user-trip/user-trip.realtime.hooks"
import { useMemo } from "react"

interface UpcomingTripsTabProps {
  squads: Squad[]
  upcomingTrips: Trip[]
  loading: boolean
}

export function UpcomingTripsTab({ squads, upcomingTrips, loading }: UpcomingTripsTabProps) {
  // Get trip IDs for attendee details - only load when this component is rendered
  const upcomingTripIds = useMemo(() => upcomingTrips.map((trip) => trip.id), [upcomingTrips])

  // Get attendee details for upcoming trips
  const { tripsAttendeesDetails } = useRealtimeTripsAttendeesDetails(upcomingTripIds)
  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-semibold">Upcoming Trips</h2>
        <Link href="/trips/create">
          <Button>
            <Plus className="mr-2 h-4 w-4" /> Create Trip
          </Button>
        </Link>
      </div>

      {upcomingTrips.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {upcomingTrips.map((trip, index) => (
            <DashboardTripCard
              key={trip.id}
              trip={trip}
              squads={squads}
              tripsAttendeesDetails={tripsAttendeesDetails}
              formatDateRange={formatDateRange}
              index={index}
            />
          ))}
        </div>
      ) : (
        <Card>
          <CardContent className="flex flex-col items-center justify-center p-6">
            <div className="w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center mb-4">
              <Compass className="h-6 w-6 text-primary" />
            </div>
            <p className="font-medium text-center">No Upcoming Trips</p>
            <p className="text-sm text-muted-foreground text-center mt-1 mb-4">
              Create a squad and start planning your next adventure
            </p>
            <Link href="/squads/create">
              <Button>Create a Squad</Button>
            </Link>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
