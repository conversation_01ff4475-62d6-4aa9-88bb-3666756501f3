import { Timestamp } from "firebase/firestore"

/**
 * Format a date range for display
 * @param startDate Start date (Timestamp or date string)
 * @param endDate End date (Timestamp or date string)
 * @returns Formatted date range string
 */
export function formatDateRange(
  startDate?: Timestamp | string | Date,
  endDate?: Timestamp | string | Date
) {
  if (!startDate || !endDate) return "TBD"

  const start =
    startDate instanceof Timestamp
      ? startDate.toDate()
      : startDate instanceof Date
        ? startDate
        : new Date(startDate)

  const end =
    endDate instanceof Timestamp
      ? endDate.toDate()
      : endDate instanceof Date
        ? endDate
        : new Date(endDate)

  return `${start.toLocaleDateString("en-US", { month: "short", day: "numeric" })} - ${end.toLocaleDateString("en-US", { month: "short", day: "numeric" })}`
}
