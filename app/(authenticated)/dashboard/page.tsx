"use client"

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/tabs"
import { DashboardSkeleton } from "./components/DashboardSkeleton"
import { useRealtimeUserSquads } from "@/lib/domains/squad/squad.realtime.hooks"
import { useRealtimeUserAllTrips } from "@/lib/domains/trip/trip.realtime.hooks"
import { Suspense, lazy, useState, useEffect, useCallback } from "react"
import { useUserStore } from "@/lib/domains/user/user.store"
import { useDemoTour } from "@/lib/domains/user/user.hooks"
import { DemoTourModal } from "@/components/demo-tour-modal"

// Lazy load tab components to reduce initial bundle size
const SquadsTab = lazy(() =>
  import("./components/SquadsTab").then((module) => ({ default: module.SquadsTab }))
)
const UpcomingTripsTab = lazy(() =>
  import("./components/UpcomingTripsTab").then((module) => ({ default: module.UpcomingTripsTab }))
)
const PastTripsTab = lazy(() =>
  import("./components/PastTripsTab").then((module) => ({ default: module.PastTripsTab }))
)

// Tab loading skeleton component
const TabSkeleton = () => (
  <div className="space-y-4">
    <div className="h-6 w-32 bg-muted animate-pulse rounded" />
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      {[...Array(3)].map((_, i) => (
        <div key={i} className="h-64 bg-muted animate-pulse rounded-lg" />
      ))}
    </div>
  </div>
)

export default function DashboardPage() {
  const user = useUserStore((state) => state.user)
  const { hasOptedOut, checking: demoTourChecking, refreshStatus } = useDemoTour()
  const [showDemoTour, setShowDemoTour] = useState(false)

  // Get squads with real-time updates
  const { squads, loading: squadsLoading } = useRealtimeUserSquads()

  // Get all trips with real-time updates
  const { upcomingTrips, pastTrips, loading: tripsLoading } = useRealtimeUserAllTrips()

  // Determine overall loading state
  const isLoading = squadsLoading || tripsLoading

  // Demo tour logic
  useEffect(() => {
    console.log("Dashboard: Demo tour effect triggered", {
      user: !!user,
      hasOptedOut,
      demoTourChecking,
      showDemoTour,
    })

    // Only show demo tour if user is loaded, hasn't opted out, and we're not checking
    if (user && hasOptedOut === false && !demoTourChecking && !showDemoTour) {
      // For new users: show after they complete the trip suggestion flow (when newUser becomes false)
      // For existing users: show on login (when they reach dashboard)
      // The key is that hasOptedOut === false means they haven't seen it or opted out
      console.log("Dashboard: Showing demo tour")
      setShowDemoTour(true)
    } else if (hasOptedOut === true && showDemoTour) {
      // If user has opted out, make sure modal is closed
      console.log("Dashboard: Hiding demo tour (user opted out)")
      setShowDemoTour(false)
    }
  }, [user, hasOptedOut, demoTourChecking, showDemoTour])

  // Handle demo tour close - this will be called when user opts out
  const handleDemoTourClose = useCallback(async () => {
    setShowDemoTour(false)
    // Refresh the demo tour status to make sure we have the latest data
    await refreshStatus()
  }, [refreshStatus])

  if (isLoading && !squads.length && !upcomingTrips.length && !pastTrips.length) {
    return <DashboardSkeleton />
  }

  return (
    <>
      <div className="p-6">
        <div className="mb-6">
          <h1 className="text-3xl font-bold">Dashboard</h1>
          <p className="text-muted-foreground">Welcome back, {user?.displayName || "Friend"}!</p>
        </div>

        <Tabs defaultValue="trips" className="space-y-4">
          <TabsList className="w-full">
            <TabsTrigger value="trips" className="flex-1 md:flex-initial">
              Upcoming Trips
            </TabsTrigger>
            <TabsTrigger value="squads" className="flex-1 md:flex-initial">
              My Squads
            </TabsTrigger>

            <TabsTrigger value="past" className="flex-1 md:flex-initial">
              Past Trips
            </TabsTrigger>
          </TabsList>

          <TabsContent value="squads">
            <Suspense fallback={<TabSkeleton />}>
              <SquadsTab squads={squads} upcomingTrips={upcomingTrips} loading={isLoading} />
            </Suspense>
          </TabsContent>

          <TabsContent value="trips">
            <Suspense fallback={<TabSkeleton />}>
              <UpcomingTripsTab squads={squads} upcomingTrips={upcomingTrips} loading={isLoading} />
            </Suspense>
          </TabsContent>

          <TabsContent value="past">
            <Suspense fallback={<TabSkeleton />}>
              <PastTripsTab squads={squads} pastTrips={pastTrips} loading={isLoading} />
            </Suspense>
          </TabsContent>
        </Tabs>
      </div>

      {/* Demo Tour Modal */}
      <DemoTourModal isOpen={showDemoTour} onClose={handleDemoTourClose} />
    </>
  )
}
