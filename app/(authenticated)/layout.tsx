"use client"

import { useEffect } from "react"
import { useRouter, usePathname } from "next/navigation"
import { useAuthStatus } from "@/lib/domains/auth/auth.hooks"
import { useAuthStore } from "@/lib/domains/auth/auth.store"
import { useUserStore } from "@/lib/domains/user/user.store"
import { useToast } from "@/components/ui/use-toast"

import { PageLoading } from "@/components/page-loading"
import { SubscriptionInitializer } from "@/components/subscription-initializer"
import { UserDataInitializer } from "@/components/user-data-initializer"
import { SettingsInitializer } from "@/components/settings-initializer"

import UserPreferencesInitializer from "@/providers/UserPreferencesInitializer"
import UserSubscriptionInitializer from "@/providers/UserSubscriptionInitializer"
import UserInitializer from "@/providers/UserInitializer"

import { AppHeader } from "@/components/app-header"
import { AppSidebar } from "@/components/app-sidebar"
import { AppFooter } from "@/components/app-footer"

export default function AuthenticatedLayout({ children }: { children: React.ReactNode }) {
  const user = useAuthStore((state) => state.user)
  const authLoading = useAuthStore((state) => state.loading)
  const appUser = useUserStore((state) => state.user)
  const appUserLoading = useUserStore((state) => state.loading)

  const { toast } = useToast()
  const router = useRouter()
  const pathname = usePathname()

  const fullscreenRoutes = [
    "/complete-profile",
    "/welcome",
    "/admin", // Admin routes have their own layout
  ]

  // Check if current route should be fullscreen
  const isFullscreenRoute = fullscreenRoutes.some((route) => pathname.startsWith(route))

  useEffect(() => {
    // Only redirect if we're not loading and there's no user
    if (!authLoading && !user) {
      toast({
        title: "Authentication required",
        description: "You must be logged in to access this page.",
        variant: "warning",
      })
      router.push("/login")
    }
  }, [user, authLoading, router])

  // Handle new user experience routing (using cached data from auth store)
  useEffect(() => {
    // Skip if still loading auth or no user
    if (authLoading || appUserLoading || !user || !appUser) return

    // Skip new user check for admin routes (they have their own flow)
    if (pathname.startsWith("/admin")) return
    // Route based on cached new user status

    if (appUser.newUser === true && pathname !== "/welcome") {
      // New user trying to access any route other than welcome → redirect to welcome
      toast({
        title: "Welcome to Togeda!",
        description: "Let's get you started with a quick tour.",
        variant: "default",
      })
      router.push("/welcome")
    } else if (appUser.newUser === false && pathname === "/welcome") {
      // Existing user trying to access welcome → redirect to dashboard
      toast({
        title: "Welcome back!",
        description: "You've already completed the welcome experience.",
        variant: "default",
      })
      router.push("/dashboard")
    }
  }, [user, authLoading, appUser, appUserLoading, pathname, router, toast])

  // Show loading state while checking authentication
  if (authLoading) {
    return <PageLoading message="Checking authentication..." />
  }

  // User is authenticated, render the children with domain-specific initializers
  return (
    <>
      {/* Initialize domain-specific stores */}
      <UserDataInitializer />
      <SubscriptionInitializer />
      <SettingsInitializer />

      <UserInitializer />
      <UserSubscriptionInitializer />
      <UserPreferencesInitializer />

      {isFullscreenRoute ? (
        // Fullscreen layout for special pages
        children
      ) : (
        // Standard layout with header, sidebar, and footer
        <div className="min-h-screen flex flex-col">
          <AppHeader />
          <div className="pt-16 flex flex-1">
            <AppSidebar />
            <main className="flex-1">{children}</main>
          </div>
          <AppFooter />
        </div>
      )}
    </>
  )
}
