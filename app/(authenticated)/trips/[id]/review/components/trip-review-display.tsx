"use client"

import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { Star, Calendar } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { TripReview } from "@/lib/domains/trip-review/trip-review.types"
import { Trip } from "@/lib/domains/trip/trip.types"

interface TripReviewDisplayProps {
  review: TripReview
  trip: Trip
}

export function TripReviewDisplay({ review, trip }: TripReviewDisplayProps) {
  const router = useRouter()

  const handleBackToTrips = () => {
    router.push("/trips")
  }

  return (
    <div className="space-y-6">
      {/* Success Message */}
      <Card className="border-green-200 bg-green-50">
        <CardContent className="pt-6">
          <div className="text-center space-y-2">
            <div className="text-green-600 text-lg font-semibold">✅ Review Submitted</div>
            <p className="text-green-700 text-sm">
              Thank you for sharing your experience! Your review helps your squad plan better future
              trips.
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Review Display */}
      <Card className="w-full max-w-2xl mx-auto">
        <CardHeader>
          <CardTitle>Your Review</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* User Info and Date */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <Avatar className="h-10 w-10">
                <AvatarImage src={review.userPhotoURL || ""} alt={review.userDisplayName || ""} />
                <AvatarFallback>
                  {review.userDisplayName?.charAt(0).toUpperCase() || "U"}
                </AvatarFallback>
              </Avatar>
              <div>
                <p className="font-medium">{review.userDisplayName || "Anonymous"}</p>
                <p className="text-sm text-muted-foreground">Your review</p>
              </div>
            </div>
            <div className="flex items-center space-x-2 text-sm text-muted-foreground">
              <Calendar className="h-4 w-4" />
              <span>
                {review.reviewDate?.toDate?.()?.toLocaleDateString() ||
                  review.createdAt?.toDate?.()?.toLocaleDateString() ||
                  "Recently"}
              </span>
            </div>
          </div>

          {/* Rating */}
          <div className="space-y-2">
            <h3 className="font-medium">Rating</h3>
            <div className="flex items-center space-x-2">
              <div className="flex items-center space-x-1">
                {[1, 2, 3, 4, 5].map((star) => (
                  <Star
                    key={star}
                    className={`h-5 w-5 ${
                      star <= review.rating
                        ? "text-yellow-400 fill-yellow-400"
                        : "text-muted-foreground"
                    }`}
                  />
                ))}
              </div>
              <span className="text-sm text-muted-foreground">{review.rating} out of 5 stars</span>
            </div>
          </div>

          {/* Feedback */}
          <div className="space-y-2">
            <h3 className="font-medium">Feedback</h3>
            <div className="bg-muted/30 rounded-lg p-4">
              <p className="text-sm leading-relaxed whitespace-pre-wrap">{review.feedback}</p>
            </div>
          </div>

          {/* Note about immutability */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
            <p className="text-blue-700 text-xs">
              <strong>Note:</strong> Reviews cannot be edited or deleted once submitted. This
              ensures the authenticity and integrity of all feedback.
            </p>
          </div>

          {/* Back Button */}
          <div className="pt-4 border-t">
            <Button onClick={handleBackToTrips} className="w-full">
              Back to Trips
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
