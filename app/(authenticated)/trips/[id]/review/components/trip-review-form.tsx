"use client"

import { useState, useTransition } from "react"
import { useR<PERSON><PERSON> } from "next/navigation"
import { Star } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { useToast } from "@/hooks/use-toast"
import { Trip } from "@/lib/domains/trip/trip.types"
import { TRIP_REVIEW_CONSTRAINTS } from "@/lib/domains/trip-review/trip-review.types"
import { AuthService } from "@/lib/domains/auth/auth.service"
import { submitTripReview } from "../actions/submit-review"

interface TripReviewFormProps {
  trip: Trip
}

export function TripReviewForm({ trip }: TripReviewFormProps) {
  const [rating, setRating] = useState(0)
  const [hoveredRating, setHoveredRating] = useState(0)
  const [feedback, setFeedback] = useState("")
  const [isPending, startTransition] = useTransition()
  const { toast } = useToast()
  const router = useRouter()

  const handleSubmit = async (formData: FormData) => {
    startTransition(async () => {
      try {
        // Get auth token
        const authToken = await AuthService.getAuthToken()
        if (!authToken) {
          toast({
            title: "Error",
            description: "You must be logged in to submit a review",
            variant: "destructive",
          })
          return
        }

        const result = await submitTripReview(trip.id, authToken, formData)

        if (result.success) {
          toast({
            title: "Review Submitted!",
            description: "Thank you for sharing your experience with your squad.",
          })
          // Redirect to trip page after successful submission
          router.push(`/trips/${trip.id}`)
        } else {
          toast({
            title: "Error",
            description: result.error || "Failed to submit review",
            variant: "destructive",
          })
        }
      } catch (error) {
        toast({
          title: "Error",
          description: "An unexpected error occurred",
          variant: "destructive",
        })
      }
    })
  }

  const handleBackToTrips = () => {
    router.push("/trips")
  }

  const isFormValid =
    rating > 0 &&
    feedback.trim().length >= TRIP_REVIEW_CONSTRAINTS.FEEDBACK_MIN_LENGTH &&
    feedback.trim().length <= TRIP_REVIEW_CONSTRAINTS.FEEDBACK_MAX_LENGTH

  return (
    <Card className="w-full max-w-2xl mx-auto shadow-lg">
      <CardHeader className="text-center pb-6">
        <CardTitle className="text-2xl">Share Your Experience</CardTitle>
        <p className="text-muted-foreground">
          Help your squad by sharing what made this trip special
        </p>
      </CardHeader>
      <CardContent>
        <form action={handleSubmit} className="space-y-6">
          {/* Star Rating */}
          <div className="space-y-4 text-center">
            <div className="space-y-2">
              <Label htmlFor="rating" className="text-lg font-medium">
                How would you rate this trip overall?
              </Label>
              <p className="text-sm text-muted-foreground">
                Click the stars to rate your experience
              </p>
            </div>
            <div className="flex items-center justify-center space-x-2">
              {[1, 2, 3, 4, 5].map((star) => (
                <button
                  key={star}
                  type="button"
                  className="p-2 hover:scale-110 transition-all duration-200 rounded-full hover:bg-muted/50"
                  onMouseEnter={() => setHoveredRating(star)}
                  onMouseLeave={() => setHoveredRating(0)}
                  onClick={() => setRating(star)}
                >
                  <Star
                    className={`h-10 w-10 transition-colors duration-200 ${
                      star <= (hoveredRating || rating)
                        ? "text-yellow-400 fill-yellow-400"
                        : "text-muted-foreground hover:text-yellow-300"
                    }`}
                  />
                </button>
              ))}
            </div>
            {rating > 0 && (
              <p className="text-lg font-medium text-yellow-600">
                {rating} star{rating !== 1 ? "s" : ""} -{" "}
                {rating === 5
                  ? "Excellent!"
                  : rating === 4
                    ? "Great!"
                    : rating === 3
                      ? "Good"
                      : rating === 2
                        ? "Fair"
                        : "Poor"}
              </p>
            )}
            <input type="hidden" name="rating" value={rating} />
          </div>

          {/* Feedback */}
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="feedback" className="text-lg font-medium">
                Tell us about your experience
              </Label>
              <p className="text-sm text-muted-foreground">
                Share what made this trip special, memorable moments, or suggestions for future
                trips
              </p>
            </div>
            <Textarea
              id="feedback"
              name="feedback"
              placeholder="What were the highlights of your trip? What would you recommend to others? Any suggestions for improvement?"
              value={feedback}
              onChange={(e) => setFeedback(e.target.value)}
              className="min-h-[140px] resize-none text-base"
              maxLength={TRIP_REVIEW_CONSTRAINTS.FEEDBACK_MAX_LENGTH}
            />
            <div className="flex justify-between text-xs text-muted-foreground">
              <span>Minimum {TRIP_REVIEW_CONSTRAINTS.FEEDBACK_MIN_LENGTH} characters required</span>
              <span
                className={
                  feedback.length > TRIP_REVIEW_CONSTRAINTS.FEEDBACK_MAX_LENGTH * 0.9
                    ? "text-orange-500"
                    : ""
                }
              >
                {feedback.length}/{TRIP_REVIEW_CONSTRAINTS.FEEDBACK_MAX_LENGTH}
              </span>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-3 pt-6 border-t">
            <Button type="submit" disabled={!isFormValid || isPending} className="flex-1">
              {isPending ? "Submitting..." : "Submit Review"}
            </Button>
            <Button
              type="button"
              variant="outline"
              onClick={handleBackToTrips}
              disabled={isPending}
              className="flex-1"
            >
              Back to Trips
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  )
}
