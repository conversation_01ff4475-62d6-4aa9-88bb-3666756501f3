"use server"

import { revalidatePath } from "next/cache"
import { redirect } from "next/navigation"
import { TRIP_REVIEW_CONSTRAINTS } from "@/lib/domains/trip-review/trip-review.types"
import { verifyAuthToken, getAdminInstance } from "@/lib/firebase-admin"

export interface SubmitReviewResult {
  success: boolean
  error?: string
  reviewId?: string
}

export async function submitTripReview(
  tripId: string,
  authToken: string,
  formData: FormData
): Promise<SubmitReviewResult> {
  try {
    // Verify the auth token
    const authResult = await verifyAuthToken(authToken)
    if (!authResult.isValid || !authResult.uid) {
      return {
        success: false,
        error: "You must be logged in to submit a review",
      }
    }

    const userId = authResult.uid

    // Extract form data
    const rating = parseInt(formData.get("rating") as string)
    const feedback = (formData.get("feedback") as string)?.trim()

    // Validate form data
    if (
      !rating ||
      rating < TRIP_REVIEW_CONSTRAINTS.RATING_MIN ||
      rating > TRIP_REVIEW_CONSTRAINTS.RATING_MAX
    ) {
      return {
        success: false,
        error: `Rating must be between ${TRIP_REVIEW_CONSTRAINTS.RATING_MIN} and ${TRIP_REVIEW_CONSTRAINTS.RATING_MAX} stars`,
      }
    }

    if (!feedback || feedback.length < TRIP_REVIEW_CONSTRAINTS.FEEDBACK_MIN_LENGTH) {
      return {
        success: false,
        error: `Feedback must be at least ${TRIP_REVIEW_CONSTRAINTS.FEEDBACK_MIN_LENGTH} characters`,
      }
    }

    if (feedback.length > TRIP_REVIEW_CONSTRAINTS.FEEDBACK_MAX_LENGTH) {
      return {
        success: false,
        error: `Feedback must be no more than ${TRIP_REVIEW_CONSTRAINTS.FEEDBACK_MAX_LENGTH} characters`,
      }
    }

    // Get Firebase Admin instance
    const { adminDb, adminFieldValue } = await getAdminInstance()
    if (!adminDb || !adminFieldValue) {
      return {
        success: false,
        error: "Server configuration error",
      }
    }

    // Verify trip exists and get trip data
    const tripDoc = await adminDb.collection("trips").doc(tripId).get()
    if (!tripDoc.exists) {
      return {
        success: false,
        error: "Trip not found",
      }
    }

    const trip = tripDoc.data()

    // Verify trip is completed
    if (trip?.status !== "completed") {
      return {
        success: false,
        error: "You can only review completed trips",
      }
    }

    // Verify user is a trip attendee
    const userTripQuery = await adminDb
      .collection("userTrips")
      .where("userId", "==", userId)
      .where("tripId", "==", tripId)
      .get()

    if (userTripQuery.empty) {
      return {
        success: false,
        error: "You must be a trip attendee to submit a review",
      }
    }

    const userTripDoc = userTripQuery.docs[0]
    const userTripStatus = userTripDoc.data()

    if (userTripStatus?.status !== "going") {
      return {
        success: false,
        error: "You must be a trip attendee to submit a review",
      }
    }

    // Check if user has already reviewed this trip
    const existingReviewQuery = await adminDb
      .collection("trips")
      .doc(tripId)
      .collection("reviews")
      .where("userId", "==", userId)
      .get()

    if (!existingReviewQuery.empty) {
      return {
        success: false,
        error: "You have already reviewed this trip",
      }
    }

    // Get user data for display information
    const userDoc = await adminDb.collection("users").doc(userId).get()
    let userDisplayName = authResult.email // Fallback to email
    let userPhotoURL = null

    if (userDoc.exists) {
      const userData = userDoc.data()
      userDisplayName = userData?.displayName || authResult.email
      userPhotoURL = userData?.photoURL || null
    }

    // Sync the trip's attendees array to ensure Firestore security rules work
    // Get all attendees from userTrips collection
    const userTripsQuery = await adminDb
      .collection("userTrips")
      .where("tripId", "==", tripId)
      .where("status", "==", "going")
      .get()

    const attendees = userTripsQuery.docs.map((doc: any) => doc.data().userId)

    // Update the trip's attendees array
    await adminDb.collection("trips").doc(tripId).update({
      attendees: attendees,
      updatedAt: adminFieldValue.serverTimestamp(),
    })

    // Create the review using Firebase Admin SDK
    const reviewRef = adminDb.collection("trips").doc(tripId).collection("reviews").doc()

    const reviewId = reviewRef.id

    const reviewData = {
      id: reviewId,
      tripId,
      userId,
      rating,
      feedback,
      userDisplayName,
      userPhotoURL,
      reviewDate: adminFieldValue.serverTimestamp(),
      createdAt: adminFieldValue.serverTimestamp(),
    }

    await reviewRef.set(reviewData)

    // Revalidate relevant pages
    revalidatePath(`/trips/${tripId}`)
    revalidatePath(`/trips/${tripId}/review`)
    revalidatePath("/dashboard")

    return {
      success: true,
      reviewId,
    }
  } catch (error) {
    console.error("Error submitting trip review:", error)
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to submit review",
    }
  }
}

export async function redirectToTrip(tripId: string) {
  redirect(`/trips/${tripId}`)
}
