"use server"

import { put } from "@vercel/blob"
import { redirect } from "next/navigation"
import {
  optimizeImage,
  OPTIMIZATION_PRESETS,
  fileToBuffer,
  validateImageBuffer,
} from "@/lib/utils/server-image-optimization"
import { MAX_FILE_SIZE, ALLOWED_TYPES } from "@/lib/utils/image-compression"

export interface UploadTravelDetailsResult {
  success: boolean
  url?: string
  error?: string
}

export async function uploadTravelDetailsAction(
  tripId: string,
  userId: string,
  type: "flight" | "accommodation",
  formData: FormData
): Promise<UploadTravelDetailsResult> {
  console.log("Upload Travel Details Action:", { tripId, userId, type })

  try {
    // Validate user ID is provided (authentication should be handled by the calling component)
    if (!userId) {
      console.log("No userId provided, redirecting to login")
      redirect("/login")
    }

    const file = formData.get("file") as File
    console.log("File:", { name: file?.name, size: file?.size, type: file?.type })

    // Validate required fields
    if (!file) {
      console.log("Validation failed: No file provided")
      return {
        success: false,
        error: "No file provided",
      }
    }

    if (!tripId) {
      console.log("Validation failed: No tripId provided")
      return {
        success: false,
        error: "Trip ID is required",
      }
    }

    if (!type || !["flight", "accommodation"].includes(type)) {
      console.log("Validation failed: Invalid type:", type)
      return {
        success: false,
        error: "Type must be either 'flight' or 'accommodation'",
      }
    }

    // Validate file type
    if (!ALLOWED_TYPES.includes(file.type)) {
      console.log("Validation failed: Invalid file type:", file.type)
      return {
        success: false,
        error: "Invalid file type. Only JPEG, PNG, and WebP images are allowed.",
      }
    }

    // Validate file size (now 50MB limit)
    if (file.size > MAX_FILE_SIZE) {
      console.log("Validation failed: File too large:", file.size)
      const maxSizeMB = Math.round(MAX_FILE_SIZE / (1024 * 1024))
      return {
        success: false,
        error: `File size too large. Maximum size is ${maxSizeMB}MB.`,
      }
    }

    // Check if Vercel Blob token is available
    if (!process.env.BLOB_READ_WRITE_TOKEN) {
      console.error("BLOB_READ_WRITE_TOKEN is not configured")
      return {
        success: false,
        error: "Storage service is not configured",
      }
    }

    // Generate a unique filename
    const timestamp = Date.now()
    const fileExtension = file.name.split(".").pop() || "jpg"
    const filename = `travel-details/${tripId}/${userId}/${type}-${timestamp}.${fileExtension}`
    console.log("Generated filename:", filename)

    try {
      console.log("Starting server-side image optimization...")

      // Convert file to buffer for processing
      const inputBuffer = await fileToBuffer(file)

      // Validate the image buffer
      const validation = await validateImageBuffer(inputBuffer)
      if (!validation.valid) {
        console.log("Image validation failed:", validation.error)
        return {
          success: false,
          error: validation.error || "Invalid image file",
        }
      }

      // Optimize the image for travel details use
      const optimizationResult = await optimizeImage(
        inputBuffer,
        OPTIMIZATION_PRESETS.travelDetails
      )

      if (!optimizationResult.success) {
        console.log("Image optimization failed:", optimizationResult.error)
        return {
          success: false,
          error: optimizationResult.error || "Failed to optimize image",
        }
      }

      console.log("Image optimization successful:", {
        originalSize: optimizationResult.originalSize,
        optimizedSize: optimizationResult.optimizedSize,
        savings: optimizationResult.originalSize - (optimizationResult.optimizedSize || 0),
      })

      // Create optimized file for upload
      const optimizedFile = new File(
        [optimizationResult.buffer!],
        filename.replace(/\.[^/.]+$/, ".webp"), // Change extension to webp
        { type: "image/webp" }
      )

      console.log("Starting Vercel Blob upload...")

      // Upload optimized image to Vercel Blob
      const blob = await put(filename.replace(/\.[^/.]+$/, ".webp"), optimizedFile, {
        access: "public",
        token: process.env.BLOB_READ_WRITE_TOKEN,
      })

      console.log("Vercel Blob upload successful:", blob.url)

      return {
        success: true,
        url: blob.url,
      }
    } catch (uploadError) {
      console.error("Vercel Blob upload error details:", {
        error: uploadError,
        message: uploadError instanceof Error ? uploadError.message : "Unknown error",
        stack: uploadError instanceof Error ? uploadError.stack : undefined,
        filename,
        fileSize: file.size,
        fileType: file.type,
      })

      // Provide more specific error messages based on the error type
      let errorMessage = "Failed to upload image to storage"
      if (uploadError instanceof Error) {
        if (uploadError.message.includes("token")) {
          errorMessage = "Storage authentication failed"
        } else if (uploadError.message.includes("size")) {
          errorMessage = "File size exceeds storage limits"
        } else if (
          uploadError.message.includes("network") ||
          uploadError.message.includes("fetch")
        ) {
          errorMessage = "Network error during upload"
        }
      }

      return {
        success: false,
        error: errorMessage,
      }
    }
  } catch (error) {
    console.error("Travel details upload error details:", {
      error,
      message: error instanceof Error ? error.message : "Unknown error",
      stack: error instanceof Error ? error.stack : undefined,
      tripId,
      userId,
      type,
    })

    return {
      success: false,
      error: "Internal server error",
    }
  }
}
