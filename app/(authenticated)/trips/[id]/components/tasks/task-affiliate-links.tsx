"use client"

import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { ExternalLink, Link as LinkIcon } from "lucide-react"
import { Task } from "@/lib/firebase-service"
import { findMultipleAffiliateLinks, TaggedAffiliateLink } from "@/lib/affiliate-links-map"

interface TaskAffiliateLinksProps {
  task: Task
}

export function TaskAffiliateLinks({ task }: TaskAffiliateLinksProps) {
  const [isExpanded, setIsExpanded] = useState(false)
  const [affiliateLinks, setAffiliateLinks] = useState<TaggedAffiliateLink[]>([])

  // Find affiliate links for this task
  useEffect(() => {
    // Find links immediately on component mount, not just when expanded
    if (affiliateLinks.length === 0) {
      const links = findMultipleAffiliateLinks(task.title, task.description || "")
      setAffiliateLinks(links)

      // Auto-expand if we found links
      if (links.length > 0) {
        setIsExpanded(true)
      }
    }
  }, [task, affiliateLinks.length])

  if (affiliateLinks.length === 0 && isExpanded) {
    return (
      <Button
        variant="ghost"
        size="sm"
        className="w-full text-xs flex items-center justify-center text-muted-foreground"
        onClick={() => setIsExpanded(false)}
      >
        No helpful links found
      </Button>
    )
  }

  return (
    <div>
      {!isExpanded ? (
        <Button
          variant="ghost"
          size="sm"
          className="w-full text-xs flex items-center justify-center text-muted-foreground"
          onClick={() => setIsExpanded(true)}
        >
          <LinkIcon className="h-3 w-3 mr-1" />
          Resources that can help you with this task
        </Button>
      ) : (
        <Card className="bg-muted/50 border-dashed">
          <CardContent className="p-3 space-y-3">
            <div className="flex items-center">
              <LinkIcon className="h-3 w-3 mr-1 text-primary" />
              <span className="text-xs font-medium">
                Helpful resources from our Affiliate Partners
              </span>
            </div>

            <div className="space-y-2">
              {affiliateLinks.map((link, index) => (
                <div key={index} className="bg-background p-2 rounded-md">
                  <div className="flex justify-between items-center">
                    <div>
                      <h4 className="text-xs font-medium">{link.title}</h4>
                      <p className="text-xs text-muted-foreground">{link.description}</p>
                    </div>
                    <Button variant="outline" size="sm" asChild className="h-7 text-xs">
                      <a
                        href={link.url}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="flex items-center gap-1"
                      >
                        <span>{link.provider}</span>
                        <ExternalLink className="h-3 w-3" />
                      </a>
                    </Button>
                  </div>
                </div>
              ))}
            </div>

            <Button
              variant="ghost"
              size="sm"
              className="w-full text-xs"
              onClick={() => setIsExpanded(false)}
            >
              Hide links
            </Button>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
