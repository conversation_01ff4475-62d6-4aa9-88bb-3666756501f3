"use client"

import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Task, User } from "@/lib/firebase-service"
import { Timestamp } from "firebase/firestore"
import { Loader2 } from "lucide-react"
import { useToast } from "@/components/ui/use-toast"

type TaskCategory = "planning" | "booking" | "preparation" | "coordination" | "during-trip"

interface TaskDialogsProps {
  editingTask: Task | null
  isEditDialogOpen: boolean
  setIsEditDialogOpen: (open: boolean) => void
  isDeleteDialogOpen: boolean
  setIsDeleteDialogOpen: (open: boolean) => void
  isAddDialogOpen: boolean
  setIsAddDialogOpen: (open: boolean) => void
  taskToDelete: Task | null
  squadMembers: User[]
  onUpdateTask: () => Promise<boolean>
  onDeleteTask: () => Promise<boolean>
  onAddTask: (taskData: {
    title: string
    description: string
    category: TaskCategory
    assigneeId: string
    dueDate: string
  }) => Promise<boolean>
  isLeader?: boolean
  isCreating?: boolean
  isUpdating?: boolean
  isDeleting?: boolean
}

export function TaskDialogs({
  editingTask,
  isEditDialogOpen,
  setIsEditDialogOpen,
  isDeleteDialogOpen,
  setIsDeleteDialogOpen,
  isAddDialogOpen,
  setIsAddDialogOpen,
  taskToDelete: _taskToDelete, // Renamed to avoid unused variable warning
  squadMembers,
  onUpdateTask,
  onDeleteTask,
  onAddTask,
  isLeader = false,
  isCreating = false,
  isUpdating = false,
  isDeleting = false,
}: TaskDialogsProps) {
  const { toast } = useToast()

  // Validation state
  const [titleError, setTitleError] = useState("")

  // Local state for editing task to prevent immediate UI updates
  const [localEditingTask, setLocalEditingTask] = useState<Task | null>(null)

  // Update local editing task when editingTask changes
  useEffect(() => {
    if (editingTask) {
      setLocalEditingTask({ ...editingTask })
    }
  }, [editingTask, isEditDialogOpen])

  // Helper function to format due date for input field
  const formatDueDateForInput = (dueDate: any): string => {
    try {
      let date: Date | null = null

      // Check if dueDate is a Timestamp object with toDate method
      if (dueDate && typeof dueDate.toDate === "function") {
        try {
          date = dueDate.toDate()
        } catch (e) {
          console.error("Error converting Timestamp to Date:", e)
        }
      }
      // If it's already a Date object
      else if (dueDate instanceof Date) {
        date = dueDate
      }
      // If it's a number (timestamp in milliseconds)
      else if (typeof dueDate === "number") {
        date = new Date(dueDate)
      }
      // If it's a string (ISO date or other format)
      else if (typeof dueDate === "string") {
        date = new Date(dueDate)
      }
      // If it's an object with seconds and nanoseconds (Firestore Timestamp-like)
      else if (dueDate && typeof dueDate === "object" && "seconds" in dueDate) {
        try {
          // Convert Firestore Timestamp-like object to milliseconds
          const seconds = (dueDate as any).seconds
          const nanoseconds = (dueDate as any).nanoseconds || 0
          date = new Date(seconds * 1000 + nanoseconds / 1000000)
        } catch (e) {
          console.error("Error converting Timestamp-like object to Date:", e)
        }
      }

      // Format date for input field (YYYY-MM-DD)
      if (date && !isNaN(date.getTime())) {
        return date.toISOString().split("T")[0]
      }

      return ""
    } catch (error) {
      console.error("Error formatting due date for input:", error, dueDate)
      return ""
    }
  }

  const [newTask, setNewTask] = useState({
    title: "",
    description: "",
    assigneeId: "",
    category: "planning" as TaskCategory,
    dueDate: "",
  })

  return (
    <>
      {/* Edit Task Dialog */}
      {editingTask && localEditingTask && (
        <Dialog
          open={isEditDialogOpen}
          onOpenChange={(open) => {
            setIsEditDialogOpen(open)
            // If dialog is closing, reset local editing task
            if (!open && editingTask) {
              setLocalEditingTask({ ...editingTask })
            }
          }}
        >
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Edit Task</DialogTitle>
              <DialogDescription>Make changes to the task details</DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="title">
                  Title <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="title"
                  value={localEditingTask.title}
                  onChange={(e) => {
                    setLocalEditingTask({ ...localEditingTask, title: e.target.value })
                    if (titleError && e.target.value.trim()) {
                      setTitleError("")
                    }
                  }}
                  disabled={isUpdating}
                  className={titleError ? "border-red-500" : ""}
                />
                {titleError && <p className="text-xs text-red-500 mt-1">{titleError}</p>}
              </div>
              <div className="space-y-2">
                <Label htmlFor="description">
                  Description <span className="text-xs text-muted-foreground">(optional)</span>
                </Label>
                <Textarea
                  id="description"
                  value={localEditingTask.description || ""}
                  onChange={(e) =>
                    setLocalEditingTask({ ...localEditingTask, description: e.target.value })
                  }
                  disabled={isUpdating}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="category">Category</Label>
                <Select
                  value={localEditingTask.category}
                  onValueChange={(value) =>
                    setLocalEditingTask({ ...localEditingTask, category: value as TaskCategory })
                  }
                  disabled={isUpdating}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select a category" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="planning">Planning</SelectItem>
                    <SelectItem value="booking">Booking</SelectItem>
                    <SelectItem value="preparation">Preparation</SelectItem>
                    <SelectItem value="coordination">Coordination</SelectItem>
                    <SelectItem value="during-trip">During Trip</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="assignee">
                  Assignee <span className="text-red-500">*</span>
                </Label>
                <Select
                  value={localEditingTask.assigneeId || ""}
                  onValueChange={(value) => {
                    console.log("Selected assignee:", value)
                    setLocalEditingTask({ ...localEditingTask, assigneeId: value })
                  }}
                  disabled={!isLeader || isUpdating}
                >
                  <SelectTrigger className={!localEditingTask.assigneeId ? "border-red-500" : ""}>
                    <SelectValue placeholder="Assign to someone" />
                  </SelectTrigger>
                  <SelectContent>
                    {squadMembers.map((member) => (
                      <SelectItem key={member.uid} value={member.uid}>
                        {member.displayName}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {!isLeader && (
                  <p className="text-xs text-muted-foreground mt-1">
                    Only the trip leader can reassign tasks
                  </p>
                )}
                {!localEditingTask.assigneeId && (
                  <p className="text-xs text-red-500 mt-1">Assignee is required</p>
                )}
              </div>
              <div className="space-y-2">
                <Label htmlFor="dueDate">
                  Due Date <span className="text-xs text-muted-foreground">(optional)</span>
                </Label>
                <Input
                  id="dueDate"
                  type="date"
                  value={
                    localEditingTask.dueDate ? formatDueDateForInput(localEditingTask.dueDate) : ""
                  }
                  onChange={(e) => {
                    if (e.target.value) {
                      setLocalEditingTask({
                        ...localEditingTask,
                        dueDate: Timestamp.fromDate(new Date(e.target.value)),
                      })
                    } else {
                      setLocalEditingTask({
                        ...localEditingTask,
                        dueDate: null,
                      })
                    }
                  }}
                  disabled={isUpdating}
                />
              </div>
            </div>
            <DialogFooter>
              <Button
                variant="outline"
                onClick={() => {
                  setIsEditDialogOpen(false)
                  // Reset local editing task to prevent stale data
                  if (editingTask) {
                    setLocalEditingTask({ ...editingTask })
                  }
                }}
                disabled={isUpdating}
              >
                Cancel
              </Button>
              <Button
                onClick={async () => {
                  setTitleError("")

                  // Copy local changes back to the original task before saving
                  if (editingTask && localEditingTask) {
                    // Create a new object with all the properties from localEditingTask
                    const updatedTask = { ...editingTask, ...localEditingTask }
                    // Update the original task reference
                    Object.assign(editingTask, updatedTask)

                    // Call the update function - validation happens in the hook
                    const success = await onUpdateTask()
                    // For debugging
                    console.log("Saving task with assignee:", updatedTask.assigneeId)

                    // If validation failed for title, show the error in the UI
                    if (!success && !localEditingTask.title?.trim()) {
                      setTitleError("Title is required")
                    }
                  }
                }}
                disabled={isUpdating}
              >
                {isUpdating ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Saving...
                  </>
                ) : (
                  "Save Changes"
                )}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}

      {/* Delete Task Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Task</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete this task? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsDeleteDialogOpen(false)}
              disabled={isDeleting}
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={async () => {
                await onDeleteTask()
              }}
              disabled={isDeleting}
            >
              {isDeleting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Deleting...
                </>
              ) : (
                "Delete"
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Add Task Dialog */}
      <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Add New Task</DialogTitle>
            <DialogDescription>Create a new task for this trip</DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="new-title">
                Title <span className="text-red-500">*</span>
              </Label>
              <Input
                id="new-title"
                value={newTask.title}
                onChange={(e) => {
                  setNewTask({ ...newTask, title: e.target.value })
                  if (titleError && e.target.value.trim()) {
                    setTitleError("")
                  }
                }}
                disabled={isCreating}
                className={titleError ? "border-red-500" : ""}
              />
              {titleError && <p className="text-xs text-red-500 mt-1">{titleError}</p>}
            </div>
            <div className="space-y-2">
              <Label htmlFor="new-description">
                Description <span className="text-xs text-muted-foreground">(optional)</span>
              </Label>
              <Textarea
                id="new-description"
                value={newTask.description}
                onChange={(e) => setNewTask({ ...newTask, description: e.target.value })}
                disabled={isCreating}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="new-category">Category</Label>
              <Select
                value={newTask.category}
                onValueChange={(value) =>
                  setNewTask({ ...newTask, category: value as TaskCategory })
                }
                disabled={isCreating}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select a category" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="planning">Planning</SelectItem>
                  <SelectItem value="booking">Booking</SelectItem>
                  <SelectItem value="preparation">Preparation</SelectItem>
                  <SelectItem value="coordination">Coordination</SelectItem>
                  <SelectItem value="during-trip">During Trip</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label htmlFor="new-assignee">
                Assignee <span className="text-red-500">*</span>
              </Label>
              <Select
                value={newTask.assigneeId}
                onValueChange={(value) => setNewTask({ ...newTask, assigneeId: value })}
                disabled={isCreating}
              >
                <SelectTrigger className={!newTask.assigneeId ? "border-red-500" : ""}>
                  <SelectValue placeholder="Assign to someone" />
                </SelectTrigger>
                <SelectContent>
                  {squadMembers.map((member) => (
                    <SelectItem key={member.uid} value={member.uid}>
                      {member.displayName}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {!newTask.assigneeId && (
                <p className="text-xs text-red-500 mt-1">Assignee is required</p>
              )}
            </div>
            <div className="space-y-2">
              <Label htmlFor="new-dueDate">
                Due Date <span className="text-xs text-muted-foreground">(optional)</span>
              </Label>
              <Input
                id="new-dueDate"
                type="date"
                value={newTask.dueDate}
                onChange={(e) => setNewTask({ ...newTask, dueDate: e.target.value })}
                disabled={isCreating}
              />
            </div>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsAddDialogOpen(false)}
              disabled={isCreating}
            >
              Cancel
            </Button>
            <Button
              onClick={async () => {
                setTitleError("")

                try {
                  // Convert string date to Timestamp before sending to parent
                  const formattedTaskData = {
                    ...newTask,
                    // Keep the dueDate as a string here - the parent component will convert it to Timestamp
                    // This maintains the expected interface of the onAddTask function
                  }

                  // Call the add task function - validation happens in the hook
                  const success = await onAddTask(formattedTaskData)

                  // Only reset the form if we successfully created the task
                  if (success) {
                    // Reset the form for next time
                    setNewTask({
                      title: "",
                      description: "",
                      assigneeId: "",
                      category: "planning",
                      dueDate: "",
                    })
                  } else if (!newTask.title.trim()) {
                    // If validation failed for title, show the error in the UI
                    setTitleError("Title is required")
                  }
                } catch (error) {
                  console.error("Error creating task:", error)
                  toast({
                    title: "Error",
                    description: "Failed to create task",
                    variant: "destructive",
                  })
                }
              }}
              disabled={isCreating}
            >
              {isCreating ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Creating...
                </>
              ) : (
                "Create Task"
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  )
}
