"use client"

import { useState, useEffect, use<PERSON>em<PERSON> } from "react"
import { useToast } from "@/components/ui/use-toast"
import { useRealtimeUserAIUsage } from "@/lib/domains/user-ai-usage/user-ai-usage.realtime.hooks"
import { AIUsageCategory } from "@/lib/domains/user-ai-usage/user-ai-usage.types"
import { useIsUserSubscribed } from "@/lib/domains/user-subscription/user-subscription.hooks"
import { SubscriptionErrorType } from "@/lib/domains/user-subscription/user-subscription.types"
import { AIUsageWarning } from "@/components/ai-usage-warning"
import { Trip } from "@/lib/domains/trip/trip.types"
import { Task, TaskCreateData } from "@/lib/domains/task/task.types"
import { User } from "@/lib/domains/user/user.types"
import { useCreateTask } from "@/lib/domains/task/task.hooks"
import { filterStaticSuggestions, findAffiliateLink } from "@/lib/affiliate-links-map"
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { ChevronLeft, ChevronRight, Plus, RefreshCw, Sparkles, ExternalLink } from "lucide-react"
import { useAITaskSuggestions } from "@/lib/domains/ai-suggestions/ai-suggestions-tasks.hooks"

interface TaskSuggestionsProps {
  trip: Trip
  tasks: (Task & { assignee?: User })[]
  currentUserId: string
  onTaskAdded: () => void
  isLeader?: boolean
}

export function TaskSuggestions({ trip, tasks, currentUserId, onTaskAdded }: TaskSuggestionsProps) {
  const { toast } = useToast()
  const isSubscribed = useIsUserSubscribed()
  const { getCategoryUsage } = useRealtimeUserAIUsage(isSubscribed)
  const { createTask } = useCreateTask()

  // Use the new AI task suggestions hook
  const {
    suggestions: aiSuggestions,
    loading,
    usageError,
    showAiSuggestions,
    usingCachedSuggestions,
    loadSuggestions,
  } = useAITaskSuggestions(trip, tasks, currentUserId)

  // Pagination constants
  const ITEMS_PER_PAGE = 3
  const [staticPage, setStaticPage] = useState(0)

  // Get all available static suggestions filtered by existing tasks
  const allStaticSuggestions = useMemo(() => {
    return filterStaticSuggestions(tasks)
  }, [tasks])

  // Calculate total pages for static suggestions
  const totalStaticPages = useMemo(() => {
    return Math.max(1, Math.ceil(allStaticSuggestions.length / ITEMS_PER_PAGE))
  }, [allStaticSuggestions])

  // Get current page of static suggestions
  const currentStaticSuggestions = useMemo(() => {
    const startIndex = staticPage * ITEMS_PER_PAGE
    return allStaticSuggestions.slice(startIndex, startIndex + ITEMS_PER_PAGE)
  }, [allStaticSuggestions, staticPage])

  // Handle pagination navigation
  const goToPreviousStaticPage = () => {
    setStaticPage((prev) => Math.max(0, prev - 1))
  }

  const goToNextStaticPage = () => {
    setStaticPage((prev) => Math.min(totalStaticPages - 1, prev + 1))
  }

  // Update static page when tasks change to ensure we're not on an invalid page
  useEffect(() => {
    setStaticPage(0)
  }, [tasks])

  const handleAddTask = async (title: string, description: string) => {
    if (!currentUserId || !trip) return

    try {
      // Find matching static suggestion to get category
      const matchingStaticSuggestion = allStaticSuggestions.find(
        (s) => s.title === title && s.description === description
      )

      // Find affiliate link for this task
      const affiliateLink = findAffiliateLink(title, description)

      // Add tags from affiliate link if found
      const tags = affiliateLink?.tags || []

      const taskData: Omit<TaskCreateData, "createdBy"> = {
        tripId: trip.id,
        title,
        description,
        // Use the category from the matching suggestion, or default to "booking"
        category: matchingStaticSuggestion?.category || "booking",
        assigneeId: currentUserId,
        // Explicitly use Timestamp.now() for null to standardize date format
        dueDate: null,
        tags: tags, // Add tags for affiliate link matching
      }

      // Create the task using the task hook
      const success = await createTask(taskData)

      if (success) {
        onTaskAdded()
      } else {
        throw new Error("Failed to create task")
      }
    } catch (error) {
      console.error("Error adding task:", error)
      toast({
        title: "Error",
        description: "Failed to add task. Please try again.",
        variant: "destructive",
      })
    }
  }

  return (
    <>
      {/* Essential Tasks Card */}
      <Card className="mt-4">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            Essential Trip Tasks
            {allStaticSuggestions.length > 0 && (
              <div className="flex items-center ml-auto gap-2">
                <span className="text-xs text-muted-foreground">
                  {staticPage + 1}/{totalStaticPages}
                </span>
                <div className="flex items-center gap-1">
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-8 w-8"
                    onClick={goToPreviousStaticPage}
                    disabled={staticPage === 0}
                    title="Previous page"
                  >
                    <ChevronLeft className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-8 w-8"
                    onClick={goToNextStaticPage}
                    disabled={staticPage >= totalStaticPages - 1}
                    title="Next page"
                  >
                    <ChevronRight className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            )}
          </CardTitle>
          <CardDescription>
            Recommended tasks to help you plan your trip to {trip.destination}
          </CardDescription>
        </CardHeader>
        <CardContent className="p-4">
          <div className="space-y-3">
            {allStaticSuggestions.length > 0 ? (
              currentStaticSuggestions.map((suggestion, index) => {
                const affiliateLink = findAffiliateLink(suggestion.title, suggestion.description)

                return (
                  <div key={index} className="border rounded-md p-3">
                    <div className="space-y-2">
                      <div>
                        <h4 className="font-medium">{suggestion.title}</h4>
                        <p className="text-sm text-muted-foreground">{suggestion.description}</p>
                      </div>

                      {affiliateLink && (
                        <div className="bg-muted/50 p-3 rounded-md">
                          <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-2">
                            <div>
                              <h4 className="text-sm font-medium">{affiliateLink.title}</h4>
                              <p className="text-xs text-muted-foreground">
                                {affiliateLink.description}
                              </p>
                            </div>
                            <Button variant="outline" size="sm" asChild className="h-8 text-xs">
                              <a
                                href={affiliateLink.url}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="flex items-center gap-1"
                              >
                                <span>{affiliateLink.provider}</span>
                                <ExternalLink className="h-3 w-3" />
                              </a>
                            </Button>
                          </div>
                        </div>
                      )}

                      <Button
                        variant="outline"
                        size="sm"
                        className="w-full"
                        onClick={() => handleAddTask(suggestion.title, suggestion.description)}
                      >
                        <Plus className="mr-2 h-4 w-4" />
                        Add to Tasks
                      </Button>
                    </div>
                  </div>
                )
              })
            ) : (
              <div className="text-center py-6">
                <p className="text-muted-foreground">
                  You've added all the essential tasks for this trip!
                </p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {usageError && usageError.show && (
        <AIUsageWarning
          errorType={usageError.errorType}
          usageData={usageError.usageData}
          onClose={() => {}}
          className="mb-4"
        />
      )}
      {/* AI Suggestions Card */}
      <Card className="mt-4" id="ai-task-suggestions">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Sparkles className="h-4 w-4 text-primary" />
            AI Task Suggestions
            {showAiSuggestions && (
              <div className="flex items-center ml-auto gap-2">
                {!isSubscribed && (
                  <span className="text-xs text-muted-foreground">
                    {getCategoryUsage(AIUsageCategory.TASK)?.count || 0}/
                    {getCategoryUsage(AIUsageCategory.TASK)?.limit || 3}
                  </span>
                )}
                {usingCachedSuggestions && (
                  <Badge variant="outline" className="text-xs bg-primary/10 border-primary/20">
                    Cached
                  </Badge>
                )}
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-8 w-8"
                  onClick={() => loadSuggestions(true)}
                  disabled={loading}
                  title="Refresh AI suggestions"
                >
                  <RefreshCw className={`h-4 w-4 ${loading ? "animate-spin" : ""}`} />
                </Button>
              </div>
            )}
          </CardTitle>
          <CardDescription>
            {showAiSuggestions
              ? `${usingCachedSuggestions ? "Cached" : "AI-powered"} task suggestions for your trip to ${trip.destination}`
              : "Get personalized task suggestions based on your trip details"}
          </CardDescription>
        </CardHeader>
        <CardContent className="p-4">
          <div className="space-y-3">
            {showAiSuggestions ? (
              // AI suggestions
              aiSuggestions.length > 0 ? (
                <>
                  {aiSuggestions.map((suggestion, index) => (
                    <div
                      key={index}
                      className="border border-primary/20 bg-primary/5 rounded-md p-3"
                    >
                      <div className="space-y-2">
                        <div>
                          <h4 className="font-medium flex items-center gap-1">
                            <Sparkles className="h-3 w-3 text-primary" />
                            {suggestion.title}
                          </h4>
                          <p className="text-sm text-muted-foreground">{suggestion.description}</p>
                        </div>

                        {/* Display multiple affiliate links if available, otherwise fallback to single link */}
                        {suggestion.affiliateLinks && suggestion.affiliateLinks.length > 0 ? (
                          <div className="space-y-2">
                            {suggestion.affiliateLinks.map((affiliateLink, linkIndex) => (
                              <div
                                key={linkIndex}
                                className="bg-background p-3 rounded-md border border-border"
                              >
                                <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-2">
                                  <div>
                                    <h4 className="text-sm font-medium">{affiliateLink.title}</h4>
                                    <p className="text-xs text-muted-foreground">
                                      {affiliateLink.description}
                                    </p>
                                  </div>
                                  <Button
                                    variant="outline"
                                    size="sm"
                                    asChild
                                    className="h-8 text-xs"
                                  >
                                    <a
                                      href={affiliateLink.url}
                                      target="_blank"
                                      rel="noopener noreferrer"
                                      className="flex items-center gap-1"
                                    >
                                      <span>{affiliateLink.provider}</span>
                                      <ExternalLink className="h-3 w-3" />
                                    </a>
                                  </Button>
                                </div>
                              </div>
                            ))}
                          </div>
                        ) : (
                          suggestion.affiliateLink && (
                            <div className="bg-background p-3 rounded-md border border-border">
                              <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-2">
                                <div>
                                  <h4 className="text-sm font-medium">
                                    {suggestion.affiliateLink.title}
                                  </h4>
                                  <p className="text-xs text-muted-foreground">
                                    {suggestion.affiliateLink.description}
                                  </p>
                                </div>
                                <Button variant="outline" size="sm" asChild className="h-8 text-xs">
                                  <a
                                    href={suggestion.affiliateLink.url}
                                    target="_blank"
                                    rel="noopener noreferrer"
                                    className="flex items-center gap-1"
                                  >
                                    <span>{suggestion.affiliateLink.provider}</span>
                                    <ExternalLink className="h-3 w-3" />
                                  </a>
                                </Button>
                              </div>
                            </div>
                          )
                        )}

                        <Button
                          variant="outline"
                          size="sm"
                          className="w-full"
                          onClick={() => handleAddTask(suggestion.title, suggestion.description)}
                        >
                          <Plus className="mr-2 h-4 w-4" />
                          Add to Tasks
                        </Button>
                      </div>
                    </div>
                  ))}

                  <Button
                    variant="outline"
                    className="w-full"
                    onClick={() => loadSuggestions(true)}
                    disabled={loading}
                  >
                    {loading ? (
                      <div className="flex items-center justify-center gap-2">
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary"></div>
                        <span className="text-xs sm:text-sm">Getting New suggestions...</span>
                      </div>
                    ) : (
                      <>
                        <RefreshCw className="mr-2 h-4 w-4" />
                        {!isSubscribed && (
                          <span className="mr-2 text-xs">
                            {getCategoryUsage(AIUsageCategory.TASK)?.count || 0}/
                            {getCategoryUsage(AIUsageCategory.TASK)?.limit || 3}
                          </span>
                        )}
                        Get New Personalized Suggestions
                      </>
                    )}
                  </Button>
                </>
              ) : (
                <div className="text-center py-6">
                  <p className="text-muted-foreground">No AI suggestions available.</p>
                  <Button
                    variant="outline"
                    className="mt-4"
                    onClick={() => loadSuggestions(true)}
                    disabled={loading}
                  >
                    <RefreshCw className="mr-2 h-4 w-4" />
                    Try again
                  </Button>
                </div>
              )
            ) : (
              // Button to get AI suggestions
              <Button
                variant="outline"
                className="w-full"
                onClick={() => loadSuggestions(false)}
                disabled={loading}
              >
                {loading ? (
                  <div className="flex items-center space-x-2">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary"></div>
                    <span className="text-xs sm:text-sm">Finding tasks suggestions...</span>
                  </div>
                ) : (
                  <>
                    <Sparkles className="mr-2 h-4 w-4" />
                    {!isSubscribed && (
                      <span className="mr-2 text-xs">
                        {getCategoryUsage(AIUsageCategory.TASK)?.count || 0}/
                        {getCategoryUsage(AIUsageCategory.TASK)?.limit || 3}
                      </span>
                    )}
                    Get Personalized Task Suggestions
                  </>
                )}
              </Button>
            )}
          </div>
        </CardContent>
      </Card>
    </>
  )
}
