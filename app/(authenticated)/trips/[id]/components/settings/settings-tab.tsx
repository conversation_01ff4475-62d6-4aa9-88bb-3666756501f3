"use client"

import { useState } from "react"
import { useRout<PERSON> } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"
import { useToast } from "@/components/ui/use-toast"
import { Trash2, AlertTriangle } from "lucide-react"
import { useDeleteTrip } from "@/lib/domains/trip/trip.hooks"
import { Trip } from "@/lib/domains/trip/trip.types"

interface SettingsTabProps {
  trip: Trip
}

export function SettingsTab({ trip }: SettingsTabProps) {
  const router = useRouter()
  const { toast } = useToast()
  const { deleteTrip } = useDeleteTrip()
  const [isDeleting, setIsDeleting] = useState(false)

  // Check if trip deletion is restricted
  const isDeletionRestricted = trip.status === "active" || trip.status === "completed"

  const handleDeleteTrip = async () => {
    // Prevent deletion if trip is active or completed
    if (isDeletionRestricted) {
      toast({
        title: "Deletion Restricted",
        description: "Active and completed trips cannot be deleted to preserve data integrity.",
        variant: "destructive",
      })
      return
    }

    setIsDeleting(true)

    try {
      const success = await deleteTrip(trip.id)

      if (success) {
        toast({
          title: "Trip deleted",
          description: "The trip has been successfully deleted.",
        })

        // Navigate back to trips list
        router.push("/trips")
      } else {
        toast({
          title: "Error",
          description: "Failed to delete the trip. Please try again.",
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error("Error deleting trip:", error)
      toast({
        title: "Error",
        description: "An unexpected error occurred. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsDeleting(false)
    }
  }

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold tracking-tight">Trip Settings</h2>
        <p className="text-muted-foreground">Manage your trip settings and preferences.</p>
      </div>

      {/* Danger Zone */}
      <Card className="border-destructive/20">
        <CardHeader>
          <CardTitle className="text-destructive flex items-center gap-2">
            <AlertTriangle className="h-5 w-5" />
            Danger Zone
          </CardTitle>
          <CardDescription>Irreversible and destructive actions for this trip.</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {isDeletionRestricted && (
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
              <div className="flex items-center gap-2 text-yellow-800">
                <span className="text-sm font-medium">🔒 Deletion Restricted</span>
              </div>
              <p className="text-yellow-700 text-xs mt-1">
                {trip.status === "active"
                  ? "Active trips cannot be deleted to preserve ongoing trip data and member access."
                  : "Completed trips cannot be deleted to maintain historical records and reviews."}
              </p>
            </div>
          )}
          <div className="rounded-lg border border-destructive/20 p-4">
            <div className="flex items-start justify-between">
              <div className="space-y-1">
                <h4 className="text-sm font-medium">Delete Trip</h4>
                <p className="text-sm text-muted-foreground">
                  {isDeletionRestricted
                    ? `This trip cannot be deleted because it is ${trip.status}. Only planning trips can be deleted.`
                    : "Permanently delete this trip and all associated data including tasks, itinerary items, and attendance records. This action cannot be undone."}
                </p>
              </div>
              <AlertDialog>
                <AlertDialogTrigger asChild>
                  <Button
                    variant="destructive"
                    size="sm"
                    disabled={isDeleting || isDeletionRestricted}
                    className="ml-4 shrink-0"
                  >
                    <Trash2 className="h-4 w-4 mr-2" />
                    {isDeletionRestricted ? "Cannot Delete" : "Delete Trip"}
                  </Button>
                </AlertDialogTrigger>
                <AlertDialogContent>
                  <AlertDialogHeader>
                    <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
                    <AlertDialogDescription asChild>
                      <div className="space-y-2">
                        <p>
                          This will permanently delete the trip <strong>"{trip.name}"</strong> and
                          all associated data including:
                        </p>
                        <ul className="list-disc list-inside space-y-1 text-sm">
                          <li>All tasks and assignments</li>
                          <li>Itinerary items and bookings</li>
                          <li>Attendance records</li>
                          <li>Trip savings and expenses</li>
                        </ul>
                        <p className="font-medium text-destructive">
                          This action cannot be undone.
                        </p>
                      </div>
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                    <AlertDialogCancel disabled={isDeleting}>Cancel</AlertDialogCancel>
                    <AlertDialogAction
                      onClick={handleDeleteTrip}
                      disabled={isDeleting}
                      className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                    >
                      {isDeleting ? "Deleting..." : "Delete Trip"}
                    </AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
