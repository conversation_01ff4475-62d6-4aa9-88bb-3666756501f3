"use client"

import { useState, useEffect, useMemo, useCallback } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { MapPin } from "lucide-react"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  ItineraryItem,
  ItineraryItemType,
  ItineraryItemUpdateData,
} from "@/lib/domains/itinerary/itinerary.types"
import {
  useCreateItineraryItem,
  useUpdateItineraryItem,
} from "@/lib/domains/itinerary/itinerary.hooks"
import { useToast } from "@/components/ui/use-toast"
import { Trip } from "@/lib/domains/trip/trip.types"
import { Timestamp } from "firebase/firestore"

interface ActivityDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  trip: Trip
  day: number
  userId: string // Kept for backward compatibility but no longer used
  activity?: ItineraryItem
  onSuccess: () => void
}

export function ActivityDialog({
  open,
  onOpenChange,
  trip,
  day,
  userId,
  activity,
  onSuccess,
}: ActivityDialogProps) {
  const isEditing = useMemo(() => !!activity, [activity])
  const { toast } = useToast()
  const [isSubmitting, setIsSubmitting] = useState(false)

  // Initialize form data with memoized default values
  const initialFormData = useMemo(
    () => ({
      title: activity?.title || "",
      description: activity?.description || "",
      location: activity?.location || "",
      time: activity?.startTime
        ? new Date(activity.startTime.toDate()).toTimeString().substring(0, 5)
        : "12:00",
      day: day,
      type: activity?.type || ("activity" as ItineraryItemType),
      isBooked: activity?.isBooked || false,
    }),
    [
      activity?.title,
      activity?.description,
      activity?.location,
      activity?.startTime,
      activity?.type,
      activity?.isBooked,
      day,
    ]
  )

  const [formData, setFormData] = useState(initialFormData)

  // Reset form data when dialog opens or initial data changes
  useEffect(() => {
    if (open) {
      setFormData(initialFormData)
    }
  }, [open, initialFormData])

  const handleChange = useCallback((field: string, value: string | number) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }))
  }, [])

  // Use the hooks for creating and updating itinerary items
  const createHook = useCreateItineraryItem()
  const updateHook = useUpdateItineraryItem(activity?.id)

  // Memoize the functions to prevent re-creation
  const create = useMemo(() => createHook.create, [createHook.create])
  const update = useMemo(() => updateHook.update, [updateHook.update])

  const handleSubmit = useCallback(
    async (e: React.FormEvent) => {
      e.preventDefault()

      if (!formData.title.trim()) {
        toast({
          title: "Error",
          description: "Activity title is required",
          variant: "destructive",
        })
        return
      }

      try {
        setIsSubmitting(true)

        // Create date objects for start and end times
        const selectedDay = new Date(trip.startDate.toDate())
        selectedDay.setDate(selectedDay.getDate() + ((formData.day as number) - 1))

        // Parse time string (HH:MM)
        const [hours, minutes] = formData.time.split(":").map(Number)

        // Set start time
        const startTime = new Date(selectedDay)
        startTime.setHours(hours, minutes, 0, 0)

        // Set end time (default to 1 hour after start time)
        const endTime = new Date(startTime)
        endTime.setHours(endTime.getHours() + 1)

        if (isEditing && activity) {
          // Update the activity using the hook
          const updateData: ItineraryItemUpdateData = {
            title: formData.title,
            description: formData.description,
            location: formData.location,
            type: formData.type,
            startTime: Timestamp.fromDate(startTime),
            endTime: Timestamp.fromDate(endTime),
            isBooked: formData.isBooked,
          }

          // Only include locationDetails if it exists and is not undefined
          if (activity.locationDetails) {
            updateData.locationDetails = activity.locationDetails
          }

          const success = await update(updateData)

          if (!success) {
            throw new Error("Failed to update activity")
          }
          // Success toast is handled by the hook
        } else {
          // Create the activity using the hook
          const itemId = await create({
            tripId: trip.id,
            title: formData.title,
            description: formData.description,
            location: formData.location,
            type: formData.type,
            startTime: Timestamp.fromDate(startTime),
            endTime: Timestamp.fromDate(endTime),
            isBooked: formData.isBooked,
          })

          if (!itemId) {
            throw new Error("Failed to create activity")
          }
          // Success toast is handled by the hook
        }

        onSuccess()
        onOpenChange(false)
      } catch (error) {
        console.error("Error saving activity:", error)
        // Error toast is handled by the hook
      } finally {
        setIsSubmitting(false)
      }
    },
    [
      formData,
      trip.startDate,
      trip.id,
      isEditing,
      activity,
      update,
      create,
      onSuccess,
      onOpenChange,
      toast,
    ]
  )

  // Memoize time options generation (static, never changes)
  const timeOptions = useMemo(() => {
    const options = []
    for (let hour = 0; hour < 24; hour++) {
      for (let minute = 0; minute < 60; minute += 30) {
        const hourStr = hour.toString().padStart(2, "0")
        const minuteStr = minute.toString().padStart(2, "0")
        const time = `${hourStr}:${minuteStr}`
        const label = new Date(`2000-01-01T${time}:00`).toLocaleTimeString([], {
          hour: "numeric",
          minute: "2-digit",
          hour12: true,
        })
        options.push({ value: time, label })
      }
    }
    return options
  }, [])

  // Memoize day options generation (only recalculate when trip dates change)
  const dayOptions = useMemo(() => {
    const options = []
    if (trip.startDate && trip.endDate) {
      const start = trip.startDate.toDate()
      const end = trip.endDate.toDate()
      const dayCount = Math.ceil((end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24)) + 1

      for (let i = 1; i <= dayCount; i++) {
        const date = new Date(start)
        date.setDate(date.getDate() + (i - 1))
        const dateStr = date.toLocaleDateString("en-US", {
          weekday: "short",
          month: "short",
          day: "numeric",
        })
        options.push({ value: i, label: `Day ${i} - ${dateStr}` })
      }
    }
    return options
  }, [trip.startDate, trip.endDate])

  // Memoize the rendered options to prevent re-rendering
  const dayOptionElements = useMemo(
    () =>
      dayOptions.map((option) => (
        <SelectItem key={option.value} value={option.value.toString()}>
          {option.label}
        </SelectItem>
      )),
    [dayOptions]
  )

  const timeOptionElements = useMemo(
    () =>
      timeOptions.map((option) => (
        <SelectItem key={option.value} value={option.value}>
          {option.label}
        </SelectItem>
      )),
    [timeOptions]
  )

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>{isEditing ? "Edit Activity" : "Add Activity"}</DialogTitle>
          <DialogDescription>
            {isEditing
              ? "Update the details of this activity in your itinerary"
              : "Add a new activity to your trip itinerary"}
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="title">Activity Title</Label>
            <Input
              id="title"
              value={formData.title}
              onChange={(e) => handleChange("title", e.target.value)}
              placeholder="e.g., Visit Museum, Dinner at Restaurant"
              required
            />
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="day">Day</Label>
              <Select
                value={formData.day.toString()}
                onValueChange={(value) => handleChange("day", parseInt(value))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select day" />
                </SelectTrigger>
                <SelectContent>{dayOptionElements}</SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="time">Time</Label>
              <Select value={formData.time} onValueChange={(value) => handleChange("time", value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Select time" />
                </SelectTrigger>
                <SelectContent>{timeOptionElements}</SelectContent>
              </Select>
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="location">Location</Label>
            <div className="flex items-center space-x-2">
              <MapPin className="h-4 w-4 text-muted-foreground" />
              <Input
                id="location"
                value={formData.location || ""}
                onChange={(e) => handleChange("location", e.target.value)}
                placeholder="e.g., Central Park, Downtown Restaurant"
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              value={formData.description || ""}
              onChange={(e) => handleChange("description", e.target.value)}
              placeholder="Add details about this activity"
              rows={3}
            />
          </div>

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? (
                <div className="flex items-center">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  {isEditing ? "Updating..." : "Adding..."}
                </div>
              ) : isEditing ? (
                "Update Activity"
              ) : (
                "Add Activity"
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
