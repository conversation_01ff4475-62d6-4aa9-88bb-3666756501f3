"use client"

import { useRef } from "react"
import { Badge } from "@/components/ui/badge"
import { Tabs<PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { ItineraryItem } from "@/lib/domains/itinerary/itinerary.types"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"

interface ItineraryDayTabsProps {
  dates: Date[]
  activeDay: string
  itineraryItems: ItineraryItem[]
  onDayChange: (day: string) => void
}

export function ItineraryDayTabs({
  dates,
  activeDay,
  itineraryItems,
  onDayChange,
}: ItineraryDayTabsProps) {
  const tabsListRef = useRef<HTMLDivElement>(null)

  // Format the day for display in the select dropdown
  const formatDayOption = (date: Date, index: number) => {
    // Check if there are activities for this day based on startTime
    const startOfDay = new Date(date)
    startOfDay.setHours(0, 0, 0, 0)

    const endOfDay = new Date(date)
    endOfDay.setHours(23, 59, 59, 999)

    const hasActivities = itineraryItems.some((item) => {
      if (!item.startTime) return false
      const itemDate = item.startTime.toDate()
      return itemDate >= startOfDay && itemDate <= endOfDay
    })

    return `Day ${index + 1} - ${date.toLocaleDateString("en-US", { month: "short", day: "numeric" })}${hasActivities ? " •" : ""}`
  }

  return (
    <>
      {/* Mobile view: Dropdown selector */}
      <div className="md:hidden w-full mb-4">
        <Select value={`day-${activeDay}`} onValueChange={onDayChange}>
          <SelectTrigger className="w-full">
            <SelectValue placeholder="Select a day" />
          </SelectTrigger>
          <SelectContent>
            {dates.map((date, index) => (
              <SelectItem key={index} value={`day-${index + 1}`}>
                {formatDayOption(date, index)}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* Desktop view: Horizontal tabs */}
      <div className="hidden md:block w-full overflow-x-auto pb-2 no-scrollbar">
        <TabsList className="inline-flex w-full" ref={tabsListRef}>
          {dates.map((date, index) => {
            // Check if this day has activities based on startTime
            const startOfDay = new Date(date)
            startOfDay.setHours(0, 0, 0, 0)

            const endOfDay = new Date(date)
            endOfDay.setHours(23, 59, 59, 999)

            const hasActivities = itineraryItems.some((item) => {
              if (!item.startTime) return false
              const itemDate = item.startTime.toDate()
              return itemDate >= startOfDay && itemDate <= endOfDay
            })

            return (
              <TabsTrigger
                key={index}
                value={`day-${index + 1}`}
                className="flex-1 relative whitespace-nowrap"
              >
                <div className="text-center">
                  <div className="font-medium">Day {index + 1}</div>
                  <div className="text-xs text-muted-foreground">
                    {date.toLocaleDateString("en-US", { month: "short", day: "numeric" })}
                  </div>
                  {hasActivities && (
                    <Badge variant="secondary" className="absolute -top-1 -right-1 h-2 w-2 p-0" />
                  )}
                </div>
              </TabsTrigger>
            )
          })}
        </TabsList>
      </div>
    </>
  )
}
