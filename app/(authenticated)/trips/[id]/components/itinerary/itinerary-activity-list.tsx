"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Plus } from "lucide-react"
import { ItineraryItem } from "@/lib/domains/itinerary/itinerary.types"
import { ItineraryActivityItem } from "./itinerary-activity-item"
import { usePagination } from "@/hooks/use-pagination"
import { PaginationControl } from "@/components/pagination-control"

interface ItineraryActivityListProps {
  date: Date
  dayNumber: number
  activities: ItineraryItem[]
  onAddActivity: () => void
  onEditActivity: (activity: ItineraryItem) => void
  onDeleteActivity: (activity: ItineraryItem) => void
  isTripCompleted?: boolean
}

export function ItineraryActivityList({
  date,
  dayNumber,
  activities,
  onAddActivity,
  onEditActivity,
  onDeleteActivity,
  isTripCompleted = false,
}: ItineraryActivityListProps) {
  // Sort activities by startTime
  const sortedActivities = [...activities].sort((a, b) => {
    if (!a.startTime || !b.startTime) return 0
    return a.startTime.seconds - b.startTime.seconds
  })

  // Set up pagination
  const pagination = usePagination({
    totalItems: sortedActivities.length,
    itemsPerPage: 10,
  })

  // Get paginated activities
  const getPaginatedActivities = () => {
    const startIndex = (pagination.currentPage - 1) * pagination.itemsPerPage
    const endIndex = startIndex + pagination.itemsPerPage
    return sortedActivities.slice(startIndex, endIndex)
  }

  // Reset pagination when activities change
  useEffect(() => {
    pagination.goToPage(1)
  }, [activities.length])

  return (
    <Card className="max-w-full">
      <CardHeader className="pb-2">
        <CardTitle className="text-lg sm:text-xl break-words">
          Day {dayNumber} -{" "}
          {date.toLocaleDateString("en-US", { weekday: "long", month: "long", day: "numeric" })}
        </CardTitle>
        <CardDescription>Itinerary for {date.toLocaleDateString()}</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4 max-w-full overflow-hidden">
        {activities.length > 0 ? (
          <>
            {getPaginatedActivities().map((item) => (
              <ItineraryActivityItem
                key={item.id}
                activity={item}
                onEdit={onEditActivity}
                onDelete={onDeleteActivity}
                isTripCompleted={isTripCompleted}
              />
            ))}

            {/* Pagination control */}
            <PaginationControl
              currentPage={pagination.currentPage}
              totalPages={pagination.totalPages}
              onPageChange={pagination.goToPage}
            />
          </>
        ) : (
          <div className="text-center p-6 border rounded-md">
            <p className="text-muted-foreground">
              {isTripCompleted
                ? "No activities were planned for this day"
                : "No activities planned for this day"}
            </p>
            {!isTripCompleted && (
              <Button variant="outline" className="mt-2" onClick={onAddActivity}>
                <Plus className="mr-2 h-4 w-4" /> Add Activity for Day {dayNumber}
              </Button>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  )
}
