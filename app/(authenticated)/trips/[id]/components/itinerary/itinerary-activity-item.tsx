"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Calendar, Edit, MapPin, Trash2, <PERSON><PERSON> } from "lucide-react"
import { ItineraryItem } from "@/lib/domains/itinerary/itinerary.types"
import { useToast } from "@/components/ui/use-toast"

interface ItineraryActivityItemProps {
  activity: ItineraryItem
  onEdit: (activity: ItineraryItem) => void
  onDelete: (activity: ItineraryItem) => void
  isTripCompleted?: boolean
}

export function ItineraryActivityItem({
  activity,
  onEdit,
  onDelete,
  isTripCompleted = false,
}: ItineraryActivityItemProps) {
  const { toast } = useToast()

  // Helper function to copy location to clipboard
  const copyLocationToClipboard = async (location: string) => {
    try {
      await navigator.clipboard.writeText(location)
      toast({
        title: "Copied to clipboard",
        description: "Location address has been copied for easy directions",
      })
    } catch (error) {
      console.error("Failed to copy to clipboard:", error)
      toast({
        title: "<PERSON><PERSON> failed",
        description: "Unable to copy to clipboard. Please copy manually.",
        variant: "destructive",
      })
    }
  }
  return (
    <div className="border rounded-md p-4 space-y-3 max-w-full overflow-hidden">
      {/* Header with title and action buttons */}
      <div className="flex flex-col sm:flex-row justify-between sm:items-start gap-2">
        <div className="flex-1 max-w-full overflow-hidden">
          <h3 className="font-medium break-words">{activity.title}</h3>
          <div className="flex items-center gap-2 text-sm text-muted-foreground mt-1">
            <Calendar className="h-3 w-3" />
            <span>
              {activity.startTime &&
                activity.startTime.toDate().toLocaleTimeString([], {
                  hour: "numeric",
                  minute: "2-digit",
                  hour12: true,
                })}
            </span>
          </div>
        </div>
        {!isTripCompleted && (
          <div className="flex gap-2 self-end sm:self-start">
            <Button variant="outline" size="sm" onClick={() => onEdit(activity)}>
              <Edit className="h-3 w-3" />
            </Button>
            <Button variant="outline" size="sm" onClick={() => onDelete(activity)}>
              <Trash2 className="h-3 w-3 text-destructive" />
            </Button>
          </div>
        )}
      </div>

      {/* Description section */}
      {activity.description && <p className="text-sm break-words">{activity.description}</p>}

      {/* Location section - separate from other elements */}
      {activity.location && (
        <div className="flex items-center gap-1 text-xs text-muted-foreground bg-muted/30 p-2 rounded-md">
          <MapPin className="h-3 w-3 flex-shrink-0" />
          <span className="flex-1 break-words">{activity.location}</span>
          <Button
            variant="ghost"
            size="sm"
            className="h-6 w-6 p-0 hover:bg-muted flex-shrink-0"
            onClick={() => copyLocationToClipboard(activity.location!)}
            title="Copy address for directions"
          >
            <Copy className="h-3 w-3" />
          </Button>
        </div>
      )}
    </div>
  )
}
