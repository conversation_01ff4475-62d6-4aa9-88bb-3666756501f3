"use client"

import { useR<PERSON>, useEffect } from "react"
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, Card<PERSON>itle } from "@/components/ui/card"
import { MessageList } from "./message-list"
import { MessageInput } from "./message-input"
import { But<PERSON> } from "@/components/ui/button"
import { ArrowDown, Bot, Loader2 } from "lucide-react"
import { useAuthStatus } from "@/lib/domains/auth/auth.hooks"
import { useHasTripChat } from "@/lib/domains/user-subscription/user-subscription.hooks"
import {
  useRealtimeMessages,
  useRealtimeNewMessages,
  useNewMessageNotifications,
  useMessageScroll,
} from "@/lib/domains/message/message.hooks"
import { useTripChatAI } from "@/lib/domains/trip-chat-ai/trip-chat-ai.hooks"
import { Trip } from "@/lib/domains/trip/trip.types"

interface ChatTabProps {
  trip: Trip
  attendees: Array<{
    id: string
    displayName?: string
    email?: string
    photoURL?: string
  }>
  isActive?: boolean
}

export function ChatTab({ trip, attendees, isActive = true }: ChatTabProps) {
  const { user } = useAuthStatus()
  const hasTripChat = useHasTripChat()
  const messagesContainerRef = useRef<HTMLDivElement>(null!)

  // AI loading state
  const { loading: aiLoading } = useTripChatAI(trip.id)

  // Determine if user can send messages (all users can chat, but trip must not be completed)
  const canSendMessages = trip.status !== "completed"

  // Debug logging for development
  if (process.env.NODE_ENV === "development") {
    console.log("Chat Tab Debug:", {
      hasTripChat,
      tripStatus: trip.status,
      canSendMessages,
      userId: user?.uid,
      note: "All users can chat, Pro users get AI commands",
    })
  }

  // Subscribe to real-time messages (always allow viewing for all users)
  const { messages, loading, error } = useRealtimeMessages(isActive ? trip.id : "", true)

  // Subscribe to new message notifications (only when active)
  useRealtimeNewMessages(isActive ? trip.id : "", user?.uid || "", true)

  // Get new message count and scroll functions
  const { newMessageCount, resetCount } = useNewMessageNotifications(trip.id)
  const { scrollToBottom } = useMessageScroll(trip.id, messagesContainerRef)

  // Auto-scroll to bottom on initial load
  useEffect(() => {
    if (messages.length > 0 && !loading) {
      scrollToBottom()
    }
  }, [messages.length, loading, scrollToBottom])

  const handleScrollToBottom = () => {
    scrollToBottom()
    resetCount()
  }

  if (error) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center text-muted-foreground">
            Failed to load messages. Please try again.
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="h-[600px] flex flex-col">
      <Card className="h-full flex flex-col">
        <CardHeader className="pb-3 flex-shrink-0">
          <CardTitle className="text-lg">Trip Chat</CardTitle>
          <p className="text-sm text-muted-foreground">
            {trip.status === "completed"
              ? "View your trip chat history."
              : "Chat with your trip attendees. Use @ to mention someone."}
          </p>
        </CardHeader>

        <CardContent className="flex-1 flex flex-col p-0 relative min-h-0">
          {/* Messages Container */}
          <div ref={messagesContainerRef} className="flex-1 overflow-y-auto p-4 space-y-4 min-h-0">
            {loading && messages.length === 0 ? (
              <div className="flex items-center justify-center h-full">
                <div className="text-center text-muted-foreground">Loading messages...</div>
              </div>
            ) : messages.length === 0 ? (
              <div className="flex items-center justify-center h-full">
                <div className="text-center text-muted-foreground">
                  {trip.status === "completed"
                    ? "No messages were sent during this trip."
                    : "No messages yet. Start the conversation!"}
                </div>
              </div>
            ) : (
              <>
                <MessageList
                  messages={messages}
                  currentUserId={user?.uid || ""}
                  attendees={attendees.map((attendee) => ({
                    id: attendee.id,
                    displayName: attendee.displayName || attendee.email || "Unknown",
                    photoURL: attendee.photoURL,
                  }))}
                  tripId={trip.id}
                  messagesContainerRef={messagesContainerRef}
                />

                {/* AI Loading Indicator */}
                {aiLoading && (
                  <div className="flex items-start gap-3 p-3 bg-muted/30 rounded-lg">
                    <div className="flex-shrink-0">
                      <div className="w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center">
                        <Bot className="h-4 w-4 text-primary" />
                      </div>
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2 mb-1">
                        <span className="text-sm font-medium text-primary">Togeda AI</span>
                      </div>
                      <div className="flex items-center gap-2 text-sm text-muted-foreground">
                        <Loader2 className="h-3 w-3 animate-spin" />
                        <span>AI is thinking...</span>
                      </div>
                    </div>
                  </div>
                )}
              </>
            )}
          </div>

          {/* New Message Indicator */}
          {newMessageCount > 0 && (
            <div className="absolute bottom-20 left-1/2 transform -translate-x-1/2 z-10">
              <Button
                variant="secondary"
                size="sm"
                onClick={handleScrollToBottom}
                className="shadow-lg"
              >
                <ArrowDown className="h-4 w-4 mr-2" />
                {newMessageCount} new message{newMessageCount > 1 ? "s" : ""}
              </Button>
            </div>
          )}

          {/* Message Input */}
          <div className="border-t p-4 flex-shrink-0">
            <MessageInput
              tripId={trip.id}
              tripName={trip.name}
              attendees={attendees.map((attendee) => ({
                id: attendee.id,
                displayName: attendee.displayName || attendee.email || "Unknown",
                photoURL: attendee.photoURL,
              }))}
              currentUser={{
                id: user?.uid || "",
                displayName: user?.displayName || "",
                ...(user?.photoURL && { photoURL: user.photoURL }),
              }}
              canSendMessages={canSendMessages}
              tripStatus={trip.status}
            />
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
