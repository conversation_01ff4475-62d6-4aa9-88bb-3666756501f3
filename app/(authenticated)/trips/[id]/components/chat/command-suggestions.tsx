"use client"

import { useEffect, useRef } from "react"
import { <PERSON><PERSON>, <PERSON>, Zap } from "lucide-react"
import {
  CommandSuggestion,
  ChatCommandCategory,
} from "@/lib/domains/chat-commands/chat-commands.types"

interface CommandSuggestionsProps {
  suggestions: CommandSuggestion[]
  onSelectCommand: (command: string) => void
  onClose: () => void
  inputRef?: React.RefObject<HTMLTextAreaElement | null>
}

export function CommandSuggestions({
  suggestions,
  onSelectCommand,
  onClose,
  inputRef,
}: CommandSuggestionsProps) {
  const containerRef = useRef<HTMLDivElement>(null)

  // Close suggestions when clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (
        containerRef.current &&
        !containerRef.current.contains(event.target as Node) &&
        inputRef?.current &&
        !inputRef.current.contains(event.target as Node)
      ) {
        onClose()
      }
    }

    document.addEventListener("mousedown", handleClickOutside)
    return () => document.removeEventListener("mousedown", handleClickOutside)
  }, [onClose, inputRef])

  // Handle keyboard navigation
  useEffect(() => {
    function handleKeyDown(event: KeyboardEvent) {
      if (event.key === "Escape") {
        onClose()
      }
    }

    document.addEventListener("keydown", handleKeyDown)
    return () => document.removeEventListener("keydown", handleKeyDown)
  }, [onClose])

  if (suggestions.length === 0) {
    return null
  }

  const getCategoryIcon = (category: ChatCommandCategory) => {
    switch (category) {
      case ChatCommandCategory.AI:
        return <Bot className="h-4 w-4 text-blue-500" />
      case ChatCommandCategory.UTILITY:
        return <Zap className="h-4 w-4 text-yellow-500" />
      case ChatCommandCategory.SOCIAL:
        return <Crown className="h-4 w-4 text-purple-500" />
      case ChatCommandCategory.PLANNING:
        return <Crown className="h-4 w-4 text-green-500" />
      default:
        return <Zap className="h-4 w-4 text-gray-500" />
    }
  }

  return (
    <div
      ref={containerRef}
      className="bg-background border border-border rounded-lg shadow-lg z-50 max-h-64 overflow-y-auto"
    >
      <div className="p-2">
        <div className="text-xs font-medium text-muted-foreground mb-2 px-2">
          Available Commands
        </div>
        <div className="space-y-1">
          {suggestions.map((suggestion) => (
            <button
              key={suggestion.command}
              onClick={() => onSelectCommand(suggestion.displayName)}
              disabled={!suggestion.isAvailable}
              className={`w-full text-left px-3 py-2 rounded-md transition-colors ${
                suggestion.isAvailable
                  ? "hover:bg-muted cursor-pointer"
                  : "opacity-50 cursor-not-allowed"
              }`}
            >
              <div className="flex items-start gap-3">
                {/* Category Icon */}
                <div className="flex-shrink-0 mt-0.5">{getCategoryIcon(suggestion.category)}</div>

                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2">
                    <span className="font-mono text-sm font-medium text-primary">
                      {suggestion.displayName}
                    </span>
                    {suggestion.requiresPro && <Crown className="h-3 w-3 text-yellow-500" />}
                    {!suggestion.isAvailable && (
                      <span className="text-xs text-muted-foreground">(Pro required)</span>
                    )}
                  </div>
                  <div className="text-xs text-muted-foreground mt-0.5">
                    {suggestion.description}
                  </div>
                  <div className="text-xs text-muted-foreground/70 mt-1 font-mono">
                    {suggestion.usage}
                  </div>
                </div>
              </div>
            </button>
          ))}
        </div>
      </div>

      {/* Footer hint */}
      <div className="border-t border-border px-3 py-2 bg-muted/30">
        <div className="text-xs text-muted-foreground">
          Press{" "}
          <kbd className="px-1 py-0.5 bg-background border border-border rounded text-xs">Esc</kbd>{" "}
          to close
        </div>
      </div>
    </div>
  )
}
