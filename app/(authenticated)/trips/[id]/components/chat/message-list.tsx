"use client"

import { useEffect, useRef, useState } from "react"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Button } from "@/components/ui/button"
import { <PERSON>ader<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON> } from "lucide-react"
import { formatDistanceToNow } from "date-fns"
import { Message } from "@/lib/domains/message/message.types"
import { useLoadMoreMessages } from "@/lib/domains/message/message.hooks"
import { getBestAvatar, getInitials } from "@/lib/utils/avatar-utils"

interface MessageListProps {
  messages: Message[]
  currentUserId: string
  attendees: Array<{
    id: string
    displayName: string
    photoURL?: string
  }>
  tripId: string
  messagesContainerRef: React.RefObject<HTMLDivElement>
}

export function MessageList({
  messages,
  currentUserId,
  attendees,
  tripId,
  messagesContainerRef,
}: MessageListProps) {
  const { loadMore, loading: loadingMore, hasMore } = useLoadMoreMessages(tripId)
  const [tooltipVisible, setTooltipVisible] = useState<string | null>(null)
  const [tooltipPosition, setTooltipPosition] = useState({ x: 0, y: 0 })
  const [timestampVisible, setTimestampVisible] = useState<string | null>(null)

  const loadMoreTriggered = useRef(false)

  // Handle scroll to detect when user is near top for infinite scroll
  useEffect(() => {
    const container = messagesContainerRef.current
    if (!container) return

    const handleScroll = () => {
      const { scrollTop } = container
      const nearTop = scrollTop < 100

      // Trigger load more when near top and not already loading
      if (nearTop && hasMore && !loadingMore && !loadMoreTriggered.current) {
        loadMoreTriggered.current = true
        loadMore().finally(() => {
          loadMoreTriggered.current = false
        })
      }
    }

    container.addEventListener("scroll", handleScroll)
    return () => container.removeEventListener("scroll", handleScroll)
  }, [hasMore, loadingMore, loadMore, messagesContainerRef])

  const formatMessageTime = (createdAt: any) => {
    if (!createdAt) return ""

    try {
      const date = createdAt.toDate ? createdAt.toDate() : new Date(createdAt)
      return formatDistanceToNow(date, { addSuffix: true })
    } catch (error) {
      return ""
    }
  }

  const renderMessageContent = (content: string) => {
    // Check if message starts with /togeda command
    const startsWithTogeda = content.trim().toLowerCase().startsWith("/togeda")

    if (startsWithTogeda) {
      // Special rendering for /togeda commands
      const togedaMatch = content.match(/^\/togeda\s+(.*)$/i)
      if (togedaMatch) {
        const prompt = togedaMatch[1]
        return (
          <span>
            <span className="inline-block px-2 py-1 rounded-md bg-gradient-to-r from-blue-500/20 to-purple-500/20 border border-blue-300/30 text-blue-700 dark:text-blue-300 font-medium text-sm mr-2">
              /togeda
            </span>
            <span>{prompt}</span>
          </span>
        )
      }
    }

    // Replace @[userId:displayName] with styled mentions
    const mentionRegex = /@\[([^:]+):([^\]]+)\]/g
    const parts = content.split(mentionRegex)

    const result = []
    for (let i = 0; i < parts.length; i += 3) {
      // Regular text
      if (parts[i]) {
        result.push(parts[i])
      }

      // Mention (userId at i+1, displayName at i+2)
      if (parts[i + 1] && parts[i + 2]) {
        const userId = parts[i + 1]
        const displayName = parts[i + 2]
        const isCurrentUser = userId === currentUserId

        result.push(
          <span
            key={`mention-${i}`}
            className={`inline-block px-1 py-0.5 rounded text-sm font-medium ${
              isCurrentUser ? "bg-primary/20 text-primary" : "bg-muted text-muted-foreground"
            }`}
          >
            @{displayName}
          </span>
        )
      }
    }

    return result
  }

  const getAttendeeInfo = (senderId: string) => {
    return attendees.find((attendee) => attendee.id === senderId)
  }

  const handleAvatarClick = (event: React.MouseEvent, messageId: string) => {
    event.preventDefault()
    const rect = event.currentTarget.getBoundingClientRect()
    setTooltipPosition({
      x: rect.left + rect.width / 2,
      y: rect.top - 10,
    })
    setTooltipVisible(messageId)

    // Auto-hide tooltip after 2 seconds
    setTimeout(() => {
      setTooltipVisible(null)
    }, 2000)
  }

  const handleClickOutside = () => {
    setTooltipVisible(null)
  }

  const handleMessageBubbleClick = (messageId: string) => {
    setTimestampVisible(timestampVisible === messageId ? null : messageId)
  }

  // Add click outside listener
  useEffect(() => {
    document.addEventListener("click", handleClickOutside)
    return () => document.removeEventListener("click", handleClickOutside)
  }, [])

  return (
    <div className="space-y-4">
      {/* Load More Button */}
      {hasMore && (
        <div className="text-center">
          <Button
            variant="ghost"
            size="sm"
            onClick={loadMore}
            disabled={loadingMore}
            className="text-muted-foreground"
          >
            {loadingMore ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Loading more messages...
              </>
            ) : (
              "Load more messages"
            )}
          </Button>
        </div>
      )}

      {/* Messages */}
      {messages.map((message) => {
        const isOwnMessage = message.senderId === currentUserId
        const isAIMessage = message.isAIResponse || message.messageType === "ai_response"
        const attendeeInfo = getAttendeeInfo(message.senderId)
        const showAvatar = true // Always show avatar
        const showName = true // Always show name and timestamp for chronological order

        return (
          <div
            key={message.id}
            className={`flex gap-3 ${isOwnMessage ? "justify-end" : "justify-start"}`}
          >
            {/* Avatar for others' messages (left side) */}
            {showAvatar && !isOwnMessage && (
              <div className="relative">
                {isAIMessage ? (
                  <div className="h-8 w-8 shrink-0 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center">
                    <Bot className="h-4 w-4 text-white" />
                  </div>
                ) : (
                  <Avatar
                    className="h-8 w-8 shrink-0 cursor-pointer hover:ring-2 hover:ring-primary/20 transition-all"
                    onClick={(e) => handleAvatarClick(e, message.id)}
                  >
                    <AvatarImage
                      src={getBestAvatar(
                        attendeeInfo?.photoURL || message.senderPhotoURL,
                        attendeeInfo?.displayName || message.senderName,
                        32
                      )}
                      alt={attendeeInfo?.displayName || message.senderName}
                    />
                    <AvatarFallback>
                      {getInitials(attendeeInfo?.displayName || message.senderName)}
                    </AvatarFallback>
                  </Avatar>
                )}
              </div>
            )}

            {/* Message Content */}
            <div
              className={`flex flex-col max-w-[70%] ${isOwnMessage ? "items-end" : "items-start"}`}
            >
              {/* Sender Name (timestamp hidden by default) */}
              {showName && (
                <div className="flex items-center gap-2 mb-1">
                  <div className="flex items-center gap-1">
                    <span className="text-sm font-medium">
                      {isAIMessage ? "Togeda AI" : attendeeInfo?.displayName || message.senderName}
                    </span>
                    {isAIMessage && <Sparkles className="h-3 w-3 text-blue-500" />}
                  </div>
                  {/* Timestamp hidden by default - only shown when message is clicked */}
                </div>
              )}

              {/* Message Bubble */}
              <div
                className={`px-3 py-2 rounded-lg max-w-full break-words cursor-pointer hover:opacity-90 transition-opacity relative group ${
                  isAIMessage
                    ? "bg-gradient-to-br from-blue-50 to-purple-50 dark:from-blue-950/30 dark:to-purple-950/30 border border-blue-200 dark:border-blue-800"
                    : isOwnMessage
                      ? "bg-primary text-primary-foreground"
                      : "bg-muted"
                }`}
                onClick={() => handleMessageBubbleClick(message.id)}
              >
                <div className="text-sm whitespace-pre-wrap">
                  {renderMessageContent(message.content)}
                </div>

                {/* Subtle timestamp indicator - only visible on hover */}
                <Clock
                  className={`absolute top-1 right-1 h-3 w-3 opacity-0 group-hover:opacity-30 transition-opacity ${
                    isOwnMessage ? "text-primary-foreground" : "text-muted-foreground"
                  }`}
                />

                {/* AI Response Indicator */}
                {isAIMessage && (
                  <div className="flex items-center gap-1 mt-2 pt-2 border-t border-blue-200 dark:border-blue-800">
                    <Bot className="h-3 w-3 text-blue-600 dark:text-blue-400" />
                    <span className="text-xs text-blue-600 dark:text-blue-400 font-medium">
                      AI Response
                    </span>
                  </div>
                )}
              </div>

              {/* Timestamp - only shown when message is clicked */}
              {timestampVisible === message.id && (
                <span className="text-xs text-muted-foreground mt-1 animate-in fade-in-0 duration-200">
                  {formatMessageTime(message.createdAt)}
                </span>
              )}
            </div>

            {/* Avatar for own messages (right side) */}
            {showAvatar && isOwnMessage && (
              <div className="relative">
                <Avatar
                  className="h-8 w-8 shrink-0 cursor-pointer hover:ring-2 hover:ring-primary/20 transition-all"
                  onClick={(e) => handleAvatarClick(e, message.id)}
                >
                  <AvatarImage
                    src={getBestAvatar(
                      attendeeInfo?.photoURL || message.senderPhotoURL,
                      attendeeInfo?.displayName || message.senderName,
                      32
                    )}
                    alt={attendeeInfo?.displayName || message.senderName}
                  />
                  <AvatarFallback>
                    {getInitials(attendeeInfo?.displayName || message.senderName)}
                  </AvatarFallback>
                </Avatar>
              </div>
            )}
          </div>
        )
      })}

      {/* Mobile-friendly tooltip */}
      {tooltipVisible && (
        <div
          className="fixed z-50 px-2 py-1 text-xs text-white bg-gray-900 rounded shadow-lg pointer-events-none transform -translate-x-1/2 -translate-y-full"
          style={{
            left: tooltipPosition.x,
            top: tooltipPosition.y,
          }}
        >
          {(() => {
            const message = messages.find((m) => m.id === tooltipVisible)
            if (!message) return ""
            const attendeeInfo = getAttendeeInfo(message.senderId)
            return attendeeInfo?.displayName || message.senderName
          })()}
          <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-900"></div>
        </div>
      )}
    </div>
  )
}
