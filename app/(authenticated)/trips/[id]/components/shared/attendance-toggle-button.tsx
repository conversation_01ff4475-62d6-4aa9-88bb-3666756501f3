"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>r<PERSON><PERSON><PERSON>, UserX, HelpCircle, Heart } from "lucide-react"
import { useToast } from "@/components/ui/use-toast"
import { useUser } from "@/lib/domains/auth/auth.hooks"
import { InlineLoading } from "@/components/inline-loading"
import { cn } from "@/lib/utils"
import { UserTripStatus } from "@/lib/domains/user-trip/user-trip.types"
import { TripStatus } from "@/lib/domains/trip/trip.types"
import { useUserTripStore } from "@/lib/domains/user-trip/user-trip.store"

interface AttendanceToggleButtonProps {
  tripId: string
  status?: UserTripStatus | null
  tripStatus?: TripStatus
  disabled?: boolean
}

export function AttendanceToggleButton({
  tripId,
  status,
  tripStatus,
  disabled = false,
}: AttendanceToggleButtonProps) {
  const user = useUser()
  const { toast } = useToast()
  const { updateUserTripStatus } = useUserTripStore()
  const [updating, setUpdating] = useState(false)

  // Check if attendance is locked (trip is active or completed)
  const isAttendanceLocked = tripStatus === "active" || tripStatus === "completed"
  const isActuallyDisabled = disabled || isAttendanceLocked

  const handleStatusChange = async (newStatus: UserTripStatus) => {
    if (!user) return

    try {
      setUpdating(true)
      const success = await updateUserTripStatus(user.uid, tripId, newStatus)

      if (success) {
        let title = "Status updated"
        let description = "Your attendance status has been updated"

        if (newStatus === "going") {
          title = "You're going!"
          description = "You've been added to the trip attendees"
        } else if (newStatus === "not-going") {
          description = "You've been marked as not attending this trip"
        } else if (newStatus === "undecided") {
          description = "You've been marked as undecided for this trip"
        } else if (newStatus === "interested") {
          title = "Interested!"
          description = "You've been marked as interested in this trip"
        }

        toast({ title, description })
      }
    } catch (error) {
      console.error("Error updating trip status:", error)
      toast({
        title: "Error",
        description: "Failed to update your attendance status",
        variant: "destructive",
      })
    } finally {
      setUpdating(false)
    }
  }

  if (!status) {
    return (
      <div className="space-y-2">
        <div className="flex gap-1 sm:gap-2">
          <div className="h-8 w-16 sm:w-20 bg-muted rounded animate-pulse"></div>
          <div className="h-8 w-16 sm:w-20 bg-muted rounded animate-pulse"></div>
          <div className="h-8 w-16 sm:w-20 bg-muted rounded animate-pulse"></div>
          <div className="h-8 w-16 sm:w-20 bg-muted rounded animate-pulse"></div>
        </div>
      </div>
    )
  }

  const isGoing = status === "going"
  const isNotGoing = status === "not-going"
  const isUndecided = status === "undecided"
  const isInterested = status === "interested"

  return (
    <div className="space-y-2">
      <div className="flex items-center gap-2">
        <span className="text-xs font-medium text-muted-foreground">Your attendance: </span>
        {/* Show locked message for active/completed trips */}
        {isAttendanceLocked && (
          <div className="text-xs text-muted-foreground bg-muted px-2 py-1 rounded">
            {tripStatus === "active" ? "🔒 Trip is active" : "🔒 Trip completed"}
          </div>
        )}
      </div>

      {/* Always show all attendance options */}
      <div className="flex gap-1 sm:gap-2">
        <Button
          variant={isGoing ? "default" : "outline"}
          size="sm"
          onClick={() => handleStatusChange("going")}
          disabled={updating || isActuallyDisabled}
          className={cn(
            "flex-1 sm:flex-none transition-all text-xs sm:text-sm px-2 sm:px-3",
            isGoing && "bg-green-500 hover:bg-green-600 text-white border-green-500",
            !isGoing && "hover:bg-green-50 hover:text-green-700 hover:border-green-200",
            updating && "opacity-70 cursor-wait"
          )}
        >
          {updating && isGoing ? (
            <>
              <InlineLoading size="small" />
              <span className="ml-1 hidden sm:inline">Updating...</span>
            </>
          ) : (
            <>
              <UserCheck className="h-3 w-3 sm:h-4 sm:w-4 sm:mr-1" />
              <span className="hidden sm:inline">Going</span>
            </>
          )}
        </Button>

        <Button
          variant={isInterested ? "default" : "outline"}
          size="sm"
          onClick={() => handleStatusChange("interested")}
          disabled={updating || isActuallyDisabled}
          className={cn(
            "flex-1 sm:flex-none transition-all text-xs sm:text-sm px-2 sm:px-3",
            isInterested && "bg-pink-500 hover:bg-pink-600 text-white border-pink-500",
            !isInterested && "hover:bg-pink-50 hover:text-pink-700 hover:border-pink-200",
            updating && "opacity-70 cursor-wait"
          )}
        >
          {updating && isInterested ? (
            <>
              <InlineLoading size="small" />
              <span className="ml-1 hidden sm:inline">Updating...</span>
            </>
          ) : (
            <>
              <Heart className="h-3 w-3 sm:h-4 sm:w-4 sm:mr-1" />
              <span className="hidden sm:inline">Interested</span>
            </>
          )}
        </Button>

        <Button
          variant={isUndecided ? "default" : "outline"}
          size="sm"
          onClick={() => handleStatusChange("undecided")}
          disabled={updating || isActuallyDisabled}
          className={cn(
            "flex-1 sm:flex-none transition-all text-xs sm:text-sm px-2 sm:px-3",
            isUndecided && "bg-yellow-500 hover:bg-yellow-600 text-white border-yellow-500",
            !isUndecided && "hover:bg-yellow-50 hover:text-yellow-700 hover:border-yellow-200",
            updating && "opacity-70 cursor-wait"
          )}
        >
          {updating && isUndecided ? (
            <>
              <InlineLoading size="small" />
              <span className="ml-1 hidden sm:inline">Updating...</span>
            </>
          ) : (
            <>
              <HelpCircle className="h-3 w-3 sm:h-4 sm:w-4 sm:mr-1" />
              <span className="hidden sm:inline">Maybe</span>
            </>
          )}
        </Button>

        <Button
          variant={isNotGoing ? "default" : "outline"}
          size="sm"
          onClick={() => handleStatusChange("not-going")}
          disabled={updating || isActuallyDisabled}
          className={cn(
            "flex-1 sm:flex-none transition-all text-xs sm:text-sm px-2 sm:px-3",
            isNotGoing && "bg-gray-500 hover:bg-gray-600 text-white border-gray-500",
            !isNotGoing && "hover:bg-red-50 hover:text-red-700 hover:border-red-200",
            updating && "opacity-70 cursor-wait"
          )}
        >
          {updating && isNotGoing ? (
            <>
              <InlineLoading size="small" />
              <span className="ml-1 hidden sm:inline">Updating...</span>
            </>
          ) : (
            <>
              <UserX className="h-3 w-3 sm:h-4 sm:w-4 sm:mr-1" />
              <span className="hidden sm:inline">No</span>
            </>
          )}
        </Button>
      </div>
    </div>
  )
}
