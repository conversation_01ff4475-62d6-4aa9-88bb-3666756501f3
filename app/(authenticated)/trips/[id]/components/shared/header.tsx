"use client"

import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { ArrowLeft, Calendar, Users, MapPin } from "lucide-react"
import { Trip } from "@/lib/domains/trip/trip.types"
import { OptimizedImage } from "@/components/optimized-image"
import { useValidatedTripImageUrl } from "@/lib/hooks/use-validated-image-url"

interface TripHeaderProps {
  trip: Trip
  attendees: string[] | any[]
  squadName?: string
}

export function TripHeader({ trip, attendees, squadName = "Squad" }: TripHeaderProps) {
  const { imageUrl, isValidating } = useValidatedTripImageUrl(trip, "1200x400")

  // Format dates with different formats for mobile and desktop
  const formatDate = (date: any, isShort = false) => {
    if (!date) return ""
    const dateObj = date.toDate ? date.toDate() : new Date(date)
    return dateObj.toLocaleDateString(undefined, {
      month: isShort ? "short" : "long",
      day: "numeric",
      year: "numeric", // Always show full year
    })
  }

  // Format dates in a more readable way
  const formatReadableDate = (date: any) => {
    if (!date) return ""
    const dateObj = date.toDate ? date.toDate() : new Date(date)
    return dateObj.toLocaleDateString(undefined, {
      weekday: "short",
      month: "long",
      day: "numeric",
      year: "numeric",
    })
  }

  const formattedDatesShort =
    trip.startDate && trip.endDate
      ? `${formatDate(trip.startDate, true)} - ${formatDate(trip.endDate, true)}`
      : "No dates set"

  const formattedDatesLong =
    trip.startDate && trip.endDate
      ? `${formatReadableDate(trip.startDate)} - ${formatReadableDate(trip.endDate)}`
      : "No dates set"

  return (
    <div className="mb-4 md:mb-6">
      <div className="relative w-full h-48 sm:h-56 md:h-64 lg:h-72 rounded-lg overflow-hidden mb-3 md:mb-4">
        <OptimizedImage
          src={imageUrl}
          alt={trip.destination}
          fill
          className={`object-cover object-center w-full h-full transition-opacity duration-300 ${
            isValidating ? "opacity-75" : "opacity-100"
          }`}
          priority
          sizes="(max-width: 640px) 100vw, (max-width: 1024px) 90vw, 1200px"
        />
        {trip.imageAttribution && (
          <div className="absolute bottom-0 right-0 bg-black/50 text-white text-xs px-2 py-1 m-1 rounded">
            {trip.imageAttribution.link ? (
              <span>Photo by {trip.imageAttribution.name}</span>
            ) : (
              <span>Photo of {trip.imageAttribution.name}</span>
            )}
          </div>
        )}
        <div className="absolute inset-0 bg-gradient-to-t from-black/80 to-transparent">
          {/* Back button - positioned at top left on mobile with caption */}
          <div className="absolute top-4 left-4 md:hidden z-10">
            <Button
              variant="secondary"
              size="sm"
              className="bg-white/20 hover:bg-white/30 active:bg-white/40 h-8 flex items-center gap-1.5 px-3 text-xs backdrop-blur-sm border border-white/20 shadow-sm transition-all duration-200 hover:shadow-md hover:scale-105 active:scale-95 rounded-full"
              asChild
            >
              <Link href="/trips" className="flex items-center">
                <ArrowLeft className="h-3.5 w-3.5 text-white" />
                <span className="text-white font-medium">Back to Trips</span>
              </Link>
            </Button>
          </div>

          {/* Main content container */}
          <div className="absolute bottom-0 left-0 right-0 p-3 sm:p-4 md:p-6">
            <div className="flex items-start md:items-end">
              {/* Back button - visible only on larger screens */}
              <div className="mr-4 hidden md:block">
                <Button
                  variant="secondary"
                  size="sm"
                  className="bg-white/20 hover:bg-white/30 active:bg-white/40 flex items-center gap-1.5 px-3 backdrop-blur-sm border border-white/20 shadow-sm transition-all duration-200 hover:shadow-md hover:scale-105 active:scale-95 rounded-full"
                  asChild
                >
                  <Link href="/trips" className="flex items-center">
                    <ArrowLeft className="h-4 w-4 text-white" />
                    <span className="text-white font-medium">Back to Trips</span>
                  </Link>
                </Button>
              </div>

              <div className="flex-1">
                {/* Title and status badge */}
                <div className="flex items-start md:items-center justify-between mb-1 md:mb-1 mt-6 md:mt-0">
                  <h1 className="text-xl sm:text-2xl md:text-3xl font-bold text-white mr-2 line-clamp-2 md:line-clamp-none">
                    {trip.destination}
                  </h1>
                  <Badge className="bg-white/90 hover:bg-white text-primary dark:bg-slate-800 dark:hover:bg-slate-700 dark:text-white shrink-0 capitalize border border-transparent">
                    {trip.status}
                  </Badge>
                </div>

                {/* Trip details - simplified and consistent layout */}
                <div className="flex flex-wrap items-center gap-x-3 md:gap-x-4 gap-y-1 md:gap-y-2 text-white/90 mt-1 md:mt-2">
                  <div className="flex items-center gap-1.5">
                    <Calendar className="h-3.5 w-3.5 sm:h-4 sm:w-4" />
                    <span className="text-sm sm:text-base hidden sm:inline">
                      {formattedDatesLong}
                    </span>
                    <span className="text-sm sm:text-base sm:hidden">{formattedDatesShort}</span>
                  </div>
                  <div className="flex items-center gap-1.5">
                    <Users className="h-3.5 w-3.5 sm:h-4 sm:w-4" />
                    <span className="text-sm sm:text-base">{attendees.length} attendees</span>
                  </div>
                  <div className="flex items-center gap-1.5">
                    <MapPin className="h-3.5 w-3.5 sm:h-4 sm:w-4" />
                    <span className="text-sm sm:text-base">{squadName}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
