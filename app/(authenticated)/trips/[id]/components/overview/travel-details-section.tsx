"use client"

import { useState } from "react"
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Upload, Eye, Plane, Building } from "lucide-react"
import { TravelDetailsUpload } from "./travel-details-upload"
import { TravelDetailsViewer } from "./travel-details-viewer"
import { useRealtimeTripTravelDetails } from "@/lib/domains/travel-details/travel-details.realtime.hooks"
import { SectionLoading } from "@/components/section-loading"
import { ErrorBoundary } from "@/components/error-boundary"
import { User } from "@/lib/domains/user/user.types"

interface TravelDetailsSectionProps {
  tripId: string
  userAttendingTrip: boolean
  attendeesDetails: User[]
  currentUserName: string
}

export function TravelDetailsSection({
  tripId,
  userAttendingTrip,
  attendeesDetails,
  currentUserName,
}: TravelDetailsSectionProps) {
  const [activeTab, setActiveTab] = useState("view")
  const { travelDetails, loading, error } = useRealtimeTripTravelDetails(tripId)

  const handleUploadComplete = () => {
    // Switch to view tab after successful upload
    setActiveTab("view")
  }

  if (loading) {
    return <SectionLoading />
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Plane className="h-5 w-5" />
            Travel Details
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <p className="text-muted-foreground">
              Failed to load travel details. Please try refreshing the page.
            </p>
          </div>
        </CardContent>
      </Card>
    )
  }

  // Count members with travel details
  const membersWithFlightDetails = travelDetails.filter((detail) => detail.flightImage).length
  const membersWithAccommodationDetails = travelDetails.filter(
    (detail) => detail.accommodationImage
  ).length

  return (
    <ErrorBoundary>
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Plane className="h-5 w-5" />
            Members Travel Details
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList
              className={`grid w-full ${userAttendingTrip ? "grid-cols-2" : "grid-cols-1"}`}
            >
              <TabsTrigger value="view" className="flex items-center gap-1 sm:gap-2">
                <Eye className="h-4 w-4" />
                <span className="hidden sm:inline">View Details</span>
                <span className="sm:hidden">View</span>
                {(membersWithFlightDetails > 0 || membersWithAccommodationDetails > 0) && (
                  <span className="ml-1 px-1.5 py-0.5 text-xs bg-primary text-primary-foreground rounded-full">
                    {Math.max(membersWithFlightDetails, membersWithAccommodationDetails)}
                  </span>
                )}
              </TabsTrigger>
              {userAttendingTrip && (
                <TabsTrigger value="upload" className="flex items-center gap-1 sm:gap-2">
                  <Upload className="h-4 w-4" />
                  <span className="hidden sm:inline">Upload</span>
                  <span className="sm:hidden">Add</span>
                </TabsTrigger>
              )}
            </TabsList>

            <TabsContent value="view" className="mt-4">
              <TravelDetailsViewer
                travelDetails={travelDetails}
                attendeesDetails={attendeesDetails.map((user) => ({
                  id: user.id,
                  displayName: user.displayName || user.email || "Unknown User",
                  photoURL: user.photoURL,
                }))}
              />
            </TabsContent>

            {userAttendingTrip && (
              <TabsContent value="upload" className="mt-4">
                <TravelDetailsUpload
                  tripId={tripId}
                  memberName={currentUserName}
                  onUploadComplete={handleUploadComplete}
                />
              </TabsContent>
            )}
          </Tabs>

          {/* Summary Stats */}
          {(membersWithFlightDetails > 0 || membersWithAccommodationDetails > 0) && (
            <div className="mt-4 pt-4 border-t">
              <div className="flex items-center justify-center gap-6 text-sm text-muted-foreground">
                {membersWithFlightDetails > 0 && (
                  <div className="flex items-center gap-2">
                    <Plane className="h-4 w-4" />
                    <span>
                      {membersWithFlightDetails} flight detail
                      {membersWithFlightDetails !== 1 ? "s" : ""}
                    </span>
                  </div>
                )}
                {membersWithAccommodationDetails > 0 && (
                  <div className="flex items-center gap-2">
                    <Building className="h-4 w-4" />
                    <span>
                      {membersWithAccommodationDetails} accommodation detail
                      {membersWithAccommodationDetails !== 1 ? "s" : ""}
                    </span>
                  </div>
                )}
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </ErrorBoundary>
  )
}
