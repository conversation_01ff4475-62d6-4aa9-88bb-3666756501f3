"use client"

import { useState, use<PERSON><PERSON><PERSON>, useEffect } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Button } from "@/components/ui/button"
import { getBestAvatar, getInitials } from "@/lib/utils/avatar-utils"
import {
  Calendar,
  DollarSign,
  Users,
  User as UserIcon,
  Edit,
  Star,
  MessageSquare,
} from "lucide-react"
import { TripProgressWidget } from "./progress-widget"
import { TripWeatherWidget } from "./weather-widget"
import { TripSavingsWidget } from "./savings-widget"
import { SquadSavingsReadinessWidget } from "./squad-savings-readiness-widget"
import { TripDetailsEdit } from "./details-edit"
import { TravelDetailsSection } from "./travel-details-section"
import { TripReviewsSection } from "./trip-reviews-section"
import { User } from "@/lib/domains/user/user.types"
import { Trip } from "@/lib/domains/trip/trip.types"
import { Task } from "@/lib/domains/task/task.types"
import { UserTrip } from "@/lib/domains"
import { useTripStore } from "@/lib/domains/trip/trip.store"
import { useUser } from "@/lib/domains/auth/auth.hooks"
import { TripReviewService } from "@/lib/domains/trip-review/trip-review.service"

interface TripOverviewTabProps {
  trip: Trip
  attendees: UserTrip[]
  attendeesDetails: User[]
  pendingTasks: Task[]
  isLeader: boolean
  squadName?: string
  userAttendingTrip: boolean
  isTripOngoing?: boolean
  isActive?: boolean
}

export function TripOverviewTab({
  trip,
  attendees,
  attendeesDetails,
  pendingTasks,
  isLeader,
  userAttendingTrip,
}: TripOverviewTabProps) {
  const [isEditing, setIsEditing] = useState(false)
  const [tripData, setTripData] = useState<Trip>(trip)
  const [userHasReviewed, setUserHasReviewed] = useState<boolean>(false)
  const [reviewLoading, setReviewLoading] = useState<boolean>(true)
  const { updateTrip } = useTripStore()
  const user = useUser()
  const router = useRouter()

  // Check if trip is completed
  const isTripCompleted = tripData.status === "completed"

  // Check if user has reviewed the trip (only for completed trips)
  useEffect(() => {
    const checkUserReview = async () => {
      if (!isTripCompleted || !user || !userAttendingTrip) {
        setReviewLoading(false)
        return
      }

      try {
        setReviewLoading(true)
        const userReview = await TripReviewService.getUserReview(tripData.id, user.uid)
        setUserHasReviewed(userReview !== null)
      } catch (error) {
        console.error("Error checking user review:", error)
        setUserHasReviewed(false)
      } finally {
        setReviewLoading(false)
      }
    }

    checkUserReview()
  }, [isTripCompleted, user, userAttendingTrip, tripData.id])

  // Memoize date conversions to prevent unnecessary re-renders
  const memoizedStartDate = useMemo(() => {
    return tripData.startDate ? tripData.startDate.toDate() : undefined
  }, [tripData.startDate])

  const memoizedEndDate = useMemo(() => {
    return tripData.endDate ? tripData.endDate.toDate() : undefined
  }, [tripData.endDate])

  // Format dates in a more readable way
  const formatReadableDate = (date: Date) => {
    return date.toLocaleDateString(undefined, {
      weekday: "short",
      month: "long",
      day: "numeric",
      year: "numeric",
    })
  }

  const handleSaveTrip = async (updatedTrip: Trip) => {
    const success = await updateTrip(updatedTrip.id, updatedTrip)
    if (success) {
      setTripData(updatedTrip)
      setIsEditing(false)
    }
  }

  return (
    <div className="space-y-4 md:space-y-6">
      {/* Row 1: Trip Details (left) and Trip Progress (right) */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Trip Details Card */}
        {isEditing ? (
          <TripDetailsEdit
            trip={tripData}
            onCancel={() => setIsEditing(false)}
            onSave={handleSaveTrip}
          />
        ) : (
          <Card>
            <CardHeader className="pb-2">
              <div className="flex justify-between">
                <CardTitle>Trip Details</CardTitle>
                {isLeader && !isTripCompleted && (
                  <Button variant="ghost" size="sm" onClick={() => setIsEditing(true)}>
                    <Edit className="h-4 w-4 mr-2" /> Edit
                  </Button>
                )}
                {isTripCompleted && (
                  <div className="flex items-center gap-2">
                    <span className="text-sm text-muted-foreground bg-green-100 text-green-800 px-2 py-1 rounded-full">
                      ✅ Completed
                    </span>
                  </div>
                )}
              </div>
            </CardHeader>
            <CardContent>
              <h3 className="font-medium text-base md:text-lg">{tripData.name}</h3>
              <p className="text-muted-foreground mt-1">
                {tripData.description || "No description provided."}
              </p>

              <div className="grid grid-cols-2 gap-3 md:gap-4 mt-3 md:mt-4">
                <div className="space-y-1">
                  <div className="text-sm text-muted-foreground flex items-center">
                    <Calendar className="h-4 w-4 mr-2" /> Dates
                  </div>
                  <div className="text-sm">
                    {tripData.startDate && tripData.endDate ? (
                      <>
                        <span className="block">
                          {formatReadableDate(tripData.startDate.toDate())}
                        </span>
                        <span className="block text-muted-foreground">to</span>
                        <span className="block">
                          {formatReadableDate(tripData.endDate.toDate())}
                        </span>
                      </>
                    ) : (
                      "No dates set"
                    )}
                  </div>
                </div>
                <div className="space-y-1">
                  <div className="text-sm text-muted-foreground flex items-center">
                    <DollarSign className="h-4 w-4 mr-2" /> Budget
                  </div>
                  <p>$ {tripData.budget}</p>
                </div>
                <div className="space-y-1">
                  <div className="text-sm text-muted-foreground flex items-center">
                    <Users className="h-4 w-4 mr-2" /> Attendees
                  </div>
                  <p>{attendees.length} people</p>
                </div>
                <div className="space-y-1">
                  <div className="text-sm text-muted-foreground flex items-center">
                    <UserIcon className="h-4 w-4 mr-2" /> Organized by
                  </div>
                  <p>{isLeader ? "You" : "Squad Leader"}</p>
                </div>
              </div>

              <div className="mt-3 md:mt-4">
                <div className="flex -space-x-2 overflow-hidden">
                  {attendeesDetails.slice(0, 5).map((attendee, index) => (
                    <Avatar key={index} className="border-2 border-background">
                      <AvatarImage
                        src={getBestAvatar(attendee.photoURL, attendee.displayName, 40)}
                        alt={attendee.displayName || "User"}
                      />
                      <AvatarFallback>{getInitials(attendee.displayName)}</AvatarFallback>
                    </Avatar>
                  ))}
                  {attendees.length > 5 && (
                    <div className="flex items-center justify-center w-10 h-10 rounded-full border-2 border-background bg-muted text-muted-foreground text-xs">
                      +{attendees.length - 5}
                    </div>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Trip Review Section - Only show for completed trips */}
        {isTripCompleted && userAttendingTrip && (
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="flex items-center gap-2">
                <Star className="h-5 w-5 text-yellow-500" />
                Trip Review
                {!reviewLoading && userHasReviewed && (
                  <span className="text-sm bg-green-100 text-green-800 px-2 py-1 rounded-full ml-2">
                    ✅ Reviewed
                  </span>
                )}
                {!reviewLoading && !userHasReviewed && (
                  <span className="text-sm bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full ml-2">
                    ⏳ Review Needed
                  </span>
                )}
              </CardTitle>
            </CardHeader>
            <CardContent>
              {reviewLoading ? (
                <div className="flex items-center justify-center py-4">
                  <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
                  <span className="ml-2 text-muted-foreground">Checking review status...</span>
                </div>
              ) : (
                <>
                  <p className="text-muted-foreground mb-4">
                    {userHasReviewed
                      ? "You've already reviewed this trip. You can view or update your review."
                      : "Share your experience with your squad and help improve future trips."}
                  </p>
                  <div className="flex flex-col sm:flex-row gap-3">
                    <Button
                      onClick={() => router.push(`/trips/${tripData.id}/review`)}
                      className="flex-1"
                      variant={userHasReviewed ? "outline" : "default"}
                    >
                      <Star className="h-4 w-4 mr-2" />
                      {userHasReviewed ? "View Review" : "Review Trip"}
                    </Button>
                    <Button
                      variant="outline"
                      onClick={() => router.push(`/trips/${tripData.id}?tab=chat`)}
                      className="flex-1"
                    >
                      <MessageSquare className="h-4 w-4 mr-2" />
                      Share Memories
                    </Button>
                  </div>
                </>
              )}
            </CardContent>
          </Card>
        )}

        {/* Trip Progress Widget - Only show if user is attending and trip is not completed */}
        {userAttendingTrip && !isTripCompleted && (
          <TripProgressWidget
            tripId={tripData.id}
            tasksCompleted={tripData.tasksCompleted}
            totalTasks={tripData.totalTasks}
            pendingTasks={pendingTasks.slice(0, 5)} /* Show up to 5 tasks */
          />
        )}
      </div>

      {/* Only show these components if user is attending */}
      {userAttendingTrip && (
        <>
          {/* Row 2: Your Trip Savings (left) and Weather Forecast (right) */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <TravelDetailsSection
              tripId={tripData.id}
              userAttendingTrip={userAttendingTrip}
              attendeesDetails={attendeesDetails}
              currentUserName={
                attendeesDetails.find((attendee) => attendee.id === user?.uid)?.displayName ||
                user?.displayName ||
                user?.email ||
                "Unknown User"
              }
            />
            <TripSavingsWidget
              tripId={tripData.id}
              tripName={tripData.destination}
              totalCost={tripData.budget}
              startDate={memoizedStartDate}
            />

            <TripWeatherWidget
              destination={tripData.destination}
              startDate={memoizedStartDate}
              endDate={memoizedEndDate}
            />
          </div>

          {/* Row 3: Squad Savings (full width) */}
          <SquadSavingsReadinessWidget
            tripId={tripData.id}
            tripName={tripData.destination}
            totalCost={6000}
            isOrganizer={isLeader}
          />
        </>
      )}

      {/* Trip Reviews Section - Show for completed trips */}
      {isTripCompleted && <TripReviewsSection tripId={tripData.id} isVisible={true} />}
    </div>
  )
}
