"use client"

import Link from "next/link"
import { Button } from "@/components/ui/button"
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON>er, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Clock } from "lucide-react"
import { Task, User } from "@/lib/firebase-service"

interface TripProgressWidgetProps {
  tripId: string
  tasksCompleted: number
  totalTasks: number
  pendingTasks: (Task & { assignee?: User })[]
}

export function TripProgressWidget({
  tripId,
  tasksCompleted,
  totalTasks,
  pendingTasks,
}: TripProgressWidgetProps) {
  return (
    <Card>
      <CardHeader className="pb-2">
        <CardTitle>Trip Progress</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="space-y-2">
            <div className="flex justify-between items-center">
              <h4 className="text-sm font-medium">Tasks Completed</h4>
              <span className="text-sm text-muted-foreground">
                {tasksCompleted} of {totalTasks}
              </span>
            </div>
            <div className="w-full h-2 bg-muted rounded-full overflow-hidden">
              <div
                className="h-full bg-primary"
                style={{
                  width: `${totalTasks > 0 ? (tasksCompleted / totalTasks) * 100 : 0}%`,
                }}
              ></div>
            </div>
          </div>

          <div className="pt-2">
            <h4 className="text-sm font-medium mb-2">Upcoming Tasks</h4>
            <div className="space-y-2">
              {pendingTasks.length > 0 ? (
                pendingTasks.map((task) => (
                  <div
                    key={task.id}
                    className="flex items-center justify-between p-2 rounded-md border"
                  >
                    <div className="flex items-center gap-2">
                      <div className="h-6 w-6 rounded-full border flex items-center justify-center">
                        <Clock className="h-3 w-3 text-muted-foreground" />
                      </div>
                      <span className="text-sm">{task.title}</span>
                    </div>
                    <Badge variant="outline" className="text-xs">
                      {task.assignee?.displayName || "Unassigned"}
                    </Badge>
                  </div>
                ))
              ) : (
                <div className="text-center p-4 border rounded-md">
                  <p className="text-sm text-muted-foreground">No pending tasks</p>
                </div>
              )}
            </div>
          </div>
        </div>
      </CardContent>
      <CardFooter>
        <Link href={`/trips/${tripId}?tab=tasks`} className="w-full">
          <Button variant="outline" className="w-full">
            View All Tasks
          </Button>
        </Link>
      </CardFooter>
    </Card>
  )
}
