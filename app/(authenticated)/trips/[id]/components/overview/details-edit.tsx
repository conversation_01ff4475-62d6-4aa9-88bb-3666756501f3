"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { useToast } from "@/components/ui/use-toast"
import { Trip } from "@/lib/domains/trip/trip.types"
import { useUpdateTrip } from "@/lib/domains/trip/trip.hooks"
import { Timestamp } from "firebase/firestore"
import { Check, X } from "lucide-react"

interface TripDetailsEditProps {
  trip: Trip
  onCancel: () => void
  onSave: (updatedTrip: Trip) => void
}

export function TripDetailsEdit({ trip, onCancel, onSave }: TripDetailsEditProps) {
  const { toast } = useToast()
  const { update, updating } = useUpdateTrip(trip.id)
  const [name, setName] = useState(trip.name)
  const [description, setDescription] = useState(trip.description || "")
  const [budget, setBudget] = useState<number>(trip.budget)
  const [startDate, setStartDate] = useState(
    trip.startDate ? trip.startDate.toDate().toISOString().split("T")[0] : ""
  )
  const [endDate, setEndDate] = useState(
    trip.endDate ? trip.endDate.toDate().toISOString().split("T")[0] : ""
  )
  const [status, setStatus] = useState<
    "planning" | "upcoming" | "active" | "completed" | "cancelled"
  >(trip.status)

  // Check if trip editing is restricted
  const isEditingRestricted = trip.status === "active" || trip.status === "completed"
  const canEditDates = trip.status === "planning" // Only allow date changes in planning phase

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    // Prevent editing if trip is active or completed
    if (isEditingRestricted) {
      toast({
        title: "Editing Restricted",
        description: "Trip details cannot be modified once the trip is active or completed.",
        variant: "destructive",
      })
      return
    }

    // Validate required fields
    if (!name || !startDate || !endDate) {
      toast({
        title: "Missing fields",
        description: "Please fill in all required fields",
        variant: "destructive",
      })
      return
    }

    // Validate budget is a number

    if (isNaN(budget) || budget <= 0) {
      toast({
        title: "Invalid budget",
        description: "Please enter a valid budget amount",
        variant: "destructive",
      })
      return
    }

    try {
      // Convert dates to Firebase Timestamp format

      const updatedTripData: any = {
        name,
        description,
        budget,
        status: status as Trip["status"],
      }

      // Add dates if they are provided - they will be normalized to UTC in the service layer
      if (startDate) {
        updatedTripData.startDate = Timestamp.fromDate(new Date(startDate))
      }

      if (endDate) {
        updatedTripData.endDate = Timestamp.fromDate(new Date(endDate))
      }

      await update(updatedTripData)

      toast({
        title: "Trip updated",
        description: "Trip details have been updated successfully",
      })

      onSave({
        ...trip,
        ...updatedTripData,
      })
    } catch (error) {
      console.error("Error updating trip:", error)
      toast({
        title: "Error",
        description: "Failed to update trip details. Please try again.",
        variant: "destructive",
      })
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Edit Trip Details</CardTitle>
        <CardDescription>
          {isEditingRestricted
            ? "Trip details are read-only for active and completed trips"
            : "Update information about your trip"}
        </CardDescription>
        {isEditingRestricted && (
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3 mt-2">
            <div className="flex items-center gap-2 text-yellow-800">
              <span className="text-sm font-medium">🔒 Editing Restricted</span>
            </div>
            <p className="text-yellow-700 text-xs mt-1">
              Trip details cannot be modified once the trip is {trip.status}. This ensures data
              integrity during and after the trip.
            </p>
          </div>
        )}
      </CardHeader>
      <form onSubmit={handleSubmit}>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="name">Trip Name</Label>
            <Input
              id="name"
              value={name}
              onChange={(e) => setName(e.target.value)}
              placeholder="Enter trip name"
              disabled={isEditingRestricted}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="destination">Destination</Label>
            <Input id="destination" value={trip.destination} disabled className="bg-muted" />
            <p className="text-sm text-muted-foreground">
              Destination cannot be changed. If the destination is incorrect, please delete this
              trip and create a new one.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="startDate">Start Date</Label>
              <Input
                id="startDate"
                type="date"
                value={startDate}
                onChange={(e) => setStartDate(e.target.value)}
                disabled={!canEditDates}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="endDate">End Date</Label>
              <Input
                id="endDate"
                type="date"
                value={endDate}
                onChange={(e) => setEndDate(e.target.value)}
                min={startDate}
                disabled={!canEditDates}
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="budget">Budget (USD)</Label>
              <div className="relative">
                <span className="absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground">
                  $
                </span>
                <Input
                  id="budget"
                  type="text"
                  pattern="[0-9]*"
                  inputMode="numeric"
                  value={budget}
                  onChange={(e) => {
                    // Only allow whole numbers (no decimals)
                    const value = e.target.value && parseInt(e.target.value)
                    setBudget(value || 0)
                  }}
                  className="pl-6"
                  placeholder="1000"
                  maxLength={10}
                  disabled={isEditingRestricted}
                />
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="status">Trip Status</Label>
              <Select
                value={status}
                onValueChange={(value) => setStatus(value as Trip["status"])}
                disabled={isEditingRestricted}
              >
                <SelectTrigger id="status">
                  <SelectValue placeholder="Select status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="planning">Planning</SelectItem>
                  <SelectItem value="upcoming">Upcoming</SelectItem>
                  <SelectItem value="active">Active</SelectItem>
                  <SelectItem value="completed">Completed</SelectItem>
                  <SelectItem value="cancelled">Cancelled</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              placeholder="Enter trip description"
              rows={4}
              disabled={isEditingRestricted}
            />
          </div>
        </CardContent>
        <CardFooter className="flex justify-between">
          <Button type="button" variant="outline" onClick={onCancel} disabled={updating}>
            <X className="mr-2 h-4 w-4" /> Cancel
          </Button>
          <Button type="submit" disabled={updating || isEditingRestricted}>
            {updating ? (
              <div className="flex items-center">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Saving...
              </div>
            ) : isEditingRestricted ? (
              <>
                <Check className="mr-2 h-4 w-4" /> Editing Disabled
              </>
            ) : (
              <>
                <Check className="mr-2 h-4 w-4" /> Save Changes
              </>
            )}
          </Button>
        </CardFooter>
      </form>
    </Card>
  )
}
