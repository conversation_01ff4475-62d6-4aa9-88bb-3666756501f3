"use client"

import { useState, useRef, useCallback } from "react"
import { But<PERSON> } from "@/components/ui/button"

import { Label } from "@/components/ui/label"
import { Plane, Building, X, Loader2, Maximize2 } from "lucide-react"
import { useTravelDetails } from "@/lib/domains/travel-details/travel-details.hooks"
import { useUser } from "@/lib/domains/auth/auth.hooks"
import { toast } from "@/components/ui/use-toast"
import { FullscreenImageViewer } from "./travel-details-fullscreen-viewer"
import { ImageUploadWithCompression } from "@/components/ui/image-upload-with-compression"
import Image from "next/image"

interface TravelDetailsUploadProps {
  tripId: string
  memberName: string
  onUploadComplete?: () => void
}

export function TravelDetailsUpload({
  tripId,
  memberName,
  onUploadComplete,
}: TravelDetailsUploadProps) {
  const user = useUser()
  const { uploadImage, isUploading, getMemberDetails } = useTravelDetails()

  const [flightFile, setFlightFile] = useState<File | null>(null)
  const [accommodationFile, setAccommodationFile] = useState<File | null>(null)
  const [fullscreenImage, setFullscreenImage] = useState<{
    url: string
    type: "flight" | "accommodation"
  } | null>(null)

  // Get existing member details
  const memberDetails = getMemberDetails(user?.uid || "")

  const handleFlightFileSelect = useCallback((file: File) => {
    setFlightFile(file)
  }, [])

  const handleAccommodationFileSelect = useCallback((file: File) => {
    setAccommodationFile(file)
  }, [])

  const handleUploadError = useCallback((error: string) => {
    toast({
      title: "Upload Error",
      description: error,
      variant: "destructive",
    })
  }, [])

  const handleUpload = useCallback(
    async (type: "flight" | "accommodation") => {
      if (!user?.uid) {
        toast({
          title: "Authentication required",
          description: "Please log in to upload images.",
          variant: "destructive",
        })
        return
      }

      const file = type === "flight" ? flightFile : accommodationFile
      if (!file) {
        toast({
          title: "No file selected",
          description: "Please select a file first.",
          variant: "destructive",
        })
        return
      }

      const success = await uploadImage(tripId, user?.uid || "", memberName, { file, type })

      if (success) {
        // Clear the selected file
        if (type === "flight") {
          setFlightFile(null)
        } else {
          setAccommodationFile(null)
        }

        onUploadComplete?.()
      } else {
        console.log("Upload failed")
      }
    },
    [user?.uid, uploadImage, tripId, memberName, onUploadComplete, flightFile, accommodationFile]
  )

  const clearFile = useCallback((type: "flight" | "accommodation") => {
    if (type === "flight") {
      setFlightFile(null)
    } else {
      setAccommodationFile(null)
    }
  }, [])

  const handleFullscreenOpen = useCallback(
    (url: string, type: "flight" | "accommodation", e?: React.MouseEvent | React.TouchEvent) => {
      if (e) {
        e.preventDefault()
        e.stopPropagation()
      }
      setFullscreenImage({ url, type })
    },
    []
  )

  const handleFullscreenClose = useCallback(() => {
    setFullscreenImage(null)
  }, [])

  const isUploadingFlight = isUploading(user?.uid || "")
  const isUploadingAccommodation = isUploading(user?.uid || "")

  return (
    <div className="space-y-6">
      {/* Flight Upload */}
      <div className="space-y-3">
        <Label className="flex items-center gap-2 text-sm font-medium">
          <Plane className="h-4 w-4" />
          Flight Details
        </Label>

        {memberDetails?.flightImage && !flightFile ? (
          <div className="relative">
            <div
              className="aspect-video relative overflow-hidden rounded-lg border group cursor-pointer"
              onClick={(e) => handleFullscreenOpen(memberDetails?.flightImage || "", "flight", e)}
              onTouchEnd={(e) => {
                e.preventDefault()
                handleFullscreenOpen(memberDetails?.flightImage || "", "flight", e)
              }}
            >
              <Image
                src={memberDetails?.flightImage || ""}
                alt="Flight details"
                fill
                className="object-cover pointer-events-none"
              />
              {/* Fullscreen Button Overlay */}
              <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors duration-200 flex items-center justify-center pointer-events-none">
                <Button
                  variant="secondary"
                  size="sm"
                  onClick={(e) => {
                    e.stopPropagation()
                    handleFullscreenOpen(memberDetails?.flightImage || "", "flight", e)
                  }}
                  className="opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-auto"
                >
                  <Maximize2 className="h-4 w-4 mr-2" />
                  View Fullscreen
                </Button>
              </div>
              {/* Mobile tap hint */}
              <div className="absolute bottom-2 left-2 sm:hidden">
                <div className="bg-black/50 text-white text-xs px-2 py-1 rounded">Tap to zoom</div>
              </div>
            </div>
            <Button variant="outline" className="w-full mt-2" onClick={() => clearFile("flight")}>
              Replace Flight Image
            </Button>
          </div>
        ) : (
          <div className="space-y-4">
            <ImageUploadWithCompression
              preset="travelDetails"
              onFileSelect={handleFlightFileSelect}
              onError={handleUploadError}
              showPreview={true}
              showCompressionStats={true}
              className="w-full"
            />
            {flightFile && (
              <Button
                onClick={() => handleUpload("flight")}
                disabled={isUploadingFlight}
                className="w-full"
              >
                {isUploadingFlight ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Uploading...
                  </>
                ) : (
                  "Upload Flight Image"
                )}
              </Button>
            )}
          </div>
        )}
      </div>

      {/* Accommodation Upload */}
      <div className="space-y-3">
        <Label className="flex items-center gap-2 text-sm font-medium">
          <Building className="h-4 w-4" />
          Accommodation Details
        </Label>

        {memberDetails?.accommodationImage && !accommodationFile ? (
          <div className="relative">
            <div
              className="aspect-video relative overflow-hidden rounded-lg border group cursor-pointer"
              onClick={(e) =>
                handleFullscreenOpen(memberDetails?.accommodationImage || "", "accommodation", e)
              }
              onTouchEnd={(e) => {
                e.preventDefault()
                handleFullscreenOpen(memberDetails?.accommodationImage || "", "accommodation", e)
              }}
            >
              <Image
                src={memberDetails?.accommodationImage || ""}
                alt="Accommodation details"
                fill
                className="object-cover pointer-events-none"
              />
              {/* Fullscreen Button Overlay */}
              <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors duration-200 flex items-center justify-center pointer-events-none">
                <Button
                  variant="secondary"
                  size="sm"
                  onClick={(e) => {
                    e.stopPropagation()
                    handleFullscreenOpen(
                      memberDetails?.accommodationImage || "",
                      "accommodation",
                      e
                    )
                  }}
                  className="opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-auto"
                >
                  <Maximize2 className="h-4 w-4 mr-2" />
                  View Fullscreen
                </Button>
              </div>
              {/* Mobile tap hint */}
              <div className="absolute bottom-2 left-2 sm:hidden">
                <div className="bg-black/50 text-white text-xs px-2 py-1 rounded">Tap to zoom</div>
              </div>
            </div>
            <Button
              variant="outline"
              className="w-full mt-2"
              onClick={() => clearFile("accommodation")}
            >
              Replace Accommodation Image
            </Button>
          </div>
        ) : (
          <div className="space-y-4">
            <ImageUploadWithCompression
              preset="travelDetails"
              onFileSelect={handleAccommodationFileSelect}
              onError={handleUploadError}
              showPreview={true}
              showCompressionStats={true}
              className="w-full"
            />
            {accommodationFile && (
              <Button
                onClick={() => handleUpload("accommodation")}
                disabled={isUploadingAccommodation}
                className="w-full"
              >
                {isUploadingAccommodation ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Uploading...
                  </>
                ) : (
                  "Upload Accommodation Image"
                )}
              </Button>
            )}
          </div>
        )}
      </div>

      {/* Fullscreen Viewer */}
      {fullscreenImage && (
        <FullscreenImageViewer
          isOpen={!!fullscreenImage}
          onClose={handleFullscreenClose}
          imageUrl={fullscreenImage.url}
          imageAlt={`${fullscreenImage.type} details`}
          memberName={memberName}
          imageType={fullscreenImage.type}
        />
      )}
    </div>
  )
}
