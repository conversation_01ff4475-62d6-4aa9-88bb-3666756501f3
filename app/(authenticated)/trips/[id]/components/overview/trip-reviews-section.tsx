"use client"

import { useState, useEffect, useMemo, useC<PERSON>back } from "react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Star, Calendar, ChevronDown, ChevronUp } from "lucide-react"
import {
  useRealtimeTripReviews,
  useRealtimeTripReviewAggregate,
} from "@/lib/domains/trip-review/trip-review.realtime.hooks"
import { TripReview } from "@/lib/domains/trip-review/trip-review.types"

interface TripReviewsSectionProps {
  tripId: string
  isVisible?: boolean
}

export function TripReviewsSection({ tripId, isVisible = true }: TripReviewsSectionProps) {
  const [showAllReviews, setShowAllReviews] = useState(false)

  // Memoize tripId to prevent unnecessary re-subscriptions
  const memoizedTripId = useMemo(() => (isVisible ? tripId : ""), [isVisible, tripId])

  const { reviews, loading: reviewsLoading } = useRealtimeTripReviews(memoizedTripId)
  const { aggregate, loading: aggregateLoading } = useRealtimeTripReviewAggregate(memoizedTripId)

  const loading = reviewsLoading || aggregateLoading

  // Memoize computed values to prevent unnecessary re-calculations
  const displayedReviews = useMemo(() => {
    return showAllReviews ? reviews : reviews.slice(0, 3)
  }, [reviews, showAllReviews])

  const hasMoreReviews = useMemo(() => reviews.length > 3, [reviews.length])

  const renderStarRating = useCallback((rating: number) => {
    return (
      <div className="flex items-center gap-1">
        {[...Array(5)].map((_, i) => (
          <Star
            key={i}
            className={`h-4 w-4 ${
              i < rating ? "text-yellow-400 fill-yellow-400" : "text-muted-foreground"
            }`}
          />
        ))}
      </div>
    )
  }, [])

  const renderReviewItem = useCallback(
    (review: TripReview) => {
      return (
        <div key={review.id} className="border rounded-lg p-4 space-y-3">
          <div className="flex items-start justify-between">
            <div className="flex items-center space-x-3">
              <Avatar className="h-8 w-8">
                <AvatarImage src={review.userPhotoURL || ""} alt={review.userDisplayName || ""} />
                <AvatarFallback>
                  {review.userDisplayName?.charAt(0).toUpperCase() || "U"}
                </AvatarFallback>
              </Avatar>
              <div>
                <p className="font-medium text-sm">{review.userDisplayName || "Anonymous"}</p>
                <div className="flex items-center space-x-2">
                  {renderStarRating(review.rating)}
                  <span className="text-xs text-muted-foreground">
                    {review.reviewDate?.toDate?.()?.toLocaleDateString() ||
                      review.createdAt?.toDate?.()?.toLocaleDateString() ||
                      "Recently"}
                  </span>
                </div>
              </div>
            </div>
          </div>
          <p className="text-sm leading-relaxed">{review.feedback}</p>
        </div>
      )
    },
    [renderStarRating]
  )

  if (!isVisible) {
    return null
  }

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Star className="h-5 w-5 text-yellow-500" />
            Trip Reviews
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="animate-pulse space-y-4">
            <div className="h-4 bg-muted rounded w-1/3"></div>
            <div className="space-y-3">
              {[...Array(2)].map((_, i) => (
                <div key={i} className="border rounded-lg p-4 space-y-3">
                  <div className="flex items-center space-x-3">
                    <div className="h-8 w-8 bg-muted rounded-full"></div>
                    <div className="space-y-1">
                      <div className="h-3 bg-muted rounded w-20"></div>
                      <div className="h-3 bg-muted rounded w-16"></div>
                    </div>
                  </div>
                  <div className="h-4 bg-muted rounded w-full"></div>
                  <div className="h-4 bg-muted rounded w-3/4"></div>
                </div>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (!reviews.length) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Star className="h-5 w-5 text-yellow-500" />
            Trip Reviews
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-6">
            <Star className="h-12 w-12 text-muted-foreground mx-auto mb-3" />
            <p className="text-muted-foreground">No reviews yet</p>
            <p className="text-sm text-muted-foreground mt-1">
              Be the first to share your experience!
            </p>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Star className="h-5 w-5 text-yellow-500" />
          Trip Reviews
        </CardTitle>
        {aggregate && aggregate.totalReviews > 0 && (
          <div className="flex items-center gap-4 text-sm text-muted-foreground">
            <div className="flex items-center gap-2">
              {renderStarRating(Math.round(aggregate.averageRating))}
              <span className="font-medium">{aggregate.averageRating.toFixed(1)} out of 5</span>
            </div>
            <span>
              ({aggregate.totalReviews} review{aggregate.totalReviews !== 1 ? "s" : ""})
            </span>
          </div>
        )}
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-3">{displayedReviews.map(renderReviewItem)}</div>

        {hasMoreReviews && (
          <div className="text-center pt-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowAllReviews(!showAllReviews)}
              className="text-muted-foreground hover:text-foreground"
            >
              {showAllReviews ? (
                <>
                  <ChevronUp className="h-4 w-4 mr-1" />
                  Show Less
                </>
              ) : (
                <>
                  <ChevronDown className="h-4 w-4 mr-1" />
                  Show All {reviews.length} Reviews
                </>
              )}
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
