"use client"

import { DollarSign } from "lucide-react"
import { <PERSON> } from "@/lib/domains/trip/trip.types"
import { FeatureComingSoon } from "@/components/feature-coming-soon"

interface TripExpensesTabProps {
  trip: Trip
  attendees: any[]
}

export function TripExpensesTab(_props: TripExpensesTabProps) {
  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-semibold">Trip Expenses</h2>
      </div>

      <FeatureComingSoon
        title="Expense Tracking Coming Soon"
        description="We're working on an awesome expense tracking and splitting feature for your trips."
        icon={<DollarSign className="h-6 w-6 md:h-8 md:w-8 text-primary" />}
      />
    </div>
  )
}
