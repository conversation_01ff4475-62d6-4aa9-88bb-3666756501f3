import { type NextRequest, NextResponse } from "next/server"
import OpenA<PERSON> from "openai"
import type { Trip, User } from "@/lib/firebase-service"
import { verifyAuth } from "@/lib/api-auth"
import { ALLOWED_TRAVEL_TYPES } from "@/lib/constants/travel-types"
import {
  findAffiliateLink,
  AFFILIATE_LINK_TAGS,
  affiliateLinksMap,
} from "@/lib/affiliate-links-map"

// Create a server-side OpenAI API client
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
})

// Helper function to format dates that can handle both Firestore Timestamps and regular Date objects/strings
function formatDate(dateValue: any): string {
  if (!dateValue) return "Not set"

  try {
    // Check if it's a Firestore Timestamp with toDate method
    if (
      dateValue &&
      typeof dateValue === "object" &&
      "toDate" in dateValue &&
      typeof dateValue.toDate === "function"
    ) {
      return new Date(dateValue.toDate()).toLocaleDateString()
    }

    // Handle regular Date objects or strings
    return new Date(dateValue).toLocaleDateString()
  } catch (error) {
    console.error("Error formatting date:", error)
    return "Invalid date"
  }
}

// Define types for our suggestion systems
interface TaskSuggestion {
  title: string
  description: string
  category: "planning" | "booking" | "preparation" | "coordination" | "during-trip"
  priority: "high" | "medium" | "low"
  tags?: string[]
  hasAffiliateLink?: boolean
  affiliateLink?: AffiliateLink
}

interface TripSuggestion {
  destination: string
  description: string
  tags: string[]
  budget: string
  imageUrl?: string
  placeId?: string
}

interface ActivitySuggestion {
  title: string
  description: string
  cost: string
  duration: string
}

interface ItinerarySuggestion {
  title: string
  description: string
  day: number
  timeOfDay: "morning" | "afternoon" | "evening"
  duration: string
  location?: string // Primary location string for easy copying
  tags?: string[]
  // Google Places integration
  googlePlaces?: {
    place_id: string
    rating?: number
    price_level?: number // 0-4 scale (0=Free, 1=$, 2=$$, 3=$$$, 4=$$$$)
    user_ratings_total?: number
    formatted_address?: string
    phone_number?: string
    website?: string
    opening_hours?: {
      open_now?: boolean
      weekday_text?: string[]
    }
    photos?: Array<{
      photo_reference: string
      width: number
      height: number
    }>
    types?: string[] // Google Places types (restaurant, tourist_attraction, etc.)
    geometry?: {
      location: {
        lat: number
        lng: number
      }
    }
  }
}

interface AffiliateLink {
  url: string
  title: string
  description: string
}

export async function POST(request: NextRequest) {
  try {
    // Verify authentication using the new auth system
    const authResult = await verifyAuth(request)

    if (!authResult.isAuthenticated) {
      // Return the error response if authentication failed
      return authResult.response
    }

    const userId = authResult.userId

    const { type, data } = await request.json()

    // Validate required parameters
    if (!type) {
      return NextResponse.json({ error: "Missing required parameter: type" }, { status: 400 })
    }

    if (!data) {
      return NextResponse.json({ error: "Missing required parameter: data" }, { status: 400 })
    }

    // Log the AI request to Firestore for auditing
    // Temporarily disable logging to Firestore until security rules are updated
    /*
    try {
      // Create a log object with required fields
      const logData: {
        type: string
        data: any
        timestamp: any
        userId?: string
      } = {
        type,
        data,
        timestamp: serverTimestamp(),
      }

      // Only add userId if it's provided
      if (userId) {
        logData.userId = userId
      }

      await addDoc(collection(db, "ai_requests"), logData)
    } catch (logError) {
      // Log the error but continue with the request
      console.error("Error logging AI request:", logError)
      // Don't fail the whole request if logging fails
    }
    */

    // Just log to console for now
    console.log(`AI request: ${type} from user ${userId || "unknown"}`)

    let result

    switch (type) {
      case "trip_suggestions":
        result = await generateTripSuggestions(
          data.preferences,
          data.previousSuggestions,
          data.locationPreference,
          data.userLocation
        )
        break
      case "itinerary":
        result = await generateItinerary(data.tripDetails)
        break
      case "chat_message":
        result = await processAIChatMessage(data.message, data.chatHistory)
        break
      case "task_suggestions":
        result = await generateTaskSuggestions(
          data.trip,
          data.userPreferences,
          data.userLocation,
          data.existingTasks || []
        )
        break
      case "task_suggestions_with_links":
        result = await generateTaskSuggestionsWithLinks(
          data.trip,
          data.userPreferences,
          data.existingTasks,
          data.userLocation
        )
        break
      case "task_completion_suggestions":
        result = await generateTaskCompletionSuggestions(
          data.taskTitle,
          data.taskDescription,
          data.tripDestination
        )
        break
      case "destination_activities":
        result = await generateDestinationActivities(
          data.destination,
          data.budget,
          data.preferences
        )
        break
      case "activity_suggestions":
        result = await generateActivitySuggestions(
          data.trip,
          data.userPreferences,
          data.day,
          data.activityPreferences,
          data.recentSuggestions
        )
        break
      case "affiliate_links":
        result = await suggestAffiliateLinks(data.taskTitle, data.taskDescription, data.destination)
        break
      case "trip_chat":
        result = await processTripChatMessage(data.tripId, data.prompt, data.userId)
        break
      default:
        return NextResponse.json({ error: "Invalid request type" }, { status: 400 })
    }

    return NextResponse.json({ result })
  } catch (error) {
    console.error("AI API error:", error)
    return NextResponse.json(
      { error: "An error occurred processing your request" },
      { status: 500 }
    )
  }
}

// Server-side implementation of OpenAI functions
async function generateTripSuggestions(
  preferences: any,
  previousSuggestions?: TripSuggestion[],
  locationPreference?: "local" | "national" | "global",
  userLocation?: { location: string; locationPlaceId: string }
) {
  try {
    // Extract key preferences
    const travelTypes = preferences.travelPreferences || []
    const budgetRange = preferences.budgetRange || [500, 2000]
    const travelSeasons = preferences.preferredTravelSeasons || []
    const availability = preferences.availabilityPreferences || []

    // Format budget for prompt
    const minBudget = Array.isArray(budgetRange) ? budgetRange[0] : 500
    const maxBudget = Array.isArray(budgetRange) ? budgetRange[1] : 2000
    const budgetStr = `$${minBudget} - $${maxBudget}`

    // Extract previous destinations to avoid repeating them
    const previousDestinations = previousSuggestions
      ? previousSuggestions.map((s) => s.destination).filter(Boolean)
      : []

    // Determine number of suggestions to generate (5 for filtering down to 3)
    const suggestionsToGenerate = 5
    const finalSuggestionsCount = 3

    // Build location-specific prompt content
    let locationPrompt = ""
    let suggestionCount = finalSuggestionsCount

    if (locationPreference === "global") {
      locationPrompt =
        "Generate suggestions from anywhere in the world. Include a mix of 1 local/national destination (if user location is available) and 2 international destinations."
      suggestionCount = suggestionsToGenerate
    } else if (locationPreference === "local" && userLocation?.location) {
      locationPrompt = `Generate suggestions within 100 miles of ${userLocation.location}. Focus on destinations that are easily accessible for day trips or weekend getaways.`
      suggestionCount = suggestionsToGenerate
    } else if (locationPreference === "national" && userLocation?.location) {
      locationPrompt = `Generate suggestions within the same country as ${userLocation.location}. Focus on domestic destinations that showcase the country's diversity.`
      suggestionCount = suggestionsToGenerate
    } else {
      // Fallback to global if no location is available for local/national
      locationPrompt = "Generate suggestions from anywhere in the world."
      suggestionCount = suggestionsToGenerate
    }

    const completion = await openai.chat.completions.create({
      model: process.env.OPENAI_MODEL || "gpt-4.1-mini",
      messages: [
        {
          role: "system",
          content:
            "You are a travel planning assistant for BroTrip, an app that helps friends plan trips together. Provide helpful, concise trip suggestions based on the user's preferences. Focus on destinations all over the world that match their travel type preferences and budget range. Ensure variety in your suggestions and avoid repeating destinations that were previously suggested.",
        },
        {
          role: "user",
          content: `Generate ${suggestionCount} trip suggestions based on these preferences:
          Travel Types: ${travelTypes.join(", ") || "Any"}
          Budget Range: ${budgetStr}
          Preferred Seasons: ${travelSeasons.join(", ") || "Any"}
          Availability: ${availability.join(", ") || "Flexible"}
          ${userLocation?.location ? `User Location: ${userLocation.location}` : ""}
          ${previousDestinations.length > 0 ? `Previously suggested (avoid these): ${previousDestinations.join(", ")}` : ""}

          LOCATION REQUIREMENTS:
          ${locationPrompt}

          IMPORTANT FILTERING REQUIREMENTS:
          ${travelTypes.length > 0 ? `- ONLY suggest destinations that match ALL of these travel types: ${travelTypes.join(" AND ")}. For example, if user selected "Beach" and "Mountains", only suggest destinations that have BOTH beaches AND mountains.` : ""}
          ${travelSeasons.length > 0 ? `- Prioritize destinations that are best visited during: ${travelSeasons.join(", ")}` : ""}
          - Ensure all suggestions fit within the budget range: ${budgetStr}

          For each suggestion, include:
          1. Destination name
          2. Brief description (1-2 sentences) explaining how it matches the selected travel types
          3. 2 relevant tags from this list only: ${ALLOWED_TRAVEL_TYPES.join(", ")} - tags MUST match the user's selected travel types
          4. Estimated budget range per person

          Format as JSON with this structure:
          {
            "suggestions": [
              {
                "destination": "Destination Name",
                "description": "Brief description explaining how this destination matches the selected travel types",
                "tags": ["Tag1", "Tag2"],
                "budget": "$X - $Y per person"
              },
              ...
            ]
          }`,
        },
      ],
      temperature: 0.7,
      max_tokens: 1000,
      response_format: { type: "json_object" },
    })

    const content = completion.choices[0].message.content
    if (!content) {
      throw new Error("No content returned from OpenAI")
    }

    try {
      const parsedContent = JSON.parse(content)
      if (parsedContent.suggestions && Array.isArray(parsedContent.suggestions)) {
        // Filter tags to ensure they only use allowed travel types
        const validatedSuggestions = parsedContent.suggestions.map(
          (suggestion: TripSuggestion) => ({
            ...suggestion,
            tags: suggestion.tags.filter((tag) => ALLOWED_TRAVEL_TYPES.includes(tag as any)),
          })
        )

        // If we generated 5 suggestions, filter down to 3 for final display
        if (
          suggestionCount === suggestionsToGenerate &&
          validatedSuggestions.length > finalSuggestionsCount
        ) {
          return validatedSuggestions.slice(0, finalSuggestionsCount)
        }

        return validatedSuggestions
      } else {
        console.error("Unexpected response structure from OpenAI:", parsedContent)
        return [
          {
            destination: "Barcelona, Spain",
            description:
              "Enjoy stunning architecture, delicious cuisine, and beautiful beaches in this Mediterranean gem.",
            tags: ["City", "Culture"],
            budget: budgetStr + " per person",
          },
          {
            destination: "Tokyo, Japan",
            description:
              "Experience the perfect blend of traditional culture and futuristic technology in this vibrant metropolis.",
            tags: ["City"],
            budget: budgetStr + " per person",
          },
          {
            destination: "Costa Rica",
            description:
              "Discover lush rainforests, stunning beaches, and abundant wildlife in this Central American paradise.",
            tags: ["Adventure"],
            budget: budgetStr + " per person",
          },
        ]
      }
    } catch (parseError) {
      console.error("Error parsing OpenAI response:", parseError)
      console.log("Raw content:", content)

      // Return the raw content if parsing fails
      return content
    }
  } catch (error) {
    console.error("Error generating trip suggestions:", error)
    throw error
  }
}

async function generateItinerary(tripDetails: any) {
  try {
    const completion = await openai.chat.completions.create({
      model: process.env.OPENAI_MODEL || "gpt-4.1-mini",
      messages: [
        {
          role: "system",
          content:
            "You are a travel planning assistant for Togeda. Create detailed day-by-day itineraries for trips.",
        },
        {
          role: "user",
          content: `Create a detailed itinerary for this trip: ${JSON.stringify(tripDetails)}`,
        },
      ],
      temperature: 0.7,
      max_tokens: 1000,
    })

    return completion.choices[0].message.content
  } catch (error) {
    console.error("Error generating itinerary:", error)
    throw error
  }
}

async function processAIChatMessage(message: string, chatHistory: any[]) {
  try {
    const completion = await openai.chat.completions.create({
      model: process.env.OPENAI_MODEL || "gpt-4.1-mini",
      messages: [
        {
          role: "system",
          content:
            "You are a helpful travel assistant for Togeda, an app that helps friends plan trips together. Provide concise, helpful responses to user queries about trip planning, activities, accommodations, and travel tips.",
        },
        ...chatHistory.map((msg: any) => ({
          role: msg.sender.id === "ai-assistant" ? ("assistant" as const) : ("user" as const),
          content: msg.content,
        })),
        {
          role: "user" as const,
          content: message,
        },
      ],
      temperature: 0.7,
      max_tokens: 500,
    })

    return completion.choices[0].message.content
  } catch (error) {
    console.error("Error processing AI chat message:", error)
    throw error
  }
}

async function generateTaskSuggestions(
  trip: Trip,
  userPreferences: Partial<User>,
  userLocation?: string,
  existingTasks: string[] = []
): Promise<TaskSuggestion[]> {
  try {
    const completion = await openai.chat.completions.create({
      model: process.env.OPENAI_MODEL || "gpt-4.1-mini",
      messages: [
        {
          role: "system" as const,
          content: `You are a travel planning assistant for Togeda, an app that helps friends plan trips together.
          Generate a list of initial tasks that users should complete when planning a trip.
          Format your response as a JSON object with a 'suggestions' array containing TaskSuggestion objects with the following structure:
          {
            "suggestions": [
              {
                "title": string,
                "description": string,
                "category": "planning" | "booking" | "preparation" | "coordination" | "during-trip",
                "priority": "high" | "medium" | "low",
                "tags": ["tag1", "tag2"] // 2-3 tags from the list below
              },
              ...
            ]
          }
          Focus on practical, actionable tasks that will help the user prepare for their trip.

          IMPORTANT:
          1. For each task, include 2-3 tags from this list only: ${AFFILIATE_LINK_TAGS.join(", ")}
          2. These tags will be used to match tasks with affiliate links, so choose them carefully.
          3. Ensure each suggestion is unique and not similar to any existing tasks.
          4. Avoid suggesting tasks that are variations of the same activity or responsibility.`,
        },
        {
          role: "user" as const,
          content: `Generate EXACTLY 6 task suggestions for a trip to ${trip.destination}.
          Trip dates: ${formatDate(trip.startDate)} to ${formatDate(trip.endDate)}.
          User preferences: ${JSON.stringify(userPreferences)}.
          ${userLocation ? `User location: ${userLocation}` : ""}
          ${existingTasks.length > 0 ? `IMPORTANT - Existing tasks (DO NOT suggest similar tasks): ${existingTasks.join(", ")}` : ""}

          Include a mix of planning, booking, preparation, and coordination tasks.
          Make sure to include appropriate tags for each task from the provided list.
          Ensure each suggestion is COMPLETELY DIFFERENT from any existing tasks.`,
        },
      ],
      temperature: 0.7,
      max_tokens: 800,
      response_format: { type: "json_object" },
    })

    const content = completion.choices[0].message.content
    if (!content) {
      throw new Error("No content returned from OpenAI")
    }

    try {
      const parsedContent = JSON.parse(content)
      if (parsedContent.suggestions && Array.isArray(parsedContent.suggestions)) {
        return parsedContent.suggestions
      } else {
        console.error("Unexpected response structure from OpenAI:", parsedContent)
        return [
          {
            title: "Book flights",
            description: `Look for flights to ${trip.destination}`,
            category: "booking" as const,
            priority: "high" as const,
            tags: ["flight", "air travel", "booking"],
          },
        ]
      }
    } catch (parseError) {
      console.error("Error parsing OpenAI response:", parseError)
      console.log("Raw content:", content)

      // Return a default suggestion when parsing fails
      return [
        {
          title: "Book flights",
          description: `Look for flights to ${trip.destination}`,
          category: "booking" as const,
          priority: "high" as const,
          tags: ["flight", "air travel", "booking"],
        },
      ]
    }
  } catch (error) {
    console.error("Error generating task suggestions:", error)
    throw error
  }
}

async function generateTaskCompletionSuggestions(
  taskTitle: string,
  taskDescription: string,
  tripDestination: string
): Promise<{ suggestion: string; affiliateLink: AffiliateLink | null }[]> {
  try {
    const completion = await openai.chat.completions.create({
      model: process.env.OPENAI_MODEL || "gpt-4.1-mini",
      messages: [
        {
          role: "system" as const,
          content: `You are a travel planning assistant for Togeda, an app that helps friends plan trips together.
          Generate practical suggestions for how to complete a specific travel-related task.
          Format your response as a JSON object with a 'suggestions' array containing objects with the following structure:
          {
            "suggestions": [
              {
                "suggestion": string // A practical, actionable suggestion for completing the task
              },
              ...
            ]
          }
          Provide 2 specific, helpful suggestions that would help someone complete this task.
          Focus on practical advice that includes specific websites, tools, or approaches.`,
        },
        {
          role: "user" as const,
          content: `Generate suggestions for completing this task for a trip to ${tripDestination}:\n\nTask: ${taskTitle}\n${taskDescription ? `Description: ${taskDescription}` : ""}`,
        },
      ],
      temperature: 0.7,
      max_tokens: 500,
      response_format: { type: "json_object" },
    })

    const content = completion.choices[0].message.content
    if (!content) {
      throw new Error("No content returned from OpenAI")
    }

    try {
      const parsedContent = JSON.parse(content)
      if (parsedContent.suggestions && Array.isArray(parsedContent.suggestions)) {
        // Process each suggestion to add affiliate links
        const suggestionsWithLinks = await Promise.all(
          parsedContent.suggestions.map(async (item: { suggestion: string }) => {
            const affiliateLink = await suggestAffiliateLinks(
              taskTitle,
              item.suggestion,
              tripDestination
            )
            return {
              suggestion: item.suggestion,
              affiliateLink,
            }
          })
        )
        return suggestionsWithLinks
      } else {
        // If the structure is unexpected but valid JSON, create a default structure
        console.log("Unexpected response structure from OpenAI:", parsedContent)
        const defaultSuggestion = `Research options for ${taskTitle} in ${tripDestination} online`
        const affiliateLink = await suggestAffiliateLinks(
          taskTitle,
          defaultSuggestion,
          tripDestination
        )
        return [
          {
            suggestion: defaultSuggestion,
            affiliateLink,
          },
        ]
      }
    } catch (parseError) {
      console.error("Error parsing OpenAI response:", parseError)
      console.log("Raw content:", content)

      // Return a default suggestion when parsing fails
      const defaultSuggestion = `Research options for ${taskTitle} in ${tripDestination} online`
      return [
        {
          suggestion: defaultSuggestion,
          affiliateLink: null,
        },
      ]
    }
  } catch (error) {
    console.error("Error generating task completion suggestions:", error)
    throw error
  }
}

// Optimized function that combines task suggestions and affiliate links in one request
async function generateTaskSuggestionsWithLinks(
  trip: Trip,
  _userPreferences: Partial<User>, // Prefixed with underscore to indicate it's not used
  existingTasks: string[] = [],
  userLocation?: string
): Promise<{
  suggestions: TaskSuggestion[]
  affiliateLinks: Record<string, AffiliateLink | null>
}> {
  try {
    // Use a more concise prompt to reduce token usage and improve response time
    const completion = await openai.chat.completions.create({
      model: process.env.OPENAI_MODEL || "gpt-4.1-mini", // Using environment variable with default
      messages: [
        {
          role: "system" as const,
          content: `You are a travel planning assistant for Togeda. Generate 3-5 practical tasks for trip planning.
          Format as JSON:
          {
            "suggestions": [
              {
                "title": "Task title",
                "description": "Brief description",
                "category": "planning" | "booking" | "preparation" | "coordination" | "during-trip",
                "priority": "high" | "medium" | "low",
                "tags": ["tag1", "tag2"] // 2 tags from: ${AFFILIATE_LINK_TAGS.slice(0, 10).join(", ")}...
              }
            ]
          }
          Be concise. Focus on essential tasks only.

          IMPORTANT:
          1. Only use tags from this list: ${AFFILIATE_LINK_TAGS.join(", ")}
          2. Ensure each suggestion is unique and not similar to any existing tasks.
          3. Avoid suggesting tasks that are variations of the same activity or responsibility.
          4. Each task should be completely different from existing tasks in both purpose and content.`,
        },
        {
          role: "user" as const,
          content: `Trip to ${trip.destination} from ${formatDate(trip.startDate)} to ${formatDate(trip.endDate)}.
          ${existingTasks.length > 0 ? `IMPORTANT - Existing tasks (DO NOT suggest similar tasks): ${existingTasks.join(", ")}` : ""}
          ${userLocation ? `From: ${userLocation}` : ""}
          Include mix of planning, booking, and preparation tasks.
          Ensure each suggestion is COMPLETELY DIFFERENT from any existing tasks.`,
        },
      ],
      temperature: 0.7,
      max_tokens: 800, // Reduced token limit for faster response
      response_format: { type: "json_object" },
    })

    const content = completion.choices[0].message.content

    if (!content) {
      throw new Error("No content returned from OpenAI")
    }

    try {
      const parsedContent = JSON.parse(content)
      if (parsedContent.suggestions && Array.isArray(parsedContent.suggestions)) {
        const suggestions = parsedContent.suggestions
        console.log("Parsed suggestions:", suggestions)
        // Generate affiliate links for all suggestions in one batch
        const affiliateLinks: Record<string, AffiliateLink | null> = {}

        for (const suggestion of suggestions) {
          // Find affiliate link for this suggestion using the centralized mapper
          // If the suggestion has tags, we'll use those directly
          if (suggestion.tags && suggestion.tags.length > 0) {
            // Find the best matching affiliate link based on the tags
            const matchingLinks = affiliateLinksMap.filter((link) => {
              return suggestion.tags.some((tag: string) => link.tags.includes(tag))
            })

            if (matchingLinks.length > 0) {
              // Sort by number of matching tags and take the best match
              const bestMatch = matchingLinks.sort((a, b) => {
                const aMatches = a.tags.filter((tag) => suggestion.tags.includes(tag)).length
                const bMatches = b.tags.filter((tag) => suggestion.tags.includes(tag)).length
                return bMatches - aMatches
              })[0]

              affiliateLinks[suggestion.title] = bestMatch
            } else {
              // Fallback to text-based matching if no tag matches
              affiliateLinks[suggestion.title] = findAffiliateLink(
                suggestion.title,
                suggestion.description,
                trip.destination
              )
            }
          } else {
            // If no tags, use the text-based matching
            affiliateLinks[suggestion.title] = findAffiliateLink(
              suggestion.title,
              suggestion.description,
              trip.destination
            )
          }
        }

        return {
          suggestions,
          affiliateLinks,
        }
      } else {
        console.error("Unexpected response structure from OpenAI:", parsedContent)
        return {
          suggestions: [
            {
              title: "Book flights",
              description: `Look for flights to ${trip.destination}`,
              category: "booking" as const,
              priority: "high" as const,
              tags: ["flight", "air travel", "booking"],
            },
          ],
          affiliateLinks: {},
        }
      }
    } catch (parseError) {
      console.error("Error parsing OpenAI response:", parseError)
      console.log("Raw content:", content)

      // Return a default suggestion when parsing fails
      return {
        suggestions: [
          {
            title: "Book flights",
            description: `Look for flights to ${trip.destination}`,
            category: "booking" as const,
            priority: "high" as const,
            tags: ["flight", "air travel", "booking"],
          },
        ],
        affiliateLinks: {},
      }
    }
  } catch (error) {
    console.error("Error generating task suggestions with links:", error)
    throw error
  }
}

async function suggestAffiliateLinks(
  taskTitle: string,
  taskDescription: string,
  destination: string
): Promise<AffiliateLink | null> {
  // Use the centralized affiliate link mapper
  return findAffiliateLink(taskTitle, taskDescription, destination)
}

async function generateDestinationActivities(
  destination: string,
  budget?: string,
  preferences?: string[]
): Promise<ActivitySuggestion[]> {
  try {
    const completion = await openai.chat.completions.create({
      model: process.env.OPENAI_MODEL || "gpt-4.1-mini",
      messages: [
        {
          role: "system" as const,
          content: `You are a travel planning assistant for Togeda, an app that helps friends plan trips together.
          Generate a list of activity suggestions for a destination.
          Format your response as a JSON object with a 'suggestions' array containing ActivitySuggestion objects with the following structure:
          {
            "suggestions": [
              {
                "title": string,
                "description": string,
                "cost": string,
                "duration": string
              },
              ...
            ]
          }
          Focus on popular and interesting activities that visitors would enjoy.`,
        },
        {
          role: "user" as const,
          content: `Generate 3 activity suggestions for ${destination}.
          ${budget ? `Budget range: ${budget}` : ""}
          ${preferences && preferences.length > 0 ? `Travel preferences: ${preferences.join(", ")}` : ""}

          Include a mix of popular attractions and local experiences. For each activity, provide:
          1. A title
          2. A brief description
          3. Estimated cost (per person)
          4. Typical duration`,
        },
      ],
      temperature: 0.7,
      max_tokens: 1000,
      response_format: { type: "json_object" },
    })

    const content = completion.choices[0].message.content
    if (!content) {
      throw new Error("No content returned from OpenAI")
    }

    try {
      const parsedContent = JSON.parse(content)
      if (parsedContent.suggestions && Array.isArray(parsedContent.suggestions)) {
        return parsedContent.suggestions
      } else {
        console.error("Unexpected response structure from OpenAI:", parsedContent)
        return [
          {
            title: `Visit popular attractions in ${destination}`,
            description: `Explore the most famous sights in ${destination}`,
            cost: budget || "$30 - $50",
            duration: "3-4 hours",
          },
        ]
      }
    } catch (parseError) {
      console.error("Error parsing OpenAI response:", parseError)
      console.log("Raw content:", content)

      // Return a default suggestion when parsing fails
      return [
        {
          title: `Visit popular attractions in ${destination}`,
          description: `Explore the most famous sights in ${destination}`,
          cost: budget || "$30 - $50",
          duration: "3-4 hours",
        },
      ]
    }
  } catch (error) {
    console.error("Error generating destination activities:", error)
    throw error
  }
}

// Helper function to search Google Places for activity suggestions
async function searchGooglePlacesForActivity(
  query: string,
  destination: string,
  activityType: string
): Promise<any[]> {
  try {
    const apiKey = process.env.GOOGLE_PLACES_API_KEY
    if (!apiKey) {
      console.warn("Google Places API key not configured")
      return []
    }

    // Build search query
    const searchQuery = `${query} ${destination}`
    let searchUrl = `https://maps.googleapis.com/maps/api/place/textsearch/json?query=${encodeURIComponent(searchQuery)}&key=${apiKey}`

    // Add type filter if specified
    if (activityType === "restaurant") {
      searchUrl += `&type=restaurant`
    } else if (activityType === "entertainment") {
      searchUrl += `&type=night_club|amusement_park|tourist_attraction`
    }

    const response = await fetch(searchUrl)
    if (!response.ok) {
      console.error("Google Places API error:", response.statusText)
      return []
    }

    const data = await response.json()
    if (data.status !== "OK" || !data.results || data.results.length === 0) {
      return []
    }

    // Return processed results
    return data.results.slice(0, 3).map((place: any) => ({
      place_id: place.place_id,
      name: place.name,
      rating: place.rating,
      price_level: place.price_level,
      user_ratings_total: place.user_ratings_total,
      formatted_address: place.formatted_address,
      types: place.types,
      photos:
        place.photos?.slice(0, 3).map((photo: any) => ({
          photo_reference: photo.photo_reference,
          width: photo.width,
          height: photo.height,
        })) || [],
      opening_hours: place.opening_hours
        ? {
            open_now: place.opening_hours.open_now,
          }
        : undefined,
      geometry: place.geometry
        ? {
            location: {
              lat: place.geometry.location.lat,
              lng: place.geometry.location.lng,
            },
          }
        : undefined,
    }))
  } catch (error) {
    console.error("Error searching Google Places for activity:", error)
    return []
  }
}

async function generateActivitySuggestions(
  trip: Trip,
  userPreferences: Partial<User>,
  day?: number,
  activityPreferences?: any,
  recentSuggestions?: string[]
): Promise<ItinerarySuggestion[]> {
  try {
    const completion = await openai.chat.completions.create({
      model: process.env.OPENAI_MODEL || "gpt-4.1-mini",
      messages: [
        {
          role: "system" as const,
          content: `You are a travel planning assistant for Togeda, an app that helps friends plan trips together.
          Generate a list of activity suggestions for a trip itinerary with Google Places integration.
          Format your response as a JSON object with a 'suggestions' array containing ItinerarySuggestion objects with the following structure:
          {
            "suggestions": [
              {
                "title": string,
                "description": string,
                "day": number,
                "timeOfDay": "morning" | "afternoon" | "evening",
                "duration": string,
                "tags": string[], // Array of relevant tags from the provided list
                "googlePlacesQuery": string, // Search query for Google Places API (for restaurants and entertainment venues)
                "activityType": "restaurant" | "entertainment" | "attraction" | "general" // Type for Google Places search
              },
              ...
            ]
          }

          GOOGLE PLACES INTEGRATION:
          - For restaurants/dining suggestions: include "googlePlacesQuery" with specific restaurant name or cuisine type + location
          - For entertainment venues: include "googlePlacesQuery" with venue name or entertainment type + location
          - For attractions: include "googlePlacesQuery" with attraction name + location
          - Set "activityType" to help categorize the Google Places search ("restaurant", "entertainment", "attraction", or "general")
          - Use specific venue names when possible for better Google Places results
          - Example: {"googlePlacesQuery": "Joe's Pizza New York", "activityType": "restaurant"}

          IMPORTANT: For each activity suggestion, you MUST include a "tags" array with relevant tags from this specific list:
          ["tour", "activity", "experience", "sightseeing", "adventure", "excursion", "museum", "attraction", "entertainment", "show", "concert", "theater", "park", "nature", "hiking", "walking", "beach", "water sports", "sports", "shopping", "market", "nightlife", "bar", "club", "spa", "wellness", "relaxation", "local", "guide", "cultural", "food", "dining", "restaurant", "booking", "reservation"]

          Choose 2-4 relevant tags for each activity that best describe what the activity involves. This is crucial for affiliate link matching.

          PERSONALIZATION GUIDELINES:
          - If activity preferences are provided, prioritize suggestions that match the user's specific preferences
          - For DINING suggestions:
            * Cuisine Types: Mexican, Italian, Vegan, Seafood, Street Food, Asian Fusion, American
            * Dining Experience: Fine Dining, Local Gems, Budget-Friendly, Rooftop/Scenic Views, Fast Casual
            * Dietary Needs: Vegetarian, Gluten-Free, Halal, Kosher, None
          - For SHOPPING suggestions:
            * Style: Boutiques, High-End/Luxury, Local Markets, Malls, Thrift/Vintage
            * Budget: Budget ($), Mid-Range ($$), High-End ($$$)
            * Focus: Fashion, Tech & Gadgets, Souvenirs, Home Goods, Art & Decor
          - For ENTERTAINMENT suggestions:
            * Venues: Live Music, Theater/Performing Arts, Comedy Clubs, Nightclubs, Outdoor Events
            * Vibe: Chill & Relaxing, Trendy & Upscale, High-Energy, Family-Friendly
            * Interests: Local Shows, Cultural Performances, DJ Sets, Festivals, Trivia/Game Nights
          - If no activity preferences are provided, focus on top-rated, popular activities
          - ALWAYS suggest real, bookable venues and experiences that exist in the destination

          DEDUPLICATION:
          - Avoid suggesting activities with titles that are too similar to recently suggested ones
          - Ensure variety in activity types and experiences
          - Focus on unique, diverse experiences that complement each other`,
        },
        {
          role: "user" as const,
          content: `Generate ${day ? "3-4 activity suggestions for day " + day : "6-8 activity suggestions"} of a trip to ${trip.destination}.
          Trip dates: ${formatDate(trip.startDate)} to ${formatDate(trip.endDate)}.
          User preferences: ${JSON.stringify(userPreferences)}.
          ${
            activityPreferences
              ? `
          Activity Preferences (Pro User - PRIORITIZE THESE):

          DINING PREFERENCES:
          - Cuisine Types: ${activityPreferences.eateries?.cuisineTypes?.join(", ") || "Any"}
          - Dining Experience: ${activityPreferences.eateries?.diningExperience?.join(", ") || "Any"}
          - Dietary Needs: ${activityPreferences.eateries?.dietaryNeeds?.join(", ") || "None"}

          SHOPPING PREFERENCES:
          - Style: ${activityPreferences.shopping?.style?.join(", ") || "Any"}
          - Budget: ${activityPreferences.shopping?.budget || "Mid-Range"}
          - Focus Areas: ${activityPreferences.shopping?.focusAreas?.join(", ") || "Any"}

          ENTERTAINMENT PREFERENCES:
          - Venues: ${activityPreferences.entertainment?.venues?.join(", ") || "Any"}
          - Vibe: ${activityPreferences.entertainment?.vibe?.join(", ") || "Any"}
          - Interests: ${activityPreferences.entertainment?.interests?.join(", ") || "Any"}

          IMPORTANT: Generate suggestions that specifically match these preferences. For example:
          - If user likes "Mexican" cuisine, suggest authentic Mexican restaurants
          - If user prefers "Local Markets" shopping, suggest local markets and artisan shops
          - If user likes "Live Music" venues, suggest concert halls, music venues, or live music bars
          - Match the budget level (Budget/Mid-Range/High-End) with appropriate venue suggestions
          - Consider dietary needs when suggesting restaurants`
              : "Focus on top-rated, popular activities."
          }
          ${
            recentSuggestions && recentSuggestions.length > 0
              ? `

          Recent suggestions to avoid duplicating: ${recentSuggestions.join(", ")}
          Please ensure new suggestions are different and offer variety.`
              : ""
          }

          Include a mix of popular attractions, local experiences, and activities that match the user's preferences.`,
        },
      ],
      temperature: 0.7,
      max_tokens: 1000,
      response_format: { type: "json_object" },
    })

    const content = completion.choices[0].message.content
    if (!content) {
      throw new Error("No content returned from OpenAI")
    }

    try {
      const parsedContent = JSON.parse(content)
      if (parsedContent.suggestions && Array.isArray(parsedContent.suggestions)) {
        // Process suggestions to add Google Places data
        const enhancedSuggestions = await Promise.all(
          parsedContent.suggestions.map(async (suggestion: any) => {
            // If the suggestion has a Google Places query, search for place data
            if (suggestion.googlePlacesQuery && suggestion.activityType) {
              try {
                const placesResponse = await searchGooglePlacesForActivity(
                  suggestion.googlePlacesQuery,
                  trip.destination,
                  suggestion.activityType
                )

                if (placesResponse && placesResponse.length > 0) {
                  const place = placesResponse[0] // Use the first/best result
                  suggestion.googlePlaces = {
                    place_id: place.place_id,
                    rating: place.rating,
                    price_level: place.price_level,
                    user_ratings_total: place.user_ratings_total,
                    formatted_address: place.formatted_address,
                    types: place.types,
                    photos: place.photos,
                    opening_hours: place.opening_hours,
                    geometry: place.geometry,
                  }
                  // Set the primary location field for easy copying
                  suggestion.location = place.formatted_address || place.name || suggestion.title
                }
              } catch (error) {
                console.error("Error fetching Google Places data for suggestion:", error)
                // Continue without Google Places data if there's an error
              }
            }

            // Remove the temporary fields used for processing
            delete suggestion.googlePlacesQuery
            delete suggestion.activityType

            return suggestion
          })
        )

        return enhancedSuggestions
      } else {
        console.error("Unexpected response structure from OpenAI:", parsedContent)
        return [
          {
            title: "Explore local attractions",
            description: `Visit popular attractions in ${trip.destination}`,
            day: day || 1,
            timeOfDay: "morning" as const,
            duration: "3 hours",
            tags: ["attraction", "sightseeing", "local"],
          },
        ]
      }
    } catch (parseError) {
      console.error("Error parsing OpenAI response:", parseError)
      console.log("Raw content:", content)

      // Return a default suggestion when parsing fails
      return [
        {
          title: "Explore local attractions",
          description: `Visit popular attractions in ${trip.destination}`,
          day: day || 1,
          timeOfDay: "morning" as const,
          duration: "3 hours",
          tags: ["attraction", "sightseeing", "local"],
        },
      ]
    }
  } catch (error) {
    console.error("Error generating activity suggestions:", error)
    throw error
  }
}

async function processTripChatMessage(
  tripId: string,
  prompt: string,
  userId: string
): Promise<string> {
  try {
    // Import server-side services
    const { TripChatAIServerService } = await import(
      "@/lib/server/domains/trip-chat-ai/trip-chat-ai.service"
    )

    const { TRIP_CHAT_LIMITS } = await import("@/lib/domains/user-ai-usage/user-ai-usage.types")

    // Validate the request using server-side service
    const validation = await TripChatAIServerService.validateAIRequest(userId, prompt)
    if (!validation.canMake) {
      let errorMessage = "I cannot process chat requests at this time"

      switch (validation.reason) {
        case "not_pro":
          errorMessage =
            "Trip chat AI is available for Pro users only. Upgrade to access this feature!"
          break
        case "daily_limit":
          errorMessage = "You've reached your daily limit of 20 AI requests. Try again tomorrow!"
          break
        case "rate_limit":
          errorMessage = "Please wait a moment before making another AI request."
          break

        case "invalid_prompt":
          errorMessage = "Please provide a valid question or prompt."
          break
      }

      throw new Error(errorMessage)
    }

    // Set processing state using server-side service
    await TripChatAIServerService.setProcessingState(userId, true)

    try {
      // Get trip context using server-side service
      const tripContext = await TripChatAIServerService.getTripContext(tripId)

      // Generate AI response
      const completion = await openai.chat.completions.create({
        model: process.env.OPENAI_MODEL || "gpt-4.1-mini",
        messages: [
          {
            role: "system",
            content: `You are Togeda AI, a helpful travel assistant for Togeda, an app that helps friends plan trips together.

Trip Context:
- Destination: ${tripContext.destination}
- Dates: ${tripContext.startDate} to ${tripContext.endDate}
- Budget: $${tripContext.budget}
- Group size: ${tripContext.attendeeCount} people
- Trip status: ${tripContext.tripStatus}
${tripContext.description ? `- Description: ${tripContext.description}` : ""}

Provide concise, helpful responses about trip planning, activities, accommodations, and travel tips. Keep responses under ${TRIP_CHAT_LIMITS.MAX_RESPONSE_LENGTH} characters. Be friendly and focus on practical advice for the group.`,
          },
          {
            role: "user",
            content: prompt,
          },
        ],
        temperature: 0.7,
        max_tokens: Math.floor(TRIP_CHAT_LIMITS.MAX_RESPONSE_LENGTH / 3), // Rough estimate for token limit
      })

      let aiResponse =
        completion.choices[0]?.message?.content || "I cannot process chat requests at this time"

      // Ensure response doesn't exceed character limit
      if (aiResponse.length > TRIP_CHAT_LIMITS.MAX_RESPONSE_LENGTH) {
        aiResponse = aiResponse.substring(0, TRIP_CHAT_LIMITS.MAX_RESPONSE_LENGTH - 3) + "..."
      }

      // Track usage using server-side service
      await TripChatAIServerService.trackUsage(userId)

      return aiResponse
    } finally {
      // Always clear processing state using server-side service
      await TripChatAIServerService.setProcessingState(userId, false)
    }
  } catch (error) {
    console.error("Error processing trip chat message:", error)

    // Clear processing state on error using server-side service
    try {
      const { TripChatAIServerService } = await import(
        "@/lib/server/domains/trip-chat-ai/trip-chat-ai.service"
      )
      await TripChatAIServerService.setProcessingState(userId, false)
    } catch (cleanupError) {
      console.error("Error clearing processing state:", cleanupError)
    }

    // Return user-friendly error message
    const errorMessage =
      error instanceof Error ? error.message : "I cannot process chat requests at this time"
    throw new Error(errorMessage)
  }
}
