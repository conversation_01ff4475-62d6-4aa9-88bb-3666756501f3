import { NextRequest, NextResponse } from "next/server"
import { verifyAuth } from "@/lib/api-auth"

export async function GET(request: NextRequest) {
  try {
    // Verify authentication using the new auth system
    const authResult = await verifyAuth(request)

    if (!authResult.isAuthenticated) {
      // Return the error response if authentication failed
      return authResult.response
    }

    const userId = authResult.userId

    // Log the places API request (optional)
    console.log(`Places Images API request from user ${userId || "unknown"}`)
    const searchParams = request.nextUrl.searchParams
    const query = searchParams.get("query")
    const placeId = searchParams.get("placeId")

    console.log("Google Places API request:", { query, placeId })

    if (!query && !placeId) {
      return NextResponse.json(
        { error: "Either query or placeId parameter is required" },
        { status: 400 }
      )
    }

    const apiKey = process.env.GOOGLE_PLACES_API_KEY

    if (!apiKey) {
      console.error("Google Places API key is not configured")
      return NextResponse.json({ error: "Image service is not configured" }, { status: 500 })
    }

    let place

    // If placeId is provided, we need to handle it differently
    if (placeId) {
      // Since we can't directly use Place Details API with referer restrictions,
      // and Text Search doesn't directly support place_id searches,
      // we'll use the query parameter if it's provided along with the placeId
      if (query) {
        // Use the query to search for the place
        const textSearchUrl = `https://maps.googleapis.com/maps/api/place/textsearch/json?query=${encodeURIComponent(query)}&key=${apiKey}`

        // Add timeout and retry logic for Google Places API calls
        const textSearchResponse = await fetch(textSearchUrl, {
          signal: AbortSignal.timeout(10000), // 10 second timeout
        })

        if (!textSearchResponse.ok) {
          const errorData = await textSearchResponse.json()
          console.error("Google Places API error:", errorData)
          return NextResponse.json(
            { error: "Failed to fetch place details" },
            { status: textSearchResponse.status }
          )
        }

        const textSearchData = await textSearchResponse.json()
        console.log("Text search results:", textSearchData)

        if (
          textSearchData.status !== "OK" ||
          !textSearchData.results ||
          textSearchData.results.length === 0
        ) {
          console.error("No places found for query:", query)
          return NextResponse.json({
            imageUrl: null,
          })
        }

        // Try to find the place with matching place_id
        const matchingPlace = textSearchData.results.find(
          (result: any) => result.place_id === placeId
        )
        console.log(
          "Looking for place ID:",
          placeId,
          "in results with IDs:",
          textSearchData.results.map((r: any) => r.place_id)
        )

        // If we found a matching place, use it
        if (matchingPlace) {
          place = matchingPlace
        }
        // Otherwise use the first result
        else {
          place = textSearchData.results[0]
          console.log("Could not find exact place ID match, using first result instead")
        }
      } else {
        // If no query is provided, we'll use a generic search for tourist attractions
        // This is a fallback and may not be accurate
        const textSearchUrl = `https://maps.googleapis.com/maps/api/place/textsearch/json?query=tourist+attractions&key=${apiKey}`

        const textSearchResponse = await fetch(textSearchUrl, {
          signal: AbortSignal.timeout(10000), // 10 second timeout
        })
        const textSearchData = await textSearchResponse.json()

        if (
          textSearchData.status !== "OK" ||
          !textSearchData.results ||
          textSearchData.results.length === 0
        ) {
          console.error("No generic places found as fallback")
          return NextResponse.json({
            imageUrl: null,
          })
        }

        // Just use the first result as a fallback
        place = textSearchData.results[0]
        console.log("Using generic place as fallback")
      }
    }
    // Otherwise, search for the place using the query
    else {
      // Use Text Search API instead of Find Place From Text
      const searchQuery = `${query} travel destination`
      const textSearchUrl = `https://maps.googleapis.com/maps/api/place/textsearch/json?query=${encodeURIComponent(searchQuery)}&key=${apiKey}`

      const textSearchResponse = await fetch(textSearchUrl, {
        signal: AbortSignal.timeout(10000), // 10 second timeout
      })

      if (!textSearchResponse.ok) {
        const errorData = await textSearchResponse.json()
        console.error("Google Places API error:", errorData)
        return NextResponse.json(
          { error: "Failed to fetch place data" },
          { status: textSearchResponse.status }
        )
      }

      const textSearchData = await textSearchResponse.json()

      // Check if we found a place
      if (
        textSearchData.status !== "OK" ||
        !textSearchData.results ||
        textSearchData.results.length === 0
      ) {
        console.error("No places found for query:", query)
        return NextResponse.json({
          imageUrl: null,
        })
      }

      place = textSearchData.results[0]
    }

    console.log("Selected place:", {
      name: place.name,
      place_id: place.place_id,
      hasPhotos: !!place.photos && place.photos.length > 0,
      photoCount: place.photos?.length || 0,
    })

    // Check if the place has photos
    if (!place.photos || place.photos.length === 0) {
      console.log("No photos available for place:", place.name)
      return NextResponse.json({
        imageUrl: null,
      })
    }

    // Get the first photo
    const photo = place.photos[0]
    console.log("Photo reference:", photo.photo_reference)

    // Construct the photo URL
    // Note: This URL will directly fetch the image from Google's servers
    const photoUrl = `https://maps.googleapis.com/maps/api/place/photo?maxwidth=1200&photoreference=${photo.photo_reference}&key=${apiKey}`

    return NextResponse.json({
      imageUrl: photoUrl,
      placeId: place.place_id,
      attribution: {
        name: place.name || query || "Location",
        photoReference: photo.photo_reference,
      },
    })
  } catch (error) {
    console.error("Error in Google Places API:", error)

    // Handle specific timeout errors
    if (error instanceof Error) {
      if (error.name === "AbortError" || error.message.includes("timeout")) {
        console.error("Google Places API request timed out")
        return NextResponse.json({ error: "Request timed out. Please try again." }, { status: 408 })
      }
      if (error.message.includes("ETIMEDOUT")) {
        console.error("Google Places API connection timed out")
        return NextResponse.json(
          { error: "Connection timed out. Please try again." },
          { status: 408 }
        )
      }
    }

    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}
