import { NextRequest, NextResponse } from "next/server"

/**
 * API endpoint to generate fresh Google Places Photo URLs
 * This handles the API key securely on the server side
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const photoReference = searchParams.get("photoReference")
    const placeId = searchParams.get("placeId")
    const maxWidth = searchParams.get("maxWidth") || "1200"

    if (!photoReference || !placeId) {
      return NextResponse.json(
        { error: "photoReference and placeId are required" },
        { status: 400 }
      )
    }

    // Get API key from environment variables (server-side only)
    const apiKey = process.env.GOOGLE_PLACES_API_KEY
    if (!apiKey) {
      console.error("GOOGLE_PLACES_API_KEY environment variable is not set")
      return NextResponse.json({ error: "Google Places API key not configured" }, { status: 500 })
    }

    // Generate the Google Places Photo API URL
    const imageUrl = `https://maps.googleapis.com/maps/api/place/photo?maxwidth=${maxWidth}&photo_reference=${photoReference}&key=${apiKey}`

    return NextResponse.json({
      imageUrl,
      photoReference,
      placeId,
    })
  } catch (error) {
    console.error("Error generating Google Places image URL:", error)
    return NextResponse.json({ error: "Failed to generate image URL" }, { status: 500 })
  }
}
