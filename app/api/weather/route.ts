import { NextRequest, NextResponse } from "next/server"
import { verifyAuth } from "@/lib/api-auth"

export async function GET(request: NextRequest) {
  try {
    // Verify authentication using the new auth system
    const authResult = await verifyAuth(request)

    if (!authResult.isAuthenticated) {
      // Return the error response if authentication failed
      return authResult.response
    }

    const userId = authResult.userId

    // Log the weather API request (optional)
    console.log(`Weather API request from user ${userId || "unknown"}`)
    // Get query parameters
    const searchParams = request.nextUrl.searchParams
    const location = searchParams.get("location")
    // Default to 7 days, but allow up to 14 days for trip planning
    const days = Math.min(parseInt(searchParams.get("days") || "7", 10), 14).toString()

    // Validate parameters
    if (!location) {
      return NextResponse.json({ error: "Location parameter is required" }, { status: 400 })
    }

    // Get API key from environment variables
    const apiKey = process.env.WEATHER_API_KEY

    if (!apiKey) {
      console.error("Weather API key is not configured")
      return NextResponse.json({ error: "Weather service is not configured" }, { status: 500 })
    }

    // Make request to Weather API
    const apiUrl = `https://api.weatherapi.com/v1/forecast.json?key=${apiKey}&q=${encodeURIComponent(location)}&days=${days}&aqi=no`

    const response = await fetch(apiUrl)

    if (!response.ok) {
      const errorData = await response.json()
      console.error("Weather API error:", errorData)

      // Handle specific error cases
      if (response.status === 400 && errorData.error?.code === 1006) {
        return NextResponse.json({ error: "Location not found" }, { status: 404 })
      }

      return NextResponse.json(
        { error: "Failed to fetch weather data" },
        { status: response.status }
      )
    }

    const data = await response.json()
    return NextResponse.json(data)
  } catch (error) {
    console.error("Error in weather API route:", error)
    return NextResponse.json({ error: "An unexpected error occurred" }, { status: 500 })
  }
}
