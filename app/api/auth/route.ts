import { type NextRequest, NextResponse } from "next/server"
import { auth } from "@/lib/firebase"
import {
  createUserWithEmailAndPassword,
  signInWithEmailAndPassword,
  signOut as firebaseSignOut,
} from "firebase/auth"

export async function POST(request: NextRequest) {
  try {
    const { action, email, password } = await request.json()

    switch (action) {
      case "signup":
        const signupResult = await createUserWithEmailAndPassword(auth, email, password)
        return NextResponse.json({ user: signupResult.user.uid })

      case "login":
        const loginResult = await signInWithEmailAndPassword(auth, email, password)
        return NextResponse.json({ user: loginResult.user.uid })

      case "logout":
        await firebaseSignOut(auth)
        return NextResponse.json({ success: true })

      default:
        return NextResponse.json({ error: "Invalid action" }, { status: 400 })
    }
  } catch (error: any) {
    console.error("Auth API error:", error)
    return NextResponse.json(
      {
        error: error.message || "An error occurred during authentication",
      },
      { status: 500 }
    )
  }
}
