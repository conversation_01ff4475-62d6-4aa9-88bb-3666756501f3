import { NextRequest, NextResponse } from "next/server"
import { verifyAuth } from "@/lib/api-auth"
import { FlatSubscriptionService } from "@/lib/domains/user-subscription/flat-subscription.service"

export async function GET(request: NextRequest) {
  try {
    // Verify authentication using the new auth system
    const authResult = await verifyAuth(request)

    if (!authResult.isAuthenticated) {
      // Return the error response if authentication failed
      return authResult.response
    }

    const userId = authResult.userId

    // Log the places API request (optional)
    console.log(`Places Search API request from user ${userId || "unknown"}`)
    const searchParams = request.nextUrl.searchParams
    const query = searchParams.get("query")
    const location = searchParams.get("location") // lat,lng format
    const radius = searchParams.get("radius") // in meters
    const type = searchParams.get("type") // place type filter
    const includeRatings = searchParams.get("includeRatings") === "true"

    if (!query) {
      return NextResponse.json({ error: "Query parameter is required" }, { status: 400 })
    }

    // Check subscription status for enhanced features
    const isSubscribed = userId
      ? await FlatSubscriptionService.hasFeatureAccess(userId, "unlimited_ai")
      : false

    const apiKey = process.env.GOOGLE_PLACES_API_KEY

    if (!apiKey) {
      console.error("Google Places API key is not configured")
      return NextResponse.json({ error: "Places service is not configured" }, { status: 500 })
    }

    // Build search URL with enhanced parameters for Pro users
    let searchUrl = `https://maps.googleapis.com/maps/api/place/textsearch/json?query=${encodeURIComponent(query)}&key=${apiKey}`

    // Add location and radius for Pro users
    if (isSubscribed && location && radius) {
      searchUrl += `&location=${encodeURIComponent(location)}&radius=${radius}`
    }

    // Add type filter if specified
    if (type) {
      searchUrl += `&type=${encodeURIComponent(type)}`
    } else {
      // Default to tourist attraction if no type specified
      searchUrl += `&type=tourist_attraction`
    }

    const response = await fetch(searchUrl, {
      signal: AbortSignal.timeout(10000), // 10 second timeout
    })

    if (!response.ok) {
      const errorData = await response.json()
      console.error("Google Places API error:", errorData)
      return NextResponse.json({ error: "Failed to fetch place data" }, { status: response.status })
    }

    const data = await response.json()

    // Check if we found any places
    if (data.status !== "OK" || !data.results || data.results.length === 0) {
      console.error("No places found for query:", query)
      return NextResponse.json({
        places: [],
      })
    }

    // Format the results with enhanced data for Pro users
    const places = data.results.map((place) => {
      const basicPlace = {
        placeId: place.place_id,
        name: place.name,
        formattedAddress: place.formatted_address,
        location: place.geometry?.location,
        types: place.types,
        hasPhoto: !!place.photos,
      }

      // Add enhanced data for Pro users
      if (isSubscribed && (includeRatings || true)) {
        return {
          ...basicPlace,
          rating: place.rating || null,
          userRatingsTotal: place.user_ratings_total || null,
          priceLevel: place.price_level || null,
          businessStatus: place.business_status || null,
          openingHours: place.opening_hours
            ? {
                openNow: place.opening_hours.open_now || null,
                weekdayText: place.opening_hours.weekday_text || null,
              }
            : null,
        }
      }

      return basicPlace
    })

    return NextResponse.json({ places })
  } catch (error) {
    console.error("Error in Google Places Search API:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}
