import { NextRequest, NextResponse } from "next/server"
import { getAdminInstance } from "@/lib/firebase-admin"
import { LocalExperienceCreateData } from "@/lib/domains/local-experiences/local-experiences.types"

/**
 * API key authentication for admin endpoints
 */
function validateApiKey(request: NextRequest): boolean {
  const authHeader = request.headers.get("Authorization")
  const expectedApiKey = process.env.LOCAL_EXPERIENCE_API_KEY

  if (!expectedApiKey) {
    console.error("LOCAL_EXPERIENCE_API_KEY environment variable is not set")
    return false
  }

  if (!authHeader || !authHeader.startsWith("Bearer ")) {
    return false
  }

  const providedApiKey = authHeader.split("Bearer ")[1]
  return providedApiKey === expectedApiKey
}

/**
 * Validate image URLs by checking if they're accessible and are valid images
 */
async function validateImageUrls(imageUrls: string[]): Promise<{ valid: boolean; error?: string }> {
  if (imageUrls.length > 3) {
    return { valid: false, error: "Maximum 3 images allowed" }
  }

  for (const url of imageUrls) {
    try {
      // Basic URL validation
      new URL(url)

      // Check if URL is accessible and returns an image
      const response = await fetch(url, { method: "HEAD" })

      if (!response.ok) {
        return { valid: false, error: `Image URL not accessible: ${url}` }
      }

      const contentType = response.headers.get("content-type")
      if (!contentType || !contentType.startsWith("image/")) {
        return { valid: false, error: `URL is not an image: ${url}` }
      }
    } catch (error) {
      return { valid: false, error: `Invalid image URL: ${url}` }
    }
  }

  return { valid: true }
}

/**
 * Validate time slot configuration
 */
function validateTimeSlot(slot: any): boolean {
  return (
    slot &&
    typeof slot === "object" &&
    typeof slot.time === "string" &&
    typeof slot.available === "boolean" &&
    typeof slot.maxGuests === "number" &&
    slot.maxGuests > 0
  )
}

/**
 * Validate availability configuration
 */
function validateAvailabilityData(availability: any): boolean {
  if (!availability || typeof availability !== "object") {
    return false
  }

  // Validate weeklySchedule if provided
  if (availability.weeklySchedule) {
    if (typeof availability.weeklySchedule !== "object") {
      return false
    }

    const validDays = ["monday", "tuesday", "wednesday", "thursday", "friday", "saturday", "sunday"]
    const scheduleKeys = Object.keys(availability.weeklySchedule)

    // Check if "default" is used with other days
    if (scheduleKeys.includes("default") && scheduleKeys.length > 1) {
      return false // "default" cannot co-exist with specific days
    }

    for (const [day, timeSlots] of Object.entries(availability.weeklySchedule)) {
      // Validate day name (including "default")
      if (day !== "default" && !validDays.includes(day)) {
        return false
      }

      // Validate time slots for this day
      if (!Array.isArray(timeSlots)) {
        return false
      }

      // Check for empty time slots array
      if (timeSlots.length === 0) {
        return false
      }

      for (const slot of timeSlots as any[]) {
        if (!validateTimeSlot(slot)) {
          return false
        }
      }
    }
  }

  return true
}

/**
 * Validate experience data and apply defaults
 */
function validateExperienceData(data: any): {
  valid: boolean
  data?: LocalExperienceCreateData
  error?: string
} {
  try {
    // Required fields validation
    const requiredFields = [
      "title",
      "description",
      "shortDescription",
      "host",
      "location",
      "pricing",
      "duration",
      "maxGuests",
      "minGuests",
      "categories",
      "images",
      "inclusions",
      "cancellationPolicy",
    ]

    for (const field of requiredFields) {
      if (!data[field]) {
        return { valid: false, error: `Missing required field: ${field}` }
      }
    }

    // Host validation
    if (!data.host.name || !data.host.responseTime || !data.host.languages || !data.host.bio) {
      return { valid: false, error: "Host must have name, responseTime, languages, and bio" }
    }

    // Location validation
    if (!data.location.address || !data.location.city || !data.location.country) {
      return { valid: false, error: "Location must have address, city, and country" }
    }

    // Pricing validation
    if (!data.pricing.basePrice || !data.pricing.currency) {
      return { valid: false, error: "Pricing must have basePrice and currency" }
    }

    if (typeof data.pricing.basePrice !== "number" || data.pricing.basePrice <= 0) {
      return { valid: false, error: "Base price must be a positive number" }
    }

    // Duration validation
    if (typeof data.duration !== "number" || data.duration <= 0) {
      return { valid: false, error: "Duration must be a positive number (in minutes)" }
    }

    // Guest count validation
    if (typeof data.maxGuests !== "number" || data.maxGuests <= 0) {
      return { valid: false, error: "Max guests must be a positive number" }
    }

    if (typeof data.minGuests !== "number" || data.minGuests <= 0) {
      return { valid: false, error: "Min guests must be a positive number" }
    }

    if (data.minGuests > data.maxGuests) {
      return { valid: false, error: "Min guests cannot be greater than max guests" }
    }

    // Categories validation
    if (!Array.isArray(data.categories) || data.categories.length === 0) {
      return { valid: false, error: "At least one category is required" }
    }

    // Images validation
    if (!Array.isArray(data.images) || data.images.length === 0) {
      return { valid: false, error: "At least one image is required" }
    }

    // Inclusions validation
    if (!Array.isArray(data.inclusions)) {
      return { valid: false, error: "Inclusions must be an array" }
    }

    // Availability validation (optional)
    if (data.availability) {
      if (!validateAvailabilityData(data.availability)) {
        return { valid: false, error: "Invalid availability configuration" }
      }
    }

    // Apply defaults for optional fields
    const experienceData: LocalExperienceCreateData = {
      ...data,
      isActive: data.isActive !== undefined ? data.isActive : true,
      bookingModel: data.bookingModel || "per_max_guest",
      updatedAt: new Date(),
    }

    return { valid: true, data: experienceData }
  } catch (error) {
    return { valid: false, error: "Invalid data format" }
  }
}

/**
 * Create availability patterns for an experience
 */
async function createExperienceAvailability(
  experienceId: string,
  availabilityConfig?: any
): Promise<{ success: boolean; error?: string }> {
  try {
    const { adminDb, adminFieldValue } = await getAdminInstance()

    if (!adminDb || !adminFieldValue) {
      return { success: false, error: "Firebase Admin not initialized" }
    }

    // Default time slots
    const defaultTimeSlots = [
      {
        availabilityId: "slot-09-00",
        time: "09:00",
        available: true,
        maxGuests: 8,
        currentBookings: 0,
      },
      {
        availabilityId: "slot-11-00",
        time: "11:00",
        available: true,
        maxGuests: 8,
        currentBookings: 0,
      },
      {
        availabilityId: "slot-14-00",
        time: "14:00",
        available: true,
        maxGuests: 8,
        currentBookings: 0,
      },
      {
        availabilityId: "slot-16-00",
        time: "16:00",
        available: true,
        maxGuests: 8,
        currentBookings: 0,
      },
    ]

    const batch = adminDb.batch()

    // Helper function to generate availability ID from time
    const generateAvailabilityId = (time: string) => {
      return `slot-${time.replace(":", "-")}`
    }

    // Helper function to process time slots (add generated fields)
    const processTimeSlots = (timeSlots: any[]) => {
      return timeSlots.map((slot: any) => ({
        availabilityId: generateAvailabilityId(slot.time),
        time: slot.time,
        available: slot.available,
        maxGuests: slot.maxGuests,
        currentBookings: 0,
      }))
    }

    if (availabilityConfig?.weeklySchedule) {
      const scheduleEntries = Object.entries(availabilityConfig.weeklySchedule)

      if (scheduleEntries.length === 1 && scheduleEntries[0][0] === "default") {
        // Handle "default" case - same time slots for all days
        const [, defaultTimeSlots] = scheduleEntries[0]
        const allDaysOfWeek = [
          "monday",
          "tuesday",
          "wednesday",
          "thursday",
          "friday",
          "saturday",
          "sunday",
        ]
        const processedTimeSlots = processTimeSlots(defaultTimeSlots as any[])

        for (const dayOfWeek of allDaysOfWeek) {
          const availabilityRef = adminDb
            .collection("localExperiences")
            .doc(experienceId)
            .collection("availability")
            .doc(dayOfWeek)

          const weeklyAvailability = {
            date: dayOfWeek,
            type: "weekly",
            daysOfWeek: [dayOfWeek],
            timeSlots: processedTimeSlots,
            isDefault: false,
            createdAt: adminFieldValue.serverTimestamp(),
            updatedAt: adminFieldValue.serverTimestamp(),
          }

          batch.set(availabilityRef, weeklyAvailability)
        }
      } else {
        // Handle specific days - different time slots per day
        for (const [dayOfWeek, dayTimeSlots] of scheduleEntries) {
          const availabilityRef = adminDb
            .collection("localExperiences")
            .doc(experienceId)
            .collection("availability")
            .doc(dayOfWeek)

          const weeklyAvailability = {
            date: dayOfWeek,
            type: "weekly",
            daysOfWeek: [dayOfWeek],
            timeSlots: processTimeSlots(dayTimeSlots as any[]),
            isDefault: false,
            createdAt: adminFieldValue.serverTimestamp(),
            updatedAt: adminFieldValue.serverTimestamp(),
          }

          batch.set(availabilityRef, weeklyAvailability)
        }
      }
    } else {
      // No configuration - use default time slots for all days
      const allDaysOfWeek = [
        "monday",
        "tuesday",
        "wednesday",
        "thursday",
        "friday",
        "saturday",
        "sunday",
      ]

      for (const dayOfWeek of allDaysOfWeek) {
        const availabilityRef = adminDb
          .collection("localExperiences")
          .doc(experienceId)
          .collection("availability")
          .doc(dayOfWeek)

        const weeklyAvailability = {
          date: dayOfWeek,
          type: "weekly",
          daysOfWeek: [dayOfWeek],
          timeSlots: defaultTimeSlots,
          isDefault: false,
          createdAt: adminFieldValue.serverTimestamp(),
          updatedAt: adminFieldValue.serverTimestamp(),
        }

        batch.set(availabilityRef, weeklyAvailability)
      }
    }

    // Also create a default fallback
    const defaultAvailabilityRef = adminDb
      .collection("localExperiences")
      .doc(experienceId)
      .collection("availability")
      .doc("default")

    const defaultAvailability = {
      date: "default",
      type: "default",
      timeSlots: defaultTimeSlots,
      isDefault: true,
      createdAt: adminFieldValue.serverTimestamp(),
      updatedAt: adminFieldValue.serverTimestamp(),
    }

    batch.set(defaultAvailabilityRef, defaultAvailability)

    await batch.commit()
    return { success: true }
  } catch (error) {
    console.error("Error creating weekly availability:", error)
    return { success: false, error: "Failed to create weekly availability" }
  }
}

/**
 * Create experience using Firebase Admin SDK
 */
async function createExperienceWithAdmin(
  experienceData: LocalExperienceCreateData
): Promise<{ success: boolean; id?: string; error?: string }> {
  try {
    const { adminDb, adminFieldValue } = await getAdminInstance()

    if (!adminDb || !adminFieldValue) {
      return { success: false, error: "Firebase Admin not initialized" }
    }

    // Create experience document
    const experienceRef = adminDb.collection("localExperiences").doc()
    const experienceId = experienceRef.id

    const newExperience = {
      ...experienceData,
      id: experienceId,
      createdAt: adminFieldValue.serverTimestamp(),
      rating: 0,
      reviewCount: 0,
    }

    await experienceRef.set(newExperience)

    // Create availability patterns (custom or default)
    const availabilityResult = await createExperienceAvailability(
      experienceId,
      experienceData.availability
    )
    if (!availabilityResult.success) {
      // If availability creation fails, we should still return success for the experience
      // but log the error
      console.error("Failed to create availability for experience:", availabilityResult.error)
    }

    return { success: true, id: experienceId }
  } catch (error) {
    console.error("Error creating experience with admin:", error)
    return { success: false, error: "Failed to create experience in database" }
  }
}

/**
 * POST /api/admin/experiences/create
 * Create a new local experience
 */
export async function POST(request: NextRequest) {
  try {
    // Validate API key
    if (!validateApiKey(request)) {
      return NextResponse.json(
        { success: false, error: "Unauthorized: Invalid API key" },
        { status: 401 }
      )
    }

    // Parse request body
    const body = await request.json()

    // Validate experience data
    const dataValidation = validateExperienceData(body)
    if (!dataValidation.valid) {
      return NextResponse.json({ success: false, error: dataValidation.error }, { status: 400 })
    }

    const experienceData = dataValidation.data!

    // Validate image URLs
    const imageValidation = await validateImageUrls(experienceData.images)
    if (!imageValidation.valid) {
      return NextResponse.json({ success: false, error: imageValidation.error }, { status: 400 })
    }

    // Create experience in database
    const createResult = await createExperienceWithAdmin(experienceData)
    if (!createResult.success) {
      return NextResponse.json({ success: false, error: createResult.error }, { status: 500 })
    }

    return NextResponse.json({
      success: true,
      data: {
        id: createResult.id,
        message: "Experience created successfully",
      },
    })
  } catch (error) {
    console.error("Error in admin experience creation:", error)
    return NextResponse.json({ success: false, error: "Internal server error" }, { status: 500 })
  }
}
