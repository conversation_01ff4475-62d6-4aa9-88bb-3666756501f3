openapi: 3.0.3
info:
  title: Togeda.ai Admin API - Local Experiences
  description: Admin API for creating local experiences with automatic weekly availability patterns
  version: 1.0.0
  contact:
    name: Togeda.ai Admin API
    email: <EMAIL>

servers:
  - url: https://your-domain.com/api/admin
    description: Production server
  - url: http://localhost:3000/api/admin
    description: Development server

security:
  - BearerAuth: []

paths:
  /experiences/create:
    post:
      summary: Create a new local experience
      description: |
        Creates a new local experience with configurable availability patterns.
        Supports three modes:
        1. weeklySchedule with specific days: Different time slots for each day
        2. weeklySchedule with "default": Same time slots for all days (Monday-Sunday)
        3. No configuration: Standard time slots (09:00, 11:00, 14:00, 16:00) for all days
      operationId: createExperience
      tags:
        - Experiences
      security:
        - BearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateExperienceRequest"
            examples:
              custom_availability_experience:
                summary: Experience with weekly schedule (different time slots per day)
              default_availability_experience:
                summary: Experience with same time slots for all days using "default"
                value:
                  title: "Weekend Farmers Market Tour"
                  description: "Explore the vibrant weekend farmers market with a local food expert. Discover seasonal produce, artisanal products, and hidden culinary gems."
                  shortDescription: "Guided weekend farmers market tour with tastings"
                  host:
                    name: "Maria Rodriguez"
                    responseTime: "Usually responds within 1 hour"
                    languages: ["English", "Spanish"]
                    bio: "Local food enthusiast and market vendor with 15+ years of experience in sustainable agriculture."
                    email: "<EMAIL>"
                    internalHostEmail: "<EMAIL>"
                  location:
                    address: "456 Market Square"
                    city: "Portland"
                    state: "OR"
                    country: "United States"
                    coordinates:
                      lat: 45.5152
                      lng: -122.6784
                    placeId: "ChIJ_xkgOm9VlVQRuQkSjZzL4yI"
                  pricing:
                    basePrice: 45
                    currency: "USD"
                  duration: 90
                  maxGuests: 8
                  minGuests: 2
                  categories: ["food", "culture"]
                  images:
                    - "https://example.com/market1.jpg"
                    - "https://example.com/market2.jpg"
                  inclusions:
                    - item: "Market tastings and samples"
                      included: true
                    - item: "Transportation to market"
                      included: false
                  cancellationPolicy: "Free cancellation up to 48 hours before the experience"
                  isActive: true
                  bookingModel: "per_max_guest"
                  availability:
                    weeklySchedule:
                      default:
                        - time: "18:00"
                          available: true
                          maxGuests: 12
                        - time: "20:00"
                          available: true
                          maxGuests: 12
              basic_experience:
                summary: Basic experience with default availability
                value:
                  title: "Sunset Photography Tour"
                  description: "Join us for a magical sunset photography experience in the heart of the city. Learn professional photography techniques while capturing stunning golden hour shots."
                  shortDescription: "Professional sunset photography tour with expert guidance"
                  host:
                    name: "Sarah Johnson"
                    responseTime: "Usually responds within 2 hours"
                    languages: ["English", "Spanish"]
                    bio: "Professional photographer with 10+ years of experience specializing in urban and landscape photography."
                    email: "<EMAIL>"
                    internalHostEmail: "<EMAIL>"
                  location:
                    address: "123 Main Street"
                    city: "San Francisco"
                    state: "CA"
                    country: "United States"
                    coordinates:
                      lat: 37.7749
                      lng: -122.4194
                    placeId: "ChIJIQBpAG2ahYAR_6128GcTUEo"
                  pricing:
                    basePrice: 75
                    currency: "USD"
                  duration: 120
                  maxGuests: 6
                  minGuests: 2
                  categories: ["outdoor", "culture"]
                  images:
                    - "https://example.com/image1.jpg"
                    - "https://example.com/image2.jpg"
                  inclusions:
                    - item: "Professional photography guidance"
                      included: true
                    - item: "Camera equipment"
                      included: false
                  cancellationPolicy: "Free cancellation up to 24 hours before the experience"
                  isActive: true
                  bookingModel: "per_max_guest"
                  availability:
                    weeklySchedule:
                      monday:
                        - time: "10:00"
                          available: true
                          maxGuests: 6
                        - time: "15:00"
                          available: true
                          maxGuests: 6
                      wednesday:
                        - time: "10:00"
                          available: true
                          maxGuests: 6
                      friday:
                        - time: "14:00"
                          available: true
                          maxGuests: 8

      responses:
        "200":
          description: Experience created successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/CreateExperienceResponse"
              examples:
                success:
                  summary: Successful creation
                  value:
                    success: true
                    data:
                      id: "exp_abc123def456"
                      message: "Experience created successfully"
        "400":
          description: Bad request - validation error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
              examples:
                missing_field:
                  summary: Missing required field
                  value:
                    success: false
                    error: "Missing required field: title"
                invalid_price:
                  summary: Invalid price
                  value:
                    success: false
                    error: "Base price must be a positive number"
                invalid_image:
                  summary: Invalid image URL
                  value:
                    success: false
                    error: "Image URL not accessible: https://invalid-url.com/image.jpg"
        "401":
          description: Unauthorized - invalid API key
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
              examples:
                unauthorized:
                  summary: Invalid API key
                  value:
                    success: false
                    error: "Unauthorized: Invalid API key"
        "500":
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
              examples:
                server_error:
                  summary: Server error
                  value:
                    success: false
                    error: "Internal server error"

components:
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      description: |
        API key authentication using Bearer token.
        Set the LOCAL_EXPERIENCE_API_KEY environment variable and include it in the Authorization header.
        Example: `Authorization: Bearer your-api-key-here`

  schemas:
    CreateExperienceRequest:
      type: object
      required:
        - title
        - description
        - shortDescription
        - host
        - location
        - pricing
        - duration
        - maxGuests
        - minGuests
        - categories
        - images
        - inclusions
        - cancellationPolicy
      properties:
        title:
          type: string
          description: Experience title
          example: "Sunset Photography Tour"
        description:
          type: string
          description: Detailed experience description
          example: "Join us for a magical sunset photography experience..."
        shortDescription:
          type: string
          description: Brief description for card display
          example: "Professional sunset photography tour with expert guidance"
        host:
          $ref: "#/components/schemas/ExperienceHost"
        location:
          $ref: "#/components/schemas/ExperienceLocation"
        pricing:
          $ref: "#/components/schemas/ExperiencePricing"
        duration:
          type: integer
          minimum: 1
          description: Duration in minutes
          example: 120
        maxGuests:
          type: integer
          minimum: 1
          description: Maximum number of guests
          example: 6
        minGuests:
          type: integer
          minimum: 1
          description: Minimum number of guests
          example: 2
        categories:
          type: array
          items:
            $ref: "#/components/schemas/ExperienceCategory"
          minItems: 1
          description: Experience categories
          example: ["outdoor", "culture"]
        images:
          type: array
          items:
            type: string
            format: uri
          minItems: 1
          maxItems: 3
          description: Array of image URLs (max 3)
          example: ["https://example.com/image1.jpg", "https://example.com/image2.jpg"]
        inclusions:
          type: array
          items:
            $ref: "#/components/schemas/ExperienceInclusion"
          description: What's included/excluded in the experience
        cancellationPolicy:
          type: string
          description: Cancellation policy text
          example: "Free cancellation up to 24 hours before the experience"
        isActive:
          type: boolean
          description: Whether the experience is active (defaults to true)
          default: true
          example: true
        bookingModel:
          $ref: "#/components/schemas/BookingModel"
        stripeProductId:
          type: string
          description: Stripe product ID for payment processing
          example: "prod_abc123"
        availability:
          $ref: "#/components/schemas/ExperienceAvailabilityConfig"

    ExperienceHost:
      type: object
      required:
        - name
        - responseTime
        - languages
        - bio
      properties:
        name:
          type: string
          description: Host name
          example: "Sarah Johnson"
        avatar:
          type: string
          format: uri
          description: Host avatar image URL
          example: "https://example.com/avatar.jpg"
        responseTime:
          type: string
          description: Typical response time
          example: "Usually responds within 2 hours"
        languages:
          type: array
          items:
            type: string
          minItems: 1
          description: Languages spoken by host
          example: ["English", "Spanish"]
        bio:
          type: string
          description: Host biography
          example: "Professional photographer with 10+ years of experience..."
        email:
          type: string
          format: email
          description: Public-facing host email
          example: "<EMAIL>"
        phone:
          type: string
          description: Host phone number
          example: "******-123-4567"
        internalHostEmail:
          type: string
          format: email
          description: Internal email for notifications
          example: "<EMAIL>"

    ExperienceLocation:
      type: object
      required:
        - address
        - city
        - country
      properties:
        address:
          type: string
          description: Street address
          example: "123 Main Street"
        city:
          type: string
          description: City name
          example: "San Francisco"
        state:
          type: string
          description: State or province
          example: "CA"
        country:
          type: string
          description: Country name
          example: "United States"
        coordinates:
          type: object
          properties:
            lat:
              type: number
              format: double
              description: Latitude
              example: 37.7749
            lng:
              type: number
              format: double
              description: Longitude
              example: -122.4194
        placeId:
          type: string
          description: Google Places ID
          example: "ChIJIQBpAG2ahYAR_6128GcTUEo"

    ExperiencePricing:
      type: object
      required:
        - basePrice
        - currency
      properties:
        basePrice:
          type: number
          format: double
          minimum: 0.01
          description: Base price per person
          example: 75.00
        currency:
          type: string
          description: Currency code (ISO 4217)
          example: "USD"
        priceBreakdown:
          type: array
          items:
            type: object
            properties:
              description:
                type: string
                example: "Equipment rental"
              amount:
                type: number
                format: double
                example: 15.00

    ExperienceInclusion:
      type: object
      required:
        - item
        - included
      properties:
        item:
          type: string
          description: Item or service description
          example: "Professional photography guidance"
        included:
          type: boolean
          description: Whether the item is included
          example: true

    ExperienceCategory:
      type: string
      enum:
        - default
        - adventure
        - food
        - culture
        - entertainment
        - outdoor
        - indoor
      description: Experience category
      example: "outdoor"

    BookingModel:
      type: string
      enum:
        - per_session
        - per_max_guest
      description: |
        Booking model:
        - per_session: Only one booking allowed per time slot
        - per_max_guest: Multiple bookings up to maxGuests capacity
      default: per_max_guest
      example: "per_max_guest"

    TimeSlot:
      type: object
      required:
        - time
        - available
        - maxGuests
      properties:
        time:
          type: string
          pattern: "^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$"
          description: Time in HH:MM format (24-hour)
          example: "10:00"
        available:
          type: boolean
          description: Whether this time slot is available for booking
          example: true
        maxGuests:
          type: integer
          minimum: 1
          description: Maximum number of guests for this time slot
          example: 6

    ExperienceAvailabilityConfig:
      type: object
      description: |
        Optional availability configuration for the experience.
        Supports three modes:
        1. weeklySchedule with specific days: Different time slots for each day
        2. weeklySchedule with "default": Same time slots for all days (Monday-Sunday)
        3. No configuration: Standard time slots for all days
      properties:
        weeklySchedule:
          type: object
          description: |
            Configure availability patterns using either:
            1. Specific days (monday, tuesday, etc.) for different time slots per day
            2. "default" key for same time slots across all days (Monday-Sunday)

            Rules:
            - "default" cannot be used with specific day names
            - Time slot arrays cannot be empty
            - availabilityId and currentBookings are automatically generated
          properties:
            default:
              type: array
              items:
                $ref: "#/components/schemas/TimeSlot"
              description: Same time slots for all days (Monday-Sunday)
            monday:
              type: array
              items:
                $ref: "#/components/schemas/TimeSlot"
              description: Time slots for Monday
            tuesday:
              type: array
              items:
                $ref: "#/components/schemas/TimeSlot"
              description: Time slots for Tuesday
            wednesday:
              type: array
              items:
                $ref: "#/components/schemas/TimeSlot"
              description: Time slots for Wednesday
            thursday:
              type: array
              items:
                $ref: "#/components/schemas/TimeSlot"
              description: Time slots for Thursday
            friday:
              type: array
              items:
                $ref: "#/components/schemas/TimeSlot"
              description: Time slots for Friday
            saturday:
              type: array
              items:
                $ref: "#/components/schemas/TimeSlot"
              description: Time slots for Saturday
            sunday:
              type: array
              items:
                $ref: "#/components/schemas/TimeSlot"
              description: Time slots for Sunday
          additionalProperties: false
          example:
            monday:
              - time: "10:00"
                available: true
                maxGuests: 6
              - time: "15:00"
                available: true
                maxGuests: 6
            friday:
              - time: "14:00"
                available: true
                maxGuests: 8

    CreateExperienceResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        data:
          type: object
          properties:
            id:
              type: string
              description: Generated experience ID
              example: "exp_abc123def456"
            message:
              type: string
              example: "Experience created successfully"

    ErrorResponse:
      type: object
      properties:
        success:
          type: boolean
          example: false
        error:
          type: string
          description: Error message
          example: "Missing required field: title"
