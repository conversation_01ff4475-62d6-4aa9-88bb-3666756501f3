import { NextRequest, NextResponse } from "next/server"
import { verifyAuth } from "@/lib/api-auth"
import { sendEmail } from "@/lib/server/email-service"

export async function POST(request: NextRequest) {
  try {
    // Verify authentication
    const authResult = await verifyAuth(request)

    if (!authResult.isAuthenticated) {
      return authResult.response
    }

    // Get the request body
    const body = await request.json()
    const { to, subject, htmlContent, templateId, params, from } = body

    if (!to || !subject || (!htmlContent && (!templateId || !params))) {
      return NextResponse.json(
        {
          error:
            "Missing required fields: to, subject, and either htmlContent or (templateId and params)",
        },
        { status: 400 }
      )
    }

    // Send the email using the server-side email service
    const result = await sendEmail({
      to,
      subject,
      htmlContent,
      templateId,
      params,
      from,
    })

    // Handle the response
    if (result.success) {
      return NextResponse.json({ success: true, messageId: result.messageId })
    } else {
      console.error("Error sending email:", result.error)
      return NextResponse.json(
        { error: "Failed to send email", details: result.error },
        { status: 500 }
      )
    }
  } catch (error) {
    console.error("Error in email API route:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}
