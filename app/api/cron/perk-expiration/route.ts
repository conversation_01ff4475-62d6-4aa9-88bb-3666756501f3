import { NextRequest, NextResponse } from "next/server"
import { PerkService } from "@/lib/domains/perk/perk.service"

/**
 * Cron job endpoint for processing perk expirations
 *
 * This endpoint should be called daily by a cron service (e.g., Vercel Cron, GitHub Actions)
 *
 * Expected to be called with:
 * - Authorization header with cron secret
 * - POST method
 */
export async function POST(request: NextRequest) {
  try {
    // Verify cron authorization
    const authHeader = request.headers.get("authorization")
    const expectedAuth = `Bearer ${process.env.CRON_SECRET}`

    if (!authHeader || authHeader !== expectedAuth) {
      console.error("Unauthorized cron request")
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    console.log("🎁 Starting perk expiration cron job...")

    const result = await PerkService.processExpiredPerks()

    const { processedUsers, expiredPerks, errors, duration } = result

    console.log(`✅ Perk expiration cron job completed successfully in ${duration}ms`)

    return NextResponse.json({
      success: true,
      message: "Perk expiration processing completed",
      data: {
        processedUsers,
        expiredPerks,
        errorCount: errors.length,
        duration,
      },
    })
  } catch (error) {
    console.error("❌ Perk expiration cron job failed:", error)

    return NextResponse.json(
      {
        success: false,
        error: "Perk expiration processing failed",
        message: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    )
  }
}

/**
 * Health check endpoint
 */
export async function GET(request: NextRequest) {
  try {
    // Verify authorization for health checks
    const authHeader = request.headers.get("authorization")
    const expectedAuth = `Bearer ${process.env.CRON_SECRET}`

    if (!authHeader || authHeader !== expectedAuth) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    return NextResponse.json({
      success: true,
      message: "Perk expiration cron endpoint is healthy",
      timestamp: new Date().toISOString(),
    })
  } catch (error) {
    return NextResponse.json(
      {
        success: false,
        error: "Health check failed",
        message: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    )
  }
}
