import { NextRequest, NextResponse } from "next/server"
import { ReferralHealthService } from "@/lib/domains/referral/referral-health.service"

/**
 * Cron job endpoint for referral system health checks
 *
 * This endpoint should be called periodically (e.g., every 6 hours) by a cron service
 *
 * Expected to be called with:
 * - Authorization header with cron secret
 * - POST method
 */
export async function POST(request: NextRequest) {
  try {
    // Verify cron authorization
    const authHeader = request.headers.get("authorization")
    const expectedAuth = `Bearer ${process.env.CRON_SECRET}`

    if (!authHeader || authHeader !== expectedAuth) {
      console.error("Unauthorized cron request")
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    console.log("🏥 Starting referral system health check cron job...")

    const result = await ReferralHealthService.performHealthCheck()

    if (result.success) {
      const {
        totalReferralCodes,
        totalUsers,
        usersWithIssues,
        commonIssues,
        systemHealth,
        perkSystemHealth,
      } = result.data!

      console.log(`✅ Referral health check completed: System is ${systemHealth}`)

      // Determine response status based on system health
      const statusCode = systemHealth === "critical" ? 500 : 200

      return NextResponse.json(
        {
          success: true,
          message: "Referral system health check completed",
          data: {
            totalReferralCodes,
            totalUsers,
            usersWithIssues,
            commonIssues,
            systemHealth,
            perkSystemHealth,
          },
        },
        { status: statusCode }
      )
    } else {
      console.error("❌ Referral health check cron job failed:", result.error?.message)

      return NextResponse.json(
        {
          success: false,
          error: "Referral system health check failed",
          message: result.error?.message,
        },
        { status: 500 }
      )
    }
  } catch (error) {
    console.error("❌ Referral health check cron job failed:", error)

    return NextResponse.json(
      {
        success: false,
        error: "Referral system health check failed",
        message: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    )
  }
}

/**
 * Manual health check endpoint (GET)
 */
export async function GET(request: NextRequest) {
  try {
    // Verify authorization for manual health checks
    const authHeader = request.headers.get("authorization")
    const expectedAuth = `Bearer ${process.env.CRON_SECRET}`

    if (!authHeader || authHeader !== expectedAuth) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    console.log("🏥 Manual referral system health check requested...")

    const result = await ReferralHealthService.performHealthCheck()

    if (result.success) {
      const {
        totalReferralCodes,
        totalUsers,
        usersWithIssues,
        commonIssues,
        systemHealth,
        perkSystemHealth,
      } = result.data!

      return NextResponse.json({
        success: true,
        message: "Manual referral health check completed",
        data: {
          totalReferralCodes,
          totalUsers,
          usersWithIssues,
          commonIssues,
          systemHealth,
          perkSystemHealth,
          timestamp: new Date().toISOString(),
        },
      })
    } else {
      return NextResponse.json(
        {
          success: false,
          error: "Referral health check failed",
          message: result.error?.message,
        },
        { status: 500 }
      )
    }
  } catch (error) {
    return NextResponse.json(
      {
        success: false,
        error: "Referral health check failed",
        message: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    )
  }
}
