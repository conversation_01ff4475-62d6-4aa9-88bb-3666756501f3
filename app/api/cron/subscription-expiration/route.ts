import { NextRequest, NextResponse } from "next/server"
import { FlatSubscriptionCronService } from "@/lib/domains/user-subscription/flat-subscription-cron.service"

/**
 * Cron job endpoint for processing subscription expirations
 *
 * This endpoint should be called daily by a cron service (e.g., Vercel Cron, GitHub Actions)
 *
 * Expected to be called with:
 * - Authorization header with cron secret
 * - POST method
 */
export async function POST(request: NextRequest) {
  try {
    // Verify cron authorization
    const authHeader = request.headers.get("authorization")
    const expectedAuth = `Bearer ${process.env.CRON_SECRET}`

    if (!authHeader || authHeader !== expectedAuth) {
      console.error("Unauthorized cron request")
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    console.log("🕐 Starting subscription expiration cron job...")

    const result = await FlatSubscriptionCronService.processSubscriptionExpirations()

    if (result.success) {
      const { processedUsers, expiredEntries, precedenceUpdates, errors, duration } = result.data!

      console.log(`✅ Cron job completed successfully in ${duration}ms`)

      return NextResponse.json({
        success: true,
        message: "Subscription expiration processing completed",
        data: {
          processedUsers,
          expiredEntries,
          precedenceUpdates,
          errorCount: errors.length,
          duration,
        },
      })
    } else {
      console.error("❌ Cron job failed:", result.error?.message)

      return NextResponse.json(
        {
          success: false,
          error: "Subscription expiration processing failed",
          message: result.error?.message,
        },
        { status: 500 }
      )
    }
  } catch (error) {
    console.error("💥 Cron job exception:", error)

    return NextResponse.json(
      {
        success: false,
        error: "Internal server error",
        message: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    )
  }
}

/**
 * Health check endpoint
 */
export async function GET(request: NextRequest) {
  try {
    // Verify authorization for health checks
    const authHeader = request.headers.get("authorization")
    const expectedAuth = `Bearer ${process.env.CRON_SECRET}`

    if (!authHeader || authHeader !== expectedAuth) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    return NextResponse.json({
      success: true,
      message: "Subscription expiration cron endpoint is healthy",
      timestamp: new Date().toISOString(),
    })
  } catch (error) {
    return NextResponse.json(
      {
        success: false,
        error: "Health check failed",
        message: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    )
  }
}
