import { NextRequest, NextResponse } from "next/server"
import { FlatSubscriptionCronService } from "@/lib/domains/user-subscription/flat-subscription-cron.service"

/**
 * Cron job endpoint for subscription system health checks
 *
 * This endpoint should be called periodically (e.g., every 6 hours) by a cron service
 *
 * Expected to be called with:
 * - Authorization header with cron secret
 * - POST method
 */
export async function POST(request: NextRequest) {
  try {
    // Verify cron authorization
    const authHeader = request.headers.get("authorization")
    const expectedAuth = `Bearer ${process.env.CRON_SECRET}`

    if (!authHeader || authHeader !== expectedAuth) {
      console.error("Unauthorized cron request")
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    console.log("🏥 Starting subscription health check cron job...")

    const result = await FlatSubscriptionCronService.performHealthCheck()

    if (result.success) {
      const { totalUsers, usersWithIssues, commonIssues, systemHealth } = result.data!

      console.log(`✅ Health check completed: System is ${systemHealth}`)

      // Determine response status based on system health
      const statusCode = systemHealth === "critical" ? 500 : 200

      return NextResponse.json(
        {
          success: true,
          message: "Subscription health check completed",
          data: {
            totalUsers,
            usersWithIssues,
            commonIssues,
            systemHealth,
          },
        },
        { status: statusCode }
      )
    } else {
      console.error("❌ Health check cron job failed:", result.error?.message)

      return NextResponse.json(
        {
          success: false,
          error: "Subscription health check failed",
          message: result.error?.message,
        },
        { status: 500 }
      )
    }
  } catch (error) {
    console.error("💥 Health check cron job exception:", error)

    return NextResponse.json(
      {
        success: false,
        error: "Internal server error",
        message: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    )
  }
}

/**
 * Manual health check endpoint (GET)
 */
export async function GET(request: NextRequest) {
  try {
    // Verify authorization for health checks
    const authHeader = request.headers.get("authorization")
    const expectedAuth = `Bearer ${process.env.CRON_SECRET}`

    if (!authHeader || authHeader !== expectedAuth) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    console.log("🏥 Manual subscription health check requested...")

    const result = await FlatSubscriptionCronService.performHealthCheck()

    if (result.success) {
      const { totalUsers, usersWithIssues, commonIssues, systemHealth } = result.data!

      return NextResponse.json({
        success: true,
        message: "Manual health check completed",
        data: {
          totalUsers,
          usersWithIssues,
          commonIssues,
          systemHealth,
          timestamp: new Date().toISOString(),
        },
      })
    } else {
      return NextResponse.json(
        {
          success: false,
          error: "Health check failed",
          message: result.error?.message,
        },
        { status: 500 }
      )
    }
  } catch (error) {
    return NextResponse.json(
      {
        success: false,
        error: "Health check failed",
        message: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    )
  }
}
