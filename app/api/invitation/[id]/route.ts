import { NextRequest, NextResponse } from "next/server"
import { getAdminInstance } from "@/lib/firebase-admin"
import { verifyAuth } from "@/lib/api-auth"

/**
 * API route to fetch invitation details using Firebase Admin SDK
 * This route requires authentication and only supports invitation-links
 * Legacy invitations are no longer supported for security reasons
 */
export async function GET(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  try {
    // Verify authentication
    const authResult = await verifyAuth(request)
    if (!authResult.isAuthenticated) {
      return authResult.response
    }

    const { id: invitationId } = await params

    if (!invitationId) {
      return NextResponse.json({ error: "Invitation ID is required" }, { status: 400 })
    }

    // Get invitation link using Admin SDK
    const { adminDb } = await getAdminInstance()

    if (!adminDb) {
      console.error("Firebase Admin Firestore is not initialized")
      return NextResponse.json({ error: "Internal server error" }, { status: 500 })
    }

    const invitationLinkDoc = await adminDb.collection("invitation-links").doc(invitationId).get()

    if (!invitationLinkDoc.exists) {
      return NextResponse.json({ error: "Invitation not found" }, { status: 404 })
    }

    const invitationLinkData = invitationLinkDoc.data()

    // Check if the invitation is expired or inactive
    const now = new Date()
    const expiresAt = invitationLinkData?.expiresAt?.toDate()

    if (!invitationLinkData?.isActive || (expiresAt && expiresAt <= now)) {
      return NextResponse.json(
        { error: "Invitation has expired or is no longer active" },
        { status: 410 }
      )
    }

    // Get invitation-sends for this invitation to include invitee data
    const squadId = invitationLinkData?.squadId
    if (!squadId) {
      return NextResponse.json({ error: "Invalid invitation data" }, { status: 400 })
    }

    // Get invitation sends for this invitation
    const invitationSendsSnapshot = await adminDb
      .collection(`squads/${squadId}/invitation-sends`)
      .where("invitationId", "==", invitationId)
      .get()

    const invitationSends = invitationSendsSnapshot.docs.map((doc: any) => ({
      id: doc.id,
      ...doc.data(),
    }))

    // Return complete invitation data including sends
    const invitationLink = {
      id: invitationLinkDoc.id,
      squadId: squadId,
      squadName: invitationLinkData?.squadName,
      inviterId: invitationLinkData?.inviterId,
      inviterName: invitationLinkData?.inviterName,
      expiresAt: invitationLinkData?.expiresAt,
      isActive: invitationLinkData?.isActive,
      createdAt: invitationLinkData?.createdAt,
      type: "invitation_link",
      invitationSends: invitationSends, // Include invitation sends data
    }

    return NextResponse.json(invitationLink)
  } catch (error) {
    console.error("Error fetching invitation:", error)
    return NextResponse.json(
      {
        error: "Failed to fetch invitation details",
        details: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    )
  }
}
