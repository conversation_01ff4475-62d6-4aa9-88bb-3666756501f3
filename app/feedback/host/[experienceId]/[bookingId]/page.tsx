"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON>, useRout<PERSON> } from "next/navigation"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Star } from "lucide-react"
import { toast } from "sonner"
import { LocalExperiencesBookingService } from "@/lib/domains/local-experiences/local-experiences-booking.service"
import { LocalExperiencesService } from "@/lib/domains/local-experiences/local-experiences.service"
import { FeedbackService } from "@/lib/domains/local-experiences/feedback.service"
import { ExperienceBooking } from "@/lib/domains/local-experiences/local-experiences-booking.types"
import { LocalExperience } from "@/lib/domains/local-experiences/local-experiences.types"
import {
  HostFeedbackFormData,
  FeedbackRating,
} from "@/lib/domains/local-experiences/feedback.types"
import { PageLoading } from "@/components/page-loading"

export default function HostFeedbackPage() {
  const params = useParams()
  const router = useRouter()
  const experienceId = params.experienceId as string
  const bookingId = params.bookingId as string

  const [experience, setExperience] = useState<LocalExperience | null>(null)
  const [booking, setBooking] = useState<ExperienceBooking | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [hasSubmitted, setHasSubmitted] = useState(false)
  const [formData, setFormData] = useState<HostFeedbackFormData>({
    whatWentWell: "",
    whatDidntGoWell: "",
    overallRating: 5,
  })

  // Load experience and booking data
  useEffect(() => {
    const loadData = async () => {
      if (!experienceId || !bookingId) return

      try {
        setIsLoading(true)

        // Get experience details
        const experienceResponse = await LocalExperiencesService.getExperience(experienceId)

        if (!experienceResponse.success || !experienceResponse.data) {
          toast.error("Experience not found")
          return
        }

        setExperience(experienceResponse.data)

        // Get booking details
        const bookingResponse = await LocalExperiencesBookingService.getBooking(bookingId)

        if (!bookingResponse.success || !bookingResponse.data) {
          toast.error("Booking not found")
          return
        }

        const bookingData = bookingResponse.data

        // Verify this booking is for this experience
        if (bookingData.experienceId !== experienceId) {
          toast.error("Booking does not match this experience")
          return
        }

        // Check if booking is completed
        if (bookingData.status !== "completed") {
          toast.error("You can only provide feedback for completed experiences")
          return
        }

        setBooking(bookingData)

        // Check if feedback has already been submitted
        const feedbackResponse = await FeedbackService.hasHostSubmittedFeedback(
          experienceId,
          bookingId
        )

        if (feedbackResponse.success && feedbackResponse.data) {
          setHasSubmitted(true)
        }
      } catch (error) {
        console.error("Error loading data:", error)
        toast.error("Failed to load experience information")
      } finally {
        setIsLoading(false)
      }
    }

    loadData()
  }, [experienceId, bookingId])

  const handleRatingChange = (rating: FeedbackRating) => {
    setFormData((prev) => ({ ...prev, overallRating: rating }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!experience || !booking) return

    // Validate form
    if (!formData.whatWentWell.trim()) {
      toast.error("Please tell us what went well")
      return
    }

    if (!formData.whatDidntGoWell.trim()) {
      toast.error("Please tell us what didn't go well")
      return
    }

    try {
      setIsSubmitting(true)

      const feedbackData = {
        bookingId,
        experienceId,
        customerId: booking.userId,
        customerEmail: booking.userEmail,
        customerName: booking.userName,
        experienceTitle: experience.title,
        experienceDate: booking.date,
        experienceTime: booking.time,
        ...formData,
      }

      const response = await FeedbackService.submitHostFeedback(feedbackData)

      if (response.success) {
        toast.success("Thank you for your feedback!")
        setHasSubmitted(true)
      } else {
        console.error("Error submitting feedback:", response.error)
        toast.error("Failed to submit feedback")
      }
    } catch (error) {
      console.error("Error submitting feedback:", error)
      toast.error("Failed to submit feedback")
    } finally {
      setIsSubmitting(false)
    }
  }

  if (isLoading) {
    return <PageLoading message="Loading feedback form..." />
  }

  if (!experience || !booking) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <Card className="max-w-md">
          <CardHeader>
            <CardTitle>Not Found</CardTitle>
            <CardDescription>
              The requested experience or booking could not be found.
            </CardDescription>
          </CardHeader>
        </Card>
      </div>
    )
  }

  if (hasSubmitted) {
    return (
      <div className="min-h-screen bg-background">
        <div className="container mx-auto px-4 py-8">
          <div className="max-w-2xl mx-auto">
            <Card>
              <CardHeader className="text-center">
                <CardTitle className="text-2xl text-green-600">Thank You!</CardTitle>
                <CardDescription>Your feedback has been submitted successfully</CardDescription>
              </CardHeader>
              <CardContent className="text-center space-y-4">
                <p className="text-muted-foreground">
                  We appreciate you taking the time to share your experience with this customer.
                  Your feedback helps us improve our platform.
                </p>
                <div className="text-sm text-muted-foreground">
                  <p>
                    <strong>Experience:</strong> {experience.title}
                  </p>
                  <p>
                    <strong>Customer:</strong> {booking.userName}
                  </p>
                  <p>
                    <strong>Date:</strong> {booking.date} at {booking.time}
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-2xl mx-auto">
          <Card>
            <CardHeader>
              <CardTitle className="text-2xl">Host Feedback</CardTitle>
              <CardDescription>Share your experience with {booking.userName}</CardDescription>
              <div className="text-sm text-muted-foreground mt-2">
                <p>
                  <strong>Experience:</strong> {experience.title}
                </p>
                <p>
                  <strong>Date:</strong> {booking.date} at {booking.time}
                </p>
                <p>
                  <strong>Guests:</strong> {booking.guests}
                </p>
              </div>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSubmit} className="space-y-6">
                {/* What went well */}
                <div className="space-y-2">
                  <Label htmlFor="whatWentWell">What went well with the experience?</Label>
                  <Textarea
                    id="whatWentWell"
                    value={formData.whatWentWell}
                    onChange={(e) =>
                      setFormData((prev) => ({ ...prev, whatWentWell: e.target.value }))
                    }
                    placeholder="Tell us about the positive aspects of hosting this customer..."
                    required
                    rows={4}
                  />
                </div>

                {/* What didn't go well */}
                <div className="space-y-2">
                  <Label htmlFor="whatDidntGoWell">What didn't go well?</Label>
                  <Textarea
                    id="whatDidntGoWell"
                    value={formData.whatDidntGoWell}
                    onChange={(e) =>
                      setFormData((prev) => ({ ...prev, whatDidntGoWell: e.target.value }))
                    }
                    placeholder="Share any challenges or areas for improvement..."
                    required
                    rows={4}
                  />
                </div>

                {/* Overall rating */}
                <div className="space-y-2">
                  <Label>Overall rating for the experience with this customer</Label>
                  <div className="flex items-center space-x-1">
                    {[1, 2, 3, 4, 5].map((rating) => (
                      <button
                        key={rating}
                        type="button"
                        onClick={() => handleRatingChange(rating as FeedbackRating)}
                        className="p-1 hover:scale-110 transition-transform"
                      >
                        <Star
                          className={`w-8 h-8 ${
                            rating <= formData.overallRating
                              ? "fill-yellow-400 text-yellow-400"
                              : "text-gray-300"
                          }`}
                        />
                      </button>
                    ))}
                    <span className="ml-2 text-sm text-muted-foreground">
                      {formData.overallRating} star{formData.overallRating !== 1 ? "s" : ""}
                    </span>
                  </div>
                </div>

                <Button type="submit" disabled={isSubmitting} className="w-full">
                  {isSubmitting ? "Submitting..." : "Submit Feedback"}
                </Button>
              </form>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
