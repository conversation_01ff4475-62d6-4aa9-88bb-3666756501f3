"use server"

import { getAdminInstance } from "@/lib/firebase-admin"
import { uploadProfilePictureAction } from "@/app/signup/actions/upload-profile-picture"
import { ReferralAdminService } from "@/lib/domains/referral/referral.admin.service"

interface OnboardingProfileData {
  userId: string
  email: string
  name: string
  bio?: string
  location?: string
  locationPlaceId?: string
  selectedTravelPreferences: string[]
  budget: string
  selectedAvailability: string[]
  selectedMonths: string[]
  selectedTravelGroups: string[]
  referralCode?: string
  invitedEmail?: string
  photoURL?: string // From OAuth provider
}

export async function completeUserProfileWithOnboardingAction(
  profileData: OnboardingProfileData,
  profilePictureFormData?: FormData
): Promise<{ success: boolean; error?: string; redirectUrl?: string }> {
  try {
    // Validate required fields
    if (!profileData.userId || !profileData.email || !profileData.name) {
      return {
        success: false,
        error: "Missing required fields: userId, email, and name are required",
      }
    }

    // Validate email match if this is an invitation signup
    if (profileData.invitedEmail) {
      const oauthEmail = profileData.email.toLowerCase()
      const expectedEmail = profileData.invitedEmail.toLowerCase()

      if (oauthEmail !== expectedEmail) {
        return {
          success: false,
          error: `Email mismatch: You were invited as ${profileData.invitedEmail}, but signed in with ${profileData.email}. Please sign in with the invited email address.`,
        }
      }
    }

    // Get Firebase Admin instances
    const { adminAuth, adminDb } = await getAdminInstance()
    if (!adminAuth || !adminDb) {
      return { success: false, error: "Firebase Admin not initialized" }
    }

    const userId = profileData.userId
    const userEmail = profileData.email
    const userPhotoURL = profileData.photoURL

    // Check if user document already exists
    const userDocRef = adminDb.collection("users").doc(userId)
    const userDoc = await userDocRef.get()

    if (userDoc.exists) {
      return { success: false, error: "User profile already exists" }
    }

    // Handle profile picture upload
    let profilePictureUrl = userPhotoURL // Default to OAuth provider photo

    if (profilePictureFormData) {
      // User uploaded a custom profile picture during onboarding
      const file = profilePictureFormData.get("file") as File
      if (file && file.size > 0) {
        try {
          const uploadResult = await uploadProfilePictureAction(profilePictureFormData)
          if (uploadResult.success && uploadResult.url) {
            profilePictureUrl = uploadResult.url
          }
        } catch (uploadError) {
          console.error("Profile picture upload failed:", uploadError)
          // Continue with OAuth photo or default avatar
        }
      }
    }

    // Generate a unique referral code for the new user
    const newUserReferralCode = await ReferralAdminService.generateReferralCode()

    // Create user document
    const userData = {
      uid: userId,
      email: userEmail,
      name: profileData.name,
      displayName: profileData.name, // Add displayName for consistency with Firebase Auth
      bio: profileData.bio || "",
      location: profileData.location || "",
      locationPlaceId: profileData.locationPlaceId || "",
      profilePictureUrl: profilePictureUrl || "",
      photoURL: profilePictureUrl || "", // Add photoURL for consistency with Firebase Auth
      referralCode: newUserReferralCode,
      newUser: true, // Mark as new user
      firstLogin: new Date(), // Add firstLogin timestamp for new user tracking
      demoTourOptedOut: false, // Default to showing demo tour
      createdAt: new Date(),
      updatedAt: new Date(),
    }

    // Create user preferences document
    const preferencesData = {
      userId,
      travelPreferences: profileData.selectedTravelPreferences,
      budget: profileData.budget,
      availabilityPreferences: profileData.selectedAvailability,
      preferredTravelSeasons: profileData.selectedMonths,
      travelGroupPreferences: profileData.selectedTravelGroups,
      createdAt: new Date(),
      updatedAt: new Date(),
    }

    // Create AI usage document
    const aiUsageData = {
      userId,
      itinerarySuggestions: 0,
      restaurantSuggestions: 0,
      activitySuggestions: 0,
      lastResetDate: new Date(),
      createdAt: new Date(),
      updatedAt: new Date(),
    }

    // Create subscription document (free tier)
    const subscriptionData = {
      userId,
      plan: "free",
      status: "active",
      createdAt: new Date(),
      updatedAt: new Date(),
    }

    // Use a batch write to create all documents atomically
    const batch = adminDb.batch()

    batch.set(userDocRef, userData)
    batch.set(adminDb.collection("user-preferences").doc(userId), preferencesData)
    batch.set(adminDb.collection("user-ai-usage").doc(userId), aiUsageData)
    batch.set(adminDb.collection("user-subscriptions").doc(userId), subscriptionData)

    // Handle referral code if provided
    if (profileData.referralCode) {
      try {
        // Find the referrer by referral code
        const referrerQuery = await adminDb
          .collection("users")
          .where("referralCode", "==", profileData.referralCode.toUpperCase())
          .limit(1)
          .get()

        if (!referrerQuery.empty) {
          const referrerDoc = referrerQuery.docs[0]
          const referrerId = referrerDoc.id

          // Create referral record
          const referralData = {
            referrerId,
            referredUserId: userId,
            referralCode: profileData.referralCode.toUpperCase(),
            status: "completed",
            createdAt: new Date(),
          }

          batch.set(adminDb.collection("referrals").doc(), referralData)
        }
      } catch (referralError) {
        console.error("Referral processing failed:", referralError)
        // Continue without failing the entire operation
      }
    }

    // Update Firebase Auth profile with name and photo
    try {
      await adminAuth.updateUser(userId, {
        displayName: profileData.name,
        photoURL: profilePictureUrl || undefined,
      })
    } catch (authUpdateError) {
      console.error("Firebase Auth profile update failed:", authUpdateError)
      // Continue without failing the entire operation
    }

    // Commit the batch
    await batch.commit()

    // Create referral code for the new user (after user document is created)
    try {
      await ReferralAdminService.createReferralCode(newUserReferralCode, userId)
      console.log("Referral code created for new user:", newUserReferralCode)
    } catch (error) {
      console.error("Error creating referral code for new user:", error)
      // Don't fail the profile completion if referral code creation fails
    }

    // Process referral code if provided
    if (profileData.referralCode) {
      console.log("🎯 Processing referral code:", profileData.referralCode, "for user:", userId)
      try {
        const referralResult = await ReferralAdminService.processReferral(
          profileData.referralCode,
          userId,
          userEmail || ""
        )

        console.log("🔄 Referral processing result:", referralResult)

        if (referralResult.success) {
          console.log("✅ Referral processed successfully:", referralResult)
        } else {
          console.error("❌ Failed to process referral:", referralResult.error)
        }
      } catch (error) {
        console.error("💥 Error processing referral code:", error)
        // Don't fail the profile completion if referral processing fails
      }
    }

    return {
      success: true,
      redirectUrl: "/dashboard",
    }
  } catch (error) {
    console.error("Complete user profile with onboarding error:", error)
    return {
      success: false,
      error: error instanceof Error ? error.message : "An unexpected error occurred",
    }
  }
}
