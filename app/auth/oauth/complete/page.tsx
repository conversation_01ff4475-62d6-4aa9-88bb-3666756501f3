"use client"

import { useEffect, useState, Suspense } from "react"
import { useRouter, useSearchParams } from "next/navigation"
import Link from "next/link"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Logo } from "@/components/ui/logo"
import { Loader2 } from "lucide-react"
import { useProviderSignIn } from "@/lib/domains/auth/auth.hooks"
import { toast } from "@/components/ui/use-toast"
import { completeUserProfileWithOnboardingAction } from "../actions/complete-user-profile-with-onboarding"

interface OnboardingData {
  name: string
  bio: string
  location: string
  locationPlaceId?: string
  selectedTravelPreferences: string[]
  budget: string
  selectedAvailability: string[]
  selectedMonths: string[]
  selectedTravelGroups: string[]
  referralCode: string
}

function OAuthCompleteContent() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const { signInWithProvider } = useProviderSignIn()
  const [_processing, setProcessing] = useState(false) // Used for profile creation step
  const [step, setStep] = useState<"signing-in" | "creating-profile" | "complete">("signing-in")

  const provider = searchParams.get("provider")
  const invitedEmail = searchParams.get("invited_email")
  const callbackUrl = searchParams.get("callback")
  const onboardingComplete = searchParams.get("onboarding_complete")

  useEffect(() => {
    if (!provider) {
      router.push("/signup")
      return
    }

    handleOAuthSignIn()
  }, [provider])

  const handleOAuthSignIn = async () => {
    if (!provider) return

    try {
      setStep("signing-in")

      // Sign in with the OAuth provider
      const result = await signInWithProvider(provider)

      if (result.success) {
        setStep("creating-profile")

        // Validate email match if this is an invitation signup
        if (invitedEmail && result.data?.user?.email) {
          const oauthEmail = result.data.user.email.toLowerCase()
          const expectedEmail = invitedEmail.toLowerCase()

          if (oauthEmail !== expectedEmail) {
            throw new Error(
              `Email mismatch: You were invited as ${invitedEmail}, but signed in with ${result.data.user.email}. Please sign in with the invited email address.`
            )
          }
        }

        // Check if this was from onboarding flow
        if (onboardingComplete === "true") {
          await processOnboardingData(result.data)
        } else {
          // Regular OAuth sign-in, redirect to callback or dashboard
          // The useAuthRedirect hook will handle checking if user needs to complete profile
          router.push(callbackUrl ? decodeURIComponent(callbackUrl) : "/dashboard")
        }
      } else {
        toast({
          title: "Sign-in failed",
          description:
            typeof result.error === "string"
              ? result.error
              : "Please try again or contact support if the problem persists.",
          variant: "destructive",
        })
        router.push("/signup")
      }
    } catch (error) {
      console.error("OAuth sign-in error:", error)
      toast({
        title: "Sign-in failed",
        description: "Please try again or contact support if the problem persists.",
        variant: "destructive",
      })
      router.push("/signup")
    }
  }

  const processOnboardingData = async (authResult: any) => {
    try {
      setProcessing(true)

      // Get onboarding data from localStorage
      const onboardingDataStr = localStorage.getItem("onboarding_data")
      const profilePictureData = localStorage.getItem("onboarding_profile_picture")
      const profilePictureName = localStorage.getItem("onboarding_profile_picture_name")
      const profilePictureType = localStorage.getItem("onboarding_profile_picture_type")

      if (!onboardingDataStr) {
        throw new Error("No onboarding data found")
      }

      const onboardingData: OnboardingData = JSON.parse(onboardingDataStr)

      // Validate email match if this is an invitation signup
      if (invitedEmail && authResult.user?.email) {
        const oauthEmail = authResult.user.email.toLowerCase()
        const expectedEmail = invitedEmail.toLowerCase()

        if (oauthEmail !== expectedEmail) {
          throw new Error(
            `Email mismatch: You were invited as ${invitedEmail}, but signed in with ${authResult.user.email}. Please sign in with the invited email address.`
          )
        }
      }

      // Prepare profile picture form data if exists
      let profilePictureFormData: FormData | undefined
      if (profilePictureData && profilePictureName && profilePictureType) {
        // Convert base64 back to file
        const response = await fetch(profilePictureData)
        const blob = await response.blob()
        const file = new File([blob], profilePictureName, { type: profilePictureType })

        profilePictureFormData = new FormData()
        profilePictureFormData.append("file", file)
      }

      // Complete user profile with onboarding data
      const result = await completeUserProfileWithOnboardingAction(
        {
          userId: authResult.user?.uid || "",
          email: authResult.user?.email || "",
          photoURL: authResult.user?.photoURL || undefined,
          name: onboardingData.name,
          bio: onboardingData.bio,
          location: onboardingData.location || undefined,
          locationPlaceId: onboardingData.locationPlaceId || undefined,
          selectedTravelPreferences: onboardingData.selectedTravelPreferences,
          budget: onboardingData.budget,
          selectedAvailability: onboardingData.selectedAvailability,
          selectedMonths: onboardingData.selectedMonths,
          selectedTravelGroups: onboardingData.selectedTravelGroups,
          referralCode: onboardingData.referralCode.trim() || undefined,
          invitedEmail: invitedEmail || undefined,
        },
        profilePictureFormData
      )

      if (result.success) {
        // Clean up localStorage
        localStorage.removeItem("onboarding_data")
        localStorage.removeItem("onboarding_profile_picture")
        localStorage.removeItem("onboarding_profile_picture_name")
        localStorage.removeItem("onboarding_profile_picture_type")

        setStep("complete")

        toast({
          title: "Welcome to Togeda.ai!",
          description: "Your account has been created successfully.",
        })

        // Redirect to callback or dashboard
        setTimeout(() => {
          router.push(callbackUrl ? decodeURIComponent(callbackUrl) : "/dashboard")
        }, 2000)
      } else {
        throw new Error(result.error || "Failed to create profile")
      }
    } catch (error: any) {
      if (error?.message === "User profile already exists") {
        toast({
          title: "Profile already exists",
          description: "We found your existing account and signed you in.",
          variant: "warning",
        })
        router.push("/dashboard")
      } else {
        console.error("Profile creation error:", error)
        toast({
          title: "Profile creation failed",
          description: "Please try again or contact support if the problem persists.",
          variant: "destructive",
        })
        router.push("/signup")
      }
    } finally {
      setProcessing(false)
    }
  }

  const getStepMessage = () => {
    switch (step) {
      case "signing-in":
        return "Signing you in with Google..."
      case "creating-profile":
        return "Creating your profile..."
      case "complete":
        return "Welcome to Togeda.ai! Redirecting..."
      default:
        return "Processing..."
    }
  }

  return (
    <div className="min-h-screen flex flex-col">
      <header className="border-b">
        <div className="container mx-auto px-4 py-4">
          <Link href="/">
            <Logo />
          </Link>
        </div>
      </header>
      <main className="flex-1 flex items-center justify-center p-4">
        <Card className="w-full max-w-md">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Loader2 className="h-5 w-5 animate-spin" />
              Completing Signup
            </CardTitle>
            <CardDescription>{getStepMessage()}</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <div
                  className={`w-2 h-2 rounded-full ${step === "signing-in" ? "bg-blue-500 animate-pulse" : step === "creating-profile" || step === "complete" ? "bg-green-500" : "bg-gray-300"}`}
                />
                <span className="text-sm">Authenticating with Google</span>
              </div>
              <div className="flex items-center gap-2">
                <div
                  className={`w-2 h-2 rounded-full ${step === "creating-profile" ? "bg-blue-500 animate-pulse" : step === "complete" ? "bg-green-500" : "bg-gray-300"}`}
                />
                <span className="text-sm">Setting up your profile</span>
              </div>
              <div className="flex items-center gap-2">
                <div
                  className={`w-2 h-2 rounded-full ${step === "complete" ? "bg-green-500" : "bg-gray-300"}`}
                />
                <span className="text-sm">Finalizing account</span>
              </div>
            </div>

            {step === "complete" && (
              <div className="mt-4 pt-4 border-t">
                <Button
                  onClick={() =>
                    router.push(callbackUrl ? decodeURIComponent(callbackUrl) : "/dashboard")
                  }
                  className="w-full"
                >
                  Continue to Dashboard
                </Button>
              </div>
            )}
          </CardContent>
        </Card>
      </main>
    </div>
  )
}

export default function OAuthCompletePage() {
  return (
    <Suspense
      fallback={
        <div className="min-h-screen flex flex-col">
          <header className="border-b">
            <div className="container mx-auto px-4 py-4">
              <Link href="/">
                <Logo />
              </Link>
            </div>
          </header>
          <main className="flex-1 flex items-center justify-center p-4">
            <Card className="w-full max-w-md">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Loader2 className="h-5 w-5 animate-spin" />
                  Loading...
                </CardTitle>
                <CardDescription>Preparing OAuth completion...</CardDescription>
              </CardHeader>
            </Card>
          </main>
        </div>
      }
    >
      <OAuthCompleteContent />
    </Suspense>
  )
}
