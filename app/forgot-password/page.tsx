"use client"

import { Suspense } from "react"
import { ForgotPasswordForm } from "./components/forgot-password-form"
import { PageLoading } from "@/components/page-loading"
import { useAuthRedirect } from "@/lib/domains/auth/auth.hooks"

function ForgotPasswordPageContent() {
  const { loading, isRedirecting } = useAuthRedirect()

  // Show loading while checking auth state or redirecting
  if (loading || isRedirecting) {
    return <PageLoading message="Loading..." />
  }

  return <ForgotPasswordForm />
}

export default function ForgotPasswordPage() {
  return (
    <Suspense fallback={<PageLoading message="Loading..." />}>
      <ForgotPasswordPageContent />
    </Suspense>
  )
}
