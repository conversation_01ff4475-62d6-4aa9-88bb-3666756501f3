{"name": "togeda-functions", "version": "1.0.0", "description": "Firebase Functions for Togeda.ai", "main": "lib/index.js", "scripts": {"build": "tsc -p .", "build:watch": "tsc --watch", "serve": "npm run build && firebase emulators:start --only functions", "shell": "npm run build && firebase functions:shell", "start": "npm run shell", "deploy": "firebase deploy --only functions", "logs": "firebase functions:log", "lint": "eslint ."}, "engines": {"node": "20"}, "dependencies": {"@getbrevo/brevo": "^2.5.0", "firebase-admin": "^12.0.0", "firebase-functions": "^4.8.0"}, "devDependencies": {"@types/node": "^18.15.0", "@typescript-eslint/eslint-plugin": "^5.12.0", "@typescript-eslint/parser": "^5.12.0", "eslint": "^8.15.0", "eslint-config-google": "^0.14.0", "typescript": "^5.0.0"}, "private": true}