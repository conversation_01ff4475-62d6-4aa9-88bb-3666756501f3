/**
 * Notification types for Firebase Functions
 * These match the shared types but are kept local to avoid import issues during deployment
 */

import * as admin from "firebase-admin"

// Notification type - matches the shared type
export type NotificationType =
  | "message_mention"
  | "trip_update"
  | "task_assigned"
  | "invitation"
  | "trip_completed"
  | "squad_member_joined"

// Notification interface compatible with Firebase Admin SDK
export interface Notification {
  id: string
  userId: string
  type: NotificationType
  title: string
  message: string
  read: boolean
  actionUrl: string
  relatedEntityId: string
  relatedEntityType?: string
  senderUserId?: string
  senderUserName?: string
  senderUserPhotoURL?: string
  createdAt: admin.firestore.Timestamp
  updatedAt?: admin.firestore.Timestamp
}

// Create data type (without id, createdAt, updatedAt)
export type NotificationCreateData = Omit<Notification, "id" | "createdAt" | "updatedAt">

/**
 * Available notification types as constants
 */
export const NotificationTypes = {
  MESSAGE_MENTION: "message_mention" as const,
  TRIP_UPDATE: "trip_update" as const,
  TASK_ASSIGNED: "task_assigned" as const,
  INVITATION: "invitation" as const,
  TRIP_COMPLETED: "trip_completed" as const,
  SQUAD_MEMBER_JOINED: "squad_member_joined" as const,
} as const

/**
 * Validates if a notification type is valid
 */
export function isValidNotificationType(type: string): type is NotificationType {
  return Object.values(NotificationTypes).includes(type as NotificationType)
}

/**
 * Creates a notification data object with proper structure and validation
 */
export function createNotificationData(params: {
  userId: string
  type: NotificationType
  title: string
  message: string
  read?: boolean
  actionUrl: string
  relatedEntityId: string
  relatedEntityType?: string
  senderUserId?: string
  senderUserName?: string
  senderUserPhotoURL?: string
}): NotificationCreateData {
  const {
    userId,
    type,
    title,
    message,
    read = false,
    actionUrl,
    relatedEntityId,
    relatedEntityType,
    senderUserId,
    senderUserName,
    senderUserPhotoURL,
  } = params

  // Validate required fields
  if (!userId || typeof userId !== "string") {
    throw new Error("userId is required and must be a string")
  }

  if (!isValidNotificationType(type)) {
    throw new Error(
      `Invalid notification type: ${type}. Must be one of: ${Object.values(NotificationTypes).join(", ")}`
    )
  }

  if (!title || typeof title !== "string") {
    throw new Error("title is required and must be a string")
  }

  if (!message || typeof message !== "string") {
    throw new Error("message is required and must be a string")
  }

  if (!actionUrl || typeof actionUrl !== "string") {
    throw new Error("actionUrl is required and must be a string")
  }

  if (!relatedEntityId || typeof relatedEntityId !== "string") {
    throw new Error("relatedEntityId is required and must be a string")
  }

  // Create the notification object
  const notification: NotificationCreateData = {
    userId,
    type,
    title,
    message,
    read,
    actionUrl,
    relatedEntityId,
  }

  // Add optional fields only if they have values
  if (relatedEntityType) notification.relatedEntityType = relatedEntityType
  if (senderUserId) notification.senderUserId = senderUserId
  if (senderUserName) notification.senderUserName = senderUserName
  if (senderUserPhotoURL) notification.senderUserPhotoURL = senderUserPhotoURL

  return notification
}
