import * as admin from "firebase-admin"
import {
  NotificationTypes,
  createNotificationData,
  NotificationCreateData,
} from "./notification.types"

/**
 * Notification service for Firebase Functions
 * Server-side implementation using Firebase Admin SDK
 */
export class NotificationService {
  /**
   * Create a new notification for a user
   */
  static async createNotification(
    userId: string,
    notificationData: NotificationCreateData
  ): Promise<string> {
    try {
      const db = admin.firestore()
      const notificationRef = db.collection("users").doc(userId).collection("notifications").doc()

      const notification = {
        ...notificationData,
        id: notificationRef.id,
        createdAt: admin.firestore.FieldValue.serverTimestamp(),
      }

      await notificationRef.set(notification)

      console.log(`Notification created for user ${userId}:`, notificationRef.id)
      return notificationRef.id
    } catch (error) {
      console.error("Error creating notification:", error)
      throw error
    }
  }

  /**
   * Create multiple notifications in batch
   */
  static async createBatchNotifications(
    notifications: Array<{ userId: string; notificationData: NotificationCreateData }>
  ): Promise<string[]> {
    try {
      const db = admin.firestore()
      const batch = db.batch()
      const notificationIds: string[] = []

      for (const { userId, notificationData } of notifications) {
        const notificationRef = db.collection("users").doc(userId).collection("notifications").doc()

        // Filter out undefined values to prevent Firebase errors
        const cleanNotificationData = Object.fromEntries(
          Object.entries(notificationData).filter(([, value]) => value !== undefined)
        )

        const notification = {
          ...cleanNotificationData,
          userId,
          id: notificationRef.id,
          createdAt: admin.firestore.FieldValue.serverTimestamp(),
        }

        batch.set(notificationRef, notification)
        notificationIds.push(notificationRef.id)
      }

      await batch.commit()

      console.log(`Batch notifications created for ${notifications.length} users:`, notificationIds)
      return notificationIds
    } catch (error) {
      console.error("Error creating batch notifications:", error)
      throw error
    }
  }

  /**
   * Create squad member joined notifications for all squad members except the new member
   */
  static async createSquadMemberJoinedNotifications(
    squadId: string,
    newMemberUserId: string,
    newMemberName: string,
    newMemberPhotoURL: string | undefined,
    squadName: string
  ): Promise<string[]> {
    try {
      // Validate required parameters
      if (!squadId || !newMemberUserId || !newMemberName || !squadName) {
        throw new Error("Missing required parameters for squad member joined notifications")
      }

      const db = admin.firestore()

      // Get all squad members except the new member
      const membersSnapshot = await db
        .collection("squads")
        .doc(squadId)
        .collection("members")
        .where("userId", "!=", newMemberUserId)
        .get()

      if (membersSnapshot.empty) {
        console.log("No existing members to notify in squad:", squadId)
        return []
      }

      // Prepare notifications for all existing members
      const notifications = membersSnapshot.docs.map((memberDoc) => {
        const memberData = memberDoc.data()

        const notificationData = createNotificationData({
          userId: memberData.userId,
          type: NotificationTypes.SQUAD_MEMBER_JOINED,
          title: "New Squad Member",
          message: `${newMemberName} joined ${squadName}`,
          read: false,
          actionUrl: `/squads/${squadId}`,
          relatedEntityId: squadId,
          relatedEntityType: "squad",
          senderUserId: newMemberUserId,
          senderUserName: newMemberName,
          senderUserPhotoURL: newMemberPhotoURL,
        })

        return {
          userId: memberData.userId,
          notificationData,
        }
      })

      // Create all notifications in batch
      const notificationIds = await this.createBatchNotifications(notifications)

      console.log(
        `Created ${notificationIds.length} squad member joined notifications for squad ${squadId}`
      )
      return notificationIds
    } catch (error) {
      console.error("Error creating squad member joined notifications:", error)
      throw error
    }
  }
}
