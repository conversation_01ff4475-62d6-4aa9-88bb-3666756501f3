import * as functions from "firebase-functions"
import * as admin from "firebase-admin"
import { NotificationService } from "../utils/notification.service"

// Note: SquadMember interface removed as it's not used in this function

/**
 * Interface for user data
 */
interface User {
  displayName?: string
  email?: string
  photoURL?: string
}

/**
 * Interface for squad data
 */
interface Squad {
  name: string
  leaderId: string
  memberCount: number
}

/**
 * Firebase Function that triggers when a new member is added to a squad
 * Sends in-app notifications to all existing squad members
 */
export const onSquadMemberJoin = functions.firestore
  .document("squads/{squadId}/members/{userId}")
  .onCreate(
    async (_snap: functions.firestore.QueryDocumentSnapshot, context: functions.EventContext) => {
      try {
        const { squadId, userId } = context.params
        // We don't need memberData for this function, but keeping the line for reference
        // const memberData = snap.data() as SquadMember;

        console.log(`New squad member added: ${userId} to squad ${squadId}`)

        // Get the new member's user data
        const db = admin.firestore()
        const userDoc = await db.collection("users").doc(userId).get()

        if (!userDoc.exists) {
          console.error(`User document not found for userId: ${userId}`)
          return {
            success: false,
            error: "User document not found",
            squadId,
            userId,
          }
        }

        const userData = userDoc.data() as User
        const newMemberName = userData.displayName || userData.email || "Unknown User"
        const newMemberPhotoURL = userData.photoURL

        // Get squad data
        const squadDoc = await db.collection("squads").doc(squadId).get()

        if (!squadDoc.exists) {
          console.error(`Squad document not found for squadId: ${squadId}`)
          return {
            success: false,
            error: "Squad document not found",
            squadId,
            userId,
          }
        }

        const squadData = squadDoc.data() as Squad
        const squadName = squadData.name || "Unknown Squad"

        console.log(`Processing notifications for new member ${newMemberName} joining ${squadName}`)

        // Create notifications for all existing squad members (excluding the new member)
        const notificationIds = await NotificationService.createSquadMemberJoinedNotifications(
          squadId,
          userId,
          newMemberName,
          newMemberPhotoURL,
          squadName
        )

        console.log(
          `Successfully created ${notificationIds.length} notifications for squad member join`
        )

        return {
          success: true,
          squadId,
          newMemberUserId: userId,
          newMemberName,
          squadName,
          notificationsCreated: notificationIds.length,
          notificationIds,
        }
      } catch (error) {
        console.error("Error in onSquadMemberJoin function:", error)

        // Don't throw the error to prevent function retries
        // Log the error and return a failure response
        return {
          success: false,
          error: error instanceof Error ? error.message : "Unknown error",
          squadId: context.params.squadId,
          userId: context.params.userId,
        }
      }
    }
  )
