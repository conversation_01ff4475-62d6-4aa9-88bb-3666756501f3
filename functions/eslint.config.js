const js = require("@eslint/js")
const tsPlugin = require("@typescript-eslint/eslint-plugin")
const tsParser = require("@typescript-eslint/parser")

module.exports = [
  js.configs.recommended,
  {
    files: ["src/**/*.{js,ts}"],
    plugins: {
      "@typescript-eslint": tsPlugin,
    },
    languageOptions: {
      parser: tsParser,
      parserOptions: {
        ecmaVersion: "latest",
        sourceType: "module",
        project: "./tsconfig.json",
      },
      globals: {
        console: "readonly",
        process: "readonly",
        Buffer: "readonly",
        __dirname: "readonly",
        __filename: "readonly",
        exports: "writable",
        module: "writable",
        require: "readonly",
        global: "readonly",
      },
    },
    rules: {
      // Basic rules
      quotes: ["error", "double"],
      "no-unused-vars": "off", // Let TypeScript handle this
      "@typescript-eslint/no-unused-vars": ["error", { argsIgnorePattern: "^_" }],

      // Disable strict formatting rules
      "no-trailing-spaces": "off",
      indent: "off",
      "max-len": "off",
      "eol-last": "off",
      "comma-dangle": "off",
      semi: "off",

      // Keep important rules
      "no-console": "off", // Allow console in Firebase Functions
      "no-undef": "off", // TypeScript handles this
    },
  },
  {
    // Ignore compiled files, node_modules, and config files
    ignores: ["lib/**/*", "node_modules/**/*", "eslint.config.js", "*.config.js"],
  },
]
