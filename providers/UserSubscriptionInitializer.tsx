"use client"

import { useEffect } from "react"
import { useAuthStore } from "@/lib/domains/auth/auth.store"
import { useUserSubscriptionStore } from "@/lib/domains/user-subscription/user-subscription.store"

import { UserSubscriptionRealtimeService } from "@/lib/domains/user-subscription"

export default function UserSubscriptionInitializer() {
  const { user: authUser, loading: authLoading } = useAuthStore()
  const { setCurrentSubscription, setLoading } = useUserSubscriptionStore()

  useEffect(() => {
    if (authLoading) return

    if (!authUser) {
      setCurrentSubscription(null)
      setLoading(false)
      return
    }

    setLoading(true)
    const unsubscribe = UserSubscriptionRealtimeService.subscribeToUserSubscription(
      authUser.uid,
      (subscription, error) => {
        if (error) console.error(error)
        setCurrentSubscription(subscription)
        setLoading(false)
      }
    )

    return () => unsubscribe()
  }, [setCurrentSubscription, setLoading, authUser, authLoading])

  return null
}
