"use client"

import { useEffect } from "react"
import { useAuthStore } from "@/lib/domains/auth/auth.store"
import { AuthRealtimeService } from "@/lib/domains/auth/auth.realtime.service"

export default function AuthInitializer() {
  const { setUser, setLoading } = useAuthStore()

  useEffect(() => {
    setLoading(true)
    const unsubscribe = AuthRealtimeService.subscribeToAuthState((user, error) => {
      if (error) console.error(error)
      setUser(user)
      setLoading(false)
    })

    return () => unsubscribe()
  }, [setUser, setLoading])

  return null
}
