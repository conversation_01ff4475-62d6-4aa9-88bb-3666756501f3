"use client"

import { useEffect } from "react"
import { useAuthStore } from "@/lib/domains/auth/auth.store"
import { useUserStore } from "@/lib/domains/user/user.store"
import { UserRealtimeService } from "@/lib/domains/user/user.realtime.service"

export default function UserInitializer() {
  const { user: authUser, loading: authLoading } = useAuthStore()
  const { setUser, setLoading } = useUserStore()

  useEffect(() => {
    if (authLoading) return

    if (!authUser) {
      setUser(null)
      setLoading(false)
      return
    }

    setLoading(true)
    const unsubscribe = UserRealtimeService.subscribeToUser(authUser.uid, (user, error) => {
      if (error) console.error(error)
      setUser(user)
      setLoading(false)
    })

    return () => unsubscribe()
  }, [setUser, setLoading, authUser, authLoading])

  return null
}
