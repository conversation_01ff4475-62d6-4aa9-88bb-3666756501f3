"use client"

import { useEffect } from "react"
import { useAuthStore } from "@/lib/domains/auth/auth.store"
import { useUserPreferencesStore } from "@/lib/domains/user-preferences/user-preferences.store"
import { UserPreferencesRealtimeService } from "@/lib/domains/user-preferences/user-preferences.realtime.service"

export default function UserPreferencesInitializer() {
  const { user: authUser, loading: authLoading } = useAuthStore()
  const { setUserPreferences, setLoading } = useUserPreferencesStore()

  useEffect(() => {
    if (authLoading) return

    if (!authUser) {
      setUserPreferences(null)
      setLoading(false)
      return
    }

    setLoading(true)
    const unsubscribe = UserPreferencesRealtimeService.subscribeToUserPreferences(
      authUser.uid,
      (userPreferences, error) => {
        if (error) console.error(error)
        setUserPreferences(userPreferences)
        setLoading(false)
      }
    )

    return () => unsubscribe()
  }, [setUserPreferences, setLoading, authUser, authLoading])

  return null
}
